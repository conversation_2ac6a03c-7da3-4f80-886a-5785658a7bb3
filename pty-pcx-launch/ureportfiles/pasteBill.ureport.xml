<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1" col-span="9"><cell-style font-size="12" forecolor="0,0,0" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[报销粘贴单]]></simple-value></cell><cell expand="None" name="A2" row="2" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[单位：]]></simple-value></cell><cell expand="Down" name="B2" row="2" col="2" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="agyName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D2" row="2" col="4" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[报销单号：]]></simple-value></cell><cell expand="Down" name="F2" row="2" col="6" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="billNo" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H2" row="2" col="8" row-span="4" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><zxing-value source="text" category="qrcode" width="100" height="100"><text><![CDATA[bill.select(billId)]]></text></zxing-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" italic="false" align="center" valign="middle"></cell-style><simple-value><![CDATA[部门：]]></simple-value></cell><cell expand="Down" name="B3" row="3" col="2" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="departmentName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D3" row="3" col="4" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[发票数量：]]></simple-value></cell><cell expand="Down" name="F3" row="3" col="6" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="ecsCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A4" row="4" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[经办：]]></simple-value></cell><cell expand="Down" name="B4" row="4" col="2" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="claimantName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D4" row="4" col="4" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[记账凭证号：]]></simple-value></cell><cell expand="Down" name="F4" row="4" col="6" col-span="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="vouNo" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A5" row="5" col="1"><cell-style font-size="8" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[报销单总金额：]]></simple-value></cell><cell expand="Down" name="B5" row="5" col="2" col-span="2"><cell-style font-size="8" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="inputAmt" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D5" row="5" col="4" col-span="2"><cell-style font-size="8" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[纸质发票总金额：]]></simple-value></cell><cell expand="Down" name="F5" row="5" col="6" col-span="2"><cell-style font-size="8" forecolor="0,0,0" font-family="宋体" align="left" valign="middle"></cell-style><dataset-value dataset-name="bill" aggregate="select" property="paperTotalAmt" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A6" row="6" col="1" col-span="6"><cell-style font-size="12" forecolor="204,204,204" font-family="宋体" align="center" valign="middle"><top-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[原始凭证粘贴处]]></simple-value></cell><cell expand="None" name="G6" row="6" col="7" col-span="3"><cell-style font-size="12" forecolor="204,204,204" font-family="宋体" align="center" valign="middle"><left-border width="1" style="dashed" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[请勿遮盖]]></simple-value></cell><cell expand="None" name="A7" row="7" col="1" col-span="6"><cell-style font-size="10" forecolor="204,204,204" font-family="宋体" align="center" valign="middle"><right-border width="2" style="dashed" color="0,0,0"/></cell-style><simple-value><![CDATA[可用空白纸粘贴原始凭证附在后方]]></simple-value></cell><cell expand="None" name="G7" row="7" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[序号]]></simple-value></cell><cell expand="None" name="H7" row="7" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[票据种类]]></simple-value></cell><cell expand="None" name="I7" row="7" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[票面金额]]></simple-value></cell><cell expand="None" name="A8" row="8" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B8" row="8" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C8" row="8" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D8" row="8" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="E8" row="8" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="F8" row="8" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><right-border width="2" style="dashed" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="Down" name="G8" row="8" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><dataset-value dataset-name="expDetail" aggregate="select" property="seq" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H8" row="8" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><dataset-value dataset-name="expDetail" aggregate="select" property="ecsBillType" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I8" row="8" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><dataset-value dataset-name="expDetail" aggregate="select" property="checkAmt" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A9" row="9" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B9" row="9" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C9" row="9" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D9" row="9" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="E9" row="9" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="F9" row="9" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><right-border width="2" style="dashed" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="G9" row="9" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="H9" row="9" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="I9" row="9" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="26"/><row row-number="2" height="18"/><row row-number="3" height="18"/><row row-number="4" height="19"/><row row-number="5" height="19"/><row row-number="6" height="19"/><row row-number="7" height="19"/><row row-number="8" height="19"/><row row-number="9" height="19"/><column col-number="1" width="83"/><column col-number="2" width="45"/><column col-number="3" width="75"/><column col-number="4" width="38"/><column col-number="5" width="45"/><column col-number="6" width="75"/><column col-number="7" width="38"/><column col-number="8" width="75"/><column col-number="9" width="75"/><datasource name="bill" type="spring" bean="pcxBillPrintData"><dataset name="bill" type="bean" method="getBillPrintData" clazz="com.pty.ureport2.entity.vo.pcx.PcxBill"><field name="approveStatus"/><field name="modifiedTime"/><field name="reason"/><field name="payTime"/><field name="completedTime"/><field name="departmentCode"/><field name="itemCode"/><field name="modifier"/><field name="vouNo"/><field name="inputAmt"/><field name="itemName"/><field name="createdTime"/><field name="id"/><field name="billNo"/><field name="auditCode"/><field name="departmentName"/><field name="vouId"/><field name="fundtypeCodes"/><field name="projectNames"/><field name="completed"/><field name="history"/><field name="loanAmt"/><field name="claimantUserCode"/><field name="isVou"/><field name="vouDate"/><field name="auditTime"/><field name="fiscal"/><field name="todoStatus"/><field name="checkAmt"/><field name="claimantName"/><field name="repayDate"/><field name="expenseNames"/><field name="bizType"/><field name="billFuncCode"/><field name="bizTypeName"/><field name="creatorName"/><field name="completedName"/><field name="content"/><field name="claimantCode"/><field name="expDepartmentCodes"/><field name="billFuncName"/><field name="billStatus"/><field name="transDate"/><field name="fundtypeNames"/><field name="attachCount"/><field name="creator"/><field name="expDepartmentNames"/><field name="mofDivCode"/><field name="agyCode"/><field name="completedStatus"/><field name="settlementAmt"/><field name="agyName"/><field name="expenseCodes"/><field name="tenantId"/><field name="ecsCount"/><field name="modifierName"/><field name="auditName"/><field name="payStatus"/><field name="projectCodes"/><field name="paperTotalAmt"/></dataset><dataset name="expDetail" type="bean" method="getBillExpDetail" clazz="com.pty.ureport2.entity.vo.pcx.PcxBillExpDetail"><field name="noEcsReason"/><field name="ecsBillKind"/><field name="source"/><field name="inputAmt"/><field name="expDetailCode"/><field name="ecsBillType"/><field name="id"/><field name="taxAmt"/><field name="seq"/><field name="mofDivCode"/><field name="agyCode"/><field name="checkReason"/><field name="ecsBillId"/><field name="ecsCheckStatus"/><field name="standAmt"/><field name="noEcsReasonName"/><field name="taxRate"/><field name="ecsAmt"/><field name="empCode"/><field name="fiscal"/><field name="expDetailName"/><field name="standReason"/><field name="billId"/><field name="expenseId"/><field name="ecsRelItemName"/><field name="tenantId"/><field name="checkAmt"/></dataset></datasource><paper type="A5" left-margin="23" right-margin="23"
    top-margin="0" bottom-margin="0" paging-mode="fitpage" fixrows="0"
    width="420" height="595" orientation="landscape" html-report-align="center" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>