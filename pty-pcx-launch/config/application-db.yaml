spring:
  datasource:
    pty:
      driverClassName: com.mysql.cj.jdbc.Driver
      jdbcUrl: ***********************************************************************************************************************************************************************************************************************************************************
      username: root
      password: V6LPNtYwLO4IYbXlRAARWEuSPWJ4gr+5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000
      maximumPoolSize: 5
      connectionTestQuery: select 1
    boss:
      driverClassName: com.mysql.cj.jdbc.Driver
      jdbcUrl: *********************************************************************************************************************************************************************************************************************************************************
      username: root
      password: V6LPNtYwLO4IYbXlRAARWEuSPWJ4gr+5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000
      maximumPoolSize: 5
      connectionTestQuery: select 1
  mybatis:
    config: ${user.dir}/config/mybatis.xml
