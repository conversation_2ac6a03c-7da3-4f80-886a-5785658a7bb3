<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.238-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-launch</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.pty.db</groupId>
            <artifactId>pty-db-starter</artifactId>
            <version>${db.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.cache</groupId>
            <artifactId>pty-cache-starter</artifactId>
            <version>${cache.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.message</groupId>
            <artifactId>pty-message-starter</artifactId>
            <version>${message.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.message</groupId>
            <artifactId>pty-message-sdk-starter</artifactId>
            <version>${message.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.search</groupId>
            <artifactId>pty-search-starter</artifactId>
            <version>${search.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-starter</artifactId>
            <version>${mad.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.ecs</groupId>
            <artifactId>pty-ecs-starter</artifactId>
            <version>${ecs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-starter</artifactId>
            <version>${pub.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.sso</groupId>
            <artifactId>pty-sso-starter</artifactId>
            <version>${sso.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-starter</artifactId>
            <version>${security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.setting</groupId>
            <artifactId>pty-setting-starter</artifactId>
            <version>${setting.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.portal</groupId>
            <artifactId>pty-portal-starter</artifactId>
            <version>${portal.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.pty.scheduler</groupId>
            <artifactId>pty-scheduler-starter</artifactId>
            <version>${scheduler.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.license</groupId>
            <artifactId>pty-license-starter</artifactId>
            <version>${license.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.fileservice</groupId>
            <artifactId>pty-fileservice-starter</artifactId>
            <version>${fileservice.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cherry</groupId>
            <artifactId>cherry-open-api</artifactId>
            <version>${cherry.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cherry</groupId>
            <artifactId>cherry-open-service</artifactId>
            <version>${cherry.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.balance</groupId>
            <artifactId>pty-balance-starter</artifactId>
            <version>${balance.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-starter</artifactId>
            <version>${ep.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.workflow2</groupId>
            <artifactId>pty-workflow2-starter</artifactId>
            <version>${wf2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.rule</groupId>
            <artifactId>pty-rule-starter</artifactId>
            <version>${rule.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.gip</groupId>
            <artifactId>pty-gip-starter</artifactId>
            <version>${gip.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.gal</groupId>
            <artifactId>pty-gal-starter</artifactId>
            <version>${gal.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.ureport2</groupId>
            <artifactId>pty-ureport2-starter</artifactId>
            <version>${ureport2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.bud</groupId>
            <artifactId>pty-bud-starter</artifactId>
            <version>${bud.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.workflow</groupId>
            <artifactId>pty-workflow-starter</artifactId>
            <version>${wf.version}</version>
        </dependency>

        <!-- 合同需要引入的 -->
        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-starter</artifactId>
            <version>${pct.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-mock</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pty.doc</groupId>
            <artifactId>pty-doc-starter</artifactId>
            <version>4.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pty.billsetting</groupId>
            <artifactId>pty-billsetting-starter</artifactId>
        </dependency>
    </dependencies>
</project>
