package com.pty.pcx.common.enu;

import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 航空公司与席别的关系
 */
@Deprecated // 使用 org.pty.mad.api.IMadExtDataService.getCabinName 替代
@AllArgsConstructor
@Getter
public enum AirlineSeatEnum {
    enum_001("CA","F,A","J,C,D,Z,R"),
    enum_002("SC","",   "C,D,P,I"),
    enum_003("ZH","",   "J,C,D,Z,R"),
    enum_004("MU","F",  "U,J,C,D,Q,I"),
    enum_005("FM","F",  "U,J,C,D,Q,I"),
    enum_006("CZ","F",  "J,C,D,I"),
    enum_007("MF","F",  "J,C,D,I"),
    enum_008("HU","",   "J,C,D,Z,R,I"),
    enum_009("GS","",   "C,D,R,I"),
    enum_010("3U","",   "C,I,J,P"),
    enum_011("JD","",   "J,C,D,Z,I"),
    enum_012("8L","",   "C,C1,C2,C3,C4,D"),
    enum_013("HO","",   "J,C,D,R,I,A"),
    enum_014("9C","",   ""),
    enum_015("G5","",   "C,A"),
    enum_016("EU","F,A","C"),
    enum_017("GJ","F,A","C,S,O,C1,C2"),
    enum_018("DR","",   "J,C,A"),
    enum_019("TV","",   "C,D,A,I"),
    enum_020("QW","",   "C,A,O");

    private String airlineCode;//航空公司代码
    private String firstCabinCode;//头等舱代码
    private String businessCabinCode;//公务舱代码

    private static final Map<String,String> CACHE_CABIN_MAP = new HashMap<>();

    /**
     * 根据航空公司名称以及舱位代码，获取席别的中文（头等舱、经济舱）
     * @param airlineCode       航空公司代码
     * @param cabinCode         舱位代码
     * @return
     */
    public static String getCabinName(String airlineCode,String cabinCode){
        if(CACHE_CABIN_MAP.isEmpty()){
            Map<String,String> cabinMap = new HashMap<>();
            for (AirlineSeatEnum value : AirlineSeatEnum.values()) {
                String[] fistCabinCodeList = StringUtil.nullToEmpty(value.getFirstCabinCode()).split(",");
                String[] businessCabinCodeList = StringUtil.nullToEmpty(value.getBusinessCabinCode()).split(",");

                Map<String, String> fistCabinMap = Arrays.stream(fistCabinCodeList).filter(a->StringUtil.isNotEmpty(a)).collect(Collectors.toMap(b -> value.getAirlineCode() + "-" + b, c -> "头等舱", (a, b) -> a));
                Map<String, String> businessCabinMap = Arrays.stream(businessCabinCodeList).filter(a->StringUtil.isNotEmpty(a)).collect(Collectors.toMap(b -> value.getAirlineCode() + "-" + b, c -> "头等舱", (a, b) -> a));

                cabinMap.putAll(fistCabinMap);
                cabinMap.putAll(businessCabinMap);
            }
            CACHE_CABIN_MAP.putAll(cabinMap);
        }
        //枚举中的头等舱和公务舱 对应 我们飞机的头等舱    不在枚举范围内的，则属于我们飞机的经济舱
        return StringUtil.isEmpty(CACHE_CABIN_MAP.get(airlineCode + "-" + cabinCode)) ? "经济舱" : CACHE_CABIN_MAP.get(airlineCode + "-" + cabinCode);
    }
}
