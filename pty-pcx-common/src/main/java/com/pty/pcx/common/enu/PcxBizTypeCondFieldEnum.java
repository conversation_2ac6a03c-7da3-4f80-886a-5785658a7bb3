package com.pty.pcx.common.enu;

import com.pty.pub.common.exception.CommonException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 票类规则条件字段枚举
 * createdTime: 2024/12/10  下午1:54
 * creator: wangbao
 **/
@AllArgsConstructor
@Getter
public enum PcxBizTypeCondFieldEnum {
    ECS_BIZ_AMT("pcx.ecsBizAmt", "票面价格", "Number"),
    ECS_BIZ_REMARK("pcx.ecsBizRemark", "票面备注", "String"),
    ;
    private String code;
    private String name;
    private String dataType;

    private static final List<Map<String, Object>> resultList = new ArrayList<>();
    public static List<Map<String, Object>> getList() {

        if (resultList.size() > 0) {
            return resultList;
        }
        for (PcxBizTypeCondFieldEnum element : PcxBizTypeCondFieldEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", element.getCode());
            map.put("name", element.getName());
            map.put("dataType", element.getDataType());
            resultList.add(map);
        }
        return resultList;
    }

    public static PcxBizTypeCondFieldEnum getByCode(String code) {
        for (PcxBizTypeCondFieldEnum element : PcxBizTypeCondFieldEnum.values()) {
            if (element.getCode().equals(code)) {
                return element;
            }
        }
        throw new CommonException("获取PcxBizTypeCondFieldEnum枚举getByCode："+code+"错误");
    }
}
