package com.pty.pcx.common.constant;

/**
 * Description: 分隔符常量类
 * createdTime: 2024/12/23  下午1:36
 * creator: wangbao
 **/
public class PcxDelimiterConstant {

    /**
     * 包含空格：and
     */
    public static final String AND = " and ";

    /**
     * 包含空格：且
     */
    public static final String QIE = " 且 ";

    /**
     * 包含空格：or
     */
    public static final String OR = " or ";

    /**
     * 包含空格：或
     */
    public static final String HUO = " 或 ";

    /**
     * 字符串标志性符号：双引号
     */
    public static final String STRING_SYMBOL = "\"";

    /**
     * 左括号
     */
    public static final String LEFT_BRACKET = "(";

    /**
     * 右括号
     */
    public static final String RIGHT_BRACKET = ")";

    /**
     * 票类规则名称的组装符号：短横杠
     */
    public static final String RULE_NAME_CONNECTOR = "-";
}
