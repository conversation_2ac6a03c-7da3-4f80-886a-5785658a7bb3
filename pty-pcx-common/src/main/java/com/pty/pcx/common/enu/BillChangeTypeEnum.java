package com.pty.pcx.common.enu;

import lombok.Getter;

@Getter
public enum BillChangeTypeEnum {

    // 0:基本信息 1 结算方式 2 预算指标
    BASIC_INFO("基本信息", "0"),
    SETTLEMENT_TYPE("结算方式", "1"),
    BUDGET_INDEX("预算指标", "2");

    private final String name;
    private final String code;
    public static BillChangeTypeEnum getByCode(String code) {
        for (BillChangeTypeEnum item : BillChangeTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    BillChangeTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
