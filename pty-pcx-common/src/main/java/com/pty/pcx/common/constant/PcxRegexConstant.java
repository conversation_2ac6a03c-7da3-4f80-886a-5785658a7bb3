package com.pty.pcx.common.constant;

/**
 * Description: 正则表达式常量类
 * createdTime: 2024/12/23  下午1:37
 * creator: wangbao
 **/
public class PcxRegexConstant {
    /**
     * 票类规则-字符串字段的筛选规则的正则表达式：例如 pcx.ecsBizRemark.contains(\"博思软件\")
     */
    public static final String BIZ_TYPE_STRING_FIELD_RULE = "([a-zA-Z0-9._]+\\.[a-zA-Z]+)(\\.[a-zA-Z]+)\\(\\\"([^\"]+)\\\"\\)";

    /**
     * 票类规则-数字字段的筛选规则的正则表达式：例如 pcx.ecsBizAmt>1000
     */
    public static final String BIZ_TYPE_NUMBER_FIELD_RULE = "([a-zA-Z0-9._]+)([><=!]+)(\\d+)";
}
