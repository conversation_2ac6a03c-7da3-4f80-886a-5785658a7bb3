package com.pty.pcx.common.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

    @Getter
    @AllArgsConstructor
    public enum BillChangeStatusEnum {

        PENDING_SUBMISSION("变更内容待提交", "0"),
        SUBMITTED("变更内容已提交", "1"),
        AWAIT_CONFIRMATION("待确认变更", "2"),
        CONFIRMED("已确认变更", "3"),
        ;

        /**
         * 状态名称
         */
        private final String name;
        /**
         * 状态编码
         */
        private final String code;
    }
