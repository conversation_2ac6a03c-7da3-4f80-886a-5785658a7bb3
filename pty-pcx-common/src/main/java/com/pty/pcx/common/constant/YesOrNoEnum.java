package com.pty.pcx.common.constant;

public enum YesOrNoEnum {
    NO("0", "否"),
    YES("1", "是");

    private final String code;
    private final String desc;

    YesOrNoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (YesOrNoEnum value : YesOrNoEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
