package com.pty.pcx.common.enu;

import com.google.common.collect.Maps;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum NoEcsReasonEnum {

    GOVERNMENT_CAR("1","公务用车", PcxConstant.TRAVEL_DETAIL_3021101),
    OFFICIAL_CAR_ON_BOARD("2","公车随程", PcxConstant.TRAVEL_DETAIL_3021101),
    LOST("3","票据遗失", PcxConstant.TRAVEL_DETAIL_3021101),
    HAVE_EXP("4","票据已报", PcxConstant.TRAVEL_DETAIL_3021101),
    UN_BACK("5","出差未回", PcxConstant.TRAVEL_DETAIL_3021101),
    OTHER("6","其他原因", PcxConstant.TRAVEL_DETAIL_3021101),
    CHANNEL_ECS("7","渠道订票", PcxConstant.TRAVEL_DETAIL_3021101),
    HOME("1","回家住", PcxConstant.TRAVEL_DETAIL_3021102),
    OTHER_SIDE_PROVIDER("2","对方提供住宿", PcxConstant.TRAVEL_DETAIL_3021102),
    NO_HOTEL("3","无住宿", PcxConstant.TRAVEL_DETAIL_3021102),
    NO_SEPARATE_BILL("4","无单独开票", PcxConstant.TRAVEL_DETAIL_3021102),
    MARGINAL_AREA_TRAVEL("5","边缘地区出差", PcxConstant.TRAVEL_DETAIL_3021102),
    DORMITORY("6","住宿舍", PcxConstant.TRAVEL_DETAIL_3021102),

    ;
    private String code;
    private String name;
    private String expenseTypeCode;

    private final static Map<String, Map<String, String>> noEcsReasonMap = new HashMap<>();
    static {
        for (NoEcsReasonEnum value : values()) {
            Map<String, String> itemMap = noEcsReasonMap.computeIfAbsent(value.getExpenseTypeCode(), key -> new HashMap<>());
            itemMap.put(value.getCode(), value.getName());
        }
    }
    public static List<NoEcsReasonEnum> getByExpenseCode(String expenseTypeCode){
        List<NoEcsReasonEnum> result = new ArrayList<>();
        for (NoEcsReasonEnum value : values()) {
            if (value.getExpenseTypeCode().equals(expenseTypeCode)){
                result.add(value);
            }
        }
        return result;
    }


    public static String getDescByCode(String expDetailCode, String noEcsReason) {
        if (StringUtil.isEmpty(noEcsReason)){
            return noEcsReason;
        }
        Map<String, String> itemMap  = noEcsReasonMap.getOrDefault(expDetailCode, Maps.newHashMap());
        return itemMap.getOrDefault(noEcsReason, noEcsReason);
    }
}
