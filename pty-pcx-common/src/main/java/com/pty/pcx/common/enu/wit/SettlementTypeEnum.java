package com.pty.pcx.common.enu.wit;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SettlementTypeEnum {
    SETTLE_TRANSFER("settle_transfer","个人转账", "3"),
    SETTLE_BUSI_TRANSFER("settle_busi_transfer","对公支付", "5"),
    SETTLE_BUSICARD("settle_busicard","公务卡", "4"),
    SETTLE_CASH("settle_cash","现金", "1"),
    SETTLE_CHEQUE("settle_cheque", "支票", "2"),
    ;
    private String code;
    private String name;
    private String bkSettlementType;

    public static SettlementTypeEnum getByCode(String code){
        for (SettlementTypeEnum type : SettlementTypeEnum.values()) {
            if(type.getCode().equals(code)){
                return type;
            }
        }
        return null;
    }

    //根据code获取name
    public static String getNameByCode(String code){
        for (SettlementTypeEnum type : SettlementTypeEnum.values()) {
            if(type.getCode().equals(code)){
                return type.getName();
            }
        }
        return "";
    }

    //根据code获取bkSettlementType
    public static String getBKSettlementByCode(String code){
        for (SettlementTypeEnum type : SettlementTypeEnum.values()) {
            if(type.getCode().equals(code)){
                return type.getBkSettlementType();
            }
        }
        return "";
    }
}
