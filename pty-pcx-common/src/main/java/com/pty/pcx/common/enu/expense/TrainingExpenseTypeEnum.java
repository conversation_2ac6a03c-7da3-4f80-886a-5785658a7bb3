package com.pty.pcx.common.enu.expense;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum TrainingExpenseTypeEnum {
    // 3021601 开头的费用类型
    EXPENSE_CODE_3021601("3021601", "综合定额", "30216", "30216"),
    EXPENSE_CODE_302160101("302160101", "住宿费", "3021601", "30216"),
    EXPENSE_CODE_302160102("302160102", "伙食费", "3021601", "30216"),
    EXPENSE_CODE_302160103("302160103", "交通、场地、资料费", "3021601", "30216"),
    EXPENSE_CODE_302160106("302160106", "其他费用", "3021601", "30216"),

    // 3021602 开头的费用类型
    EXPENSE_CODE_3021602("3021602", "师资费", "30216", "30216"),
    EXPENSE_CODE_302160201("302160201", "讲课费", "3021602", "30216"),
    EXPENSE_CODE_302160202("302160202", "住宿费", "3021602", "30216"),
    EXPENSE_CODE_302160203("302160203", "伙食费", "3021602", "30216");

    private final String code;
    private final String name;
    private final String parentCode;
    private final String lastCode;

    TrainingExpenseTypeEnum(String code, String name, String parentCode, String lastCode) {
        this.code = code;
        this.name = name;
        this.parentCode = parentCode;
        this.lastCode = lastCode;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static TrainingExpenseTypeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 code 获取与其 name 相同的所有枚举值列表
     */
    public static List<TrainingExpenseTypeEnum> getSameNameGroupByCode(String code) {
        TrainingExpenseTypeEnum target = getByCode(code);
        if (target == null) {
            return new ArrayList<>();
        }
        return Arrays.stream(values())
                .filter(type -> type.getName().equals(target.getName()))
                .collect(Collectors.toList());
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getParentCode() {
        return parentCode;
    }

    public String getLastCode() {
        return lastCode;
    }
}