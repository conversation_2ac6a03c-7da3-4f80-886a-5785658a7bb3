package com.pty.pcx.common.util;

import com.pty.pub.common.bean.Response;
import lombok.Data;
import lombok.Getter;

@Data
public class CheckMsg<T> {
    /**
     * 20240716 业务标记码-通用的需要前端页面二次确认逻辑
     * 用不着每个场景用不同的错误编码,如果需要增加找tianlei沟通
     */
    public static final long STATUS_BIZ_NEED_CONFIRM = 409;

    @Getter
    private String msgInfo;
    private boolean success = true;
    private T data;
    private Long errorCode;


    public CheckMsg<T> setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
        return this;
    }


    public CheckMsg<T> setSuccess(boolean stu) {
        this.success = stu;
        return this;
    }

    public T getData() {
        return data;
    }

    public CheckMsg<T> setData(T data) {
        this.data = data;
        return this;
    }


    public CheckMsg<T> setErrorCode(Long errorCode) {
        this.errorCode = errorCode;
        return this;
    }
    public CheckMsg<T> success(CheckCallback callback) throws Exception {
        if (this.isSuccess()) {
            return callback.doCallback();
        }
        return this;
    }

    public CheckMsg<T> fail(CheckCallback callback) throws Exception {
        if (!this.isSuccess()) {
            callback.doCallback();
        }
        return this;
    }

    public static <T> CheckMsg<T> fail(String... msg) {
        CheckMsg<T> check = new CheckMsg<>();
        check.setSuccess(false);
        check.setErrorCode(Long.valueOf(Response.FAIL_CODE));
        if (msg != null && msg.length > 0) {
            check.setMsgInfo(msg[0]);
        }
        return check;
    }


    public static <T> CheckMsg<T> success(T data) {
        CheckMsg<T> check = new CheckMsg<>();
        check.setData(data);
        return check;
    }

    public static <T> CheckMsg<T> success(T data,String msg) {
        CheckMsg<T> check = new CheckMsg<>();
        check.setData(data);
        check.setMsgInfo(msg);
        return check;
    }

    public static <T> CheckMsg<T> success(String... msg) {
        CheckMsg<T> check = new CheckMsg<>();
        if (msg != null && msg.length > 0) {
            check.setMsgInfo(msg[0]);
        }
        return check;
    }

    public static <T> CheckMsg<T> successStr(String... msg) {
        CheckMsg<T> check = new CheckMsg<>();
        if (msg != null && msg.length > 0) {
            check.setMsgInfo(msg[0]);
        }
        return check;
    }

    public interface CheckCallback {
         CheckMsg doCallback() throws Exception;
    }

}
