package com.pty.pcx.common.enu.wit;

public enum RuleOperatorEnum {

    ADD("+", "加"),
    SUB("-", "减"),
    MULT("*","乘"),
    DIV("/", "除"),
    ENDOW("=", "赋值"),
    EQUALITY("==", "相等"),
    INEQUALITY("<>", "不等于"),
    GREATER(">", "大于"),
    GREATER_OR_EQUALITY(">=", "大于等于"),
    LESS("<", "小于"),
    LESS_OR_EQUALITY("<=", "小于等于"),
    NOT(" not ", "取反"),
    AND(" and ", "并且"),
    OR(" or ", "或者");


    private String operator;

    private String operatorDesc;

    RuleOperatorEnum(String operator, String operatorDesc) {
        this.operator = operator;
        this.operatorDesc = operatorDesc;
    }


    public String getOperator() {
        return operator;
    }

    public String getOperatorDesc() {
        return operatorDesc;
    }
}
