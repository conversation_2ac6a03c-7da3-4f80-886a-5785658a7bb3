package com.pty.pcx.common.util;


import cn.hutool.core.date.DateUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;

public class PcxDateUtil extends DateUtil {

    public static long betweenMinuteDateTime(String startTime, String endTime){
        if (startTime.compareTo(endTime) > 0){
            return betweenMinuteDateTime(endTime, startTime);
        }
        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        return start.until(end, java.time.temporal.ChronoUnit.MINUTES);
    }

    /**
     * 计算两个日期之间的天数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 天数
     */
    public static Integer calculateDays(String startTime,  String endTime){
        if (startTime == null || endTime == null || startTime.trim().isEmpty() || endTime.trim().isEmpty()) {
            return 0;
        }

        try {
            LocalDate start = LocalDate.parse(startTime.trim());
            LocalDate end = LocalDate.parse(endTime.trim());
            long daysBetween = ChronoUnit.DAYS.between(start, end);

            if (daysBetween >= 0) {
                //确保不会溢出 Integer 范围（根据业务场景决定是否需要）
                if (daysBetween + 1 > Integer.MAX_VALUE) {
                    throw new IllegalArgumentException("时间跨度太大，天数计算溢出");
                }
                return (int)(daysBetween + 1);
            } else {
                throw new IllegalArgumentException("开始时间不能大于结束时间");
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("时间格式不正确，应为 YYYY-MM-DD 格式", e);
        }

    }


}
