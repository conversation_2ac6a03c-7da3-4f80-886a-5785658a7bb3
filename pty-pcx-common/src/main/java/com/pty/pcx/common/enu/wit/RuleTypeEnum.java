package com.pty.pcx.common.enu.wit;

import lombok.Getter;

@Getter
public enum RuleTypeEnum {
    REAL("REAL", "业务真实性"),
    LEGAL("LEGAL", "规范性"),
    RATIONAL("RATIONAL", "票据合规性"),
    COMPLETE("COMPLETE", "单据完整性"),
    CORRECT("CORRECT", "金额正确性"),
    TIMELY("TIMELY", "及时性审核");

    private String code;

    private String name;

    RuleTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }
}
