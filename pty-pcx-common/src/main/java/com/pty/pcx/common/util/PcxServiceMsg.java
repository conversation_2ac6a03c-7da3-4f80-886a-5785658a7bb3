package com.pty.pcx.common.util;

public class PcxServiceMsg {

	public static final int STATUS_BIZ_SUCCESS = 200;

	/**
	 * 业务标记码-需要前台再次确认情形
	 */
	public static final int STATUS_BIZ_NEED_CONFIRM = 409;

	/**
	 * 业务标记码-需要前台再次确认是否
	 */
	public static final int STATUS_BIZ_NEED_LOGIC_CHOICE = 411;

	private String msgInfo;

	private Integer statusCode;

	private Object data;

	public boolean isSuccess() {
		if (statusCode == null) {
			return false;
		}
		if (statusCode.intValue() == STATUS_BIZ_SUCCESS) {
			return true;
		}
		return false;
	}

	public static PcxServiceMsg fail(String... msg) {
		return fail(STATUS_BIZ_NEED_CONFIRM, msg);
	}

	public static PcxServiceMsg failChoice(String... msg) {
		return fail(STATUS_BIZ_NEED_LOGIC_CHOICE, msg);
	}

	public static PcxServiceMsg success(String... msg) {
		PcxServiceMsg check = new PcxServiceMsg();
		check.setStatusCode(STATUS_BIZ_SUCCESS);
		if (msg != null && msg.length > 0) {
			check.setMsgInfo(msg[0]);
		}
		return check;
	}

	public static PcxServiceMsg fail(Integer statusCode, String... msg) {
		PcxServiceMsg check = new PcxServiceMsg();
		check.setStatusCode(statusCode);
		if (msg != null && msg.length > 0) {
			check.setMsgInfo(msg[0]);
		}
		return check;
	}

	public PcxServiceMsg setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
		return this;
	}

	public Object getData() {
		return data;
	}

	public PcxServiceMsg setData(Object data) {
		this.data = data;
		return this;
	}

	public String getMsgInfo() {
		return msgInfo;
	}

	public PcxServiceMsg setMsgInfo(String msgInfo) {
		this.msgInfo = msgInfo;
		return this;
	}

}
