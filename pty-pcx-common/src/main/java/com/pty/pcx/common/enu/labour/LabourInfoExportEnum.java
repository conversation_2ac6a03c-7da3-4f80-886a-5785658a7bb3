package com.pty.pcx.common.enu.labour;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 劳务人员信息导出
 */
public enum LabourInfoExportEnum {

    ORGANIZATION("单位", "organization"),
    USER_NAME("姓名", "userName"),
    LABOUR_CODE("劳务人员编号", "labourCode"),
    FUNC_LEVEL("职称", "funcLevel"),
    ID("收款行编号","id"),
    ID_TYPE_NAME("证件类型","idTypeName"),
    ID_NO("证件号码","idNo"),
    ACCOUNT_NO("卡号","accountNo"),
    ACCOUNT_NAME("户名","accountName"),
    WORKER_NUM("开户行","bankName"),
    PHONENO("手机号", "phoneNo"),
    MONEY("金额",""); //只需要导出列，不需要导出数据


    private String title;
    private String code;

    LabourInfoExportEnum(String title, String code){
        this.title = title;
        this.code = code;

    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String code) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    private static final List<Map<String,Object>> ress = new ArrayList<>();

    public static List<Map<String,Object>> getList() {

        if (ress.size() > 0) {
            return ress;
        }
        for (LabourInfoExportEnum levExportEnum : LabourInfoExportEnum.values()) {
            Map<String,Object>  map = new HashMap<>();
            map.put("title",levExportEnum.getTitle());
            map.put("code",levExportEnum.getCode());
            ress.add(map);
        }
        return ress;
    }
}
