package com.pty.pcx.common.util;


import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 单据类型代码工具类
 * 用于处理新字段budgetCtrl与旧字段isCtrlBudget之间的转换
 */
public class BudgetCtrlUtil {


    /**
     * 检查预算控制是否启用指定类型
     * @param budgetCtrl 预算控制类型（逗号分隔）
     * @param targetType 目标类型
     * @return 是否启用预算控制
     */
    public static boolean isBudgetCtrlEnabled(String budgetCtrl, String targetType) {
        if (StringUtils.isBlank(budgetCtrl) || StringUtils.isBlank(targetType)) {
            return false;
        }
        
        List<String> types = Arrays.asList(budgetCtrl.split(","));
        return types.contains(targetType.trim());
    }
}