package com.pty.pcx.common.enu;


import com.pty.pcx.common.constant.PcxConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ExtraSubsidyTypeEnum {

    EXTRA_WORK(1,"出差节假日加班", PcxConstant.HOLIDAY_WORK_SUBSIDY),
    MANAGE(2,"项目经理",PcxConstant.PM_SUBSIDY);

    private Integer code;
    private String name;
    private String valsetCode;

    public static ExtraSubsidyTypeEnum getEnumByCode(Integer extraType) {
        for (ExtraSubsidyTypeEnum value : ExtraSubsidyTypeEnum.values()) {
            if (value.getCode().equals(extraType)) {
                return value;
            }
        }
        return null;
    }

    public static ExtraSubsidyTypeEnum getEnumByValsetCode(String valsetCode) {
        for (ExtraSubsidyTypeEnum value : ExtraSubsidyTypeEnum.values()) {
            if (value.getValsetCode().equals(valsetCode)) {
                return value;
            }
        }
        return null;
    }
}
