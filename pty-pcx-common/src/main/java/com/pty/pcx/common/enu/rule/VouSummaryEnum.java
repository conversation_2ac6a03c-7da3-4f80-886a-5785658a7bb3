package com.pty.pcx.common.enu.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:  自定义凭证摘要枚举
 * @date: 2025/1/16 14:38
 */
public enum VouSummaryEnum {

  DEPARTMENT("departmentName","部门"),
  CLAIMANT("claimantName","报销人"),
  REASON("reason","报销事由"),
  EXPENSE_TYPE("expenseName","费用类型名称"),
  BILL_TYPE("billTypeName","单据类型名称"),
  STAFF("staff","出差人"),
  ITEM_NAME("name","（会议/培训）名称"),
  HT_INFO("htInfo","合同明细"),
  ANY_STRING("anyString","自定义字符串");


  private String code;
  private String name;
  private static final List<Map<String, Object>> ress = new ArrayList<>();

  private VouSummaryEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public static List<Map<String, Object>> getList() {

    if (ress.size() > 0) {
      return ress;
    }
    for (VouSummaryEnum gse : VouSummaryEnum.values()) {
      Map<String, Object> map = new HashMap();
      map.put("code", gse.getCode());
      map.put("name", gse.getName());
      ress.add(map);
    }
    return ress;
  }
}
