package com.pty.pcx.common.enu.wit;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum RuleElementEnum {

    // 主单据
    CONTENTBILL("pex.pexExpenseBill.contentBill", "MAIN"),
    // 经费来源
    FUNDSOURCE("pex.pexExpenseBill.fundSources", "FUND"),
    // 费用明细
    EXPENSE("pex.pexExpenseBill.expenses", "EXPENSE"),
    // 汇款转账
    TRANSFERSETTLEMENT("pex.pexExpenseBill.settlements.transferSettlement" ,"TRANS"),
    // 现金
    CASHSETTLEMENT("pex.pexExpenseBill.settlements.cashSettlement" ,"CASHS"),
    // 支票
    CHEQUESETTLEMENT("pex.pexExpenseBill.settlements.chequeSettlement" ,"CHEQS"),
    // 公务卡
    CARDSETTLEMENT("pex.pexExpenseBill.settlements.cardSettlement" ,"CARDS"),
    // 发票
    INVOICE("pex.pexExpenseBill.invoiceInfo", "INVOICE");

    private String oriElement;

    private String convertElement;


    private static final Map<String, RuleElementEnum> CACHE_ELEMENT_MAP = new ConcurrentHashMap<>();

    RuleElementEnum(String oriElement, String convertElement) {
        this.oriElement = oriElement;
        this.convertElement = convertElement;
    }


    public String getOriElement() {
        return oriElement;
    }

    public String getConvertElement() {
        return convertElement;
    }



    public static String getOriElement(String convertElement) {
        if(CACHE_ELEMENT_MAP.isEmpty()){
            CACHE_ELEMENT_MAP.putAll(Arrays.stream(RuleElementEnum.values()).collect(Collectors.toMap(en->en.getConvertElement(), Function.identity(),(a, b)->a)));
        }
        RuleElementEnum ruleElementEnum = CACHE_ELEMENT_MAP.get(Objects.isNull(convertElement) ? "" : convertElement);
        return Objects.isNull(ruleElementEnum) ? "" : ruleElementEnum.getOriElement();
    }
}
