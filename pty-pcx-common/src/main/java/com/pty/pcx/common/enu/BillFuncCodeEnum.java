package com.pty.pcx.common.enu;

import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum BillFuncCodeEnum {

    APPLY("申请单", "apply", "1"),
    EXPENSE("报销单", "expense", "2"),
    LOAN("借款单", "loan", "3"),
    REPAYMENT("还款单", "repay", "4"),
    CONTRACT("合同单", "ht", "5"),
    PLAN("计划单", "plan", "6"),
    PREPAYMENT("对公预付", "prepayment", "7"),
    ARRIVAL("到票报销", "arrival", "8"),
    REFUND("对公预付退款", "refund", "9"),
    ;

    private final String name;
    private final String code;
    private final String funcCode;

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static String getNameByCode(String code) {
        for (BillFuncCodeEnum item : BillFuncCodeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.name;
            }
        }
        return "";
    }

    public static String getNameByFuncCode(String funcCode) {
        for (BillFuncCodeEnum billFuncCodeEnum : BillFuncCodeEnum.values()) {
            if (billFuncCodeEnum.getFuncCode().equals(funcCode)) {
                return billFuncCodeEnum.getName();
            }
        }
        return StringUtil.EMPTY;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static BillFuncCodeEnum getByCode(String code) {
        for (BillFuncCodeEnum item : BillFuncCodeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    private static final List<Map<String, Object>> resultList = new ArrayList<>();
    public static List<Map<String, Object>> getList() {

        if (resultList.size() > 0) {
            return resultList;
        }
        for (BillFuncCodeEnum element : BillFuncCodeEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", element.getCode());
            map.put("name", element.getName());
            map.put("funcCode", element.getFuncCode());
            resultList.add(map);
        }
        return resultList;
    }
}