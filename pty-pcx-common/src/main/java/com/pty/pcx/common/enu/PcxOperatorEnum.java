package com.pty.pcx.common.enu;

import com.pty.pub.common.exception.CommonException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum PcxOperatorEnum {
    EQUALS(0, "等于（字符串）", ".equals", "String"),
    BAOHAN(1, "包含", ".contains", "String"),
    DAYUDENGYU(2, "大于等于", ">=", "Number"),
    DAYU(3, "大于", ">", "Number"),
    DENGYU(4, "等于（数字）", "=", "Number"),
    XIAOYU(5, "小于", "<", "Number"),
    XIAOYUDENGYU(6, "小于等于", "<=", "Number"),
    BUDENGYU(7, "不等于", "!=", "Number"),
    STARTSWITH(8, "开头为", ".startsWith", "String"),
    ENDSWITH(9, "结尾为", ".endsWith", "String"),
    ;
    private Integer code;
    private String name;
    private String symbol;
    private String dataType;

    private static final List<Map<String, Object>> ress = new ArrayList<>();
    public static List<Map<String, Object>> getList() {

        if (ress.size() > 0) {
            return ress;
        }
        for (PcxOperatorEnum gse : PcxOperatorEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", gse.getCode());
            map.put("name", gse.getName());
            map.put("symbol", gse.getSymbol());
            map.put("dataType", gse.getDataType());
            ress.add(map);
        }
        return ress;
    }

    public static PcxOperatorEnum getByCode(Integer code) {
        for (PcxOperatorEnum element : PcxOperatorEnum.values()) {
            if (element.getCode().equals(code)) {
                return element;
            }
        }
        throw new CommonException("获取PcxOperatorEnum枚举getByCode："+code+"错误");
    }

    public static PcxOperatorEnum getBySymbol(String symbol) {
        for (PcxOperatorEnum element : PcxOperatorEnum.values()) {
            if (element.getSymbol().equals(symbol)) {
                return element;
            }
        }
        throw new CommonException("获取PcxOperatorEnum枚举getBySymbol："+symbol+"错误");
    }
}
