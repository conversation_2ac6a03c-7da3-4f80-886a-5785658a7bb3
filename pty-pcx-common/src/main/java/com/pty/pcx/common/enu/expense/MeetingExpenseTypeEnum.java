package com.pty.pcx.common.enu.expense;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum MeetingExpenseTypeEnum {
    // 3021501 开头的费用类型
    EXPENSE_CODE_3021501("3021501", "综合定额", "30215", "30215"),
    EXPENSE_CODE_302150101("302150101", "住宿费", "3021501", "30215"),
    EXPENSE_CODE_302150102("302150102", "伙食费", "3021501", "30215"),
    EXPENSE_CODE_302150103("302150103", "其他费用", "3021501", "30215"),

    // 3021502 开头的费用类型
    EXPENSE_CODE_3021502("3021502", "线上费用", "30215", "30215"),

    // 3021503 开头的费用类型
    EXPENSE_CODE_3021503("3021503", "专家咨询费", "30215", "30215"),
    ;

    private final String code;
    private final String name;
    private final String parentCode;
    private final String lastCode;

    MeetingExpenseTypeEnum(String code, String name, String parentCode, String lastCode) {
        this.code = code;
        this.name = name;
        this.parentCode = parentCode;
        this.lastCode = lastCode;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static MeetingExpenseTypeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 code 获取与其 name 相同的所有枚举值列表
     */
    public static List<MeetingExpenseTypeEnum> getSameNameGroupByCode(String code) {
        MeetingExpenseTypeEnum target = getByCode(code);
        if (target == null) {
            return new ArrayList<>();
        }
        return Arrays.stream(values())
                .filter(type -> type.getName().equals(target.getName()))
                .collect(Collectors.toList());
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getParentCode() {
        return parentCode;
    }

    public String getLastCode() {
        return lastCode;
    }
}