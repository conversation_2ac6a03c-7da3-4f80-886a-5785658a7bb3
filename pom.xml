<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.pty.pcx</groupId>
    <artifactId>pty-pcx</artifactId>
    <version>4.0.1.238-ENT-SNAPSHOT</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.pty</groupId>
        <artifactId>pty-parent</artifactId>
        <version>4.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <db.version>4.0.2.4-ENT-SNAPSHOT</db.version>
        <sso.version>4.2.0.1-ENT-SNAPSHOT</sso.version>
        <pub.version>4.0.4.10-ENT-SNAPSHOT</pub.version>
        <license.version>4.1.0-SNAPSHOT</license.version>
        <cache.version>4.0.1-SNAPSHOT</cache.version>
        <security.version>4.7.0.25-ENT-SNAPSHOT</security.version>
        <search.version>4.0.0-SNAPSHOT</search.version>
        <message.version>4.1.0.15-ENT-SNAPSHOT</message.version>
        <setting.version>4.0.4-SNAPSHOT</setting.version>
        <billsetting.version>4.0.0-SNAPSHOT</billsetting.version>
        <fileservice.version>4.0.4.7-ENT-SNAPSHOT</fileservice.version><!--上传文件相关-->
        <scheduler.version>4.0.0-ENT-SNAPSHOT</scheduler.version><!--定时任务-->
        <portal.version>4.1.0-ENT-SNAPSHOT</portal.version><!--门户相关-->
        <wf2.version>1.0.0.19-ENT-SNAPSHOT</wf2.version><!--工作流引擎2-->
        <wf.version>4.1.0-SNAPSHOT</wf.version><!--工作流引擎-->
        <mad.version>4.13.0.59-ENT-SNAPSHOT</mad.version><!--基础设置-->
        <ureport2.version>4.6.0.6-ENT-SNAPSHOT</ureport2.version>
        <bud.version>4.12.0.3-ENT-SNAPSHOT</bud.version><!--指标相关-->
        <balance.version>4.12.0-ENT-SNAPSHOT</balance.version><!--额度相关-->
        <!--电子凭证相关开始-->
        <ecs.version>********-ENT-SNAPSHOT</ecs.version>
        <cherry.version>4.2.0-SNAPSHOT</cherry.version>
        <!--电子凭证相关结束-->
        <!--规则引擎开始-->
        <rule.version>1.1.0-ENT-SNAPSHOT</rule.version>
        <easyrules.version>4.1.0</easyrules.version>
        <!--规则引擎结束-->
        <!--支付相关开始-->
        <ep.version>4.8.0-ENT-SNAPSHOT</ep.version><!--网银支付-->
        <!--支付相关结束-->
        <!--记账相关开始-->
        <gip.version>4.13.0-SNAPSHOT</gip.version><!--会计平台-->
        <gal.version>4.13.0-SNAPSHOT</gal.version><!--财务核算-->
        <pct.version>*********-ENT-SNAPSHOT</pct.version><!--财务核算依赖pct-->
        <!--记账相关结束-->
        <!--临时公共包（应该在pty-parent定义）-->
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <itextpdf.version>5.5.13</itextpdf.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mysql.version>8.0.11</mysql.version>
        <jsqlparser.version>4.3</jsqlparser.version>
        <open.api.version>********-ENT-SNAPSHOT</open.api.version>
        <ofdrw-converter.version>2.3.6</ofdrw-converter.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-io.version>2.16.1</commons-io.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.ofdrw</groupId>
                <artifactId>ofdrw-converter</artifactId>
                <version>${ofdrw-converter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.servicecomb</groupId>
                <artifactId>provider-rest-common</artifactId>
                <version>${org.apache.servicecomb.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.pub</groupId>
                <artifactId>pty-pub-common</artifactId>
                <version>${pub.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.pub</groupId>
                <artifactId>pty-pub-service</artifactId>
                <version>${pub.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.security</groupId>
                <artifactId>pty-security-api</artifactId>
                <version>${security.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.ureport2</groupId>
                <artifactId>pty-ureport2-datasource</artifactId>
                <version>${ureport2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pty.security</groupId>
                <artifactId>pty-security-entity</artifactId>
                <version>${security.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.security</groupId>
                <artifactId>pty-security-service</artifactId>
                <version>${security.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.setting</groupId>
                <artifactId>pty-setting-api</artifactId>
                <version>${setting.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.billsetting</groupId>
                <artifactId>pty-billsetting-api</artifactId>
                <version>${billsetting.version}</version>
                <!--<scope>provided</scope>-->
            </dependency>
            <dependency>
                <groupId>com.pty.license</groupId>
                <artifactId>pty-license-common</artifactId>
                <version>${license.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.license</groupId>
                <artifactId>pty-license-api</artifactId>
                <version>${license.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.license</groupId>
                <artifactId>pty-license-entity</artifactId>
                <version>${license.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.mad</groupId>
                <artifactId>pty-mad-common</artifactId>
                <version>${mad.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.mad</groupId>
                <artifactId>pty-mad-entity</artifactId>
                <version>${mad.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.mad</groupId>
                <artifactId>pty-mad-api</artifactId>
                <version>${mad.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.fileservice</groupId>
                <artifactId>pty-fileservice-api</artifactId>
                <version>${fileservice.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.portal</groupId>
                <artifactId>pty-portal-api</artifactId>
                <version>${portal.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.workflow2</groupId>
                <artifactId>pty-workflow2-api</artifactId>
                <version>${wf2.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.workflow2</groupId>
                <artifactId>pty-workflow2-extend</artifactId>
                <version>${wf2.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.billsetting</groupId>
                <artifactId>pty-billsetting-starter</artifactId>
                <version>${billsetting.version}</version>
                <!--<scope>provided</scope>-->
            </dependency>
            <!-- 强制排除 JUnit 4 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>[0,)</version>
                <scope>provided</scope>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.pty.ecs</groupId>
                <artifactId>pty-ecs-common</artifactId>
                <version>${ecs.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-mock</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.pty.ecs</groupId>
                <artifactId>pty-ecs-entity</artifactId>
                <version>${ecs.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-mock</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.pty.ecs</groupId>
                <artifactId>pty-ecs-starter</artifactId>
                <version>${ecs.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-mock</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.pty.ecs</groupId>
                <artifactId>pty-ecs-api</artifactId>
                <version>${ecs.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-mock</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.pty.pct</groupId>
                <artifactId>pty-pct-common</artifactId>
                <version>${pct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.pct</groupId>
                <artifactId>pty-pct-entity</artifactId>
                <version>${pct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.pct</groupId>
                <artifactId>pty-pct-api</artifactId>
                <version>${pct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.ep</groupId>
                <artifactId>pty-ep-api</artifactId>
                <version>${ep.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.ep</groupId>
                <artifactId>pty-ep-entity</artifactId>
                <version>${ep.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.ep</groupId>
                <artifactId>pty-ep-common</artifactId>
                <version>${ep.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.balance</groupId>
                <artifactId>pty-balance-api</artifactId>
                <version>${balance.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.bud</groupId>
                <artifactId>pty-bud-api</artifactId>
                <version>${bud.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.bud</groupId>
                <artifactId>pty-bud-entity</artifactId>
                <version>${bud.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.balance</groupId>
                <artifactId>pty-balance-entity</artifactId>
                <version>${balance.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.message</groupId>
                <artifactId>pty-message-service</artifactId>
                <version>${message.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.message</groupId>
                <artifactId>pty-message-sdk-starter</artifactId>
                <version>${message.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- Easy Rules 核心库依赖 -->
            <dependency>
                <groupId>com.pty.rule</groupId>
                <artifactId>pty-rule-api</artifactId>
                <version>${rule.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.rule</groupId>
                <artifactId>pty-rule-common</artifactId>
                <version>${rule.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jeasy</groupId>
                <artifactId>easy-rules-core</artifactId>
                <version>${easyrules.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.mapstruct/mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.openapi</groupId>
                <artifactId>pty-openapi-sdk-starter</artifactId>
                <version>${open.api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.pty.openapi</groupId>
                <artifactId>pty-openapi-entity</artifactId>
                <version>${open.api.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>pty-pcx-api</module>
        <module>pty-pcx-common</module>
        <module>pty-pcx-entity</module>
        <module>pty-pcx-rest</module>
        <module>pty-pcx-resource</module>
        <module>pty-pcx-service</module>
        <module>pty-pcx-starter</module>
        <module>pty-pcx-launch</module>
        <module>pty-pcx-external</module>
    </modules>

    <distributionManagement>
        <!--        <repository>-->
        <!--            <id>releases</id>-->
        <!--            <name>Internal Releases</name>-->
        <!--            <url>http://**************:2004//nexus/repository/maven-releases/</url>-->
        <!--        </repository>-->
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Internal Releases</name>
            <url>http://**************:2004//nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!--					<skip>true</skip>-->
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
