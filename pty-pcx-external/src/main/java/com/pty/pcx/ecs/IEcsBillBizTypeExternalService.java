package com.pty.pcx.ecs;

import com.pty.ecs.vo.biztype.EcsBillBizTypeVO;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;

import java.util.List;

/**
 * Description: 电子凭证业务类型接口
 * createdTime: 2024/12/17  下午5:07
 * creator: wangbao
 **/
public interface IEcsBillBizTypeExternalService {

    CheckMsg<List<EcsBillBizTypeVO>> select(PcxBasExpBizTypeQO pcxBasExpBizTypeQO);

}
