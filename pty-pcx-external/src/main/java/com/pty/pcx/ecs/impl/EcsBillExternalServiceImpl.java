package com.pty.pcx.ecs.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.pty.ecs.api.bill.IPersonalBillService;
import com.pty.ecs.api.external.IEcsExternalBillService;
import com.pty.ecs.common.EcsServiceMsg;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.ecs.qo.bill.EcsBillFileQO;
import com.pty.pcx.api.bas.IPcxBasExpBizTypeService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.ecs.EcsModifiedBillDTO;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;
import com.pty.pcx.qo.ecs.BuildExpRelQO;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.compared.PcxEcsComparedResultQO;
import com.pty.pcx.vo.bas.PcxBasExpBizTypeVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class EcsBillExternalServiceImpl implements IEcsBillExternalService {
    @Autowired
    private IPersonalBillService personalBillService;

    @Autowired
    @RestClientReference(microServiceNames = "ecs")
    private IEcsExternalBillService iEcsExternalBillService;

    @Autowired
    private IPcxBasExpBizTypeService pcxBasExpBizTypeService;

    private final static String ECS_FLAG_DATA = "data";
    private final static String ECS_FLAG_BILL = "bill";
    private final static String ECS_FLAG_FILE = "file";
    private final static String ECS_FLAG_BILLRELFILE = "billRelFile";
    private final static String ECS_FLAG_AGENCY_CODE = "agencyCode";
    private final static String ECS_FLAG_MOF_DIV_CODE = "mofDivCode";
    private final static String ECS_FLAG_BILL_LIST = "billList";
    private final static String ECS_FLAG_FISCAL = "fiscalYear";
    public final static String ECS_FLAG_CONTRACT = "file_contract";
    private final static String ECS_FLAG_MODIFIED_ATTR  = "modifiedAttr";


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class BaseQO{
        private String agyCode;
        private String mofDivCode;
        private String fiscal;
    }
    @Override
    public Map<String, List> queryBillList2(ExpInvoiceQO qo) {
        List<String> noBizTypeBill = new ArrayList<>();
        Map<String, List<PcxBasExpType>> ecsBillExpTypeMap = new HashMap<>();
        Map<String, EcsMsgDTO> ecsMsgMap = new HashMap<>();
        return queryEcsBill(qo, noBizTypeBill, ecsBillExpTypeMap, ecsMsgMap);
    }

    @Override
    public Map<String, List<PcxBasExpType>> queryBillExpTypeList(ExpInvoiceQO qo) {
        List<String> noBizTypeBill = new ArrayList<>();
        Map<String, List<PcxBasExpType>> ecsBillExpTypeMap = new HashMap<>();
        Map<String, EcsMsgDTO> ecsMsgMap = new HashMap<>();
        queryEcsBill(qo, noBizTypeBill, ecsBillExpTypeMap, ecsMsgMap);
        return ecsBillExpTypeMap;
    }

    @Override
    public Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMap(ExpInvoiceQO qo) {
        List<String> noBizTypeBill = new ArrayList<>();
        Map<String, List<PcxBasExpType>> ecsBillExpTypeMap = new HashMap<>();
        Map<String, EcsMsgDTO> ecsMsgMap = new HashMap<>();
        Map<String, Map<String, List<Object>>> ecsBillMap = queryEcsBillMap(qo);
        BaseQO baseQO = BaseQO.builder()
                .agyCode(qo.getAgyCode())
                .fiscal(qo.getFiscal())
                .mofDivCode(qo.getMofDivCode())
                .build();

        transEcsDto(ecsBillMap, baseQO, noBizTypeBill, ecsBillExpTypeMap, ecsMsgMap);

        Map<String, List<Object>> billMap = ecsBillMap.get(ECS_FLAG_BILL);
        Map<String, List<Object>> fileRelMap = ecsBillMap.get(ECS_FLAG_BILLRELFILE);
        List<JSONObject> billJsonList = Lists.newArrayList();
        if (Objects.nonNull(billMap) && !billMap.isEmpty()){
            billMap.values().stream().flatMap(Collection::stream).forEach(bill->{
                JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(bill));
                String billId = jsonObj.getString("billId");
                if (StringUtil.isNotBlank(billId)){
                    List<Object> fileList = fileRelMap.get(billId);
                    if (CollectionUtils.isNotEmpty(fileList)){
                        jsonObj.put("fileList", JSON.parseArray(JSON.toJSONString(fileList)));
                    }
                }
                billJsonList.add(jsonObj);
            });
        }
        return Pair.of(ecsMsgMap, billJsonList);
    }

    @Override
    public Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMapByEcsIds(List<String> ecsBillIds, String fiscal, String agyCode, String mofDivCode) {
        if (CollectionUtils.isEmpty(ecsBillIds)){
            return Pair.of(new HashMap<>(), Lists.newArrayList());
        }
        ExpInvoiceQO expInvoiceQO = new ExpInvoiceQO();
        expInvoiceQO.setMofDivCode(mofDivCode);
        expInvoiceQO.setFiscal(fiscal);
        expInvoiceQO.setAgyCode(agyCode);
        List<ExpInvoiceQO.EscBillQO> billList = new ArrayList<>();
        for (String ecsBillId : ecsBillIds) {
            ExpInvoiceQO.EscBillQO ecsBillQO = new ExpInvoiceQO.EscBillQO();
            ecsBillQO.setBillId(ecsBillId);
            ecsBillQO.setBillAttachType(0);
            billList.add(ecsBillQO);
        }
        expInvoiceQO.setBillList(billList);
        return getEcsMsgMap(expInvoiceQO);
    }

    @Override
    public List<JSONObject> getEcsBillList(List<String> ecsBillIds, String fiscal, String agyCode, String mofDivCode) {
        if (CollectionUtils.isEmpty(ecsBillIds)){
            return Lists.newArrayList();
        }
        ExpInvoiceQO expInvoiceQO = new ExpInvoiceQO();
        expInvoiceQO.setMofDivCode(mofDivCode);
        expInvoiceQO.setFiscal(fiscal);
        expInvoiceQO.setAgyCode(agyCode);
        List<ExpInvoiceQO.EscBillQO> billList = new ArrayList<>();
        for (String ecsBillId : ecsBillIds) {
            ExpInvoiceQO.EscBillQO ecsBillQO = new ExpInvoiceQO.EscBillQO();
            ecsBillQO.setBillId(ecsBillId);
            ecsBillQO.setBillAttachType(0);
            billList.add(ecsBillQO);
        }
        expInvoiceQO.setBillList(billList);
        Map<String, Map<String, List<Object>>> ecsBillMap = queryEcsBillMap(expInvoiceQO);
        Map<String, List<Object>> billMap = ecsBillMap.get(ECS_FLAG_BILL);
        Map<String, List<Object>> fileRelMap = ecsBillMap.get(ECS_FLAG_BILLRELFILE);
        List<JSONObject> result = Lists.newArrayList();
        if (Objects.nonNull(billMap) && !billMap.isEmpty()){
            billMap.values().stream().flatMap(Collection::stream).forEach(bill->{
                JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(bill));
                String billId = jsonObj.getString("billId");
                if (StringUtil.isNotBlank(billId)){
                    List<Object> fileList = fileRelMap.get(billId);
                    if (CollectionUtils.isNotEmpty(fileList)){
                        jsonObj.put("fileList", JSON.parseArray(JSON.toJSONString(fileList)));
                    }
                }
                result.add(jsonObj);
            });
        }
        return result;
    }

    @Override
    public List<Map<String, String>> selectEcsAnalysisData(PcxEcsComparedResultQO qo) {
        EcsBillFileQO ecsBillFileQO = new EcsBillFileQO();
        ecsBillFileQO.setAgencyCode(qo.getAgyCode());
        ecsBillFileQO.setMofDivCode(qo.getMofDivCode());
        ecsBillFileQO.setAttachId(qo.getSourceAttachId());
        ecsBillFileQO.setFiscalYear(Integer.valueOf(qo.getFiscal()));
        Response analysisData = iEcsExternalBillService.getAnalysisData(ecsBillFileQO);
        if (analysisData.isSuccess()){
            return (List<Map<String, String>>) analysisData.getData();
        }
        return Collections.emptyList();
    }

    public Map<String, Map<String, List<Object>>> queryEcsBillMap(ExpInvoiceQO qo){
        Map<String, Object> ecsQOMap = new HashMap<>();
        ecsQOMap.put(ECS_FLAG_AGENCY_CODE, qo.getAgyCode());
        ecsQOMap.put(ECS_FLAG_MOF_DIV_CODE,qo.getMofDivCode());
        ecsQOMap.put(ECS_FLAG_BILL_LIST, qo.getBillList());
        ecsQOMap.put(ECS_FLAG_FISCAL, Integer.parseInt(qo.getFiscal()));
        Map<String, Object> list = iEcsExternalBillService.list(ecsQOMap);
        log.info("ecsBillList:{}", JSON.toJSONString(list));
        return JSON.parseObject(JSON.toJSONString(list), Map.class);
    }

    public Map<String, List> queryEcsBill(ExpInvoiceQO qo,
                                          List<String> noBizTypeBill,
                                          Map<String,List<PcxBasExpType>> ecsBillExpTypeMap,
                                          Map<String, EcsMsgDTO> ecsMsgMap){
        Map<String, Map<String, List<Object>>> map = queryEcsBillMap(qo);
        BaseQO baseQO = BaseQO.builder()
                .agyCode(qo.getAgyCode())
                .fiscal(qo.getFiscal())
                .mofDivCode(qo.getMofDivCode())
                .build();
        Map<String, List> ecsListMap = transEcsDto(map, baseQO, noBizTypeBill, ecsBillExpTypeMap, ecsMsgMap);
        log.info("ecsBillList transDto:{}", JSON.toJSONString(ecsListMap));
        log.info("no bizTypeBill:{}", noBizTypeBill);
        return ecsListMap;
    }


    private Map<String, List> transEcsDto(Map<String, Map<String, List<Object>>> map,
                                          BaseQO baseQO,
                                          List<String> noBizTypeBill,
                                          Map<String, List<PcxBasExpType>> ecsBillExpTypeMap,
                                          Map<String, EcsMsgDTO> ecsMsgMap) {
        Map<String, List> result = new HashMap<>();
        Map<String, List<Object>> bill = map.getOrDefault(ECS_FLAG_BILL, new HashMap<>());
        Map<String, List<Object>> billRelFile = map.getOrDefault(ECS_FLAG_BILLRELFILE, new HashMap<>());
        Map<String, List<Object>> billModifiedAttrMap = map.getOrDefault(ECS_FLAG_MODIFIED_ATTR, new HashMap<>());
        Map<String, EcsModifiedBillDTO> modifiedBillDTOMap = collectBillModifiedAttr(billModifiedAttrMap);
        for (Map.Entry<String, List<Object>> listEntry : bill.entrySet()) {
            transDto(result, listEntry, billRelFile, baseQO, noBizTypeBill, ecsBillExpTypeMap, ecsMsgMap, modifiedBillDTOMap);
        }
        Map<String, List<Object>> fileMap = map.get(ECS_FLAG_FILE);
        if (Objects.nonNull(fileMap) && !fileMap.isEmpty()){
            transContractDTO(result, fileMap.get(ECS_FLAG_CONTRACT));
        }
        return result;
    }

    private Map<String, EcsModifiedBillDTO> collectBillModifiedAttr(Map<String, List<Object>> billModifiedAttrMap) {
        Map<String, EcsModifiedBillDTO> result = new HashMap<>();
        if (Objects.nonNull(billModifiedAttrMap) && !billModifiedAttrMap.isEmpty()){
            for (Map.Entry<String, List<Object>> entry : billModifiedAttrMap.entrySet()) {
                JSONObject jsonObject = (JSONObject) entry.getValue();
                EcsModifiedBillDTO ecsModifiedBillDTO = new EcsModifiedBillDTO();
                result.put(entry.getKey(), ecsModifiedBillDTO);
                Object o = jsonObject.get("billDesc");
                List<String> billDescList = new ArrayList<>();
                if (Objects.nonNull(o) && o instanceof JSONArray){
                    JSONArray jsonArray = (JSONArray) o;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        billDescList.add(jsonArray.getString(i));
                    }
                }
                ecsModifiedBillDTO.setBillDesc(billDescList);
                Object o2 = jsonObject.get("deatilDecs");
                Map<Integer, List<String>> detailDescMap = new HashMap<>();
                ecsModifiedBillDTO.setDetailDescMap(detailDescMap);
                if (Objects.nonNull(o2) && o2 instanceof JSONObject){
                    JSONObject json = (JSONObject) o2;
                    int index = json.size();
                    for (int i = 0; i < index; i++) {
                        Integer key = i + 1;
                        Object detail = json.get(String.valueOf(key));
                        List<String> detailDescList = new ArrayList<>();
                        if (Objects.nonNull(detail) && detail instanceof JSONArray){
                            JSONArray detailArray = (JSONArray) detail;
                            for (int i1 = 0; i1 < detailArray.size(); i1++) {
                                detailDescList.add(detailArray.getString(i1));
                            }
                        }
                        detailDescMap.put(key, detailDescList);
                    }
                }
            }
        }
        return result;
    }

    private void transContractDTO(Map<String, List> result, List<Object> fileContract) {
        if (CollectionUtils.isEmpty(fileContract)){
            return;
        }
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = ecsBillDisposeMap.get(ECS_FLAG_CONTRACT);
        if (Objects.isNull(ecsBillDispose)){
            return;
        }
        ecsBillDispose.tranAndCollectDto(ECS_FLAG_CONTRACT, fileContract, Maps.newHashMap(), result, Maps.newHashMap(), Lists.newArrayList(), Maps.newHashMap(), Maps.newHashMap());
    }

    /**
     * 把每种票转换成对于的dto，看billRelFile中是否有他关联的附件，放到dto里面
     * 收集票类，去查询票对应的费用类型
     */
    private void transDto(Map<String, List> result,
                          Map.Entry<String, List<Object>> entry,
                          Map<String, List<Object>> billRelFile,
                          BaseQO baseQO,
                          List<String> noBizTypeBill,
                          Map<String, List<PcxBasExpType>> ecsBillExpTypeMap,
                          Map<String, EcsMsgDTO> ecsMsgMap,
                          Map<String, EcsModifiedBillDTO> modifiedBillDTOMap) {
        // billId -> bizTypeCode, totalAmt  查票类对应的费用类型使用
        Map<String, Pair<String, BigDecimal>> ecsBillBizMap = new HashMap<>();
        //票类，rai，atr，bus等
        String key = entry.getKey();
        //后面处理票的费用类型使用
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = ecsBillDisposeMap.get(key);
        if (Objects.isNull(ecsBillDispose)){
            return;
        }
        //当前处理的票类的转换后数据
        Pair<String, List> tempEcsResult = ecsBillDispose.tranAndCollectDto(key, entry.getValue(), ecsBillBizMap, result, billRelFile, noBizTypeBill, ecsMsgMap, modifiedBillDTOMap);

        fillExpTypeByBizTypeCode(ecsBillBizMap, tempEcsResult, baseQO, ecsBillExpTypeMap);
    }

    private void fillExpTypeByBizTypeCode(Map<String, Pair<String, BigDecimal>> ecsBillBizMap,
                                          Pair<String, List> tempPair,
                                         BaseQO baseQO,
                                          Map<String, List<PcxBasExpType>> ecsBillExpTypeMap) {
        if (Objects.isNull(ecsBillBizMap) || ecsBillBizMap.isEmpty()){
            return;
        }
        //组装参数查询票类对应的费用类型
        List<PcxBasExpBizTypeQO> pcxBasExpBizTypeQOList = new ArrayList<>();
        for (Map.Entry<String, Pair<String, BigDecimal>> entry : ecsBillBizMap.entrySet()) {
            PcxBasExpBizTypeQO qo = new PcxBasExpBizTypeQO();
            qo.setMofDivCode(baseQO.getMofDivCode());
            qo.setAgyCode(baseQO.getAgyCode());
            qo.setFiscal(baseQO.getFiscal());
            qo.setEcsBizTypeCode(entry.getValue().getKey());
            qo.setEcsBizAmt(entry.getValue().getValue());
            qo.setEcsBizId(entry.getKey());
            qo.setTenantId(PtyContext.getTenantId());
            pcxBasExpBizTypeQOList.add(qo);
        }
        log.info("selectExpTypeList:{}", JSON.toJSONString(pcxBasExpBizTypeQOList));
        CheckMsg checkMsg = null;
        try {
            checkMsg = pcxBasExpBizTypeService.selectExpTypeList(pcxBasExpBizTypeQOList);

            if (!checkMsg.isSuccess()){
                log.error("查询票中对应费用类型失败:{}", checkMsg.getMsgInfo());
                return;
            }
        } catch (Exception e) {
            log.error("selectExpTypeList error", e);
            return;
        }
        List<PcxBasExpBizTypeVO> pcxBasExpBizTypeQOS = (List<PcxBasExpBizTypeVO>) checkMsg.getData();
        Map<String, PcxBasExpBizTypeVO> map = pcxBasExpBizTypeQOS.stream()
                .collect(Collectors.toMap(PcxBasExpBizTypeVO::getEcsBizId, Function.identity(), (key1, key2) -> key1));
        for (PcxBasExpBizTypeVO pcxBasExpBizTypeQO : pcxBasExpBizTypeQOS) {
            ecsBillExpTypeMap.put(pcxBasExpBizTypeQO.getEcsBizId(), pcxBasExpBizTypeQO.getPcxBasExpTypeList());
        }
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = ecsBillDisposeMap.get(tempPair.getKey());
        if (Objects.isNull(ecsBillDispose)){
            return;
        }
        ecsBillDispose.fillExpTypeIfExists(tempPair.getValue(), map);
    }

    private static Map<String, EcsDtoTransHelper.EcsBillDispose> ecsBillDisposeMap = new HashMap<>();
    static {
        ecsBillDisposeMap.put(EcsEnum.BillType.RAI.getCode(), EcsDtoTransHelper.RaiDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.ATR.getCode(), EcsDtoTransHelper.AtrDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.STEAMER_TICKET.getCode(), EcsDtoTransHelper.BoatDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.BUS.getCode(), EcsDtoTransHelper.BusDispose.instance);

        ecsBillDisposeMap.put(EcsEnum.BillType.INV_ORD.getCode(), EcsDtoTransHelper.GeneralDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.INV_SPCL.getCode(), EcsDtoTransHelper.GeneralDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.EINV.getCode(), EcsDtoTransHelper.GeneralDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.PRINT.getCode(), EcsDtoTransHelper.GeneralDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.BLOCKCHAIN.getCode(), EcsDtoTransHelper.GeneralDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.EFI.getCode(), EcsDtoTransHelper.EfiDispose.instance);

        ecsBillDisposeMap.put(EcsEnum.BillType.ROLL.getCode(), EcsDtoTransHelper.RollDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.QUOTA.getCode(), EcsDtoTransHelper.SubOtherDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.TICKET.getCode(), EcsDtoTransHelper.SubOtherDispose.instance);
//        ecsBillDisposeMap.put(EcsEnum.BillType.SUB_OTHER.getCode(), EcsDtoTransHelper.SubOtherDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.TAXI.getCode(), EcsDtoTransHelper.TaxiDispose.instance);
        ecsBillDisposeMap.put(EcsEnum.BillType.TOLL.getCode(), EcsDtoTransHelper.TollDispose.instance);

        ecsBillDisposeMap.put(EcsEnum.BillType.FILE_CONTRACT.getCode(), EcsDtoTransHelper.ContractDispose.instance);
    }

    public static Map<String, EcsDtoTransHelper.EcsBillDispose> getEcsBillDisposeMap() {
        return new HashMap<>(ecsBillDisposeMap);
    }


    @Override
    public EcsServiceMsg buildExpRel(BuildExpRelQO qo) {
        Map<String, Object> map = (Map)new BeanMap(qo);
        return personalBillService.buildExpRel(map);
    }

    @Override
    public EcsServiceMsg deleteExpRel(BuildExpRelQO qo) {
        Map<String, Object> map = (Map)new BeanMap(qo);
        return personalBillService.deleteExpRel(map);
    }

    @Override
    public Response expInfoSave(UpdateEcsBillDTO qo) {
        Map<String, Object> map = (Map)new BeanMap(qo);
        Map<String, Object> newMap = new HashMap<>();
        newMap.putAll(map);
        newMap.remove("ecsBillInfo");

        if (Objects.nonNull(qo.getEcsBillInfo())){
            qo.getEcsBillInfo().forEach((k,v)->newMap.put(k,v));
        }
        newMap.remove("class");
        log.info("expInfoSave:{}", JSON.toJSONString(newMap));
        return iEcsExternalBillService.update(newMap);
    }
}
