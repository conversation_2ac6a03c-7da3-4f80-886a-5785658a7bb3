package com.pty.pcx.trans;

import com.pty.fileservice.entity.PaAttach;
import com.pty.pa.security.entity.PtyUser;
import com.pty.pa.security.entity.vo.PtyOrgVo;
import com.pty.pa.security.entity.vo.PtyRoleVo;
import com.pty.pcx.dto.pa.PaAttachDTO;
import com.pty.pcx.dto.pa.PaOrgDTO;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.dto.rule.PaBizRuleDto;
import com.pty.pcx.dto.rule.PtyRuleDto;
import com.pty.rule.entity.PaBizRule;
import com.pty.rule.entity.PtyRule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PaTransformer {

    PaTransformer INSTANCE = Mappers.getMapper(PaTransformer.class);

    PaUserDTO toDto(PtyRoleVo paUser);

    List<PaUserDTO> toDto(List<PtyRoleVo> paUsers);

    PaUserDTO entityToDto(PtyUser ptyUser);

    List<PaUserDTO> entityToDto(List<PtyUser> ptyUsers);

    List<PaBizRuleDto> bizRuleToDto(List<PaBizRule> bizRules);

    List<PtyRuleDto> ruleToDto(List<PtyRule> p);

    PaOrgDTO toDto(PtyOrgVo ptyOrgVo);

    PaAttachDTO toDto(PaAttach paAttach);

    List<PaAttachDTO> toAttachDtos(List<PaAttach> paAttachList);
}
