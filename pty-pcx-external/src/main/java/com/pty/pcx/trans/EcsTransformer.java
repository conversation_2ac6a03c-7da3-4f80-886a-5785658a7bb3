package com.pty.pcx.trans;

import com.pty.ecs.qo.biz.EcsBizAppQO;
import com.pty.ecs.qo.biz.EcsBizExpQO;
import com.pty.ecs.qo.open.EcsOpenBillRelQO;
import com.pty.pcx.dto.ecs.bill.EcsOpenBillRelDTO;
import com.pty.pcx.dto.ecs.biz.EcsBizAppDTO;
import com.pty.pcx.dto.ecs.biz.EcsBizExpDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EcsTransformer {

    EcsTransformer INSTANCE = Mappers.getMapper(EcsTransformer.class);


    EcsBizExpQO toEcsBizExpQO(EcsBizExpDTO bizExpDTO);

    EcsBizAppQO toEcsBizAppQO(EcsBizAppDTO bizAppDTO);

    EcsOpenBillRelQO toEcsOpenBillRelQO(EcsOpenBillRelDTO openBillRelDTO);
}
