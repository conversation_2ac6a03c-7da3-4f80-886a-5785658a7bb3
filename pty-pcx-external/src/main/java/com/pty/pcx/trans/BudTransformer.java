package com.pty.pcx.trans;

import com.pty.balance.entity.PtyBudBalance;
import com.pty.bud.entity.vo.BudPexBalanceVo;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.dto.bill.PcxBillBalanceDTO;
import com.pty.pcx.entity.bill.PcxBillBalance;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BudTransformer {

    BudTransformer INSTANCE = Mappers.getMapper(BudTransformer.class);

    BudBalanceDTO toDTO(PcxBillBalance balance);

    List<BudBalanceDTO> toDTOs(List<PtyBudBalance> balances);

    BudPexBalanceVo toBudVo(PcxBillBalanceDTO balance);

}
