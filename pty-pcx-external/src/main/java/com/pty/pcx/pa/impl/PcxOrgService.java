package com.pty.pcx.pa.impl;

import com.pty.pa.security.api.OrgService;
import com.pty.pa.security.entity.PtyOrg;
import com.pty.pa.security.entity.vo.PtyOrgVo;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.pa.PaOrgDTO;
import com.pty.pcx.pa.IPcxOrgService;
import com.pty.pcx.trans.PaTransformer;
import com.pty.pub.common.rest.RestClientReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PcxOrgService implements IPcxOrgService {

    @Autowired
    @RestClientReference(microServiceNames = "pa")
    private OrgService orgService;
    @Override
    public PaOrgDTO selectByOrgCode(PcxBaseDTO pcxBaseDTO) {
        PtyOrg cond = new PtyOrg();
        cond.setOrgCode(pcxBaseDTO.getAgyCode());
        cond.setMofDivCode(pcxBaseDTO.getMofDivCode());
        cond.setFiscal(Integer.valueOf(pcxBaseDTO.getFiscal()));
        PtyOrgVo orgInfo = orgService.getOrgInfo(cond);
        return PaTransformer.INSTANCE.toDto(orgInfo);
    }
}
