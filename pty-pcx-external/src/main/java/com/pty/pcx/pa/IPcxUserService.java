package com.pty.pcx.pa;

import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.dto.pa.PaUserInvoiceDTO;

import java.util.List;

public interface IPcxUserService {


    List<PaUserDTO> selectByUserCodes(List<String> userCodes);

    PaUserDTO selectUserInfo(String userId);

    /**
     * 获取所有委托代报人
     */
    List<String> selectDelegateUserIds(String userCode);

    String selectBeReportUserCode(String userCode);

    /**
     * 查询用户的发票信息
     * @param pcxBaseDTO
     * @param userCode
     * @return
     */
    PaUserInvoiceDTO getUserInvHead(PcxBaseDTO pcxBaseDTO, String userCode);
}
