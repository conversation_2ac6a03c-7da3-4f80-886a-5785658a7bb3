package com.pty.pcx.pa.impl;

import com.pty.mad.entity.PaValset;
import com.pty.pcx.pa.IPcxValSetService;
import com.pty.pub.common.rest.RestClientReference;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IPaValsetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

@Indexed
@Service
@Slf4j
public class PaValSetService implements IPcxValSetService {

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IPaValsetService paValsetService;

    @Override
    public List<PaValset> selectByValsetCode(PaValset paValset) {
        return paValsetService.selectByValsetCode(paValset);
    }
}
