package com.pty.pcx.ep.impl;

import cn.hutool.core.lang.Assert;
import com.pty.ep.api.IEpPayService;
import com.pty.ep.api.IEpPwdService;
import com.pty.ep.entity.ep.EpPayBill;
import com.pty.ep.entity.ep.EpPayBillInfo;
import com.pty.ep.entity.ep.EpPayPwd;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.ep.IEpPayBillExternalService;
import com.pty.pcx.qo.treasurypay.detail.PcxBillPayQO;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;

@Indexed
@Service
@Slf4j
public class IEpPayBillExternalServiceImpl implements IEpPayBillExternalService {

    @Autowired(required = false)
    @RestClientReference(microServiceNames = {"ep"})
    private IEpPwdService epPwdService;

    @Autowired(required = false)
    @RestClientReference(microServiceNames = {"ep"})
    private IEpPayService epPayService;

    /**
     * 校验支付密码
     * @param pcxBillPayQO
     * @return
     */
    @Override
    public CheckMsg checkEpPayPwd(PcxBillPayQO pcxBillPayQO) {
        // 检查支付密码
        String userCode = PtyContext.getUsername();
        String agyCode = pcxBillPayQO.getAgyCode();
        String payPwd = pcxBillPayQO.getPayPwd();
        String mofDivCode = pcxBillPayQO.getMofDivCode();
        EpPayPwd epPayPwd;
        if (StringUtil.isNotEmpty(userCode) && StringUtil.isNotEmpty(agyCode) && StringUtil.isNotEmpty(payPwd)) {
            epPayPwd = new EpPayPwd();
            epPayPwd.setUserCode(userCode);
            epPayPwd.setAgyCode(agyCode);
            epPayPwd.setPayPwd(payPwd);
            epPayPwd.setMofDivCode(mofDivCode);
        } else {
            return CheckMsg.fail("支付密码为空，请检查！！！");
        }
        com.pty.mad.common.CheckMsg checkMsg = epPwdService.checkPayPwd(epPayPwd);
        log.info("校验密码结果：{}", checkMsg.getMsgInfo());
        if (!checkMsg.isSuccess()) {
            log.error("支付密码校验失败" + checkMsg.getMsgInfo());
            return CheckMsg.fail(checkMsg.getMsgInfo());
        }

        return CheckMsg.success();
    }

    /**
     * 普通支付
     * @param payBillList
     * @return
     */
    @Override
    public CheckMsg startEpPay(List<EpPayBill> payBillList) {
        // 发送并批量支付
        com.pty.mad.common.CheckMsg checkMsg = epPayService.createAndSendEPayBill(payBillList);
        Assert.state(checkMsg.isSuccess(), "发送并支付失败,{}", checkMsg.getMsgInfo(), payBillList);
        return CheckMsg.success(checkMsg.getData());
    }

    /**
     * 确认支付状态
     * @param epPayBill
     * @return
     */
    @Override
    public void checkEpPayStatus(EpPayBill epPayBill){
        try {
            // 确认发送请求，不能回滚
            epPayService.getSendingStatus(epPayBill);
        } catch (Exception e) {
            log.error(String.format("结算单{%s}--确认请求发送失败",epPayBill.getBillId()));
        }
    }

    /**
     * 查询支付状态
     * @param billPayDetail
     * @return
     */
    @Override
    public List<EpPayBillInfo> getEpPayStatus(PcxBillPayDetail billPayDetail){
        // 查询ep支付单状态
        EpPayBillInfo epPayBillInfo = new EpPayBillInfo();
        epPayBillInfo.setFromId(billPayDetail.getBillId());
        epPayBillInfo.setField05(billPayDetail.getPayNo());
        return epPayService.getPayBillInfos(epPayBillInfo);
    }
}
