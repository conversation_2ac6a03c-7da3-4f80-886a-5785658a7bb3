package com.pty.pcx.pct;

import com.pty.pct.api.IContractService;
import com.pty.pct.entity.PctBill;
import com.pty.pct.entity.dto.PlanPayStatusDTO;
import com.pty.pct.entity.vo.PctBillInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;


import java.util.List;
@Indexed
@Service
@Slf4j
public class PctExternalServiceImpl implements IPctExternalService{

    @Autowired
    private IContractService contractService;
    @Override
    public PctBillInfoVO selectBillInfo(String billId) {
        PctBill bill = new PctBill();
        bill.setBillId(billId);
        return contractService.selectBillInfo(bill);
    }

    /**
     * 更新合同支付状态
     * @param planPayStatusDTOs
     */
    @Override
    public void updateContractPayStatus(List<PlanPayStatusDTO> planPayStatusDTOs) {
        contractService.updatePayStatus(planPayStatusDTOs);
    }
}
