package com.pty.pcx.mad.impl;

import com.pty.mad.entity.MadAirportStation;
import com.pty.mad.entity.MadArea;
import com.pty.mad.entity.MadTrainStation;
import com.pty.pcx.mad.IMadStationExternalService;
import com.pty.pub.common.rest.RestClientReference;
import lombok.extern.slf4j.Slf4j;
import org.pty.mad.api.IMadAreaService;
import org.pty.mad.api.IMadTrainStationService;
import org.pty.mad.api.MadAirportStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Slf4j
@Indexed
@Service
public class PtyMadStationServiceImpl implements IMadStationExternalService {

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadTrainStationService madTrainStationService;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private MadAirportStationService madAirportStationService;

    @Autowired
    @RestClientReference(microServiceNames = {"mad"})
    private IMadAreaService madAreaService;
    @Override
    public List<MadTrainStation> selectByStationNames(Set<String> stationNames) {
        return madTrainStationService.selectByStationNamesLike(stationNames);
    }

    @Override
    public List<MadAirportStation> selectByAirportStationNames(Set<String> stationNames) {
        return madAirportStationService.selectByAirportStationNames(stationNames);
    }

    @Override
    public List<MadAirportStation> selectByAirportStationCodes(Set<String> stationCodes) {
        return madAirportStationService.selectByAirportStationCodes(stationCodes);
    }

    @Override
    public List<MadArea> selectByAreaNames(Set<String> names) {
        return madAreaService.selectByNames(names);
    }
}
