package com.pty.pcx.mad;

import com.pty.mad.qo.MadSearchAllAccInfoQO;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pub.common.bean.Response;

import java.util.List;

public interface IMadEmployeeExternalService {

    List<MadEmployeeDTO> selectAgyEmployee(PcxMadBaseQO pcxMadBaseQO);

    MadEmployeeDTO selectAgyEmployee(PcxBaseDTO pcxBaseDTO, String empCode);

    /**
     * 查询人员信息
     * @param pcxBaseDTO
     * @param empCode
     * @return
     */
    MadEmployeeDTO selectEmployeeByEmpCode(PcxBaseDTO pcxBaseDTO, String empCode);

    /**
     * 查询人员信息
     * @param pcxBaseDTO
     * @param userCode
     * @return
     */
    MadEmployeeDTO selectEmployeeByUserCode(PcxBaseDTO pcxBaseDTO, String userCode);

    /**
     * 查询人员信息
     * @param pcxBaseDTO
     * @param empName
     * @return
     */
    List<MadEmployeeDTO> selectEmployeeByEmpName(PcxBaseDTO pcxBaseDTO, String empName);


    /**
     * 根据madCode查询人员信息
     * @param madCodes
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<MadEmployeeDTO> selectByMadCodes(List<String> madCodes, String agyCode, Integer fiscal, String mofDivCode);

    /**
     * 搜索个人和对公账户
     * @param searchQO
     * @return
     */
    Response searchAllAccountInfo(MadSearchAllAccInfoQO searchQO);

    /**
     * 加载财务人员的来源
     * @param pcxBaseDTO
     * @return
     */
    String selectFinanceSource(PcxBaseDTO pcxBaseDTO);

    /**
     * 根据userCode查询人员信息
     * @param userCodes
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<MadEmployeeDTO> selectByUserCodes(List<String> userCodes, String agyCode, Integer fiscal, String mofDivCode);

    /**
     * 根据ukExFiscal查询人员信息
     * @param ukExFiscals
     * @param fiscal
     * @return
     */
    List<MadEmployeeDTO> selectByUkExFiscals(List<String> ukExFiscals, Integer fiscal);
}
