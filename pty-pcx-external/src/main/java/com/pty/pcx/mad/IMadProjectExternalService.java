package com.pty.pcx.mad;


import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.mad.MadProjectDTO;

import java.util.List;

public interface IMadProjectExternalService {
    /**
     * 获取项目信息
     * @param pcxBaseDTO
     * @param madCodes
     * @return
     */
    List<MadProjectDTO> selectByMadCodes(PcxBaseDTO pcxBaseDTO,List<String> madCodes);

    /****
     * 获取本单位的项目
     * @param pcxBaseDTO
     * @return
     */
    List<MadProjectDTO> getAgyProject(PcxBaseDTO pcxBaseDTO);
}
