package com.pty.pcx.mad;

import com.pty.mad.entity.MadWorkLocations;
import com.pty.mad.qo.MadWorkLocationsByEmpQO;
import com.pty.mad.vo.MadEmployeeWithWorkLocationVO;

import java.util.List;

public interface IMadWorklocationsExternalService {

   MadWorkLocations selectById(String id);

   List<MadWorkLocations> selectByIds(List<String> ids);

   List<MadEmployeeWithWorkLocationVO> selectListByEmpCodes(MadWorkLocationsByEmpQO madWorkLocationsByEmpQO);
}
