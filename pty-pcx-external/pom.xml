<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.238-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-external</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ecs</groupId>
            <artifactId>pty-ecs-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ecs</groupId>
            <artifactId>pty-ecs-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ecs</groupId>
            <artifactId>pty-ecs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.setting</groupId>
            <artifactId>pty-setting-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.fileservice</groupId>
            <artifactId>pty-fileservice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.balance</groupId>
            <artifactId>pty-balance-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.bud</groupId>
            <artifactId>pty-bud-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.bud</groupId>
            <artifactId>pty-bud-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.gip</groupId>
            <artifactId>pty-gip-api</artifactId>
            <version>${gip.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.pty.gip</groupId>
            <artifactId>pty-gip-entity</artifactId>
            <version>${gip.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.pty.gip</groupId>
            <artifactId>pty-gip-common</artifactId>
            <version>${gip.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.pty.gal</groupId>
            <artifactId>pty-gal-api</artifactId>
            <version>${gal.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>pty-pct-entity</artifactId>
                    <groupId>com.pty.pct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
