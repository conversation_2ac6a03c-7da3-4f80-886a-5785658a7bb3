package com.pty.pcx;

import com.pty.pa.security.filter.ContextFilter;
import com.pty.pub.common.anno.MyBatisDao;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Indexed;

@Indexed
@Configuration
@ComponentScan(basePackages = { "com.pty.pcx" }, lazyInit = true)
@ConditionalOnProperty(prefix = "pty.module.pcx", name = "enabled", havingValue = "true", matchIfMissing = true)
@MapperScan(lazyInitialization = "true", basePackages = {
  "com.pty.pcx.dao"}, annotationClass = MyBatisDao.class, sqlSessionFactoryRef = "pty-mybatis-session")
public class PtyPcxStarter {


    @Bean
    @ConditionalOnMissingBean
    public FilterRegistrationBean<ContextFilter> loggingFilter(){
        FilterRegistrationBean<ContextFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ContextFilter());
        registrationBean.addUrlPatterns("/*"); // 设置你想要拦截的路径
        return registrationBean;
    }
}
