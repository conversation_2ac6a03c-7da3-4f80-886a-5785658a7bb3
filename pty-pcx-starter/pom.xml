<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.238-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-starter</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-rest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-resource</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-service</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>make-assem-zip</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <zip destfile="target/pcx-${project.version}.zip" encoding="utf-8">
                                    <zipfileset dir="../pty-pcx-api/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-common/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-external/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-entity/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-resource/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-rest/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="../pty-pcx-service/target" prefix="" excludes="*-sources.jar"
                                                includes="*.jar"/>
                                    <zipfileset dir="target" prefix="" excludes="*-sources.jar" includes="*.jar"/>
                                </zip>
                                <move file="target/pcx-${project.version}.zip"
                                      tofile="../pcx-${project.version}.zip"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>