INSERT INTO PA_AUTOMATIC_TASKS
(task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times,
is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month,
hour_of_day, min_of_day, sys_id, agy_code)
VALUES
(732, 'AutoSendPayApply', '自动发送支付申请', NULL, 1, '2022-12-27 00:00:00', '2023-12-31 00:00:00', 1800,
 10000000, 1, 'com.pty.pas.task.AutoSendPayApply', NULL, NULL, 3, NULL, NULL, NULL, NULL, NULL, 'pas', '*');

INSERT INTO PA_AUTOMATIC_TASKS
(task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times,
is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month,
hour_of_day, min_of_day, sys_id, agy_code)
VALUES
(733, 'AutoGetPayResult', '自动发送支付单据状态查询', NULL, 1, '2022-12-27 00:00:00', '2023-12-31 00:00:00', 1800,
 10000000, 1, 'com.pty.pas.task.AutoGetPayResult', NULL, NULL, 3, NULL, NULL, NULL, NULL, NULL, 'pas', '*');

INSERT INTO pa_automatic_tasks(task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (735, 'AutoGetPayRefund', '自动查询国库集中支付资金退回通知书', NULL, 2, '', '', NULL, NULL, 0, 'com.pty.pas.task.AutoGetPayRefund', NULL, NULL, 3, NULL, NULL, NULL, 2, 0, 'pas', '*');

INSERT INTO pa_automatic_tasks(task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (734, 'AutoAuditTask', '重新对签章审核失败的支付信息进行送审，每天早晨凌晨2点执行一次', NULL, 2, '', '', NULL, NULL, 0, 'com.pty.pas.task.AutoAuditTask', NULL, NULL, 3, NULL, NULL, NULL, 2, 0, 'pas', '*');

INSERT INTO pa_automatic_tasks (task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (1, 'RenderVouHead', '自动提交记账凭证主表，每天早晨凌晨2点执行一次', NULL, 2, '', '', NULL, NULL, 1, 'com.pty.pas.task.gla.RenderVouHead', NULL, NULL, 3, NULL, NULL, NULL, 2, 0, 'pas', '*');
INSERT INTO pa_automatic_tasks (task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (2, 'RenderVouDetail', '自动提交记账凭证分录表，每天早晨凌晨2点执行一次', NULL, 2, '', '', NULL, NULL, 1, 'com.pty.pas.task.gla.RenderVouDetail', NULL, NULL, 3, NULL, NULL, NULL, 2, 0, 'pas', '*');
INSERT INTO pa_automatic_tasks (task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (3, 'RenderGlaVouBillRelation', '自动提交记账凭证与原始会计凭证关联表，每天早晨凌晨2点执行一次', NULL, 2, '', '', NULL, NULL, 1, 'com.pty.pas.task.gla.RenderGlaVouBillRelation', NULL, NULL, 3, NULL, NULL, NULL, 2, 0, 'pas', '*');

INSERT INTO pa_automatic_tasks (task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (736, 'AutoSendLocalPayApply', '地方-自动发送集中支付申请录入', NULL, 2, '', '', NULL, NULL, 1, 'com.pty.pas.task.local.AutoSendLocalPayApply', '', NULL, 3, NULL, NULL, NULL, 0, 0, 'bud', '*');
INSERT INTO pa_automatic_tasks (task_id, task_code, task_name, task_desc, task_type, start_date, end_date, task_interval, run_times, is_enabled, task_class, task_param, remark, schedule_type, month_of_year, day_of_week, day_of_month, hour_of_day, min_of_day, sys_id, agy_code) VALUES (737, 'AutoSendLocalPayApplyIncCert', '地方-自动发送单位资金支付申请录入', NULL, 2, '', '', NULL, NULL, 1, 'com.pty.pas.task.local.AutoSendLocalPayApplyIncCert', '', NULL, 3, NULL, NULL, NULL, 0, 0, 'bud', '*');
