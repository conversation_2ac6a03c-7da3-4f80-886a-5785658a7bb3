package com.pty.pcx.entity.bas;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Description: 票类规则（"费用类型-电子凭证类型-业务规则"关系）
 * createdTime: 2024/12/6  上午10:29
 * creator: wang<PERSON>
 **/
@Entity
@Table(name = "pcx_bas_exp_biz_type")
@Data
@NoArgsConstructor
public class PcxBasExpBizType implements Serializable {

    private static final long serialVersionUID = -5687604075418679498L;

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 费用类型编码
     */
    @Column(name = "expense_code")
    private String expenseCode;

    /**
     * 费用类型名称
     */
    @Column(name = "expense_name")
    private String expenseName;

    /**
     * 电子凭证业务类型编码
     */
    @Column(name = "ecs_biz_type_code")
    private String ecsBizTypeCode;

    /**
     * 电子凭证业务类型名称
     */
    @Column(name = "ecs_biz_type_name")
    private String ecsBizTypeName;

    /**
     * 业务规则ID
     */
    @Column(name = "biz_rule_code")
    private String bizRuleCode;

    /**
     * 业务规则名称（前提条件）
     */
    @Column(name = "biz_rule_name")
    private String bizRuleName;

    /**
     * 单位编码
     */
    @Column(name = "agy_code")
    private String agyCode;

    /**
     * 区划编码
     */
    @Column(name = "mof_div_code")
    private String mofDivCode;

    /**
     * 年度
     */
    @Column(name = "fiscal")
    private String fiscal;

    /**
     * 序号
     */
    @Column(name = "seq")
    private Integer seq;

    /**
     * 修改人编码
     */
    @Column(name = "modifier")
    private String modifier;

    /**
     * 修改人名称
     */
    @Column(name = "modifier_name")
    private String modifierName;

    /**
     * 修改时间
     */
    @Column(name = "modified_time")
    private String modifiedTime;

    /**
     * 创建人代码
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 创建人名称
     */
    @Column(name = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private String createdTime;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private String tenantId;

}
