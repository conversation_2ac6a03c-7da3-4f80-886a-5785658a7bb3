package com.pty.pcx.entity.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pcx_bas_exp_expeco")
@NoArgsConstructor
public class PcxBasExpExpeco implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty("关联表主键Id")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("费用类型代码")
    @Column(name = "expense_code")
    private String expenseCode;

    @ApiModelProperty("费用类型名称")
    @Column(name = "expense_name")
    private String expenseName;

    @ApiModelProperty("经济分类代码")
    @Column(name = "expeco_code")
    private String expecoCode;

    @ApiModelProperty("经济分类名称")
    @Column(name = "expeco_name")
    private String expecoName;

    @ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("排序")
    @Column(name = "seq")
    private int seq;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;
}