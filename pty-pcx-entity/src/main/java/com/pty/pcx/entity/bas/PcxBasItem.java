package com.pty.pcx.entity.bas;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pty.pcx.common.valid.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Entity
@Table(name = "pcx_bas_item")
@NoArgsConstructor
public class PcxBasItem implements Serializable {
    private static final long serialVersionUID = -57531382908687083L;

    @Id
    @ApiModelProperty("主键")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("父级代码")
    @Column(name = "parent_code")
    private String parentCode;

    @ApiModelProperty("父级名称")
    @Column(name = "parent_name")
    private String parentName;

    @ApiModelProperty("事项类型代码")
    @Column(name = "item_code")
    private String itemCode;

    @ApiModelProperty("事项类型名称")
    @Column(name = "item_name")
    private String itemName;

    @ApiModelProperty("事项说明")
    @Column(name = "summary")
    private String summary;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    @NotBlank(message = "单位编码不能为空", groups = {Query.class})
    private String agyCode;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    @NotBlank(message = "单位编码不能为空", groups = {Query.class})
    private String mofDivCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    @NotBlank(message = "单位编码不能为空", groups = {Query.class})
    private String fiscal;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

    @ApiModelProperty("创建人代码")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("排序")
    @Column(name = "seq")
    private Integer seq;

    @ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;

    @ApiModelProperty("是否为子节点")
    @Column(name = "is_leaf")
    private Integer isLeaf;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("单据类型代码，多个用逗号分隔(expense,loan,apply)")
    @Column(name = "billtype_code")
    private String billtypeCode;

    @ApiModelProperty("启用预算控制的单据类型，多个用逗号分隔(expense,loan,apply)")
    @Column(name = "budget_ctrl")
    private String budgetCtrl;

    @TableField(exist = false)
    @ApiModelProperty("业务类型")
    private Integer bizType;
}