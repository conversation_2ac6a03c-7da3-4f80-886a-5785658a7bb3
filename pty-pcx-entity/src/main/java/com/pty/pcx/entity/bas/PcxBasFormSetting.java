package com.pty.pcx.entity.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 表单字段设置(PcxBasFormSetting)实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 19:36:35
 */
@Data
@Entity
@Table(schema = "pcx_bas_form_setting")
public class PcxBasFormSetting implements Serializable {
    private static final long serialVersionUID = -70693154087397636L;

    @Id
	@ApiModelProperty("主键")
    @Column(name = "id")
    private String id;

	@ApiModelProperty("表单种类（费用）")
    @Column(name = "form_classify")
    private String formClassify;

	@ApiModelProperty("表单类型编码（费用编码等等）")
    @Column(name = "form_code")
    private String formCode;

	@ApiModelProperty("表单类型名称")
    @Column(name = "form_name")
    private String formName;

	@ApiModelProperty("表单字段类型 （费用专属字段/费用明细字段/计划字段）")
    @Column(name = "form_type")
    private String formType;

	@ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;

	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

	@ApiModelProperty("单据种类编码")
    @Column(name = "bill_func_code")
    private Integer billFuncCode;

	@ApiModelProperty("字段code编码")
    @Column(name = "field_value")
    private String fieldValue;

    @ApiModelProperty("字段name编码")
    @Column(name = "field_label")
    private String fieldLabel;

	@ApiModelProperty("字段名称")
    @Column(name = "field_title")
    private String fieldTitle;

	@ApiModelProperty("名称")
    @Column(name = "field_name")
    private String fieldName;

	@ApiModelProperty("是否可编辑")
    @Column(name = "is_edit")
    private Integer isEdit;

	@ApiModelProperty("允许为空")
    @Column(name = "is_null")
    private Integer isNull;

	@ApiModelProperty("是否启用")
    @Column(name = "is_enabled")
    private Integer isEnabled;

    @ApiModelProperty("是否补充")
    @Column(name = "is_addition")
    private Integer isAddition;

	@ApiModelProperty("附注")
    @Column(name = "notes")
    private String notes;

	@ApiModelProperty("附注内容")
    @Column(name = "remarks")
    private String remarks;

	@ApiModelProperty("表单类型")
    @Column(name = "editor_code")
    private String editorCode;

	@ApiModelProperty("表单类型名称")
    @Column(name = "editor_name")
    private String editorName;

	@ApiModelProperty("字体")
    @Column(name = "font_style")
    private String fontStyle;

	@ApiModelProperty("显示类型 code or name")
    @Column(name = "show_type")
    private String showType;

	@ApiModelProperty("数据类型code")
    @Column(name = "data_type_code")
    private String dataTypeCode;

	@ApiModelProperty("数据类型name")
    @Column(name = "data_type_name")
    private String dataTypeName;

	@ApiModelProperty("数据来源编码")
    @Column(name = "data_source_code")
    private String dataSourceCode;

    @ApiModelProperty("数据来源名称")
    @Column(name = "data_source_name")
    private String dataSourceName;

	@ApiModelProperty("数据种类code")
    @Column(name = "data_classify_code")
    private String dataClassifyCode;

	@ApiModelProperty("数据种类name")
    @Column(name = "data_classify_name")
    private String dataClassifyName;

	@ApiModelProperty("默认值")
    @Column(name = "def_val")
    private String defVal;

    @ApiModelProperty("默认值规则")
    @Column(name = "def_val_rule")
    private String defValRule;

	@ApiModelProperty("影响字段")
    @Column(name = "field_control")
    private String fieldControl;

	@ApiModelProperty("设置显示关系")
    @Column(name = "show_relation")
    private String showRelation;

	@ApiModelProperty("字段最大长度")
    @Column(name = "max_length")
    private Integer maxLength;

	@ApiModelProperty("字段最小长度")
    @Column(name = "min_length")
    private Integer minLength;

	@ApiModelProperty("是否扩展字段")
    @Column(name = "is_extend")
    private Integer isExtend;

	@ApiModelProperty("排序")
    @Column(name = "seq")
    private Integer seq;

	@ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

	@ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

	@ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

	@ApiModelProperty("创建人代码")
    @Column(name = "creator_code")
    private String creatorCode;

	@ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

	@ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("是否只能选择末级")
    @Column(name = "only_select_list")
    private Integer onlySelectLast;

}

