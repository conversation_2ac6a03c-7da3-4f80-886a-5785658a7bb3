package com.pty.pcx.entity.wit;

import com.pty.pub.common.anno.Desc;
import com.pty.rule.entity.PtyRule;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "pcx_audit_rule")
@Desc(value = "无纸化报销单位规则表")

public class PcxAuditRule extends PtyRule {

  @Column(name = "from_id")
  private String fromId;

  @Column(name = "agy_code")
  private String agyCode;

  @Column(name = "agy_name")
  private String agyName;

  @Column(name = "fiscal")
  private String fiscal;

  @Column(name = "mof_div_code")
  private String mofDivCode;

}
