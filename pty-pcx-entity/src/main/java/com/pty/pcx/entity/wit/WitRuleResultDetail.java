package com.pty.pcx.entity.wit;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pub.common.anno.Desc;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;

import cn.hutool.crypto.digest.MD5;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Table(name = "pty_rule_result_detail")
@Desc(value = "规则稽核明细表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WitRuleResultDetail {

  @Id
  @Column(name = "id")
  private String id;

  @Column(name = "result_id")
  private String resultId;

  @Column(name = "bill_id")
  private String billId;

  @Column(name = "fiscal")
  private String fiscal;

  @Column(name = "rule_id")
  private String ruleId;

  @Column(name = "rule_name")
  private String ruleName;

  @Column(name = "rule_type")
  private String ruleType;

  @Column(name = "rule_reference")
  private String ruleReference;
  /**
   * 1=处理 2=未处理
   */
  @Column(name = "status")
  private String status;

  @Column(name = "audit_type")
  private String auditType;

  @Column(name = "rule_identify")
  private Integer ruleIdentify;

  @Desc("稽核时间")
  @Column(name = "created_time")
  private String createdTime;

  @Column(name = "creator")
  private String creator;

  @Desc("错误提示信息")
  @Column(name = "err_msg")
  private String errMsg;

  @Desc("当前数据")
  @Column(name = "cur_data")
  private String curData;

  @Desc("数据信息（json格式，前端定位用）")
  @Column(name = "data_info")
  private String dataInfo;

  @Desc("1-通过 2-不通过")
  @Column(name = "pass_state")
  private Integer passState;

  @Desc("不通过时填写说明")
  @Column(name = "pass_cause")
  private String passCause;

  @Column(name = "remarks")
  private String remarks;

  @Column(name = "attach_id")
  private String attachId;

  @Column(name = "rule_field")
  private String ruleField;

  /**
   * 处理方式
   */
  @Column(name = "field1")
  private String field1;
  /**
   * 附件名称
   */
  @Column(name = "field2")
  private String field2;

  @Column(name = "field3")
  private String field3;

  @Column(name = "field4")
  private String field4;

  @Column(name = "field5")
  private String field5;

  @Column(name = "ref_block")
  private String refBlock;

  /**
   * 查询辅助字段
   */
  @JsonIgnore
  private List<String> ruleIds;


  public String key() {
    return MD5.create().digestHex("KEY" + billId + "-" +"-"+ auditType + fiscal + "-" + creator + "-" + ruleId + "-"+ ruleType +"-"+(StringUtil.isNotEmpty(this.curData)?this.curData:"") + (StringUtil.isNotEmpty(this.ruleField)?this.ruleField:"")+refBlock);
  }

  public WitRuleResultDetailHis tolHis() {
    WitRuleResultDetailHis detailHis = new WitRuleResultDetailHis();
    detailHis.setId(IDGenerator.id());
    detailHis.setDetailId(this.id);
    detailHis.setDetailVer(this.createdTime);
    detailHis.setBillId(this.billId);
    detailHis.setFiscal(this.fiscal);
    detailHis.setResultId(this.resultId);
    detailHis.setRuleId(this.ruleId);
    detailHis.setRuleName(this.ruleName);
    detailHis.setRuleType(this.ruleType);
    detailHis.setRuleReference(this.ruleReference);
    detailHis.setRefBlock(this.refBlock);
    detailHis.setStatus(this.status);
    detailHis.setAuditType(this.auditType);
    detailHis.setRuleIdentify(this.ruleIdentify);
    detailHis.setCreatedTime(this.createdTime);
    detailHis.setCreator(this.creator);
    detailHis.setErrMsg(this.errMsg);
    detailHis.setCurData(this.curData);
    detailHis.setDataInfo(this.dataInfo);
    detailHis.setPassState(this.passState);
    detailHis.setPassCause(this.passCause);
    detailHis.setRemarks(this.remarks);
    detailHis.setAttachId(this.attachId);
    detailHis.setRuleField(this.ruleField);
    detailHis.setField1(this.field1);
    detailHis.setField2(this.field2);
    detailHis.setField3(this.field3);
    detailHis.setField4(this.field4);
    detailHis.setField5(this.field5);
    return detailHis;
  }
}
