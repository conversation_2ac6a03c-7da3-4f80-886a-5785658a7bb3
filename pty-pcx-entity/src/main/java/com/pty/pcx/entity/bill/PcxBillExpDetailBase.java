package com.pty.pcx.entity.bill;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pub.common.anno.Desc;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class PcxBillExpDetailBase implements Serializable {
    @Id
    @ApiModelProperty("主键ID")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("单据主键ID")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("费用主键ID")
    @Column(name = "expense_id")
    private String expenseId;

    @ApiModelProperty("费用类型编码")
    @Column(name = "exp_detail_code")
    private String expDetailCode;

    @ApiModelProperty("票据金额（1对1，汇总，拆分）")
    @Column(name = "ecs_amt")
    private BigDecimal ecsAmt;

    @ApiModelProperty("费用录入金额")
    @Column(name = "input_amt")
    private BigDecimal inputAmt;

    @ApiModelProperty("费用核定金额")
    @Column(name = "check_amt")
    private BigDecimal checkAmt;

    @Desc("财务费用核定金额")
    @TableField(exist = false)
    private BigDecimal checkApprovedAmt;

    @Desc("财务费用核定金额")
    @TableField(exist = false)
    private BigDecimal inputApprovedAmt;

    @ApiModelProperty("费用承担部门编码")
    @Column(name = "department_code")
    private String departmentCode;

    @ApiModelProperty("费用承担部门")
    @Column(name = "department_name")
    private String departmentName;

    @ApiModelProperty("抵扣税率")
    private BigDecimal taxRate;

    @ApiModelProperty("抵扣税")
    private BigDecimal taxAmt;

    @ApiModelProperty("标准金额")
    private BigDecimal standAmt;

    @ApiModelProperty("发票张数")
    private Integer ecsNum = 1;

    @Column(name = "source" )
    @ApiModelProperty("来源 ecs，replenish，manual")
    private String source;

    @ApiModelProperty("无票原因")
    private String noEcsReason;

    @ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

    @ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    //城市间交通费，如果关联了改签，这里记录改签对于的明细id
    //通用报销出发地
    @ApiModelProperty("自定义01字段")
    @Column(name = "field01")
    private String field01;

    //通用报销出发时间
    @ApiModelProperty("自定义02字段")
    @Column(name = "field02")
    private String field02;

    //通用报销到达地
    @ApiModelProperty("自定义03字段")
    @Column(name = "field03")
    private String field03;

    //通用报销到达时间
    @ApiModelProperty("自定义04字段")
    @Column(name = "field04")
    private String field04;

    //通用报销明细的备注字段
    @ApiModelProperty("自定义05字段")
    @Column(name = "field05")
    private String field05;

    //记录手动添加的明细关联的附件是否定额发票
    @ApiModelProperty("自定义06字段")
    @Column(name = "field06")
    private String field06;

    @ApiModelProperty("自定义07字段")
    @Column(name = "field07")
    private String field07;

    @ApiModelProperty("自定义08字段")
    @Column(name = "field08")
    private String field08;

    @ApiModelProperty("自定义09字段")
    @Column(name = "field09")
    private String field09;

    @ApiModelProperty("自定义10字段")
    @Column(name = "field10")
    private String field10;

    @ApiModelProperty("自定义01字段code")
    @Column(name = "acitem01_code")
    private String acitem01Code;

    @ApiModelProperty("自定义01字段name")
    @Column(name = "acitem01_name")
    private String acitem01Name;

    @ApiModelProperty("自定义02字段code")
    @Column(name = "acitem02_code")
    private String acitem02Code;

    @ApiModelProperty("自定义02字段name")
    @Column(name = "acitem02_name")
    private String acitem02Name;

    @ApiModelProperty("自定义03字段code")
    @Column(name = "acitem03_code")
    private String acitem03Code;

    @ApiModelProperty("自定义03字段name")
    @Column(name = "acitem03_name")
    private String acitem03Name;

    @ApiModelProperty("自定义04字段code")
    @Column(name = "acitem04_code")
    private String acitem04Code;

    @ApiModelProperty("自定义04字段name")
    @Column(name = "acitem04_name")
    private String acitem04Name;

    @ApiModelProperty("自定义05字段code")
    @Column(name = "acitem05_code")
    private String acitem05Code;

    @ApiModelProperty("自定义05字段name")
    @Column(name = "acitem05_name")
    private String acitem05Name;

    @ApiModelProperty("拓展元素6")
    @Column(name = "acitem06_code")
    private String acitem06Code;

    @ApiModelProperty("拓展元素6")
    @Column(name = "acitem06_name")
    private String acitem06Name;

    @ApiModelProperty("拓展元素7")
    @Column(name = "acitem07_code")
    private String acitem07Code;

    @ApiModelProperty("拓展元素7")
    @Column(name = "acitem07_name")
    private String acitem07Name;

    @ApiModelProperty("拓展元素8")
    @Column(name = "acitem08_code")
    private String acitem08Code;

    @ApiModelProperty("拓展元素8")
    @Column(name = "acitem08_name")
    private String acitem08Name;

    @ApiModelProperty("拓展元素9")
    @Column(name = "acitem09_code")
    private String acitem09Code;

    @ApiModelProperty("拓展元素9")
    @Column(name = "acitem09_name")
    private String acitem09Name;

    @ApiModelProperty("拓展元素10")
    @Column(name = "acitem10_code")
    private String acitem10Code;

    @ApiModelProperty("拓展元素10")
    @Column(name = "acitem10_name")
    private String acitem10Name;


    //票生成费用明细时打上票据所属职员
    //中间数据生成票关联关系时使用
    @TableField(exist = false)
    private String empCode;
    @TableField(exist = false)
    private String ecsRelItemName;
    //明细关联的票信息
    @TableField(exist = false)
    private String ecsBillId;
    @TableField(exist = false)
    private String ecsBillNo;
    @TableField(exist = false)
    private String ecsBillDate;
    @TableField(exist = false)
    private String ecsItems;
    //财务岗票验证状态
    @TableField(exist = false)
    private Integer ecsCheckStatus;
    //财务岗票未通过原因
    @TableField(exist = false)
    private String checkReason;
    //票的类型
    @TableField(exist = false)
    private String ecsBillKind;
    //凭证类型
    @TableField(exist = false)
    private String ecsBillType;
    @TableField(exist = false)
    private String ecsBillTypeName;
    //无票原因名称
    @TableField(exist = false)
    private String noEcsReasonName;
    //超标原因
    @TableField(exist = false)
    private String standReason = "";

    //票比对结果id
    @TableField(exist = false)
    private String compareResultId;
    //比对票的凭证类型
    @TableField(exist = false)
    private String compareEcsBillType;
    //是否验真，1=验真成功，2=验真为假，3=未修改，4=手动修改
    @TableField(exist = false)
    private Integer isValid;
    @TableField(exist = false)
    private String ecsRelId;
    @TableField(exist = false)
    private String expenseTypeCodes;
    @TableField(exist = false)
    private String expenseTypeNames;


    public static PcxBillExpDetailBase buildInstance(Map<String, Object> param) {
        // 获取 expDetailCode 或 fallback 到 expenseCode
        String expenseCodeObj = (String) param.get("expDetailCode");
        if (expenseCodeObj == null) {
            expenseCodeObj = (String) param.get("expenseCode");
        }

        // 空值或长度不足时默认为空字符串，避免异常
        String expenseCode = "";
        if (expenseCodeObj != null && expenseCodeObj.length() >= 5) {
            expenseCode = expenseCodeObj.substring(0, 5);
        } else if (expenseCodeObj != null) {
            expenseCode = expenseCodeObj; // 可选：保留原始值而不是置空
        }

        // 构建类型映射关系
        Map<String, Class<? extends PcxBillExpDetailBase>> typeMap = new HashMap<>();
        typeMap.put(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.TRAVEL.getCode(), PcxBillExpDetailTravel.class);
        typeMap.put(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.ABROAD.getCode(), PcxBillExpDetailAbroad.class);
        typeMap.put(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.INLANDFEE.getCode(), PcxBillExpDetailInlandfee.class);
        typeMap.put(PcxBillProcessConstant.ExpenseProcessBeanEnum.TRAINING.getCode(), PcxBillExpDetailTraining.class);
        typeMap.put(PcxBillProcessConstant.ExpenseProcessBeanEnum.MEETING.getCode(), PcxBillExpDetailMeeting.class);

        // 查找匹配的类并构建实例
        Class<? extends PcxBillExpDetailBase> targetClass = typeMap.get(expenseCode);
        if (targetClass != null) {
            return JSONObject.parseObject(JSONObject.toJSONString(param), targetClass);
        }
        // 默认情况
       return JSONObject.parseObject(JSONObject.toJSONString(param), PcxBillExpDetailTraining.class);
    }

    public String getExpDetailCode() {
        if(StringUtil.isEmpty(expDetailCode)){
            return StringUtil.EMPTY;
        }
        return expDetailCode;
    }
}
