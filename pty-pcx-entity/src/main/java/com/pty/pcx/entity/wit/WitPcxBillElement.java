package com.pty.pcx.entity.wit;

import java.util.List;

import lombok.Data;

public class WitPcxBillElement {

    @Data
    public static class BillBlock {
      private String type;

      private String block;

      private List<BillElement> billElement;
    }

    @Data
    public static class BillElement {

        private String name;

        private String code;

        private String model;

        private String enumCode;
    }


    @Data
    public static class Block {

        private String name;

        private String code;
    }

    @Data
    public static class GroupBlock {

        private int sort;

        private String groupCode;

        private String groupName;

        private List<Block> blockList;
    }

    @Data
    public static class BillMethod {

        private String method;

        private String methodName;

    }
}
