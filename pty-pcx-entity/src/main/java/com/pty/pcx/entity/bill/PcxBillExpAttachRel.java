package com.pty.pcx.entity.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 费用明细与附件关联表(PcxBillExpDetailAttachRel)实体类
 *
 * <AUTHOR>
 * @since 2024-12-20 10:21:40
 */
@Data
@Entity
@Table(schema = "pcx_bill_exp_attach_rel")
public class PcxBillExpAttachRel implements Serializable {
    private static final long serialVersionUID = -30892770598451169L;

    @Id
	@ApiModelProperty("主键id")
    @Column(name = "id")
    private String id;
    
	@ApiModelProperty("单据主键ID")
    @Column(name = "bill_id")
    private String billId;

    @ApiModelProperty("谁与附件关联，1=票，2=费用明细")
    @Column(name = "rel_type")
    private Integer relType;
    
    @ApiModelProperty("附件类型")
    @Column(name = "ecs_bill_type")
    private String ecsBillType;

    @ApiModelProperty("关联编码\n" +
            "如果是票，则存储的是ecsBillId\n" +
            "如果是住宿费，则存储的是hotelGroup\n" +
            "如果是其他费用，则存储的是detailId")
    @Column(name = "rel_id")
    private String relId;
    
	@ApiModelProperty("附件ID")
    @Column(name = "attach_id")
    private String attachId;
    
	@ApiModelProperty("文件名称")
    @Column(name = "file_name")
    private String fileName;
    
	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;
    
	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;
    
	@ApiModelProperty("区划代码")
    @Column(name = "mof_div_code")
    private String mofDivCode;
    
	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;
    
}

