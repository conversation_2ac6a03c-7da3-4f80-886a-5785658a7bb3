package com.pty.pcx.entity.bas;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 城市淡旺季表(PcxBasCityPeak)实体类
 *
 */
@Data
@Entity
@Table(schema = "pcx_bas_city_peak")
public class PcxBasCityPeak implements Serializable {
    private static final long serialVersionUID = -4843484243667825485L;

    @Id
	@ApiModelProperty("关联表主键Id")
    @Column(name = "id")
    private String id;

    @ApiModelProperty("类型 1-住宿费")
    @Column(name = "`type`")
    private Integer type;

	@ApiModelProperty("数据编码")
    @Column(name = "data_code")
    private String dataCode;

	@ApiModelProperty("数据名称")
    @Column(name = "data_name")
    private String dataName;

    @ApiModelProperty("数据编码类型 1-省级 2-市级 3-区县级")
    @Column(name = "data_level")
    private Integer dataCodeLevel;

    @ApiModelProperty("旺季时间段")
    @Column(name = "peak_date_json")
    private String peakDateJson;

    @ApiModelProperty("开始时间")
    @Column(name = "start_day")
    private String startDay;

    @ApiModelProperty("结束时间")
    @Column(name = "end_day")
    private String endDay;

    @ApiModelProperty("淡旺季类型 1-旺季 2-淡季")
    @Column(name = "peak_type")
    private Integer peakType;

	@ApiModelProperty("单位编码")
    @Column(name = "agy_code")
    private String agyCode;

	@ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

	@ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

	@ApiModelProperty("租户ID")
    @Column(name = "tenant_id")
    private String tenantId;

    @ApiModelProperty("创建人编码")
    @Column(name = "creator")
    private String creator;

    @ApiModelProperty("创建人名称")
    @Column(name = "creator_name")
    private String creatorName;

    @ApiModelProperty("创建时间")
    @Column(name = "created_time")
    private String createdTime;

    @ApiModelProperty("修改人编码")
    @Column(name = "modifier")
    private String modifier;

    @ApiModelProperty("修改人名称")
    @Column(name = "modifier_name")
    private String modifierName;

    @ApiModelProperty("修改时间")
    @Column(name = "modified_time")
    private String modifiedTime;

}

