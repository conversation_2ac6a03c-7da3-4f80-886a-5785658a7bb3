package com.pty.pcx.util;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BalanceTypeEnum;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.qo.bill.PcxBillBalanceMergeQO;
import com.pty.pcx.qo.bill.PcxBillBalanceQO;
import com.pty.pcx.vo.bill.PcxBillBalanceMergeVO;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.StringUtil;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class FundSourceFlushUtil {

    public static List<PcxBillBalanceQO> mergeToBalance(List<PcxBillBalanceMergeQO> list, PcxBill pcxBill,String positionCode) {
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }

        list.forEach(item -> {
            if (StringUtil.isEmpty(item.getExpenseCode())){
                item.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
                item.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            }
            if (StringUtil.isEmpty(item.getBalanceSource())){
                item.setBalanceSource(pcxBill.getBillFuncCode());
            }
        });
        //校验数据
        if (PositionEnum.isFinance(positionCode)){
            boolean isExpense = list.stream().anyMatch(item -> StringUtil.isEmpty(item.getExpenseCode()) || Objects.equals(PcxConstant.UNIVERSAL_EXPENSE_CODE, item.getExpenseCode()));
            if (isExpense && Objects.equals(pcxBill.getBillFunc(), BillFuncCodeEnum.EXPENSE.getCode())) {
                throw new RuntimeException("请完善报销单的费用类型");
            }

            boolean b = list.stream().anyMatch(item -> StringUtil.isEmpty(item.getExpenseCode())
                    || StringUtil.isEmpty(item.getDepartmentCode())
                    || StringUtil.isEmpty(item.getBalanceNo())
                    || StringUtil.isEmpty(item.getProjectBalanceNo())
                    || Objects.isNull(item.getUsedAmt())
                    || item.getUsedAmt().compareTo(BigDecimal.ZERO) == 0
            );
            //申请的经费来源可以为0 （借款和报销不能为0）
            if (b && ObjectUtil.notEqual(pcxBill.getBillFunc(), BillFuncCodeEnum.APPLY.getCode())) {
                throw new RuntimeException("经费来源指标数据不能为空");
            }
        }else{
            //过滤指标都为空的数据
            list = list.stream().filter(item->StringUtil.isNotEmpty(item.getProjectBalanceId())).collect(Collectors.toList());
        }

        List<PcxBillBalanceQO> result = Lists.newArrayList();
        //按经费来源分组指标信息
        List<PcxBillBalanceMergeQO> finalList = list;
        list.forEach(item -> {
            if (PositionEnum.isFinance(positionCode)) {
                //判断是否已经存在项目经费来源
                if (result.stream().noneMatch(balance -> Objects.equals(item.getExpenseCode(), balance.getExpenseCode())
                                                        && Objects.equals(item.getDepartmentCode(), balance.getDepartmentCode())
                                                        && Objects.equals(item.getProjectBalanceNo(), balance.getBalanceNo()))){
                    //从指标中拆分项目额度信息 并记录
                    PcxBillBalanceQO projectBalance = new PcxBillBalanceQO();
                    BeanUtils.copyProperties(item, projectBalance);
                    projectBalance.setBalanceId(item.getProjectBalanceId());
                    projectBalance.setBalanceNo(item.getProjectBalanceNo());
                    projectBalance.setBalanceType(item.getProjectBalanceType());
                    projectBalance.setProjectCode(item.getProjectCode());
                    projectBalance.setProjectName(item.getProjectName());
                    //从指标汇总金额
                    List<PcxBillBalanceMergeQO> pcxBillBalanceVOs = finalList.stream().filter(
                            balance -> Objects.equals(item.getExpenseCode(), balance.getExpenseCode())
                                    && Objects.equals(item.getDepartmentCode(),balance.getDepartmentCode())
                                    && Objects.equals(item.getProjectBalanceNo(), balance.getProjectBalanceNo())
                                    && BalanceTypeEnum.BUD.getCode().equals(balance.getBalanceType())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(pcxBillBalanceVOs)) {
                        projectBalance.setUsedAmt(pcxBillBalanceVOs.stream().map(PcxBillBalanceMergeQO::getUsedAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    }else {
                        projectBalance.setUsedAmt(BigDecimal.ZERO);
                    }
                    projectBalance.setBillId(pcxBill.getId());
                    projectBalance.setBillFuncCode(pcxBill.getBillFuncCode());
                    projectBalance.setAgyCode(pcxBill.getAgyCode());
                    projectBalance.setAgyName(pcxBill.getAgyName());
                    projectBalance.setFiscal(pcxBill.getFiscal());
                    projectBalance.setMofDivCode(pcxBill.getMofDivCode());
                    result.add(projectBalance);
                }
                PcxBillBalanceQO budProjectBalance = new PcxBillBalanceQO();
                BeanUtils.copyProperties(item, budProjectBalance);
                budProjectBalance.setBillId(pcxBill.getId());
                budProjectBalance.setBillFuncCode(pcxBill.getBillFuncCode());
                budProjectBalance.setAgyCode(pcxBill.getAgyCode());
                budProjectBalance.setAgyName(pcxBill.getAgyName());
                budProjectBalance.setFiscal(pcxBill.getFiscal());
                budProjectBalance.setMofDivCode(pcxBill.getMofDivCode());
                result.add(budProjectBalance);
            }else{
                //没有指标只记录项目额度
                PcxBillBalanceQO projectBalance = new PcxBillBalanceQO();
                BeanUtils.copyProperties(item, projectBalance);
                projectBalance.setBillId(pcxBill.getId());
                projectBalance.setBillFuncCode(pcxBill.getBillFuncCode());
                projectBalance.setAgyCode(pcxBill.getAgyCode());
                projectBalance.setAgyName(pcxBill.getAgyName());
                projectBalance.setFiscal(pcxBill.getFiscal());
                projectBalance.setMofDivCode(pcxBill.getMofDivCode());
                if (StringUtil.isEmpty(item.getExpenseCode())){
                    if (Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.COMMON.getCode())){
                        projectBalance.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
                        projectBalance.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
                    }
                    if (Objects.equals(pcxBill.getBizType(), ItemBizTypeEnum.TRAVEL.getCode())){
                        projectBalance.setExpenseCode(PcxConstant.TRAVEL_EXPENSE_30211);
                        projectBalance.setExpenseName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
                    }
                }
                projectBalance.setBalanceId(item.getProjectBalanceId());
                projectBalance.setBalanceNo(item.getProjectBalanceNo());
                projectBalance.setBalanceType(item.getProjectBalanceType());
                projectBalance.setProjectCode(item.getProjectCode());
                projectBalance.setProjectName(item.getProjectName());
                projectBalance.setUsedAmt(item.getProjectUsedAmt());
                result.add(projectBalance);
            }
        });
        return result;
    }

    public static List<PcxBillBalanceMergeVO> balanceToMerge(List<PcxBillBalanceVO> list,String positionCode) {

        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<PcxBillBalanceMergeVO> result = Lists.newArrayList();
        //财务岗或者不是整单分摊需要按照标准展示
        List<PcxBillBalanceVO> ibalBalances = list.stream().filter(item -> !BalanceTypeEnum.BUD.getCode().equals(item.getBalanceType())).collect(Collectors.toList());

        ibalBalances.forEach(item -> {
            PcxBillBalanceMergeVO mergeVO = new PcxBillBalanceMergeVO();
            BeanUtils.copyProperties(item, mergeVO);
            mergeVO.setBalanceType(BalanceTypeEnum.BUD.getCode());
            mergeVO.setProjectBalanceId(item.getBalanceId());
            mergeVO.setProjectBalanceNo(item.getBalanceNo());
            mergeVO.setProjectUsedAmt(item.getUsedAmt());
            //查询对应的bud  额度
            Optional<PcxBillBalanceVO> budBalance = list.stream().filter(
                    balance -> Objects.equals(item.getExpenseCode(),balance.getExpenseCode())
                            && Objects.equals(item.getDepartmentCode(),balance.getDepartmentCode())
                            && Objects.equals(item.getProjectCode(),balance.getProjectCode())
                            && Objects.equals(item.getBudDepartmentCode(),balance.getBudDepartmentCode())
                            && Objects.equals(BalanceTypeEnum.BUD.getCode(),balance.getBalanceType())).findFirst();
            if (budBalance.isPresent()){
                PcxBillBalanceVO pcxBillBalanceVO = budBalance.get();
                mergeVO.setBalanceId(pcxBillBalanceVO.getBalanceId());
                mergeVO.setBalanceNo(pcxBillBalanceVO.getBalanceNo());
                mergeVO.setBalanceAmt(pcxBillBalanceVO.getBalanceAmt());
                mergeVO.setTotalAmt(pcxBillBalanceVO.getTotalAmt());
                mergeVO.setUsedAmt(pcxBillBalanceVO.getUsedAmt());
            }else {
                mergeVO.setBalanceId(null);
                mergeVO.setBalanceNo(null);
            }
            result.add(mergeVO);
        });
        return result;
    }
}
