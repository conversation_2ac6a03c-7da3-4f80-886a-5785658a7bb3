package com.pty.pcx.dto.mad;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MadAgyAccountDTO {

    @ApiModelProperty(value = "单位代码")
    private String agyCode;

    @ApiModelProperty(value = "单位名称")
    private String agyName;

    @Id
    @ApiModelProperty(value = "银行账号")
    private String accountCode;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    @ApiModelProperty(value = "银行代码")
    private String bankCode;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行网点代码")
    private String banknodeCode;

    @ApiModelProperty(value = "银行网点名称")
    private String banknodeName;

    @ApiModelProperty(value = "银行网点行号")
    private String banknodeNo;

    @ApiModelProperty(value = "人行联行号")
    private String pbcNo;

    @ApiModelProperty(value = "人行联行名称")
    private String pbcName;

    @ApiModelProperty(value = "账户类型")
    private String accountType;

    @ApiModelProperty(value = "开户日期")
    private String startDate;

    @ApiModelProperty(value = "到期日期")
    private String endDate;

    @ApiModelProperty(value = "销户日期")
    private String cancelDate;

    @ApiModelProperty(value = "账户地址")
    private String address;

    @ApiModelProperty(value = "是否启用    1启用  0不启用")
    private String isUsed = "1";

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "查询下页标识")
    private String nextFlag;

    @ApiModelProperty(value = "最后查询日期")
    private String lastDate;

    @ApiModelProperty(value = "是否默认   1是  0否")
    private String isDefault;

    @ApiModelProperty(value = "账套code")
    private String acbCode;

    @ApiModelProperty(value = "账套名字")
    private String acbName;

    @ApiModelProperty(value = "会计科目code")
    private String acoCode;

    @ApiModelProperty(value = "会计科目name")
    private String acoName;

    @ApiModelProperty(value = "会计科目fname")
    private String acoFname;

    @ApiModelProperty(value = "期市余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "银行期初余额")
    private BigDecimal bankBalance;

    @ApiModelProperty(value = "mofDivCode")
    private String mofDivCode;

    @ApiModelProperty(value = "srcAccountCode")
    private String srcAccountCode;

    @ApiModelProperty(value = "srcAgyCode")
    private String srcAgyCode;

    @ApiModelProperty(value = "币种编码")
    private String curCode;

    @ApiModelProperty(value = "币种名称")
    private String curName;

    @ApiModelProperty(value = "是否完成互联互通签约,1已签约，2未签约")
    private Integer isEvesSign;

    @ApiModelProperty(name = "城市编码")
    private String cityCode;

    @ApiModelProperty(name = "城市名称")
    private String cityName;
}
