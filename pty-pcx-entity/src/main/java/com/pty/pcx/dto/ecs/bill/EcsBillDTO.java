package com.pty.pcx.dto.ecs.bill;

import com.pty.pcx.dto.ecs.EcsDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EcsBillDTO extends EcsDTO {



    protected String billId;

    protected String attachId;

    protected String relId;

    protected String detailId;

    protected String billTypeCode;

    protected String billNo;

    protected Integer isDeleted;

    protected String keyword;

    protected Integer isShowTarBillNo;

    protected String billNoLike;

    protected String updateUserCode;

    protected String updateUser;

    protected String updateTime;

    protected Integer billAttachType;

    /**
     * 凭证来源。{@link EcsEnum.BillSrcCode}
     */
    protected String billSrcCode;

    protected String fileFormat;

    protected Integer isRelExp;

    protected Integer isRelVou;

    protected List<String> billIds = new ArrayList<>();

    protected List<String> fileIds = new ArrayList<>();

    protected List<String> bizBillIds = new ArrayList<>();

    protected String randomStr;

    protected String claimer;

    protected List<String> billTypeCodes;

    protected String billDate;

    //todo 后续单独的qo
    protected String billDateFrom;

    //todo 后续单独的qo
    protected String billDateTo;

    //todo 后续单独的qo
    /**
     * 摘要关键字
     * */
    protected String billDescptLike;

    //todo 后续单独的qo
    /**
     * 三方利用-开票方
     * */
    protected String billIssuerLike;

    protected String billReciverLike;

    protected BigDecimal billAmt;

    protected BigDecimal billAmtFrom;

    protected BigDecimal billAmtTo;

    private String verificationCode;

    private List<Integer> validList;

    private String targetBillId;

    private String fileName;
    /**
     * 是否收入票据： 0:无关票（“银行票据”页面） 1:是（“收入票”页面） 2:否（“单位的票”、“我的票”页面）
     */
    private Integer isIncomeBill;

    private List<String> billNoList;

    private String fileNameLike;
    /**
     * 值为1，查询验真不为假的状态
     */
    private Integer validStatus;

    private String billStatus;

    /**
     * 删除时使用表示需删除标签的标签主键
     * 查询时使用表示过滤条件
     */
    private String classId;

    /**
     * 结构化详情id
     */
    private List<String> detailIdList;

    /**
     * 0:全部
     * 1:本人票据
     * 2:被授权票据
     */
    private Integer isPersonalBill;

    /**
     * 关联事项标签查询：1则过滤已经存在事项标签的票据和附件，0则不过滤
     */
    private Integer isRelatedBusClass;

    /**
     * 排序方式：0-降序从大到小， 1-升序从小到大
     */
    private Integer sortByField;

    /**
     * 分组字段
     */
    private String sortField;

    /**
     * 查询可报销单据的种类 {@link EcsEnum.BillTypeKind}
     * 备注：不传或者为空则查询所有种类
     */
    private String billTypeKind;

    /**
     * 支付方使用的银行编码
     */
    private String bankCode;

    /**
     * 支付方使用的银行名称
     */
    private String bankName;

    private Integer isRelDisplayAttach;

    /**
     * 附件id集合
     */
    private List<String> attachIds;

    /**
     * 接取前端传回的票据信息集合
     */
    private List<EcsBillDTO> ecsBillVOList;
    // 是否过滤其他类型附件  1-是  2-否
    private Integer isFilterOtherFile;
    // 票在编辑的时候，如果切换类型，这个字段放切换之前的类型
    private String oldBillTypeCode;

    /**
     * 解析类型 {@link EcsEnum.AnalysisTypeEnum}
     */
    private String analysisType;

    /**
     * 是否包含已使用的  1-是 2-否
     */
    private Integer isIncludedUsed;

    /**
     * 授权用户编码 (代报人编码)
     */
    private String authUserCode;
}
