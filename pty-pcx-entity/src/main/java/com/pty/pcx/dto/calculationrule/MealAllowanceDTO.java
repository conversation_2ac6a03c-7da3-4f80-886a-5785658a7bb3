package com.pty.pcx.dto.calculationrule;

import lombok.Data;

/**
 * 差旅费伙食补贴计算dto
 */
@Data
public class MealAllowanceDTO {

    /**
     * 出差城市
     */
    private String departureCity;

    /**
     * 出差省份
     */
    private String departureProvince;

    /**
     * 工作地所在城市
     */
    private String workplaceCity;

    /**
     * 工作地所在省份
     */
    private String workplaceProvince;

    /**
     * 出差天数
     */
    private String travelDays;

    /**
     * 住宿舍总天数
     */
    private String dormDays;

    /**
     * 食宿类型 0-无伙食有宿舍  1-有伙食有宿舍 ,此处用于pty_rule的规则匹配  在此之外第三种情况为住宿舍天数为0，类型为2
     */
    private String stayType;

    /**
     * 是否住宿舍
     */
    private Boolean isStayInDormitory;

    /**
     * 补助标准
     */
    private String standard;

    /**
     * 人员费控级别
     */
    private String budLevel;

    /**
     * 标准代码
     */
    private String standardCode;
    /**
     * 人员所属部门编码
     */
    private String departmentCode;
    private String jobLevel;

    /**
     * 工作日住宿舍天数
     */
    private String workDormDays;

    /**
     * 工作日天数
     */
    private String workDays;

    /**
     * 行程类型 1-有返程
     */
    private String travelType;

    /**
     * 其他补助
     */
    private String extraSubsidy;

    /**
     * 节假日加班天数
     */
    private String holidayWorkDays;

    /**
     * 项目经理天数
     */
    private String pmDays;
}
