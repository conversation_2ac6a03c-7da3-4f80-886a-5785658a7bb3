package com.pty.pcx.dto.ecs.inv;

import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.util.List;

// 原始报销凭证-出租车票 taxi
@Data
public class PcxTaxiInvoiceDto extends InvoiceBaseDto{

    private String licensePlateNumber;  // 车牌号
    private String deptTime;            // 上车时间
    private String destTime;            // 下车时间
    private String mileage;             // 里程
    private String province;            // 省份
    private String city;                // 城市


    /**
     * 票据业务类型
     */
    private String bizTypeCode;
    private String bizTypeName;

    /**
     * 票对于费用类型
     */
    private List<PcxBasExpType> expTypeList;

}
