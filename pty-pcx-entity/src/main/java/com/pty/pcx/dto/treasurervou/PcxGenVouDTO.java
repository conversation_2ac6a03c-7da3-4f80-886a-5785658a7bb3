package com.pty.pcx.dto.treasurervou;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.qo.treasurervou.PcxTreasurerVouQO;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 会计记账制单查询单据数据DTO
 */
@Data
@Builder
public class PcxGenVouDTO implements Serializable {

    private PcxTreasurerVouQO pcxTreasurerVouQO;
    // 不返回前端
    @JsonIgnore
    private List<PcxBill> pcxBills;
    @JsonIgnore
    private List<PcxBillPayDetail> pcxBillPayDetailList;

    private String vouId;// 凭证id
    private String vouNo;// 凭证号
    private String vouDate;// 凭证日期
}
