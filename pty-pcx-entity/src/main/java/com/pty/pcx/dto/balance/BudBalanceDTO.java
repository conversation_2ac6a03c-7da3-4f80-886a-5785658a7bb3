package com.pty.pcx.dto.balance;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BudBalanceDTO {
    /**
     * 平衡ID
     */
    protected String balanceId;

    /**
     * 平衡编号
     */
    protected String balanceNo;

    /**
     * 平衡类型
     */
    protected String balanceType;

    /**
     * 财政年度
     */
    protected Integer fiscal;

    /**
     * 机构代码
     */
    protected String agyCode;

    /**
     * 机构名称
     */
    protected String agyName;

    /**
     * 功能代码
     */
    protected String expfuncCode;

    /**
     * 功能名称
     */
    protected String expfuncName;

    /**
     * 政府预期公司代码
     */
    protected String govexpecoCode;

    /**
     * 政府预期公司名称
     */
    protected String govexpecoName;

    /**
     * 预期公司代码
     */
    protected String expecoCode;

    /**
     * 预期公司名称
     */
    protected String expecoName;

    /**
     * 部门项目代码
     */
    protected String depproCode;

    /**
     * 部门项目名称
     */
    protected String depproName;

    /**
     * 项目代码
     */
    protected String projectCode;

    /**
     * 项目名称
     */
    protected String projectName;

    /**
     * 项目类型代码
     */
    protected String protypeCode;

    /**
     * 项目类型名称
     */
    protected String protypeName;

    /**
     * 资金类型代码
     */
    protected String fundtypeCode;

    /**
     * 资金类型名称
     */
    protected String fundtypeName;

    /**
     * 预算来源代码
     */
    protected String budsourceCode;

    /**
     * 预算来源名称
     */
    protected String budsourceName;

    /**
     * 资金来源代码
     */
    protected String fundsourceCode;

    /**
     * 资金来源名称
     */
    protected String fundsourceName;

    /**
     * 支付类型代码
     */
    protected String paytypeCode;

    /**
     * 支付类型名称
     */
    protected String paytypeName;

    /**
     * 支出类型代码
     */
    protected String exptypeCode;

    /**
     * 支出类型名称
     */
    protected String exptypeName;

    /**
     * 设置模式代码
     */
    protected String setmodeCode;

    /**
     * 设置模式名称
     */
    protected String setmodeName;

    /**
     * 币种代码
     */
    protected String currentCode;

    /**
     * 币种名称
     */
    protected String currentName;

    /**
     * 部门代码
     */
    protected String departmentCode;

    /**
     * 部门名称
     */
    protected String departmentName;

    /**
     * 员工代码
     */
    protected String employeeCode;

    /**
     * 员工名称
     */
    protected String employeeName;

    /**
     * 财务类型代码
     */
    protected String fatypeCode;

    /**
     * 财务类型名称
     */
    protected String fatypeName;

    /**
     * 辅助项目01代码
     */
    protected String acitem01Code;

    /**
     * 辅助项目01名称
     */
    protected String acitem01Name;

    /**
     * 辅助项目02代码
     */
    protected String acitem02Code;

    /**
     * 辅助项目02名称
     */
    protected String acitem02Name;

    /**
     * 辅助项目03代码
     */
    protected String acitem03Code;

    /**
     * 辅助项目03名称
     */
    protected String acitem03Name;

    /**
     * 辅助项目04代码
     */
    protected String acitem04Code;

    /**
     * 辅助项目04名称
     */
    protected String acitem04Name;

    /**
     * 辅助项目05代码
     */
    protected String acitem05Code;

    /**
     * 辅助项目05名称
     */
    protected String acitem05Name;

    /**
     * 辅助项目06代码
     */
    protected String acitem06Code;

    /**
     * 辅助项目06名称
     */
    protected String acitem06Name;

    /**
     * 辅助项目07代码
     */
    protected String acitem07Code;

    /**
     * 辅助项目07名称
     */
    protected String acitem07Name;

    /**
     * 辅助项目08代码
     */
    protected String acitem08Code;

    /**
     * 辅助项目08名称
     */
    protected String acitem08Name;

    /**
     * 辅助项目09代码
     */
    protected String acitem09Code;

    /**
     * 辅助项目09名称
     */
    protected String acitem09Name;

    /**
     * 辅助项目10代码
     */
    protected String acitem10Code;

    /**
     * 辅助项目10名称
     */
    protected String acitem10Name;

    /**
     * 辅助项目11代码
     */
    protected String acitem11Code;

    /**
     * 辅助项目11名称
     */
    protected String acitem11Name;

    /**
     * 辅助项目12代码
     */
    protected String acitem12Code;

    /**
     * 辅助项目12名称
     */
    protected String acitem12Name;

    /**
     * 辅助项目13代码
     */
    protected String acitem13Code;

    /**
     * 辅助项目13名称
     */
    protected String acitem13Name;

    /**
     * 辅助项目14代码
     */
    protected String acitem14Code;

    /**
     * 辅助项目14名称
     */
    protected String acitem14Name;

    /**
     * 辅助项目15代码
     */
    protected String acitem15Code;

    /**
     * 辅助项目15名称
     */
    protected String acitem15Name;

    /**
     * 辅助项目16代码
     */
    protected String acitem16Code;

    /**
     * 辅助项目16名称
     */
    protected String acitem16Name;

    /**
     * 辅助项目17代码
     */
    protected String acitem17Code;

    /**
     * 辅助项目17名称
     */
    protected String acitem17Name;

    /**
     * 辅助项目18代码
     */
    protected String acitem18Code;

    /**
     * 辅助项目18名称
     */
    protected String acitem18Name;

    /**
     * 辅助项目19代码
     */
    protected String acitem19Code;

    /**
     * 辅助项目19名称
     */
    protected String acitem19Name;

    /**
     * 辅助项目20代码
     */
    protected String acitem20Code;

    /**
     * 辅助项目20名称
     */
    protected String acitem20Name;

    /**
     * 辅助项目21代码
     */
    protected String acitem21Code;

    /**
     * 辅助项目21名称
     */
    protected String acitem21Name;

    /**
     * 辅助项目22代码
     */
    protected String acitem22Code;

    /**
     * 辅助项目22名称
     */
    protected String acitem22Name;

    /**
     * 辅助项目23代码
     */
    protected String acitem23Code;

    /**
     * 辅助项目23名称
     */
    protected String acitem23Name;

    /**
     * 辅助项目24代码
     */
    protected String acitem24Code;

    /**
     * 辅助项目24名称
     */
    protected String acitem24Name;

    /**
     * 辅助项目25代码
     */
    protected String acitem25Code;

    /**
     * 辅助项目25名称
     */
    protected String acitem25Name;

    /**
     * 辅助项目26代码
     */
    protected String acitem26Code;

    /**
     * 辅助项目26名称
     */
    protected String acitem26Name;

    /**
     * 辅助项目27代码
     */
    protected String acitem27Code;

    /**
     * 辅助项目27名称
     */
    protected String acitem27Name;

    /**
     * 辅助项目28代码
     */
    protected String acitem28Code;

    /**
     * 辅助项目28名称
     */
    protected String acitem28Name;

    /**
     * 辅助项目29代码
     */
    protected String acitem29Code;

    /**
     * 辅助项目29名称
     */
    protected String acitem29Name;

    /**
     * 辅助项目30代码
     */
    protected String acitem30Code;

    /**
     * 辅助项目30名称
     */
    protected String acitem30Name;

    /**
     * 辅助项目31代码
     */
    protected String acitem31Code;

    /**
     * 辅助项目31名称
     */
    protected String acitem31Name;

    /**
     * 辅助项目32代码
     */
    protected String acitem32Code;

    /**
     * 辅助项目32名称
     */
    protected String acitem32Name;

    /**
     * 销售标志代码
     */
    protected String salflagCode;

    /**
     * 销售标志名称
     */
    protected String salflagName;

    /**
     * 采购标志代码
     */
    protected String purflagCode;

    /**
     * 采购标志名称
     */
    protected String purflagName;

    /**
     * 项目用户
     */
    protected String projectUser;

    /**
     * 项目用户名
     */
    protected String projectUserName;

    /**
     * 预算项目代码
     */
    protected String buditemCode;

    /**
     * 预算项目名称
     */
    protected String buditemName;

    /**
     * 目录代码
     */
    protected String catalogueCode;

    /**
     * 目录名称
     */
    protected String catalogueName;

    /**
     * 摘要
     */
    protected String summary;

    /**
     * 部门划分代码
     */
    private String mofDivCode;

    /**
     * 来源ID
     */
    protected String fromId;

    /**
     * 财务ID代码
     */
    protected String finaceidCode;

    /**
     * 财务ID名称
     */
    protected String finaceidName;

    /**
     * 财务金额
     */
    private BigDecimal financeAmt;

    /**
     * 初始余额金额
     */
    private BigDecimal qczcAmt;

    /**
     * 期初结余金额
     */
    private BigDecimal qmjzAmt;

    /**
     * 总金额
     */
    private BigDecimal totalAmt;

    /**
     * 已用金额
     */
    private BigDecimal usedAmt;

    /**
     * 余额金额
     */
    private BigDecimal balanceAmt;

    /**
     * 增加金额
     */
    private BigDecimal addAmt;

    /**
     * 减少金额
     */
    private BigDecimal subAmt;

    /**
     * 贷款金额
     */
    private BigDecimal loanAmt;

    /**
     * 已用贷款金额
     */
    private BigDecimal loanUsedAmt;

    /**
     * 贷款余额金额
     */
    private BigDecimal loanBalanceAmt;

    /**
     * 申请金额
     */
    private BigDecimal applyAmt;

    /**
     * 已用申请金额
     */
    private BigDecimal applyUsedAmt;

    /**
     * 申请余额金额
     */
    private BigDecimal applyBalanceAmt;

    /**
     * 支出金额
     */
    private BigDecimal expenseAmt;

    /**
     * 已用支出金额
     */
    private BigDecimal expenseUsedAmt;

    /**
     * 支出支付金额
     */
    private BigDecimal expensePayAmt;

    /**
     * 合同金额
     */
    private BigDecimal htAmt;

    /**
     * 已用合同金额
     */
    private BigDecimal htUsedAmt;

    /**
     * 额度金额
     */
    private BigDecimal quotaAmt;

    /**
     * 已用额度金额
     */
    private BigDecimal quotaUsedAmt;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmt;

    /**
     * 已用采购金额
     */
    private BigDecimal purchaseUsedAmt;

    /**
     * 合同余额金额
     */
    private BigDecimal htBalanceAmt;

    /**
     * 资产金额
     */
    private BigDecimal assetAmt;

    /**
     * 预算金额
     */
    private BigDecimal galAmt;

    /**
     * 预算外金额
     */
    private BigDecimal pexGalAmt;

    /**
     * 创建日期
     */
    private String createDate;

    /**
     * 录入人ID
     */
    private String inputorId;

    /**
     * 录入人名称
     */
    private String inputorName;

    /**
     * 预算余额ID
     */
    private String budBalanceId;

    /**
     * 是否预算项目
     */
    private String isBuditem;

    /**
     * 预算层级
     */
    private String budLevel;

    /**
     * 支出贷款金额
     */
    private BigDecimal expenseLoanAmt;

    /**
     * 贷款支付金额
     */
    private BigDecimal loanPayAmt;


}
