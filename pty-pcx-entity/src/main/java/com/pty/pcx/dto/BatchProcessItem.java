package com.pty.pcx.dto;

import lombok.Data;

import java.util.List;
import java.util.function.BiConsumer;

@Data
public class BatchProcessItem<M, T> {

    private List<T> dataList;

    private Class<M> clazz;

    private BiConsumer<M, T> biConsumer;

    public BatchProcessItem(List<T> dataList, Class<M> clazz, BiConsumer<M, T> biConsumer) {
        this.dataList = dataList;
        this.clazz = clazz;
        this.biConsumer = biConsumer;
    }

    public Class<M> getClazz() {
        return clazz;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public BiConsumer<M, T> getBiConsumer() {
        return biConsumer;
    }
}