package com.pty.pcx.dto.ecs.file;

import com.pty.pcx.dto.ecs.inv.InvoiceBaseDto;
import lombok.Data;


// 原始报销凭证-合同 file_contract

@Data
public class PcxFileContractDto extends InvoiceBaseDto {
    private String fileId;              // 附件id, 报销用于回写电子凭证状态, 判断是否已报销
    private String fileName;            // 附件名称
    private String fileFormat;          // 附件类型, pdf/jpg/png等, 用于前端渲染
    private String attachId;



}
