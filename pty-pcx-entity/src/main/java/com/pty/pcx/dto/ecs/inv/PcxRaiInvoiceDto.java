package com.pty.pcx.dto.ecs.inv;

import com.alibaba.fastjson.annotation.JSONField;
import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// 原始报销凭证-铁路电子客票 rai
@Data
public class PcxRaiInvoiceDto extends InvoiceBaseDto{

    private String deptStationName;     // 始发站
    private String destStationName;     // 目的站
    private String trainNo;             // 车次
    private String travelDate;          // 乘车日期
    private String deptTime;            // 乘车时间
    @JSONField(name = "ticketAmt")
    private BigDecimal totalAmt;        // 实际报销金额
    private String seatLevel;           // 火车票席别(一等座、二等座)
    private String passName;            // 乘客姓名
    private String passId;              // 身份证号

    private BigDecimal taxRate;
    private BigDecimal taxAmt;


    /**
     * 票据业务类型
     */
    private String bizTypeCode;
    private String bizTypeName;

    /**
     * 票对于费用类型
     */
    private List<PcxBasExpType> expTypeList;

    private String empCode;
    private String empName;

}
