package com.pty.pcx.dto.treasurervou;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class PcxGipVouBillDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sysId = "PCX";//系统编号，对应会计平台模板编号
    //private String mergeBatch;//合并标识 值相同的单据会被合并
    private String tableName = "pcx_bill";//单据对应表名称
    private String loginDate;//登录日期
    private Integer perd;//会计期间
    private String summary;//凭证摘要
    private String billId;//单据id
    private String orgType;//机构类型
    private String industryType;//行业类型

    private List<Map<String, Object>> detailBills;

    public Integer getVouPerd(String transDate){
        return Integer.parseInt(transDate.substring(5,7),10);
    }

    @lombok.Data
    static class GipVouBillDetailDTO {
        private String sysId = "PCX";//系统编号，对应会计平台模板编号
        private String tableName = "pcx_bill_pay_detail";//单据对应表名称
        private String detailId;//明细id
        private String billId;//单据id
        private Integer perd;//会计期间


    }
}
