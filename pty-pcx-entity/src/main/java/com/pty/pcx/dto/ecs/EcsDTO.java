package com.pty.pcx.dto.ecs;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EcsDTO implements Serializable {
    // 当前页
    private Integer pageIndex = 1;

    // 每页数据量
    private Integer pageSize = 10;

    private String userCode;

    private String userName;

    private String bizTypeCode;

    private String bizTypeName;

    protected String mofDivCode;

    protected String agencyCode;

    protected String agencyName;
}
