package com.pty.pcx.dto.calculationrule;

import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.rule.RuleDomainObject;
import com.pty.rule.RuleDomainObject.Domain;
import lombok.Data;

import java.util.List;

@Data
@Domain(ns = "pcxCalculation", module = "pcxCalculation")
public class CalculationRuleDTO extends RuleDomainObject {

    /**
     * 指定的唯一key，用于返回值区分
     */
    private String key;

    /**
     * 单位编码
     */
    private String agyCode;

    /**
     * 年度
     */
    private String fiscal;

    /**
     * 区划
     */
    private String mofDivCode;

    /**
     * 费用类型
     */
    private String expenseType;
    /**
     * 伙食补助参数对象
     */
    private MealAllowanceDTO  mealAllowanceDTO;

    /**
     * 匹配到的标准集合
     */
    private List<PcxBillExpStandResult> standResultList;

}
