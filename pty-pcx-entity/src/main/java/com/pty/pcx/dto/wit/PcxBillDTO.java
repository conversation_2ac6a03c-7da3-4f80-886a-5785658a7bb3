package com.pty.pcx.dto.wit;

import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pcx.vo.bill.PcxBillBalanceVO;
import com.pty.pcx.vo.bill.PcxBillRelationVO;
import com.pty.pcx.vo.bill.PcxBillSettlementVO;
import com.pty.pcx.vo.ecs.WitEcsVO;
import com.pty.pub.common.anno.Desc;
import com.pty.pub.common.util.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 报销单实体
 */
@Data
public class PcxBillDTO {

    @Desc("主单据")
    private PcxBill mainBill = new PcxBill();

    @Desc("费用信息")
    private List<PcxBillExpBase> expenses = new ArrayList<>();

    @Desc("关联信息")
    private List<PcxBillRelationVO> relations = new ArrayList<>();

    @Desc("明细信息")
    private List<PcxBillExpDetailBase> details = new ArrayList<>();

    @Desc("附件信息")
    private List<PcxBillAttachRelationVO> attachs= new ArrayList<>();

    @Desc("结算信息")
    private List<PcxBillSettlementVO> settles = new ArrayList<>();

    @Desc("关联单据信息")
    private List<PcxBillDTO> refBills = new ArrayList<>();

    @Desc("预算指标信息")
    private List<PcxBillBalanceVO> balances = new ArrayList<>();

    @Desc("发票信息")
    private List<WitEcsVO> invoices = new ArrayList<>();

    @Desc("上下文信息")
    private Map<String, Object> context = new ConcurrentHashMap<>();

    @Desc("关联单据本次使用金额")
    private BigDecimal usedAmt = BigDecimal.ZERO;

    @Desc("关联单据类型")
    private String relBillFuncCode = "";

    private Object single = new Object();

    @Desc("是否虚拟单据")
    private Integer isVirtual = -1;



    @Desc("错误信息")
    private String errMsg;

    /**
     * 稽核成功不要调用此方法
     * 规则引擎不会渲染错误模板，提示信息会带入下一条规则中
     * @param msg
     */
    public void putErrMsg(String msg) {
        StringBuffer buffer = new StringBuffer(msg);
        String errMsg = StringUtil.isEmpty(this.errMsg) ? "" : this.errMsg;
        this.errMsg = buffer.append(errMsg).toString();
    }

    public String fillErrTemp() {
        String errMsg = this.errMsg;
        if (StringUtil.isNotEmpty(errMsg) && errMsg.startsWith("<br>")) {
            errMsg = errMsg.substring(4);
        }
        this.errMsg = null;
        return errMsg;
    }

    @Data
    public static final class Expense extends PcxBillExpBase {
        @Desc("明细信息")
        private List<PcxBillExpDetailBase> detail = new ArrayList<>();
    }
}
