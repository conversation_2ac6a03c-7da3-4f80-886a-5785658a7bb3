package com.pty.pcx.dto.transfer;

import com.pty.openapi.integration.dto.reimbursement.travel.TravelExpenseDetailDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ExtendTravelExpenseDetailDTO extends TravelExpenseDetailDTO {

    private String detailId;

    /**
     * 原始部门编码
     */
    private String originalDepartmentCode;

    private String ecsBillId;

    private String expDetailCode;

    /**
     * 行程 ID
     */
    private String tripId;

}

