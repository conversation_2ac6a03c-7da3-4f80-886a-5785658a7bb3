package com.pty.pcx.dto.ecs.biz;

import com.pty.pcx.dto.ecs.EcsDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EcsBizDTO extends EcsDTO implements Serializable {
    /**
     * 单据主键
     */
    @Id
    private String billId;

    /**
     * 业务年度
     */
    private Integer fiscalYear;

    /**
     * 单位编码
     */
    private String agencyCode;

    /**
     * 单位名称
     */
    private String agencyName;

    /**
     * 凭证类型编码(app:申请审批单,exp:报销费用支出单据,rece:电子收据,pct:合同,sal:工资单,pay:支付凭证)
     */
    private String billTypeCode;

    /**
     * 凭证类型名称
     */
    private String billTypeName;

    /**
     * 单据日期
     */
    private String billDate;

    /**
     * 票据金额
     */
    private BigDecimal billAmt;

    /**
     * 凭证摘要
     */
    private String billDescpt;

    /**
     * 凭证来源。{@link EcsEnum.BillSrcCode}
     */
    private String billSrcCode;

    /**
     * 是否有可显示附件
     */
    private Integer isRelDisplayAttach;

    /**
     * 是否已报销。1已报销，2未报销
     */
    private Integer isRelExp;

    /**
     * 是否已记账。1已记账，2未记账
     */
    private Integer isRelVou;

    /**
     * 是否已归档。1已归档，2未归档
     */
    private Integer isArchived;

    /**
     * 凭证所属人代码
     */
    private String billOwner;

    /**
     * 凭证所属人姓名
     */
    private String billOwnerName;

    /**
     * 凭证创建人代码
     */
    private String inputer;

    /**
     * 凭证创建人姓名
     */
    private String inputerName;

    /**
     * 创建时间、上传时间
     */
    private String inputTime;

    /**
     * 创建人
     */
    private String createUserCode;

    /**
     * 创建人姓名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String updateUserCode;

    /**
     * 修改人姓名
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 是否删除，1是，2否
     */
    private Integer isDeleted;

    /**
     * 版本
     */
    private Integer ver;

    /**
     * 行政区划代码
     */
    private String mofDivCode;

    /**
     * 租户ID
     */
    private String tenantId;

    private List<String> billIds;
}
