package com.pty.pcx.dto.ecs.inv;

import com.alibaba.fastjson.annotation.JSONField;
import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// 原始报销凭证-船票 steamer_ticket
@Data
public class PcxBoatInvoiceDto extends InvoiceBaseDto{


    private String time;            // 乘坐时间
    private String passName;        // 乘客姓名
    private String passId;          // 身份证号
    private String deptStation;     // 出发港口
    private String destStation;     // 到达港口
    private String seatLevel;       //TODO 船票席别(特等舱、一等舱、二等舱等)

    private String empCode;
    private String empName;
    /**
     * 票据业务类型
     */
    private String bizTypeCode;
    private String bizTypeName;

    /**
     * 票对于费用类型
     */
    private List<PcxBasExpType> expTypeList;
}
