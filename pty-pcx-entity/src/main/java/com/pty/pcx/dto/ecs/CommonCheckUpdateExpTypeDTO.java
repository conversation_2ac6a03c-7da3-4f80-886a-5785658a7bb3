package com.pty.pcx.dto.ecs;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@AllArgsConstructor
@Data
public class CommonCheckUpdateExpTypeDTO{
    private String sourceTypeCode;
    private String targetTypeCode;
    private BigDecimal sourceCheckAmt;
    private BigDecimal targetCheckAmt;

    public static CommonCheckUpdateExpTypeDTO of(String sourceTypeCode, String targetTypeCode, BigDecimal sourceCheckAmt, BigDecimal targetCheckAmt){
        return new CommonCheckUpdateExpTypeDTO(sourceTypeCode, targetTypeCode, sourceCheckAmt, targetCheckAmt);
    }
}
