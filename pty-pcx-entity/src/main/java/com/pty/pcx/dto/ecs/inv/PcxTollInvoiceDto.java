package com.pty.pcx.dto.ecs.inv;

import com.alibaba.fastjson.annotation.JSONField;
import com.pty.pcx.entity.bas.PcxBasExpType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

// 原始报销凭证-过路过桥费 toll
@Data
public class PcxTollInvoiceDto extends InvoiceBaseDto{

    private String time;            // 开票时间
    private String deptStation;     // 过路过桥入口站
    private String destStation;     // 过路过桥出口站
    private String vehicleType;     // 车型

    @JSONField(name = "field10")
    private String city;


    /**
     * 票据业务类型
     */
    private String bizTypeCode;
    private String bizTypeName;

    /**
     * 票对于费用类型
     */
    private List<PcxBasExpType> expTypeList;

}
