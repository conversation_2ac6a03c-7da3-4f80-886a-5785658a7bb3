package com.pty.pcx.qo.ecs;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class UpdateEcsQO extends ExpInvoiceQO{
    @NotBlank(message = "报销单id不能为空", groups = {UpdateEcsDetail.class, EcsExpView.class})
    private String billId;
    /**
     * 票据id
     */
    @NotBlank(message = "票据id不能为空", groups = {UpdateEcsDetail.class, EcsExpView.class})
    private String ecsBillId;

    /**
     * 票据id
     */
    @NotBlank(message = "票据类型不能为空", groups = {UpdateEcsDetail.class, EcsExpView.class})
    private String ecsBillType;
    /**
     * 票据编号
     */
    @NotBlank(message = "票据编号不能为空", groups = {UpdateEcsDetail.class})
    private String billNo;

    @NotNull(message = "票金额不能为空", groups = {UpdateEcsDetail.class})
    private BigDecimal billAmt;

    @NotBlank(message = "费用类型不能为空", groups = {UpdateEcsDetail.class})
    private String expenseTypeCode;

    /**
     * ecs票信息
     */
    private JSONObject ecsBillInfo;

    /**
     * 费用明细列表
     */
    @NotEmpty(message = "项目费用明细列表不能为空", groups = {UpdateEcsDetail.class})
    @Valid
    private List<EcsItemExpense> itemExpenseList;
    /**
     * 支付信息列表
     */
    @Valid
    private List<EcsSettlQO> settlList;

    @Valid
    private List<AttachRelQO> ecsBillClassRelList;

}
