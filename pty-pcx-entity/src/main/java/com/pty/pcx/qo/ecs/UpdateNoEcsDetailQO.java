package com.pty.pcx.qo.ecs;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.LinkedHashMap;
import java.util.List;

@Data
public class UpdateNoEcsDetailQO extends ExpInvoiceQO{

    @NotBlank(message = "报销单不能为空", groups = {UpdateEcsDetail.class})
    private String billId;
    private String detailId;

    @NotBlank(message = "费用类型编码不能为空", groups = {UpdateEcsDetail.class})
    private String expenseTypeCode;

    /**
     * 费用明细
     */
    @NotEmpty(message = "费用明细数据不能为空", groups = {UpdateEcsDetail.class})
    private LinkedHashMap expenseList;

    @Valid
    private List<DetailAttachRelQO> attachList;

    /**
     * 是否仅报补助
     */
    private boolean onlySubsidy;

    /**
     * 是否定额发票
     */
    private boolean quota;
}
