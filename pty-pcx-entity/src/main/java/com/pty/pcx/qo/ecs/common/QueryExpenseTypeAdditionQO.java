package com.pty.pcx.qo.ecs.common;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class QueryExpenseTypeAdditionQO {
    @NotBlank(message = "年度不能为空")
    private String fiscal;
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;
    @NotBlank(message = "单位不能为空")
    private String agyCode;
    @NotBlank(message = "费用类型不能为空")
    private String expenseTypeCode;
}
