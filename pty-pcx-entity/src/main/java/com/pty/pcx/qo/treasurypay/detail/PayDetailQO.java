package com.pty.pcx.qo.treasurypay.detail;

import com.pty.pcx.entity.bill.PcxBillBalance;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PayDetailQO implements Serializable {

    private String billId;

    private AllocateTypeEnum allocateType;

    private List<? extends PcxBillBalance> balanceList;



    public enum AllocateTypeEnum {
        /**
         * 1. 按顺序分配
         */
        ORDER,
        /**
         * 2. 按金额分配
         */
        AMOUNT,
        /**
         * 3. 手工选择
         */
        OTHER
    }
}
