package com.pty.pcx.qo.mad;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MadAgyInfoLimitQO {

    @NotBlank(message = "单位编码不能为空")
    private String agyCode;

    @NotNull(message = "单位编码不能为空")
    private Integer fiscal;

    private String atomCode;

    @NotBlank(message = "区划不能为空")
    private String mofDivCode;

    private String madName;

    private String keyword;
}
