package com.pty.pcx.qo.datascope;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
public class DataScopeQO {

    @ApiModelProperty("用户编码")
    @NotBlank(message = "用户编码不能为空")
    private String userCode;

    @ApiModelProperty("区划编码")
    @NotBlank(message = "区划编码不能为空")
    private String mofDivCode;

    @ApiModelProperty("单位")
    @NotBlank(message = "单位不能为空")
    private String agyCode;

    @ApiModelProperty("年度")
    @NotBlank(message = "年度不能为空")
    private String fiscal;

    @ApiModelProperty("租户id")
    private String tenantId;
}
