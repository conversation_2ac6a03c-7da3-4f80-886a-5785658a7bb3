package com.pty.pcx.qo.ecs;

import com.google.common.collect.Lists;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pcx.entity.bill.PcxEcsSettl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 发票dto的包装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvoiceDtoWrapper<T, E extends PcxBillExpBase, D extends PcxBillExpDetailBase> {
    //发票类型
    private String type;
    //发票dto
    private T dto;
    //对应的费用list
    private List<E> expenseList;
    //对应的费用明细list
    private List<D> expDetailList;
    //支付信息
    private List<PcxEcsSettl> settlList;
    //对应的标准明细list
    private List<PcxBillExpStandResult> standList = Lists.newArrayList();
    private PcxBasExpType matchExpType;
    //标识发票处理的结果
    private Integer flag;
    //是否跳过加工处理
    private boolean skipProcess = false;

    private int lackEmpCode;//1=缺少员工，2=只缺少员工信息


    private boolean needTripPlan;

    public void addSettl(List<PcxEcsSettl> settlList){

        if (CollectionUtils.isNotEmpty(settlList)){
            if (this.settlList == null) {
                this.settlList = new ArrayList<>();
            }
            this.settlList.addAll(settlList);
        }
    }

    public void addExpense(E e) {
        if (expenseList == null) {
            expenseList = new ArrayList<>();
        }
        expenseList.add(e);
    }

    public void addExpDetail(D d) {
        if (expDetailList == null) {
            expDetailList = new ArrayList<>();
        }
        expDetailList.add(d);
    }

    public void addStandList(List<PcxBillExpStandResult> results) {
        if (standList == null) {
            standList = new ArrayList<>();
        }
        standList.addAll(results);
    }

    public static enum InvoiceFlag {
        UN_CONFIRM(0, "未匹配"),
        CONFIRM(1, "已匹配"),
        UN_AVAILABLE(9, "不可用")
        ;
        private Integer code;
        private String name;

        InvoiceFlag(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
