package com.pty.pcx.qo.bill;


import com.pty.pcx.qo.bill.analysis.PcxAnalysisBalQO;
import com.pty.pcx.qo.bill.analysis.PcxAnalysisExpenseQO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PcxAnalysisQO {

    private List<PcxAnalysisExpenseQO> expenses;

    private List<PcxAnalysisBalQO> balanceInfo;

    private String userCode;

    private String userName;

    private String agyCode;

    private String fiscal;

    private String mofDivCode;

    private String tenantId;
}
