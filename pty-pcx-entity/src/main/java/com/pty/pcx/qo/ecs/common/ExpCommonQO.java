package com.pty.pcx.qo.ecs.common;

import com.pty.pcx.qo.ecs.*;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 发票
 */
@Data
public class ExpCommonQO {
    /**
     * ecs票id列表
     */
    @NotEmpty(message = "发票信息不能为空", groups = {AddEcs.class})
    @Valid
    private List<EscBillQO> billList;

    @NotBlank(message = "年度不能为空", groups = {StartEcs.class, AddEcs.class, DelEcs.class, UpdateEcsDetail.class})
    private String fiscal;
    @NotBlank(message = "单位不能为空", groups = {StartEcs.class, AddEcs.class, DelEcs.class, UpdateEcsDetail.class})
    private String agyCode;
    @NotBlank(message = "区划不能为空", groups = {StartEcs.class, AddEcs.class, DelEcs.class, UpdateEcsDetail.class})
    private String mofDivCode;
    private String tenantId;

    @NotBlank(message = "人员编码不能为空", groups = {StartEcs.class, AddEcs.class, DelEcs.class, UpdateEcsDetail.class})
    private String userCode; //人员
    @NotBlank(message = "人员名称不能为空", groups = {StartEcs.class, AddEcs.class, DelEcs.class, UpdateEcsDetail.class})
    private String userName;

    @Data
    public static class EscBillQO {
        @NotBlank(message = "billId不能为空", groups = {StartEcs.class})
        private String billId;
        @NotNull(message = "billAttachType不能为空", groups = {StartEcs.class})
        private Integer billAttachType;
    }
}
