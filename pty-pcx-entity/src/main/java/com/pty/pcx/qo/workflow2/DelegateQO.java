package com.pty.pcx.qo.workflow2;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.common.valid.Update;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DelegateQO extends SubmitQO{

    public interface Delegate extends Update {

    }

    @NotBlank(message = "年度", groups = {Delegate.class})
    private String fiscal;

    @NotBlank(message = "委派人员编码不能为空", groups = {Delegate.class})
    private String transferUserCode;

    /**
     * 委派人员编码集合
     */
    @JsonIgnore
    private List<String> transferUserCodes;
}
