package com.pty.pcx.qo.ecs;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DetailMountTripQO {
    @NotBlank(message = "年度不能为空")
    private String fiscal;
    @NotBlank(message = "单位不能为空")
    private String agyCode;
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;
    @NotBlank(message = "票id不能为空")
    private String billId;
    @NotBlank(message = "明细id不能为空")
    private String detailId;
    @NotBlank(message = "行程id不能为空")
    private String tripId;

    private String remark;

}
