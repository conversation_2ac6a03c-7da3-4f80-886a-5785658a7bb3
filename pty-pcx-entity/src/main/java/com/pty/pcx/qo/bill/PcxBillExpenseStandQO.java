package com.pty.pcx.qo.bill;

import com.pty.pcx.common.valid.Delete;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PcxBillExpenseStandQO {
    /**
     * 单据编码
     */
    @ApiModelProperty("单据ID")
    @NotBlank(message = "单据ID不能为空")
    private String billId;

    @ApiModelProperty("单位编码")
    @NotBlank(message = "单位编码不能为空")
    private String agyCode;

    @ApiModelProperty("年度")
    @NotBlank(message = "年度不能为空")
    private String fiscal;

    @ApiModelProperty("区划")
    @NotBlank(message = "区划不能为空")
    private String mofDivCode;

    @ApiModelProperty("租户id")
    private String tenantId;
}
