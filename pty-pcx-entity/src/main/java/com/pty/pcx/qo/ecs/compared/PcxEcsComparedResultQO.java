package com.pty.pcx.qo.ecs.compared;


import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PcxEcsComparedResultQO extends PcxEcsComparedResult {

    //当前页
    private Integer pageIndex;

    // 每页数据量
    private Integer pageSize;

    private List<String> idList;

    private String userCode;

    private String userName;
}
