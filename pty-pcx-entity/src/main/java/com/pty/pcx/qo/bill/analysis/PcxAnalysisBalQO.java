package com.pty.pcx.qo.bill.analysis;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class PcxAnalysisBalQO {

    private BigDecimal htBalanceAmt;
    private BigDecimal applyUsedAmt;
    private String departmentCode;
    private String project;
    private BigDecimal balanceAmt;
    private String buditemCode;
    private BigDecimal qmjzAmt;
    private String agy;
    private String balanceType;
    private BigDecimal loanUsedAmt;
    private String balanceNo;
    private String projectCode;
    private String isBuditem;
    private String department;
    private BigDecimal htUsedAmt;
    private String createDate;
    private String departmentName;
    private String summary;
    private BigDecimal applyBalanceAmt;
    private String mofDivCode;
    private BigDecimal financeAmt;
    private String agyCode;
    private String expecoName;
    private String buditemName;
    private BigDecimal loanAmt;
    private String fromId;
    private BigDecimal applyAmt;
    private BigDecimal totalAmt;
    private BigDecimal htAmt;
    private String agyName;
    private String expecoCode;
    private String fiscal;
    private String budBalanceId;
    private BigDecimal qczcAmt;
    private String buditem;
    private BigDecimal loanBalanceAmt;
    private String expeco;
    private String projectName;
    private String balanceId;
    private BigDecimal usedAmt;
    private BigDecimal currentUsedAmt;
}
