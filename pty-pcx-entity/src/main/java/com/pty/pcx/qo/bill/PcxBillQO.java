package com.pty.pcx.qo.bill;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.valid.BillSave;
import com.pty.pcx.common.valid.BillView;
import com.pty.pcx.common.valid.Delete;
import com.pty.pcx.common.valid.Download;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpAss;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.qo.contract.ContractVO;
import com.pty.pcx.qo.ecs.EcsExpMatchQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailSaveQO;
import com.pty.pcx.util.FundSourceFlushUtil;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * 单据QO保存更新单据
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class PcxBillQO extends PcxBaseDTO implements Serializable {
    /**
     * 单据编码
     */
    @ApiModelProperty("单据ID")
    @NotBlank(message = "单据ID不能为空", groups = {Delete.class, Download.class})
    private String billId;

    @ApiModelProperty("单据类型")
    private String billFuncCode;

    @ApiModelProperty("是否校验")
    private Integer isValidate;

    @ApiModelProperty("是否送审")
    private Integer isApprove;

    @ApiModelProperty("当送审时添加审批意见")
    private String comment;

    @ApiModelProperty("信息")
    private List<String> classifyCodes;

    @ApiModelProperty("岗位代码")
    @NotBlank(message = "岗位代码不能为空", groups = {BillSave.class, BillView.class})
    private String positionCode;

    @ApiModelProperty("事项类型代码")
    @NotBlank(message = "区划不能为空", groups = {BillSave.class})
    private String itemCode;

    @ApiModelProperty("事项类型名称")
    @NotBlank(message = "区划不能为空", groups = {BillSave.class})
    private String itemName;

    @ApiModelProperty("业务日期")
    @NotBlank(message = "区划不能为空", groups = {BillSave.class})
    private String transDate;

    @ApiModelProperty("登陆人的编码")
    private String userCode;

    @ApiModelProperty("登陆人的名称")
    private String userName;

    @ApiModelProperty("单据id集合")
    private List<String> ids;
    /**
     * 拓展信息
     */
    @ApiModelProperty("主单信息")
    private Map<String, Object> basicInfo;

    @ApiModelProperty("表单设置")
    private JSONObject billSetting;

    /**
     * 拓展信息
     */
    @ApiModelProperty("费用专属属性")
    private List<Map<String, Object>> specificity;
    /**
     * 拓展信息
     */
    @ApiModelProperty("费用明细专属属性")
    private List<Map<String, Object>> expenseDetail;

    /**
     * 拓展信息
     */
    @ApiModelProperty("费用明细专属属性")
    private EcsExpMatchQO ecsExpMatch;

    /**
     * 附件信息
     */
    @ApiModelProperty("附件信息")
    private List<PcxBillAttachRelationQO> attachList;

    /**
     * 结算信息
     */
    @ApiModelProperty("结算信息")
    private Map<String, List<PcxBillSettlementQO>> settlement;

    /**
     * 关联单据信息
     */
    @ApiModelProperty("关联单据信息")
    private List<PcxBillRelationQO> pcxBillLoanRelations;

    /**
     * 关联单据信息
     */
    @ApiModelProperty("关联计划、公函单据信息")
    private PcxBillRelationQO plan;

    /**
     * 关联借款
     */
    @ApiModelProperty("关联借款单据信息")
    private List<PcxBillRelationQO> loan;

    /**
     * 关联申请
     */
    @ApiModelProperty("关联借款单据信息")
    private PcxBillRelationQO apply;

    /**
     * 前端数据
     * 预算指标信息
     */
    @ApiModelProperty("预算指标信息")
    private List<PcxBillBalanceMergeQO> fundSource;

    /**
     * 实际使用数据预算指标信息
     */
    @ApiModelProperty("预算指标信息")
    private List<PcxBillBalanceQO> budget;

    /**
     * 单据信息
     */
    private PcxBill pcxBill = new PcxBill();
    /**
     * 费用信息
     */
    private List<PcxBillExpBase> expenseList = Lists.newArrayList();
    /**
     * 明细信息
     */
    private List<PcxBillExpDetailBase> expenseDetailList = Lists.newArrayList();

    private PayDetailSaveQO payDetail;

    @ApiModelProperty(value = "办结状态")
    private String completedStatus;

    @ApiModelProperty(value = "关联合同")
    private ContractVO contract;

    private Integer bizType;

    /**
     * 来访人员
     */
    @ApiModelProperty("来访人员")
    private List<PcxBillExpAss> visitors ;

    /**
     * 接待安排
     */
    @ApiModelProperty("接待安排")
    private List<PcxBillExpAss> arrange ;

    private String expenseCode;

    /**
     * 动态匹配对象（更新保存的时候）整个单据的时候调用
     *
     * @return
     */
    public void buildInstance() {
        PcxBill pcxBill = JSONObject.parseObject(JSONObject.toJSONString(this.basicInfo), PcxBill.class);
        if (Objects.isNull(pcxBill)) {
            pcxBill = new PcxBill();
        }

        //TODO 拓展字段置为空，目前只有分摊类型，分摊类型不需要前端save保存，如果有其他拓展属性是需要前端save的，在特殊处理
        pcxBill.setExpandJson(null);
        this.setPcxBill(pcxBill);

        // 主表
        List<PcxBillExpBase> newExpenses = getPcxBillExpBases(this);
        this.setExpenseList(newExpenses);

        // 明细表
        List<PcxBillExpDetailBase> newExpenseDetails = getPcxBillDetailExpBases(this);
        this.setExpenseDetailList(newExpenseDetails);
        if (StringUtil.isEmpty(this.getBillFuncCode()) && StringUtil.isNotEmpty(pcxBill.getBillFuncCode())) {
            this.setBillFuncCode(pcxBill.getBillFuncCode());
        }
        List<PcxBillBalanceQO> budget = getBudgets(this);
        //申请单如果存在费用 则通过费用明细构建构建费用主表以及单据主表的录入以及核定金额、
        this.buildExpBillAmt(newExpenseDetails, newExpenses, pcxBill);
        this.setBudget(budget);
    }

    /**
     * 构建费用申请单据金额
     *
     * 该方法用于计算并设置费用申请单据及其明细的金额。主要处理逻辑包括：
     * 1. 仅处理申请类型的单据
     * 2. 汇总费用明细金额到费用主表
     * 3. 汇总各费用主表金额到单据总金额
     *
     * @param newExpenseDetails 新增的费用明细列表，包含各明细的金额信息
     * @param newExpenses 新增的费用主表列表，需要更新其金额信息
     * @param pcxBill 待处理的单据对象，将更新其总金额
     */
    private void buildExpBillAmt(List<PcxBillExpDetailBase> newExpenseDetails,
                                List<PcxBillExpBase> newExpenses,
                                PcxBill pcxBill) {
        // 仅处理申请类型的单据
        if(!Objects.equals(BillFuncCodeEnum.APPLY.getCode(), this.getBillFuncCode())) return;

        // 费用明细为空时不做处理
        if (CollectionUtil.isEmpty(newExpenseDetails)) return;

        // 初始化单据总金额
        pcxBill.setInputAmt(BigDecimal.ZERO);
        pcxBill.setCheckAmt(BigDecimal.ZERO);

        // 遍历费用主表，计算每个主表对应的明细金额总和
        for (PcxBillExpBase expense : newExpenses) {
            BigDecimal inputAmt = BigDecimal.ZERO;
            BigDecimal checkAmt = BigDecimal.ZERO;

            // 汇总当前费用主表对应的所有明细金额
            for (PcxBillExpDetailBase expenseDetail : newExpenseDetails) {
                expenseDetail.setCheckAmt(expenseDetail.getInputAmt() == null ? BigDecimal.ZERO: expenseDetail.getInputAmt());
                if (Objects.equals(expense.getId(), expenseDetail.getExpenseId())) {
                    inputAmt = inputAmt.add(expenseDetail.getInputAmt() == null ? BigDecimal.ZERO: expenseDetail.getInputAmt());
                    checkAmt = checkAmt.add(expenseDetail.getCheckAmt() == null ? BigDecimal.ZERO: expenseDetail.getCheckAmt());
                    //如果是单费用则还是进行累加。汇总到一起
                }else if(newExpenses.size() == 1){
                    inputAmt = inputAmt.add(expenseDetail.getInputAmt() == null ? BigDecimal.ZERO: expenseDetail.getInputAmt());
                    checkAmt = checkAmt.add(expenseDetail.getCheckAmt() == null ? BigDecimal.ZERO: expenseDetail.getCheckAmt());
                }
            }
            if(checkAmt.compareTo(BigDecimal.ZERO) == 0){
                checkAmt = inputAmt;
            }
            expense.setCheckAmt(checkAmt);
            expense.setInputAmt(inputAmt);

            // 将当前费用主表金额累加到单据总金额
            pcxBill.setInputAmt(pcxBill.getInputAmt().add(inputAmt));
            pcxBill.setCheckAmt(pcxBill.getCheckAmt().add(checkAmt));
        }
    }


    /**
     * 获取预算指标
     *
     * @param pcxBillQO
     * @return
     */
    private List<PcxBillBalanceQO> getBudgets(PcxBillQO pcxBillQO) {
        return FundSourceFlushUtil.mergeToBalance(JSONArray.parseArray(JSONObject.toJSONString(pcxBillQO.getFundSource()), PcxBillBalanceMergeQO.class),
                pcxBillQO.getPcxBill(),
                pcxBillQO.getPositionCode());
    }

    /**
     * 对象转换
     *
     * @param bizQO
     * @return
     */
    private List<PcxBillExpBase> getPcxBillExpBases(PcxBillQO bizQO) {
        List<Map<String,Object>> expenses = bizQO.getSpecificity();
        if (CollectionUtil.isEmpty(expenses)) return Lists.newArrayList();
        List<PcxBillExpBase> newExpenses = Lists.newArrayList();
        Integer bizType = bizQO.getBizType();
        if (Objects.isNull(bizType) && Objects.nonNull(bizQO.getPcxBill().getBizType())){
            bizType = bizQO.getPcxBill().getBizType();
        }
        //TODO 暂时去掉默认差旅 bizType
        bizQO.getPcxBill().setBizType(bizType);
        for (Map<String, Object> item : expenses) {
            //根据费用转换对象
            PcxBillExpBase expBase = PcxBillExpBase.buildInstance(item, bizType);
            if (expBase != null) {
                newExpenses.add(expBase);
            }
        }
        return newExpenses;
    }

    /**
     * 对象转换
     *
     * @param bizQO
     * @return
     */
    private List<PcxBillExpDetailBase> getPcxBillDetailExpBases(PcxBillQO bizQO) {
        List<Map<String, Object>> expenses;
        if (CollectionUtil.isNotEmpty(bizQO.getSpecificity()) && Objects.nonNull(bizQO.getSpecificity().get(0).get("expenseCode")) && Objects.equals(PcxBillProcessConstant.ExpenseProcessBeanEnum.ABROAD.getCode(), bizQO.getSpecificity().get(0).get("expenseCode"))) {
            expenses = (List<Map<String, Object>>) bizQO.getSpecificity().get(0).get("pcxBillExpDetailAbroad");
        } else {
            expenses = bizQO.getExpenseDetail();
        }
        if (CollectionUtil.isEmpty(expenses)) return Lists.newArrayList();
        List<PcxBillExpDetailBase> newExpenses = Lists.newArrayList();
        expenses.forEach(item -> {
            //根据费用转换对象
            PcxBillExpDetailBase expBase = PcxBillExpDetailBase.buildInstance(item);
            if (expBase != null){
                newExpenses.add(expBase);
            }
        });
        return newExpenses;
    }
}
