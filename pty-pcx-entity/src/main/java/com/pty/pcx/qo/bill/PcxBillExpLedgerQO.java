package com.pty.pcx.qo.bill;

import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.entity.bill.PcxBill;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单据主表(PcxBill)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:02
 */
@Data
public class PcxBillExpLedgerQO extends PcxBill {

    private String itemCodeLike;

    private String transStartDate;

    private String transEndDate;

    private BigDecimal startCheckAmt;

    private BigDecimal endCheckAmt;

    private List<String> agyCodeList;

    private List<String> departmentCodeList;

    private List<String> expDepartmentCodeList;

    private List<String> ids;

    private List<String> billFuncCodes;

    private JSONObject dataScope;

    private String dataScopeCode;

    private String dataScopeUserCode;

    private String[] dataScopeDeptCodes;

    private String[] dataScopeAgyCodes;

    private String sortOrder;

    private String sortField;

    private String keyword;

    private List<String> keywordFields;

    private String voucherDateStart;

    private String voucherDateEnd;

    private String voucherWord;

    private String voucherNo;

    private String externalBillNo;

    private String paymentNo;

}
