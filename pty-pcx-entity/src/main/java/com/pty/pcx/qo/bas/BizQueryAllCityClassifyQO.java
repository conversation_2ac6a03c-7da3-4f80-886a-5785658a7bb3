package com.pty.pcx.qo.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class BizQueryAllCityClassifyQO {

    @ApiModelProperty("单位名称")
    @Column(name = "agy_code")
    private String agyCode;

    @ApiModelProperty("年度")
    @Column(name = "fiscal")
    private String fiscal;

    @ApiModelProperty("区划")
    @Column(name = "mof_div_code")
    private String mofDivCode;

//    @ApiModelProperty("是否系统预置")
//    @Column(name = "is_sys")
//    private Integer isSys;

    //1=城市分类 2=职务分类 3-城市淡旺季
    @ApiModelProperty("分类类型")
    @Column(name = "classify_type")
    private Integer classifyType;

    @ApiModelProperty("费用明细编码")
    @Column(name = "expense_type_code")
    private String expenseTypeCode;
}
