package com.pty.pcx.qo.bas;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BatchUpdateCityClassifyQO {

    @ApiModelProperty("单位名称")
    private String agyCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("区划")
    private String mofDivCode;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("用户名称")
    private String userName;

//    @ApiModelProperty("是否系统预置")
//    private Integer isSys;

    @ApiModelProperty("分类类型")
    private Integer classifyType;

    @ApiModelProperty("费用明细编码")
    private String expenseTypeCode;

    @ApiModelProperty("城市分类列表")
    private List<PcxBasCityClassifyQO> classifyList;
}
