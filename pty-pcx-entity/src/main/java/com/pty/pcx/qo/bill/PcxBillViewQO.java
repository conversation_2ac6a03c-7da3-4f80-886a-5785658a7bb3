package com.pty.pcx.qo.bill;

import com.pty.pcx.common.valid.BillView;
import com.pty.pcx.common.valid.Delete;
import com.pty.pcx.common.valid.Download;
import com.pty.pcx.dto.PcxBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 *
 * 单据QO保存更新单据
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class PcxBillViewQO extends PcxBaseDTO implements Serializable {
    /**
     * 单据编码
     */
    @ApiModelProperty("单据ID")
    @NotBlank(message = "单据ID不能为空", groups = {Delete.class, Download.class,BillView.class})
    private String billId;

    @ApiModelProperty("单据类型")
    private String billFuncCode;

    @ApiModelProperty("查看数据的块")
    private List<String> viewType;

    @ApiModelProperty("岗位代码")
    private String positionCode;
}
