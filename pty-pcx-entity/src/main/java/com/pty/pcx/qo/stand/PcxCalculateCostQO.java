package com.pty.pcx.qo.stand;

import lombok.Data;

import java.util.List;

@Data
public class PcxCalculateCostQO {

    private String billId;
    private String fiscal;
    private String agyCode;
    private String mofDivCode;
    private String tenantId;
    /**
     * 事项编码
     */
    private String itemCode;
    private String itemName;

    private String userCode;
    private String userName;

    /**
     *费用类型编码
     */
    private String expenseCode;

    /**
     *费用明细编码集合
     */
    private List<String> expDetailCodes;

    /**
     * 是否省内
     */
    private Integer isInProvince;

    /**
     *会议、培训类别编码
     */
    private String typeCode;

    /**
     *会议、培训开始时间
     */
    private String startTime;

    /**
     *会议、培训结束时间
     */
    private String finishTime;

    /**
     *培训开始时间
     */
    private String startDate;

    /**
     *培训结束时间
     */
    private String finishDate;

    /**
     * 会议形式
     */
    private String meetingFormatCode;

    /**
     * 外地代表
     */
    private Integer outsideNum;

    /**
     * 参会、参培、参与招待 人数
     */
    private Integer peopleNum;

    /**
     * 工作人员 人数
     */
    private Integer workerNum;

    /**
     * 参会、参培天数
     */
    private Integer duration;

    /**
     *会议、培训地点编码
     */
    private String regionCode;
    /**
     *会议、培训地点名称
     */
    private String regionName;

    /**
     * 招待费来访人数
     */
    private Integer visitPeopleNum;

    /**
     * 陪同人数
     */
    private Integer peerStaffNum;



}
