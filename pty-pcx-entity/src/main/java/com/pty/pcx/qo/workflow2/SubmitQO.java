package com.pty.pcx.qo.workflow2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SubmitQO extends ProcessQO{

    // 后加签人
    @ApiModelProperty("加签人")
    private String afterApproveEmployeeCode;

    @ApiModelProperty("审批意见")
    private String comment;

    @ApiModelProperty("单据id集合")
    private List<String> billIds;
}
