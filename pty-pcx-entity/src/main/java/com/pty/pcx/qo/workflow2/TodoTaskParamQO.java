package com.pty.pcx.qo.workflow2;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TodoTaskParamQO implements Serializable {


    // 业务维度属性
    protected String tenantId;
    protected String mofDivCode;
    protected String agyCode;
    protected String busiModule;
    protected String busiType;
    protected String busiId;
    // 流程的创建人
    protected String creator;
    protected List<String> creators;
    // 流程的待办人
    protected String candidateUser;
    protected Set<String> candidateUsers;
}
