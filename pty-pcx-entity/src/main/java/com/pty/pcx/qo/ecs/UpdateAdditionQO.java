package com.pty.pcx.qo.ecs;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 更新明细补充信息
 */
@Data
public class UpdateAdditionQO {

    @NotBlank(message = "报销单id不能为空")
    private String billId;
    @NotEmpty(message = "费用明细id不能为空")
    private List<String> detailIds;
    @NotBlank(message = "费用承担部门不能为空")
    private String departmentCode;

    private String departmentName;

    private String budGetItemCode;

    private String budGetItem;

    private String accountingExpenseItemCode;

    private String accountingExpenseItem;

    private String customerCode;

    private String customer;

    private String biIncomeTypeCode;

    private String biIncomeType;

    private String accountingIncomeTypeCode;

    private String accountingIncomeType;

    private String tenderingProCode;

    private String tenderingPro;

    private String devProCode;

    private String devPro;

    private String a8PpProCode;

    private String a8PpPro;

    private String deferredAssetsCode;

    private String deferredAssets;
}
