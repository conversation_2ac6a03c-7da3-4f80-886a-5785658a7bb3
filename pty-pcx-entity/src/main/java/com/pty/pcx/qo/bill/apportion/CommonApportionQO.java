package com.pty.pcx.qo.bill.apportion;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CommonApportionQO {

    private String id;

    @NotEmpty(message = "分摊部门信息不能为空")
    @Valid
    private List<CommonApportionDeptQO> deptList;
    private String expenseTypeCode;
    private String expenseTypeName;
    @NotNull(message = "分摊金额不能为空")
    private BigDecimal apportionAmt;

}
