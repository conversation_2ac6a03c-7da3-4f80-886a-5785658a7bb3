package com.pty.pcx.qo.wit;

import lombok.Data;

import java.io.Serializable;

/**
 * 稽核规则关联附件
 * <AUTHOR>
 * @date 2025/03/12
 */
@Data
public class WitCreateRuleQO implements Serializable {

    /**
     * 稽核结果id
     */
    private String ruleDetailId;

    /**
     * 单据信息
     */
    private String billId;

    /**
     * 稽核锚点
     */
    private String classifyCode;
    private String classifyName;
    /**
     * 行id
     */
    private String rowId;
    /**
     * 字段值
     */
    private String fieldValue;
    private String fieldName;
    private String fieldTitle;
    /**
     * block code
     */
    private String blockCode;
    private String blockName;
    private String blockTitle;

    /**
     * 错误信息
     */
    private String errMsg;

}
