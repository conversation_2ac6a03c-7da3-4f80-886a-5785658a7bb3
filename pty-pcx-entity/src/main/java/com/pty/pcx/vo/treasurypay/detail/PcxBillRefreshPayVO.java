package com.pty.pcx.vo.treasurypay.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcxBillRefreshPayVO implements Serializable {


    private static final long serialVersionUID = 1L;
    @ApiModelProperty("单据id")
    private String billId;
    @ApiModelProperty("支付申请单号")
    private String payNo;
    @ApiModelProperty("支付状态code")
    private String payStatusCode;
    @ApiModelProperty("支付状态name")
    private String payStatusName;
}
