package com.pty.pcx.vo.bill;

import com.pty.pcx.vo.treasurypay.detail.PaySettlementBalanceVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@SuperBuilder
public class PcxBillPayDetailSummaryVO implements Serializable {

    @ApiModelProperty("总金额")
    private BigDecimal totalAmt;


    @ApiModelProperty("支付明细")
    List<PaySettlementBalanceVO> payDetailList;
}
