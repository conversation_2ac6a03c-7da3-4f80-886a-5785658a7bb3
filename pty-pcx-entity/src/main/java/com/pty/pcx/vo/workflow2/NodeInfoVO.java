package com.pty.pcx.vo.workflow2;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class NodeInfoVO implements Serializable {

    private String nodeId;
    private String nodeName;
    private List<String> userCodes;
}
