package com.pty.pcx.vo.positionblock;

import com.pty.pcx.dto.block.BlockProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class BlockPropertyVO {
    private String fieldLabel;

    private String fieldValue;

    private String fieldTitle;

    private Integer isRequired;

    private Integer isEdit;

    private String fieldName;

    private String showType;

    private String notes;

    private String remarks;

    private String editorCode;

    private String dataTypeCode;

    private String dataClassifyCode;

    private String dataSourceCode;

    private String isAddition;

    //通用报销，补充信息，核算项目，费用项目默认值使用，其他场景没有赋值
    private String defVal = "";

    //费用类型为 因公出国境任务并且专属属性中存在特定的（人民币预算、外币预算）时才存在值
    private Map<String, List<BlockProperty>>  fieldInfo;
}
