package com.pty.pcx.vo.bill.apportion;

import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TravelAmtApportionListVO {

    @ApiModelProperty("分摊列表")
    private List<AmtApportionVO> apportionList = new ArrayList<>();

    @ApiModelProperty("行程列表")
    private List<TripVO> tripList = new ArrayList<>();

    @ApiModelProperty("补充信息元素列表")
    private List<BlockPropertyVO> blockPropertyList;

    @ApiModelProperty("部门信心")
    private List<String> apportionDeptList;
}
