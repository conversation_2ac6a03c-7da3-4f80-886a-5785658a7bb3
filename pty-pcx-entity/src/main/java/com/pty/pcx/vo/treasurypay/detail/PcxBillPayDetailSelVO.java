package com.pty.pcx.vo.treasurypay.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class PcxBillPayDetailSelVO {

    @ApiModelProperty("单位code")
    private String agyCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("区划")
    private String mofDivCode;

    @ApiModelProperty("主单据ID")
    private String billId;

    @ApiModelProperty("支付单ID")
    private String payNo;

    @ApiModelProperty("付款人全称")
    private String payAccountName;

    @ApiModelProperty("付款人开户行")
    private String payBankName;

    @ApiModelProperty("付款人账号")
    private String payAccountNo;

    @ApiModelProperty("付款人账号类型")
    private String payAccountType;

    @ApiModelProperty("收款人全称")
    private String payeeAccountName;

    @ApiModelProperty("收款人开户行")
    private String payeeBankName;

    @ApiModelProperty("收款人账号")
    private String payeeAccountNo;

    @ApiModelProperty("付款金额")
    private BigDecimal checkAmt;

    @ApiModelProperty("付款方式code")
    private String payTypeCode;

    @ApiModelProperty("付款方式name")
    private String payTypeName;

    @ApiModelProperty("支付日期")
    private String payDate;

    @ApiModelProperty("预算指标No")
    private String balanceNo;

    @ApiModelProperty("政府经济分类code")
    private String govexpecoCode;

    @ApiModelProperty("政府经济分类name")
    private String govexpecoName;

    @ApiModelProperty("部门经济分类code")
    private String expecoCode;

    @ApiModelProperty("部门经济分类name")
    private String expecoName;

    @ApiModelProperty("资金用途")
    private String amtUseWay;

    @ApiModelProperty("制单人ID")
    private String billCreator;

    @ApiModelProperty("制单人name")
    private String billCreatorName;

    @ApiModelProperty("是否批量支付")
    private Integer isBatchPay;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("来源单据")
    private String billNo;

    @ApiModelProperty("银行回单")
    private String bankReceipt;

    @ApiModelProperty("支付凭证")
    private String payVoucher;
}
