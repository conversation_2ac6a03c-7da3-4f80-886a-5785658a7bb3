package com.pty.pcx.vo.bill.apportion;

import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CommonAmtApportionListVO {

    @ApiModelProperty("分摊列表")
    private List<AmtApportionVO> apportionList = new ArrayList<>();

    @ApiModelProperty("补充信息列表")
    Map<String, List<BlockPropertyVO>> blockPropertyMap = new HashMap<>();

    @ApiModelProperty("分摊类型")
    private Integer apportionType;

    @ApiModelProperty("部门信心")
    private List<String> apportionDeptList;

}
