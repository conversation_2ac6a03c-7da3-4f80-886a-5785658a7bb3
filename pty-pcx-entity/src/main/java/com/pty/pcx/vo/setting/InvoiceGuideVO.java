package com.pty.pcx.vo.setting;

import com.pty.pcx.entity.setting.PaFieldSetting;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class InvoiceGuideVO {
    @ApiModelProperty("单位")
    private String agyCode;

    @ApiModelProperty("年度")
    private String fiscal;

    @ApiModelProperty("区划编码")
    private String mofDivCode;

    @ApiModelProperty("应用编码")
    private String columnCode;

    @ApiModelProperty("应用名称")
    private String columnName;

    @ApiModelProperty("应用图标")
    private String extField01;

    @ApiModelProperty("应用引导页")
    private String extField02;

    @ApiModelProperty("1：app 2：小程序")
    private String extField03;

    @ApiModelProperty("引导页跳转标题")
    private String extField04;

    @ApiModelProperty("引导页跳转地址")
    private String extField05;
}
