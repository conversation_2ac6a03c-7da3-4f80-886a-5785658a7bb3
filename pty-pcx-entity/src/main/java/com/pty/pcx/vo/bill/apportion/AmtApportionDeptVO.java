package com.pty.pcx.vo.bill.apportion;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AmtApportionDeptVO {

    private String id;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tripSegment;

    @ApiModelProperty("比例")
    private BigDecimal departmentRate;
    @ApiModelProperty("金额")
    private BigDecimal departmentAmt;
    @ApiModelProperty("承担部门编码")
    private String departmentCode;
    @ApiModelProperty("承担部门名称")
    private String departmentName;

    @ApiModelProperty("费用类型编码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String expenseTypeCode;
    @ApiModelProperty("费用类型名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String expenseTypeName;

    @ApiModelProperty("自定义01字段code")
    private String acitem01Code;

    @ApiModelProperty("自定义01字段name")
    private String acitem01Name;

    @ApiModelProperty("自定义02字段code")
    private String acitem02Code;

    @ApiModelProperty("自定义02字段name")
    private String acitem02Name;

    @ApiModelProperty("自定义03字段code")
    private String acitem03Code;

    @ApiModelProperty("自定义03字段name")
    private String acitem03Name;

    @ApiModelProperty("自定义04字段code")
    private String acitem04Code;

    @ApiModelProperty("自定义04字段name")
    private String acitem04Name;

    @ApiModelProperty("自定义05字段code")
    private String acitem05Code;

    @ApiModelProperty("自定义05字段name")
    private String acitem05Name;

    @ApiModelProperty("拓展元素6")
    private String acitem06Code;

    @ApiModelProperty("拓展元素6")
    private String acitem06Name;

    @ApiModelProperty("拓展元素7")
    private String acitem07Code;

    @ApiModelProperty("拓展元素7")
    private String acitem07Name;

    @ApiModelProperty("拓展元素8")
    private String acitem08Code;

    @ApiModelProperty("拓展元素8")
    private String acitem08Name;

    @ApiModelProperty("拓展元素9")
    private String acitem09Code;

    @ApiModelProperty("拓展元素9")
    private String acitem09Name;

    @ApiModelProperty("拓展元素10")
    private String acitem10Code;

    @ApiModelProperty("拓展元素10")
    private String acitem10Name;
}
