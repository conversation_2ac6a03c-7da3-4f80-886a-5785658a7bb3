package com.pty.pcx.vo.ecs;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 票信息vo
 */
@Data
public class EcsExpenseVO {

    //费用id
    private String expenseId;

    public EcsExpenseVO() {
        standResult = new PcxBillExpStandResult();
    }

    //明细id
    private String detailId;
    /**
     * 票据id
     */
    private String ecsBillId;

    /**
     * 费用类型编码
     */
    private String expenseTypeCode;
    /**
     * 费用类型名称
     */
    private String expenseTypeName;

    private String displayBizTypeName;
    private Integer isValid;
    private Integer relFileHotelCnt;
    private Integer relFileTaxiCnt;
    private Integer relOtherFileCnt;

    /**
     * 票据类型 报销通过ecs票类型，费用类型进行转换
     * 火车票，飞机票，住宿票
     */
    private String ecsBillType;

    private String ecsBillNo;

    private String ecsBillDate;

    private String startTime;
    private String finishTime;

    /**
     * 票价
     */
    private BigDecimal ticketAmt;

    private BigDecimal inputAmt;

    private BigDecimal checkAmt;

    /** 票里面项目名称*/
    private String itemName;

    /**
     * 人员
     */
    private String empName;
    @JsonIgnore
    private String empTempName;
    @JsonIgnore
    private String empCode;

    //
    private String remark;


    /**
     * ecs       票生成
     * replenish 系统补充
     * manual    手动添加
     */
    private String source;

    //无票原因
    private String noEcsReason;
    private String noEcsReasonName;

    /**
     * 城市间交通费的话是时间 |坐席
     * 其他费用的话是开票时间
     */
    private String transNo;

    private int tripFlag = 0;

    @JsonIgnore
    private String cityCode;
    @JsonIgnore
    private String cityName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public String getStandValue() {
        return standResult.getStandValue();
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public String getStandValueName() {
        return standResult.getStandValueName();
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public String getStandCode() {
        return standResult.getStandCode();
    }
    @JsonIgnore
    public String getStandOverReason() {
        return standResult.getReason();
    }
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public String getStandOverHandType(){
        return standResult.getHandleType();
    }

    @JsonIgnore
    private PcxBillExpStandResult standResult;
    @JsonIgnore
    private String tripId;
    @JsonIgnore
    private Integer tripSeq;
    @JsonIgnore
    private String changeDetailId;

    @JsonIgnore
    private String sortDate;

    @JsonIgnore
    private String finishDate;


    private String detailJson;

    private List<PcxAttachRelVO> attachRelList;

    @JsonIgnore
    private String startCityCode;
    @JsonIgnore
    private String startCity;
    @JsonIgnore
    private String endCityCode;
    @JsonIgnore
    private String endCity;
    @JsonIgnore
    private String startPlace;
    @JsonIgnore
    private String endPlace;

    /**
     * 坐席
     */
    private String inputSeatLevel;

    /**
     * 车型
     */
    private String carType;
    /**
     * 里程
     */
    private String mileage;

    /**
     * 未匹配的票提示信息
     */
    private String hintMessage;

    /**
     * 当前票关联的id集合，网约车，住宿费一个票会有多个明细
     */
    @JsonIgnore
    private Set<String> brotherDetails = new HashSet<>();
    //属于我自己的明细id集合
    @JsonIgnore
    private Set<String> myDetailIds = new HashSet<>();
    //属于下一节点的明细id集合
    @JsonIgnore
    private Set<String> nextDetailIds = new HashSet<>();
    /**
     * 行程包括的费用明细id集合，审核岗按行程查看费用明细使用
     */
    @JsonIgnore
    private List<String> tripDetailIds = new ArrayList<>();

    @JsonIgnore
    private String mountTrip;

    /**
     * 票关联的明细id
     */
    private String ecsRelId;

    /**
     * 发票说明
     */
    private String ecsBillDesc;
}
