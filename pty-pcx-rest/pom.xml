<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pty.pcx</groupId>
        <artifactId>pty-pcx</artifactId>
        <version>4.0.1.238-ENT-SNAPSHOT</version>
    </parent>

    <artifactId>pty-pcx-rest</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.apache.servicecomb</groupId>
            <artifactId>provider-rest-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pcx</groupId>
            <artifactId>pty-pcx-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pub</groupId>
            <artifactId>pty-pub-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.license</groupId>
            <artifactId>pty-license-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.security</groupId>
            <artifactId>pty-security-log</artifactId>
            <version>${security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.setting</groupId>
            <artifactId>pty-setting-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.billsetting</groupId>
            <artifactId>pty-billsetting-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.mad</groupId>
            <artifactId>pty-mad-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.balance</groupId>
            <artifactId>pty-balance-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.workflow2</groupId>
            <artifactId>pty-workflow2-extend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.ep</groupId>
            <artifactId>pty-ep-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pty.pct</groupId>
            <artifactId>pty-pct-api</artifactId>
            <version>${pct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pty.portal</groupId>
            <artifactId>pty-portal-api</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>