package com.pty.pcx.controller.setting;


import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Indexed
@RequestMapping(value = "/pcx/businessRule")
@Api(value = "业务规则设置表接口", tags = {"业务规则设置表控制层"})
@Slf4j
@RestSchema(schemaId = "/businessRuleOptionController")
@RestController
public class BusinessRuleOptionController {

    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;

    @PostMapping(value = "/pageList")
    @ApiOperation(value = "业务规则分页查询")
    public Response<?> selectPage(@RequestBody PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.selectWithPage(paOptionQO);
        }catch (Exception e){
            log.error("分页查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/getAll")
    @ApiOperation(value = "查询所有的业务规则设置")
    public Response<?> getAll(@RequestBody PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.getAll(paOptionQO);
        }catch (Exception e){
            log.error("查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/getRuleMap")
    @ApiOperation(value = "获取所有业务的集合，key为业务编码，value为optValue")
    public Response<?> getRuleMap(@RequestBody PaOptionQO paOptionQO){
        try{
            CheckMsg<?> ruleMap = businessRuleOptionService.getRuleMap(paOptionQO);
            if(!ruleMap.isSuccess()){
                return Response.fail().setMsg(ruleMap.getMsgInfo());
            }
            return Response.success(ruleMap.getData());
        }catch (Exception e){
            log.error("查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/getGroupList")
    @ApiOperation(value = "查询分组")
    public Response<?> getGroupList(@RequestBody PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.getGroupList(paOptionQO);
        }catch (Exception e){
            log.error("查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/selectById")
    @ApiOperation(value = "通过主键查询单条数据")
    public Response<?> selectById(@RequestBody PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.selectById(paOptionQO);
        }catch (Exception e){
            log.error("查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/remove")
    @ApiOperation(value = "删除业务规则")
    public Response<?> remove(@RequestBody  PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.remove(paOptionQO);
        }catch (Exception e){
            log.error("删除报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/updateById")
    @ApiOperation(value = "修改业务规则")
    public Response<?> updateById(@RequestBody  PaOptionQO paOptionQO) {
        try{
            return businessRuleOptionService.updateById(paOptionQO);
        }catch (Exception e){
            log.error("修改报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }
}
