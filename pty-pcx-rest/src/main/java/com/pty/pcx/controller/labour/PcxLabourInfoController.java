package com.pty.pcx.controller.labour;


import com.pty.pcx.api.labour.ILabourInfoService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.PcxExportQo;
import com.pty.pcx.qo.labour.PcxLabourInfoQO;
import com.pty.pcx.vo.ExcelExportVo;
import com.pty.pub.common.anno.IgnoreAuthe;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.HashMap;
import java.util.List;

@Api(value = "劳务信息接口", tags = "劳务信息api")
@RestSchema(schemaId = "labourInfoController")
@RequestMapping("/pcx/labourInfo")
@Slf4j
@Indexed
public class PcxLabourInfoController {

  @Autowired
  private ILabourInfoService labourInfoService;
  @Autowired
  private CacheManager cacheManager;

  private final String ehcacheExcelName = "excel";


  @PostMapping(path = "/pageList")
  @ApiOperation(value = "获取列表数据接口")
  public Response list(@RequestBody PcxLabourInfoQO pcxLabourInfoQO) {
    try {
      return Response.success().setData(labourInfoService.listData(pcxLabourInfoQO));
    } catch (Exception e) {
      e.printStackTrace();
      return Response.fail().setMsg("查询失败：" + e.getMessage());
    }
  }

  @PostMapping(path = "/save")
  @ApiOperation("新增辅助明细")
  public Response save(@RequestBody PcxLabourInfoQO pcxLabourInfoQO) {
    CheckMsg msg = labourInfoService.insert(pcxLabourInfoQO);
    if (msg.isSuccess()) {
      return Response.success().setMsg("新增成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }

  @PostMapping(path = "/modify")
  @ApiOperation("修改保存")
  public Response modify(@RequestBody PcxLabourInfoQO pcxLabourInfoQO) {
    CheckMsg msg = labourInfoService.update(pcxLabourInfoQO);
    if (msg.isSuccess()) {
      return Response.success().setMsg("修改成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }

  @PostMapping(path = "/updateByIdCard")
  @ApiOperation("通过身份证号修改姓名、户名、开户行、帐号")
  public Response updateByIdCard(@RequestBody List<PcxLabourInfoQO> labourInfos) {
    CheckMsg msg = labourInfoService.updateByIdCard(labourInfos);
    if (msg.isSuccess()) {
      return Response.success().setMsg("修改成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }

  @PostMapping(path = "/removeByIds")
  @ApiOperation("删除接口")
  public Response remove(@RequestBody List<String> ids) {
    CheckMsg msg = labourInfoService.delByIds(ids);
    if (msg.isSuccess()) {
      return Response.success().setMsg("删除成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }

  @PostMapping(path = "/enabled")
  @ApiOperation("启用接口")
  public Response enabled(@RequestBody List<String> ids) {
    CheckMsg msg = labourInfoService.updateStatusByIds(ids, PubConstant.LOGIC_TRUE);
    if (msg.isSuccess()) {
      return Response.success().setMsg("启用成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }

  @PostMapping(path = "/noEnabled")
  @ApiOperation("停用接口")
  public Response noEnabled(@RequestBody List<String> ids) {
    CheckMsg msg = labourInfoService.updateStatusByIds(ids, PubConstant.LOGIC_FALSE);
    if (msg.isSuccess()) {
      return Response.success().setMsg("停用成功");
    } else {
      return Response.fail().setMsg(msg.getMsgInfo());
    }
  }


//缓存  表头数据
  @ApiOperation(value = "缓存请求参数数据", notes = "缓存导出excel请求参数数据")
  @PostMapping(value = "/excelParams")
  public Response saveExcelParams(@RequestBody HashMap<String, Object> params) {
    String id = StringUtil.getUUID();
    cacheManager.getCache(ehcacheExcelName).put(id, params);
    return Response.success().setData(id);
  }

  //表头下载
  @GetMapping(path = "/excelExport/{id}")
  @ApiOperation(value = "表头下载")
  @IgnoreAuthe
  @ApiResponses({
      @ApiResponse(code = 200, response = File.class, message = "")
  })
  @SuppressWarnings("resource")
  public ResponseEntity<byte[]> ExcelExport(@PathVariable("id") String id) {
    HashMap<String, Object> params = (HashMap) cacheManager.getCache(ehcacheExcelName).get(id).get();
    List list = (List<ExcelExportVo>) params.get("excelExportVoList");

    String userCode = StringUtil.nullToEmpty(params.get("userCode"));
    String agyCode = StringUtil.nullToEmpty(params.get("agyCode"));
    String mofDivCode = StringUtil.nullToEmpty(params.get("mofDivCode"));
    String fiscal = StringUtil.nullToEmpty(params.get("fiscal"));
    if (null != params) {
      PcxExportQo exportQo = labourInfoService.excelExport(list, userCode, agyCode, fiscal,mofDivCode);
      return ResponseEntity.ok()
          .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+exportQo.getFileName())
          .body(exportQo.getBytes());
    }
    throw new CommonException("入参为空");

  }

  @PostMapping(path = "/uploadExcel")
  @ApiOperation(value = "导入excel")
  public Response uploadExcel(@RequestParam("file") MultipartFile file, PcxLabourInfoQO pcxLabourInfoQO) {
    CheckMsg check = labourInfoService.uploadExcel(file, pcxLabourInfoQO);
    if (check.isSuccess()) {
      return Response.success().setMsg(check.getMsgInfo());
    } else {
      return Response.fail().setMsg(check.getMsgInfo());
    }
  }

  //缓存导出劳务人员信息请求参数数据
  @ApiOperation(value = "缓存导出劳务人员信息请求参数数据", notes = "缓存导出劳务人员信息请求参数数据")
  @PostMapping(value = "/excelLabourInfoParams")
  public Response excelLabourInfoParams(@RequestBody PcxLabourInfoQO dto) {
    String id = StringUtil.getUUID();
    cacheManager.getCache(ehcacheExcelName).put(id, dto);
    return Response.success().setData(id);
  }

  @GetMapping(path = "/exportLabourInfoExcel/{id}")
  @ApiOperation(value = "导出劳务人员信息")
  @IgnoreAuthe
  @ApiResponses({
      @ApiResponse(code = 200, response = File.class, message = "")
  })
  public ResponseEntity<byte[]>  exportLabourInfoExcel(@PathVariable("id") String id) {
    PcxLabourInfoQO dto =cacheManager.getCache(ehcacheExcelName).get(id,PcxLabourInfoQO.class);
    if(dto!=null ){
      PcxExportQo exportQo = labourInfoService.exportLabourInfoExcel(dto);
      return ResponseEntity.ok()
          .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename="+exportQo.getFileName())
          .body(exportQo.getBytes());
    }
    throw new CommonException("入参为空");
  }
}
