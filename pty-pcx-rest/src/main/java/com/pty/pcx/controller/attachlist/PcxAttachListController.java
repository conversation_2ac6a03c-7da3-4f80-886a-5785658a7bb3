package com.pty.pcx.controller.attachlist;

import com.pty.pcx.api.attachlist.IPcxAttachListService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.valid.Delete;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.qo.attachlist.PcxAttachListQO;
import com.pty.pcx.qo.attachlist.PcxAttachListRelationQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: PcxAttachListController
 * @Description: 附件清单接口
 * @Date: 2024/11/6  下午8:14
 * @Author: wangbao
 **/
@Api(value = "附件清单接口", tags = {"附件清单接口"})
@RestSchema(schemaId = "pcxAttachListController")
@RequestMapping("/pcx/attachList")
@Indexed
@Slf4j
@RestController
public class PcxAttachListController {

    @Autowired
    private IPcxAttachListService pcxAttachListService;

    /**
     * 保存附件清单：新增或修改
     */
    @PostMapping(path = "/saveOrUpdate")
    @ApiOperation(value = "新增或修改附件清单")
    public Response saveOrUpdate(@RequestBody List<PcxAttachListQO> paAttachListQOList) {
        try {
            CheckMsg checkMsg = pcxAttachListService.saveOrUpdate(paAttachListQOList);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setMsg("附件清单保存成功");
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 删除附件清单：如果附件清单关联数量大于0，删除失败，返回false；否则删除成功，返回true
     */
    @PostMapping(path = "/deleteById")
    @ApiOperation(value = "删除未使用的附件清单")
    public Response deleteById(@RequestBody @Validated(Delete.class) PcxAttachListQO paAttachListQO) {
        try {
            CheckMsg checkMsg = pcxAttachListService.deleteByAttachListId(paAttachListQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo()).setData(checkMsg.getData());
            }
            return Response.success().setMsg(checkMsg.getMsgInfo());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 查询附件清单：以功能类型、功能编码为主要线索
     */
    @PostMapping(path = "/selectByFuncCode")
    @ApiOperation(value = "通过单据类型查询附件清单")
    public Response selectByFuncCode(@RequestBody @Validated(Query.class) PcxAttachListQO paAttachListQO) {
        try {
            CheckMsg checkMsg = pcxAttachListService.selectByFuncCode(paAttachListQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setMsg("附件清单查询成功").setData(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }



    /**
     * 保存单据关联购汇单附件关系
     * @param relationQoList 关联关系列表
     * @return 操作结果
     */
    @PostMapping(value = "/saveForexReceiptRel")
    @ApiOperation(value = "保存单据关联购汇单附件关系")
    public Response<?> saveExchangeRelation(@RequestBody @Valid List<PcxAttachListRelationQO> relationQoList) {
        try {
            CheckMsg<?> voidCheckMsg = pcxAttachListService.saveForexReceiptRel(relationQoList);
            if (!voidCheckMsg.isSuccess()) {
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (Exception e) {
            log.error("保存购汇单单据关联关系异常 req {}", relationQoList, e);
            return Response.commonResponse(Response.FAIL_CODE, "保存购汇单单据关联关系异常");
        }
    }

    /**
     * 删除保存单据关联购汇单附件关系
     * @param relationQoList 关联关系列表
     * @return 操作结果
     */
    @PostMapping(value = "/deleteForexReceiptRel")
    @ApiOperation(value = "删除保存单据关联购汇单附件关系")
    public Response<?> deleteExchangeRelation(@RequestBody @Valid List<PcxAttachListRelationQO> relationQoList) {
        try {
            CheckMsg<?> voidCheckMsg = pcxAttachListService.deleteForexReceiptRel(relationQoList);
            if (!voidCheckMsg.isSuccess()) {
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (Exception e) {
            log.error("删除购汇单单据关联关系异常 req {}", relationQoList, e);
            return Response.commonResponse(Response.FAIL_CODE, "删除购汇单单据关联关系异常");
        }
    }

}
