package com.pty.pcx.controller.bill;

import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.ecs.ChangeItemQO;
import com.pty.pcx.qo.ecs.DetailMountTripQO;
import com.pty.pcx.qo.ecs.QueryEcsModifyFieldQO;
import com.pty.pcx.qo.ecs.QueryNoEcsDetailQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Api(value = "查询电子凭证接口", tags = {"查询电子凭证接口"})
@RestSchema(schemaId = "PcxBillEcsController")
@RequestMapping("/pcx/ecs")
@RestController
@Indexed
@Slf4j
public class PcxBillEcsController {

    @Resource
    private EcsProcessService ecsProcessService;

    @PostMapping(value = "/queryEcsModifyField")
    @ApiOperation(value = "查询票修改的属性")
    public Response queryEcsModifyField(@RequestBody @Valid QueryEcsModifyFieldQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsProcessService.queryEcsModifyField(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询票修改的属性异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询票修改的属性异常");
        }
    }

    @PostMapping(value = "/queryNoEcsDetail")
    @ApiOperation(value = "查询无票明细")
    public Response queryNoEcsDetail(@RequestBody @Valid QueryNoEcsDetailQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsProcessService.queryNoEcsDetail(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询无票明细异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询无票明细异常");
        }
    }

    @PostMapping(value = "/detailMountTrip")
    @ApiOperation(value = "行程外的票挂载行程")
    public Response detailMountTrip(@RequestBody @Valid DetailMountTripQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsProcessService.detailMountTrip(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("行程外的票挂载行程异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "行程外的票挂载行程异常");
        }
    }

    @PostMapping(value = "/changeItem")
    @ApiOperation(value = "修改事项")
    public Response changeItem(@RequestBody @Valid ChangeItemQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsProcessService.changeItem(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("修改事项异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "修改事项异常");
        }
    }
}
