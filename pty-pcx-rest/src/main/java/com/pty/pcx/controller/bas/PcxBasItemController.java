package com.pty.pcx.controller.bas;

import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.ecs.EcsItemService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.ecs.QueryAllowChangeItemListQO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.bas.BasItemVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * (PcxBasItem)表控制层
 * <AUTHOR>
 * @since 2024-10-24 22:05:43
 */
@Indexed
@RequestMapping(value = "/pcx/pcxBasItem")
@Api(value = "事项类型接口", tags = {"(PcxBasItem)表控制层"})
@Slf4j
@RestSchema(schemaId = "/pcxBasItemController")
@RestController
public class PcxBasItemController {

    @Autowired
    private IPcxBasItemService pcxBasItemService;
    @Resource
    private EcsItemService ecsItemService;

    @PostMapping(value = "/pageList")
    @ApiOperation(value = "分页查询")
    public Response<?> selectPage(@RequestBody PcxBasItemQO pcxBasItemQO) {
        try{
            return pcxBasItemService.selectWithPage(pcxBasItemQO);
        }catch (Exception e){
            log.error("分页查询报错：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/selectById")
    @ApiOperation(value = "通过主键查询单条数据")
    public Response<?> selectById(@RequestBody @Validated(Query.class)  PcxBasItemQO pcxBasItemQO) {
        try {
            if(StringUtil.isEmpty(pcxBasItemQO.getId())){
                return Response.fail().setMsg("请选择需要查询的事项类型");
            }
            PcxBasItemVO pcxBasItemVO = pcxBasItemService.selectById(pcxBasItemQO);
            return Response.success().setData(pcxBasItemVO);
        } catch (Exception e) {
            log.error("查询单条数据失败：{}", e.getMessage(),e);
            return Response.fail().setCode(Response.FAIL_CODE).setMsg("事项类型启用失败");
        }
    }

    @PostMapping(path = "/enable")
    @ApiOperation("事项类型启用")
    public Response<?> enable(@RequestBody PcxBasItemQO pcxBasItemQO) {
        Response<?> res = Response.commonResponse(Response.SUCCESS_CODE,"事项类型启用成功");
        try {
            res = pcxBasItemService.disEnableOrEnableByQO(pcxBasItemQO, PubConstant.LOGIC_TRUE);
        } catch (Exception e) {
            log.error("事项类型启用失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("事项类型启用失败");
        }
        return res;
    }

    @PostMapping(path = "/disEnable")
    @ApiOperation("事项类型停用")
    public Response<?> disEnable(@RequestBody PcxBasItemQO pcxBasItemQO) {
        Response<?> res = Response.commonResponse(Response.SUCCESS_CODE,"事项类停用成功");
        try {
            res =  pcxBasItemService.disEnableOrEnableByQO(pcxBasItemQO, PubConstant.LOGIC_FALSE);
        } catch (Exception e) {
            log.error("事项类型停用失败：{}", e.getMessage(),e);
            res.setCode(Response.FAIL_CODE).setMsg("事项类型停用失败");
        }
        return res;
    }

    @PostMapping(path = "/save")
    @ApiOperation("新增事项类型")
    public Response<?> save(@RequestBody PcxBasItemQO pcxBasItem) {
        try {
           return pcxBasItemService.save(pcxBasItem);
        }catch (Exception e){
            log.error("新增失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/update")
    @ApiOperation("修改事项类型")
    public Response<?> update(@RequestBody PcxBasItemQO pcxBasItem) {
        try {
           return pcxBasItemService.updateById(pcxBasItem);
        }catch (Exception e){
            log.error("修改失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/remove")
    @ApiOperation("删除事项类型")
    public Response<?> remove(@RequestBody PcxBasItemQO pcxBasItemQO) {
        try {
           return pcxBasItemService.deleteById(pcxBasItemQO);
        }catch (Exception e){
            log.error("删除失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/getAll")
    @ApiOperation("获取所有的事项类型")
    public Response<?> getAll(@RequestBody PcxBasItemQO pcxBasItemQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasItemService.getAll(pcxBasItemQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("删除失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(path = "/getTopLevelItem")
    @ApiOperation("获取一级的费用类型")
    public Response<?> getTopLevelItem(@RequestBody PcxBasItemQO pcxBasItemQO) {
        try {
            return pcxBasItemService.getTopLevelItem(pcxBasItemQO);
        }catch (Exception e){
            log.error("删除失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }
     @PostMapping(path = "/getOwnItem")
     @ApiOperation("获取我拥有的的事项类型/借款类型")
    public Response<?> getSelfItem(@RequestBody PcxBasItemQO pcxBasItemQO){
         try {
             List<BasItemVO> ownItem = pcxBasItemService.getOwnItem(pcxBasItemQO);
             return Response.success(ownItem);
         }catch (Exception e){
             log.error("获取自己拥有的的事项类型失败：{}", e.getMessage(),e);
             return Response.fail().setMsg(e.getMessage());
         }
    }

    @PostMapping(path = "/getRootItem")
    @ApiOperation("pc端获取顶级事项类型")
    public Response<?> getRootItem(@RequestBody PcxBasItemQO pcxBasItemQO){
        try {
            CheckMsg<?> checkMsg =pcxBasItemService.getRootItem(pcxBasItemQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("获取自己拥有的的事项类型失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @PostMapping(value = "/allowChangeItemList")
    @ApiOperation(value = "查询允许变更的事项列表")
    public Response allowChangeItemList(@RequestBody @Valid QueryAllowChangeItemListQO qo) {
        try {
            CheckMsg<?> checkMsg = ecsItemService.allowChangeItemList(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询允许变更的事项列表异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询允许变更的事项列表异常");
        }
    }
}
