package com.pty.pcx.controller.ecs;

import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.ecs.IPcxEcsComparedResultService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import com.pty.pcx.qo.ecs.compared.PcxEcsComparedResultQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Indexed
@RequestMapping(value = "/pcx/ecs/compared", method = {RequestMethod.POST})
@Api(value = "ecs票据对比结果相关接口", tags = {"ecs票据对比结果表控制层"})
@RestSchema(schemaId = "/pcxEcsComparedResultController")
@RestController
@Slf4j
public class PcxEcsComparedResultController {

    @Autowired
    private IPcxEcsComparedResultService pcxEcsComparedResultService;

    @PostMapping(value = "/paperCompared")
    @ApiOperation(value = "获取纸质电子票据对比信息")
    public Response paperCompared(@RequestBody PcxEcsComparedResultQO qo) {
       try {
           CheckMsg<Map<String, Integer>> result =  pcxEcsComparedResultService.paperCompared(qo);
           if (result.isSuccess()){
               return Response.success().setData(result.getData());
           }
           return Response.fail().setMsg(result.getMsgInfo());
       }catch (Exception e){
         log.error("获取纸质电子票据对比信息异常", e);
         return Response.fail().setMsg("获取纸质电子票据对比信息异常");
       }
    }

    @PostMapping(value = "/selectWithPage")
    @ApiOperation(value = "获取纸质电子票据对比信息列表")
    public Response selectWithPage(@RequestBody PcxEcsComparedResultQO qo){
        try {
            Response<PageInfo<PcxEcsComparedResult>> pageInfoResponse = pcxEcsComparedResultService.selectWithPage(qo);
            if(pageInfoResponse.isSuccess()){
                return Response.success().setData(pageInfoResponse.getData());
            }
            return Response.fail().setMsg(pageInfoResponse.getMsg());
        }catch (Exception e){
            log.error("获取纸质电子票据对比信息列表异常", e);
            return Response.fail().setMsg("获取纸质电子票据对比信息列表异常");
        }
    }

    @PostMapping(value = "/remove")
    @ApiOperation(value = "删除纸质电子票据对比信息")
    public Response remove(@RequestBody PcxEcsComparedResultQO qo){
        try {
            CheckMsg checkMsg = pcxEcsComparedResultService.remove(qo);
            if(checkMsg.isSuccess()){
                return Response.success().setMsg("删除成功");
            }
            return Response.fail().setMsg(checkMsg.getMsgInfo());
        }catch (Exception e){
            log.error("删除纸质电子票据对比信息异常", e);
            return Response.fail().setMsg("删除纸质电子票据对比信息异常");
        }
    }

    @PostMapping(value = "/selectById")
    @ApiOperation(value = "查询纸质对比的附件以及对比信息")
    public Response selectById(@RequestBody PcxEcsComparedResultQO qo){
        try {
            CheckMsg<Map> result = pcxEcsComparedResultService.selectById(qo);
            if (result.isSuccess()){
                return Response.success().setData(result.getData());
            }
            return Response.fail().setMsg(result.getMsgInfo());
        }catch (Exception e){
            log.error("查询纸质对比的附件以及对比信息异常", e);
            return Response.fail().setMsg("查询纸质对比的附件以及对比信息异常");
        }
    }


    @PostMapping(value = "/selectComparedDetail")
    @ApiOperation(value = "查询纸质对比的附件以及对比信息")
    public Response selectResultById(@RequestBody PcxEcsComparedResultQO qo){
        try {
            CheckMsg<PcxEcsComparedResult> result = pcxEcsComparedResultService.selectComparedDetail(qo);
            if (result.isSuccess()){
                return Response.success().setData(result.getData());
            }
            return Response.fail().setMsg(result.getMsgInfo());
        }catch (Exception e){
            log.error("查询纸质对比的附件以及对比信息异常", e);
            return Response.fail().setMsg("查询纸质对比的附件以及对比信息异常");
        }
    }

    @PostMapping(value = "/selectResultByBillId")
    @ApiOperation(value = "查询驳回原因中的票据对比信息")
    public Response selectResultByBillId(@RequestBody PcxEcsComparedResultQO qo){
        try {
            CheckMsg<List<PcxEcsComparedResult>> result = pcxEcsComparedResultService.selectResultByBillId(qo);
            if (result.isSuccess()){
                return Response.success().setData(result.getData());
            }
            return Response.fail().setMsg(result.getMsgInfo());
        }catch (Exception e){
            log.error("查询驳回原因中的票据对比信息异常", e);
            return Response.fail().setMsg("查询驳回原因中的票据对比信息异常");
        }
    }

    @PostMapping(value = "/manual")
    @ApiOperation(value = "手动对比")
    public Response manual(@RequestBody PcxEcsComparedResultQO qo){
        try {
            CheckMsg checkMsg = pcxEcsComparedResultService.manual(qo);
            if(checkMsg.isSuccess()){
                return Response.success().setMsg("手动对比成功");
            }
            return Response.fail().setMsg(checkMsg.getMsgInfo());
        }catch (Exception e){
            log.error("手动对比异常", e);
            return Response.fail().setMsg("手动对比异常");
        }
    }
}
