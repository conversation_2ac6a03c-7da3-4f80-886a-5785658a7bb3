package com.pty.pcx.controller.setting;

import com.pty.pcx.api.setting.ApplyRuleService;
import com.pty.pcx.common.enu.ApplyControlEnum;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.qo.setting.ApplyRuleQO;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费用申请规则
 * <AUTHOR>
 * @date 2024/11/08
 */
@Indexed
@RequestMapping(value = "/pcx/applyRule")
@Api(value = "费用对应申请规则接口", tags = {"申请规则控制层"})
@Slf4j
@RestSchema(schemaId = "/applyRule")
@RestController
public class ApplyRuleController {

    @Autowired
    private ApplyRuleService applyRuleService;

    /**
     * 查询费用申请规则的枚举
     *
     * @return 多条数据
     */
    @PostMapping(value = "/selectApplyControlEnums")
    @ApiOperation(value = "查询费用申请规则的枚举")
    public Response<?> selectApplyControlEnums(){
        return Response.success(ApplyControlEnum.toList());
    }

    /**
     * 更新费用的申请规则
     * @param applyRuleQO
     * @return
     */
    @PostMapping(value = "/update")
    @ApiOperation(value = "更新费用申请规则")
    public Response<?> update(@Validated(Update.class) @RequestBody ApplyRuleQO applyRuleQO) {
        CheckMsg<?> checkMsg = applyRuleService.update(applyRuleQO);
        if (!checkMsg.isSuccess()){
            return Response.fail().setMsg(checkMsg.getMsgInfo());
        }
        return Response.success("");
    }

    /**
     * 查看费用申请规则
     * @param applyRuleQO
     * @return
     */
    @PostMapping(value = "/view")
    @ApiOperation(value = "更新费用申请规则")
    public Response<?> view(@Validated(Query.class) @RequestBody ApplyRuleQO applyRuleQO) {
        return applyRuleService.view(applyRuleQO);
    }

    @PostMapping(value = "/applyRuleFormSetting")
    @ApiOperation(value = "查询pcxFormSetting, 根据单位编码，年度，区划，业务类型等")
    public Response<?> selectPcxFormSetting(@RequestBody ApplyRuleQO qo) {
        log.debug("查询pcxFormSetting, 根据单位编码，年度，区划，业务类型等{}", qo);
        Assert.notNull(qo, "上行参数为空");
        Assert.notNull(qo.getAgyCode(), "单位编码参数不能为空");
        Assert.notNull(qo.getFiscal(), "年度参数不能为空");
        Assert.notNull(qo.getMofDivCode(), "区划参数不能为空");
        return Response.success(this.applyRuleService.selectPcxFormSetting(qo));
    }
}
