package com.pty.pcx.controller.stand;

import cn.hutool.core.util.ObjectUtil;
import com.pty.pcx.api.stand.PcxStandAbroadService;
import com.pty.pcx.common.valid.Enable;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.entity.stand.PcxStandAbroad;
import com.pty.pcx.entity.stand.qo.PcxStandAbroadQO;
import com.pty.pcx.qo.bill.PcxBillAbroadTripQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 支出标准-出国标准(PcxStandAbroad)表控制层
 * <AUTHOR>
 * @since 2024-11-04 14:28:04
 */
@Indexed
@Slf4j
@RequestMapping(value = "/pcx/pcxStandAbroad", method = {RequestMethod.POST})
@Api(value = "支出标准-出国标准接口", tags = {"支出标准-出国标准(PcxStandAbroad)表控制层"})
@RestSchema(schemaId = "/pcxStandAbroadController")
@RestController
public class PcxStandAbroadController {

    @Autowired
    private PcxStandAbroadService pcxStandAbroadService;

    /**
     * 通过主键查询单条数据
     * @param id 主键
     * @return 单条数据
     */
    @PostMapping(value = "/selectById")
    @ApiOperation(value = "通过主键查询单条数据")
    public Response<?> selectById(@RequestParam String id) {
        return Response.success(this.pcxStandAbroadService.selectById(id));
    }

    /**
     * 通过主键查询单条数据
     * @param qo 请求参数
     * @return 单条数据
     */
    @PostMapping(value = "/selectAll")
    @ApiOperation(value = "查询所有出国标准")
    public Response<?> selectAll(@Validated(Query.class) @RequestBody PcxStandAbroadQO qo) {
        PcxStandAbroad abroad = PcxStandAbroad.builder()
                .agyCode(qo.getAgyCode())
                .mofDivCode(qo.getMofDivCode())
                .fiscal(qo.getFiscal())
                .build();
        return Response.success(this.pcxStandAbroadService.selectList(abroad));
    }

    /**
     * 根据国家编码,城市编码集合,币种进行更新
     */
    @PostMapping(value = "/updateByCountryCityCurrency")
    @ApiOperation(value = "根据国家编码,城市编码集合,币种进行更新")
    public Response<?> updateByCountryCityCurrency(@Validated(Update.class) @RequestBody PcxStandAbroadQO qo) {
        PcxStandAbroad abroad = PcxStandAbroad.builder()
                .fiscal(qo.getFiscal())
                .mofDivCode(qo.getMofDivCode())
                .agyCode(qo.getAgyCode())
                .countryCode(qo.getCountryCode())
                .cityCodes(qo.getCityCodeSet())
                .currencyCode(qo.getCurrencyCode())
                .currencyName(qo.getCurrencyName())
                .stayAmt(qo.getStayAmt())
                .otherAmt(qo.getOtherAmt())
                .foodAmt(qo.getFoodAmt())
                .build();
        return Response.success(this.pcxStandAbroadService.rebuildCityInfo(abroad));
    }

    /**
     * 根据ids禁用、启用
     * @param qo 请求参数
     * @return 更新条数
     */
    @PostMapping(value = "/enableByIds")
    @ApiOperation(value = "根据ids禁用、启用")
    public Response<?> enableByIds(@Validated(Enable.class) @RequestBody PcxStandAbroadQO qo) {
        PcxStandAbroad abroad = PcxStandAbroad.builder()
                .isEnabled(qo.getIsEnabled())
                .ids(qo.getIds())
                .build();
        return Response.success(this.pcxStandAbroadService.update(abroad));
    }

    /**
     * 根据国外行程构建信息构建对应支出标准
     * @param qoList 行程信息请求参数列表
     * @return 构建结果
     */
    @PostMapping(value = "/buildByTripInfo")
    @ApiOperation(value = "根据国外行程构建信息构建对应支出标准")
    public Response<?> buildByTripInfo(@Validated @RequestBody PcxBillAbroadTripQO qo) {
        try {
            List<Map<String, Object>> result = this.pcxStandAbroadService.buildByTripInfo(qo);
            if (CollectionUtil.isEmpty(result)) {
                return Response.success().setMsg("暂无出国行程信息");
            }
            return Response.success(result);
        } catch (Exception e) {
            log.error("根据国外行程构建信息构建对应支出标准构建出错", e);
            return Response.fail().setMsg(e.getMessage());
        }
    }
}
