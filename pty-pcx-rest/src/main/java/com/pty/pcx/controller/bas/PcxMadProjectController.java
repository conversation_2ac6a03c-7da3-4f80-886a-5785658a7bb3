package com.pty.pcx.controller.bas;

import com.pty.mad.common.CheckMsg;
import com.pty.pcx.api.bas.IPcxMadProjectService;
import com.pty.pcx.qo.bas.PcxBasExpExpecoQO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Indexed
@RequestMapping(value = "/pcx/project")
@Api(value = "基础资料项目接口", tags = {"基础资料项目控制层"})
@Slf4j
@RestSchema(schemaId = "/pcxMadProjectController")
@RestController
public class PcxMadProjectController {

    @Autowired
    private IPcxMadProjectService pcxMadProjectService;

    @PostMapping(value = "/getAgyProject")
    @ApiOperation(value = "查询所有的")
    public Response getAgyProject(@RequestBody PcxMadBaseQO pcxMadBaseQO) {
        try{
            CheckMsg checkMsg = pcxMadProjectService.getAgyProject(pcxMadBaseQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success().setData(checkMsg.getData());
        }catch (Exception e){
            return Response.fail().setMsg(e.getMessage());
        }
    }
}
