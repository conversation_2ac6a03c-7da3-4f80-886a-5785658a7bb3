package com.pty.pcx.controller.valid;

import com.pty.pub.common.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;
@Slf4j
@ControllerAdvice(basePackages = {"com.pty.pcx.controller"})
public class ValidatedExceptionHandler {

    /**
     * 处理@Validated参数校验失败异常
     * @param exception 异常类
     * @return 响应
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Response<?> exceptionHandler(MethodArgumentNotValidException exception){
        BindingResult result = exception.getBindingResult();
        StringBuilder stringBuilder = new StringBuilder();
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            errors.forEach(p -> {
                FieldError fieldError = (FieldError) p;
                log.warn("错误的请求参数: 对象 [{}],属性 [{}],说明 [{}]", fieldError.getObjectName(), fieldError.getField(), fieldError.getDefaultMessage());
                stringBuilder
                        .append(stringBuilder.length() > 0 ? "," : "")
                        .append(fieldError.getDefaultMessage());
            });
        }
        return Response.commonResponse(Boolean.FALSE, stringBuilder.toString());
    }

    /**
     * 处理@Validated参数校验失败异常
     * @param bindException 参数绑定异常类
     * @return 响应
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BindException.class)
    public Response<?> bindExceptionHandler(BindException bindException){
        BindingResult result = bindException.getBindingResult();
        StringBuilder stringBuilder = new StringBuilder();
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            errors.forEach(p -> {
                FieldError fieldError = (FieldError) p;
                log.warn("错误的请求参数: 对象 [{}],属性 [{}],说明 [{}]", fieldError.getObjectName(), fieldError.getField(), fieldError.getDefaultMessage());
                stringBuilder
                        .append(stringBuilder.length() > 0 ? "," : "")
                        .append(fieldError.getDefaultMessage());
            });
        }
        return Response.commonResponse(Boolean.FALSE, stringBuilder.toString());
    }

    /**
     * 处理非法参数异常
     * @param exception 非法参数
     * @return 响应
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(IllegalArgumentException.class)
    public Response<?> illegalArgumentExceptionHandler(IllegalArgumentException exception){
        return Response.commonResponse(Boolean.FALSE, exception.getMessage());
    }

}


