package com.pty.pcx.controller.bas;

import com.pty.pcx.api.bas.PcxBasCityClassifyService;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.qo.bas.BatchUpdateCityClassifyQO;
import com.pty.pcx.qo.bas.BizQueryAllCityClassifyQO;
import com.pty.pcx.qo.bas.QueryCityPeakClassifyQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import com.pty.pub.common.bean.Response;

/**
 * 城市分类表(PcxBasCityClassify)表控制层
 * <AUTHOR>
 * @since 2024-11-08 10:06:13
 */
@Indexed
@RequestMapping(value = "/pcx/basCityClassify", method = {RequestMethod.POST})
@Api(value = "城市分类对应城市列表接口", tags = {"城市分类表(PcxBasCityClassify)表控制层"})
@RestSchema(schemaId = "/pcxBasCityClassifyController")
@Slf4j
@RestController
public class PcxBasCityClassifyController {

    @Autowired
    private PcxBasCityClassifyService pcxBasCityClassifyService;


    /**
     * 查询城市分类
     * @param qo 查询条件
     * @return 分类对于城市，其他分类对应城市列表
     */
    @PostMapping(value = "/getClass")
    @ApiOperation(value = "查询业务城市分类信息")
    public Response getClass(@RequestBody BizQueryAllCityClassifyQO qo) {
        try{
            qo.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_CITY);
            return Response.success().setData(pcxBasCityClassifyService.selectCityClassify(qo));
        }catch (Exception e){
            log.error("查询业务城市分类信息失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }

    }

    /**
     * 查询城市旺季列表
     * @param qo 查询条件
     * @return 分类对于城市，其他分类对应城市列表
     */
    @PostMapping(value = "/getPeakClass")
    @ApiOperation(value = "查询城市旺季信息")
    public Response getPeakClass(@RequestBody BizQueryAllCityClassifyQO qo) {
        try{
            qo.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
            return Response.success().setData(pcxBasCityClassifyService.selectCityClassify(qo));
        }catch (Exception e){
            log.error("查询城市旺季信息信息失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }

    }

    /**
     * 查询业务城市分类信息
     * @param queryAllCityClassifyQO 查询条件
     * @return 分类对于城市，其他分类对应城市列表
     */
    @PostMapping(value = "/bizSelectAllClassify")
    @ApiOperation(value = "查询业务城市分类信息")
    public Response bizSelectAllClassify(@RequestBody BizQueryAllCityClassifyQO queryAllCityClassifyQO) {
        try{
            queryAllCityClassifyQO.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_CITY);
            return this.pcxBasCityClassifyService.bizSelectAllClassify(queryAllCityClassifyQO);
        }catch (Exception e){
            log.error("查询业务城市分类信息失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }

    }

    /**
     * 更新城市分类对应列表数据
     * @param  batchUpdateCityClassifyQO 保存数据
     * @return
     */
    @PostMapping(value = "/updateCityClassify")
    @ApiOperation(value = "更新城市分类对应列表数据")
    public Response updateCityClassify(@RequestBody BatchUpdateCityClassifyQO batchUpdateCityClassifyQO) {
        try{
            return this.pcxBasCityClassifyService.updateCityClassify(batchUpdateCityClassifyQO);
        }catch (Exception e){
            log.error("更新城市分类对应列表数据失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

    @PostMapping(value = "/isCityPeak")
    @ApiOperation(value = "查询城市是否淡旺季")
    public Response isCityPeak(@RequestBody QueryCityPeakClassifyQO queryCityPeakClassifyQO) {
        try{
            return Response.success(this.pcxBasCityClassifyService.isCityPeak(queryCityPeakClassifyQO));
        }catch (Exception e){
            log.error("查询城市是否淡旺季失败：{}", e.getMessage(),e);
            return Response.fail().setMsg("操作失败");
        }
    }

    @PostMapping(value = "/getCityPeakDate")
    @ApiOperation(value = "查询城市淡旺季日期")
    public Response getCityPeakDate(@RequestBody String classifyCode) {
        return Response.success(this.pcxBasCityClassifyService.getCityPeakDateByClassifyCode(classifyCode));
    }
}
