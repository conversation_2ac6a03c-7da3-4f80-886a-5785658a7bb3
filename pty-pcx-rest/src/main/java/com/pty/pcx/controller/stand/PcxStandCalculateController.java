package com.pty.pcx.controller.stand;

import com.pty.pcx.api.stand.PcxStandCalculateService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.valid.Query;
import com.pty.pcx.qo.stand.PcxCalculateCostQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费用标准计算表控制层
 *
 * <AUTHOR>
 * @since 2025-05-16 15:45:49
 */
@Slf4j
@Indexed
@RequestMapping(value = "/pcx/pcxStandCalculate", method = {RequestMethod.POST})
@Api(value = "费用标准计算相关接口", tags = {"费用标准计算表控制层"})
@RestSchema(schemaId = "/pcxStandCalculateController")
@RestController
public class PcxStandCalculateController {

    @Autowired
    private PcxStandCalculateService pcxStandCalculateService;

    /**
     * 计算会议费用标准
     *
     * @param calculateCostQO 计算参数（包含参与人数、时长等）
     * @return 计算结果
     */
    @PostMapping(value = "/calculateMeetingCost")
    @ApiOperation(value = "计算会议费用标准")
    public Response<?> calculateMeetingCost(@Validated(Query.class) @RequestBody PcxCalculateCostQO calculateCostQO) {
        try {
            CheckMsg<?> checkMsg = pcxStandCalculateService.calculateMeetingCost(calculateCostQO);
            if (!checkMsg.isSuccess()) {
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            log.error("计算会议费用标准失败", e);
            return Response.fail().setMsg("计算会议费用标准失败：" + e.getMessage());
        }
    }

    /**
     * 计算培训费用标准
     *
     * @param calculateCostQO 计算参数（包含参与人数、时长等）
     * @return 计算结果
     */
    @PostMapping(value = "/calculateTrainingCost")
    @ApiOperation(value = "计算培训费用标准")
    public Response<?> calculateTrainingCost(@Validated(Query.class) @RequestBody PcxCalculateCostQO calculateCostQO) {
        try {
            CheckMsg<?> checkMsg = pcxStandCalculateService.calculateTrainingCost(calculateCostQO);
            if (!checkMsg.isSuccess()) {
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            log.error("计算培训费用标准失败", e);
            return Response.fail().setMsg("计算培训费用标准失败：" + e.getMessage());
        }
    }

    /**
     * 计算招待费用标准
     *
     * @param calculateCostQO 计算参数（包含参与人数、标准等级等）
     * @return 计算结果
     */
    @PostMapping(value = "/calculateHospitalityCost")
    @ApiOperation(value = "计算招待费用标准")
    public Response<?> calculateHospitalityCost(@Validated(Query.class) @RequestBody PcxCalculateCostQO calculateCostQO) {
        try {
            CheckMsg<?> checkMsg = pcxStandCalculateService.calculateHospitalityCost(calculateCostQO);
            if (!checkMsg.isSuccess()) {
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            log.error("计算招待费用标准失败", e);
            return Response.fail().setMsg("计算招待费用标准失败：" + e.getMessage());
        }
    }
}