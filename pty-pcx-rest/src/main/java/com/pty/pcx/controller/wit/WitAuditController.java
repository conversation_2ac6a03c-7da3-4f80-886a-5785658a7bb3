package com.pty.pcx.controller.wit;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.api.wit.MsgInfo;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.enu.wit.AuditRuleTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.entity.wit.WitRuleResultDetail;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bill.PcxBillAuditRuleQO;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.wit.WitAttachQO;
import com.pty.pcx.qo.wit.WitCreateRuleQO;
import com.pty.pcx.service.impl.wit.WitAsyncyService;
import com.pty.pcx.vo.wit.AuditRuleVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.StringUtil;
import com.pty.rule.RuleAct;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(value = "单据智能稽核预审相关接口")
@RestSchema(schemaId = "pcxWitAuditController")
@RestController
@RequestMapping("/pcx/wit")
@Slf4j
@Indexed
public class WitAuditController {

  @Autowired
  private PcxBillService pcxBillService;

  @Autowired
  private IWitAuditRuleService witAuditRuleService;

  @Autowired
  private WitAsyncyService witAsyncyService;
  @Resource
  private PcxBillDao pcxBillDao;
  @Resource
  private PcxBasExpTypeDao pcxBasExpTypeDao;

  @PostMapping("/audit/rule")
  @ApiOperation("单据智能稽核预审（实时保存）")
  @SneakyThrows
  public Response<AuditRuleVO> auditRule(@RequestBody PcxBillAuditRuleQO bill) {
    try {
      if (Objects.isNull(bill.getNonSave())) {
        fillBizType(bill);
        //保存
        bill.buildInstance();
        PcxBill pcxBill = pcxBillService.saveOrUpdate(bill);
        if(StringUtil.isEmpty(bill.getBillId())){
          bill.setBillId(pcxBill.getId());
        }
      }

      CheckMsg<List<WitRuleResult>> msgInfoCheckMsg = pcxBillService.auditRule(bill.getBillId(), bill.getClassifyCodes());
      if (!msgInfoCheckMsg.isSuccess()){
        return Response.businessFailResponse(msgInfoCheckMsg.getMsgInfo());
      }
      AuditRuleVO auditRuleVO = new AuditRuleVO();
      //汇总结果
      if(CollectionUtils.isEmpty(msgInfoCheckMsg.getData())){
        auditRuleVO.setAuditRuleResult(RuleAct.ACCESS.toString());
      }
      if (msgInfoCheckMsg.isSuccess() && CollectionUtils.isNotEmpty(msgInfoCheckMsg.getData())) {
        auditRuleVO.setAuditRuleResult(witAuditRuleService.getResult(msgInfoCheckMsg.getData(),StrUtil.emptyToDefault(bill.getPositionCode(), PositionEnum.MAKE_BILL.getCode())));
        if (PositionEnum.MAKE_BILL.getCode().equals(bill.getPositionCode())){
          auditRuleVO.setAuditRule(filterDeleteDetail(msgInfoCheckMsg.getData()));
        }else{
          auditRuleVO.setAuditRule(msgInfoCheckMsg.getData());
        }
      }
      auditRuleVO.setBillId(bill.getBillId());

      return Response.success(auditRuleVO);
    }catch (Exception e){
      log.error("单据智能稽核预审异常 req {}", bill, e);
      return Response.businessFailResponse(e.getMessage());
    }
  }

  private void fillBizType(PcxBillQO qo){
    Integer bizType = null;
    PcxBill pcxBill = JSONObject.parseObject(JSONObject.toJSONString(qo.getBasicInfo()), PcxBill.class);
    if (Objects.isNull(pcxBill) && StringUtil.isNotEmpty(qo.getBillId())){
      PcxBill bill = pcxBillDao.selectById(qo.getBillId());
      if (Objects.nonNull(bill)){
        bizType = bill.getBizType();
      }
    }else if (StringUtil.isNotEmpty(qo.getFiscal(), qo.getAgyCode(), qo.getMofDivCode(), qo.getItemCode())){
      PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
      pcxBasExpTypeQO.setFiscal(qo.getFiscal());
      pcxBasExpTypeQO.setAgyCode(qo.getAgyCode());
      pcxBasExpTypeQO.setMofDivCode(qo.getMofDivCode());
      pcxBasExpTypeQO.setItemCode(qo.getItemCode());
      List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectItemExpenseTypeList(pcxBasExpTypeQO);
      ItemBizTypeEnum bizTypeEnum = ItemBizTypeEnum.COMMON;
      if (CollectionUtils.isNotEmpty(pcxBasExpTypes)) {
        bizTypeEnum = ItemBizTypeEnum.expenseTypeMatchBizType(pcxBasExpTypes.get(0).getExpenseCode());
      }
      bizType = bizTypeEnum.getCode();
    }
    qo.setBizType(bizType);
  }


  @PostMapping("/audit/getMsgInfo")
  @ApiOperation("获取异步信息结果")
  public Response<?> getMsgInfo(@RequestBody MsgInfo msg) {
    MsgInfo msgInfo = witAsyncyService.getMsgInfo(msg.getCacheKey());
    return Response.success().setData(msgInfo);
  }

  @PostMapping("/audit/queryRuleResult")
  @ApiOperation("获取单据智能稽核结果")
  @SneakyThrows
  public Response<?> queryRuleResult(@RequestBody WitRuleResult result) {
    if (StringUtil.isEmpty(result.getBillId())) {
      return Response.fail().setMsg("参数billId不能为空！");
    }
    result.setAuditType(AuditRuleTypeEnum.SUBMIT.getCode());
    CheckMsg<?> msg = witAuditRuleService.queryRuleResult(result);
    return Response.success(msg.getData());
  }


  @PostMapping("/audit/queryRuleResultAll")
  @ApiOperation("获取单据智能稽核结果(平铺展示)")
  @SneakyThrows
  public Response queryRuleResultAll(@RequestBody WitRuleResult result) {
    if (StringUtil.isEmpty(result.getBillId())) {
      return Response.fail().setMsg("参数billId不能为空！");
    }
    result.setAuditType(AuditRuleTypeEnum.SUBMIT.getCode());
    CheckMsg<List<WitRuleResult>> msg = witAuditRuleService.queryRuleResult(result);
    List<WitRuleResultDetail> resultDetails = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(msg.getData())){
        msg.getData().forEach(ruleResult -> {
            resultDetails.addAll(ruleResult.getDetailList());
        });
    }
    return Response.success().setData(resultDetails);
  }


  /**
   * 处理稽核结果
   * @param detail
   * @return
   */
  @PostMapping("/audit/handle")
  @ApiOperation("处理稽核结果")
  public Response<?> auditHandle(@RequestBody WitRuleResultDetail detail) {
    CheckMsg<?> msg = witAuditRuleService.auditHandle(detail);
    if (!msg.isSuccess()){
      return Response.fail().setMsg(msg.getMsgInfo());
    }
    if (StringUtil.isNotEmpty(detail.getStatus())){
      if (Objects.equals(detail.getStatus(), "2")){
        return Response.fail().setMsg("已取消");
      }
      if (Objects.equals(detail.getStatus(), "1")){
        return Response.success().setMsg("处理成功");
      }
    }
    return Response.success().setMsg("处理成功");
  }



  /**
   * 关联附件
   * @param witAttachQO
   * @return
   */
  @PostMapping("/audit/addAttachRel")
  @ApiOperation("处理稽核结果")
  public Response<?> addAttachRel(@RequestBody WitAttachQO witAttachQO) {
    //返回会打上标记

    witAuditRuleService.addAttachRel(witAttachQO);
    return Response.success();
  }

  /**
   * 关联附件
   * @param witAttachQO
   * @return
   */
  @PostMapping("/audit/delAttachRel")
  @ApiOperation("处理稽核结果")
  public Response<?> delAttachRel(@RequestBody WitAttachQO witAttachQO) {
    witAuditRuleService.delAttachRel(witAttachQO);
    return Response.success();
  }




  private List<WitRuleResult> filterDeleteDetail(List<WitRuleResult> data) {
    // 过滤掉删除的处理方式
    if (data == null || data.isEmpty()) {
      return data;
    }



    for (WitRuleResult datum : data) {
      if (datum == null || datum.getDetailList() == null || datum.getDetailList().isEmpty()) {
        continue;
      }
      //分组同一锚点的数据
      Map<String, List<WitRuleResultDetail>> groupByCurData = datum.getDetailList().stream()
              .filter(detail -> StrUtil.isNotEmpty(detail.getCurData()))
              .collect(Collectors.groupingBy(WitRuleResultDetail::getCurData));
      if (MapUtil.isNotEmpty(groupByCurData)){
        for (Map.Entry<String, List<WitRuleResultDetail>> stringListEntry : groupByCurData.entrySet()) {
          int index = 0;
          for (WitRuleResultDetail detail : stringListEntry.getValue()) {
            if (detail == null || detail.getField1() == null) {
              continue;
            }
            //如果明细定位到同一个锚点 则处理方式只显示包含删除的处理方式
            if (detail.getField1().contains("5")) {
              if (index != 0) {
                detail.setField1("");
              }
              index++;
            }
          }
        }
      }
    }
    return data;
  }


  /**
   * 创建人工稽核
   * @param createRuleQO
   * @return
   */
  @PostMapping("/audit/createRule")
  @ApiOperation("创建人工稽核")
  public Response<?> createRule(@RequestBody WitCreateRuleQO createRuleQO) {
    //创建人工稽核
    WitRuleResultDetail rule = witAuditRuleService.createRule(createRuleQO);
    String dataInfo = rule.getDataInfo();
    JSONObject jsonObject = JSONObject.parseObject(dataInfo);
    jsonObject.put("errMsg",rule.getErrMsg());
    jsonObject.put("ruleDetailId",rule.getId());
    return Response.success().setData(jsonObject);
  }


  @PostMapping("/audit/queryManualRuleResultAll")
  @ApiOperation("查询人工稽核(平铺展示)")
  @SneakyThrows
  public Response queryManualRuleResultAll(@RequestBody WitRuleResult result) {
    if (StringUtil.isEmpty(result.getBillId())) {
      return Response.fail().setMsg("参数billId不能为空！");
    }
    result.setAuditType(AuditRuleTypeEnum.MANUAL.getCode());
    CheckMsg<List<WitRuleResult>> msg = witAuditRuleService.queryRuleResult(result);
    List<JSONObject> resultDetails = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(msg.getData())){
      WitRuleResult index = msg.getData().get(0);
      msg.getData().forEach(ruleResult -> {
        ruleResult.getDetailList().forEach(rule -> {
          String dataInfo = rule.getDataInfo();
          JSONObject jsonObject = JSONObject.parseObject(dataInfo);
          jsonObject.put("errMsg",rule.getErrMsg());
          jsonObject.put("ruleDetailId",rule.getId());
          resultDetails.add(jsonObject);
        });
      });
      //封装 map信息
      Map<String,Object> resultMap = Maps.newLinkedHashMap();
      resultMap.put("editFlag",StrUtil.isEmpty(index.getField1())?"1":index.getField1());
      resultMap.put("ruleResultDetail",resultDetails);
      return Response.success().setData(resultDetails);
    }
    return Response.success().setData(resultDetails);
  }


}
