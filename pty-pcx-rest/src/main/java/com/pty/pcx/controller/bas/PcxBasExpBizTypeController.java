package com.pty.pcx.controller.bas;

import com.pty.pcx.api.bas.IPcxBasExpBizTypeService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: 票类规则
 * createdTime: 2024/12/6  下午5:08
 * creator: wangbao
 **/
@Indexed
@RequestMapping(value = "/pcx/basExpBizType")
@Api(value = "费用对应票类规则接口", tags = {"票类规则PcxBasExpTypeController控制层"})
@Slf4j
@RestSchema(schemaId = "/pcxBasExpBizTypeController")
@RestController
public class PcxBasExpBizTypeController {

    @Autowired
    private IPcxBasExpBizTypeService pcxBasExpBizTypeService;

    /**
     * 查询票类规则
     */
    @PostMapping(path = "/list")
    @ApiOperation(value = "查询票类规则")
    public Response<?> selectList(@RequestBody PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.select(pcxBasExpBizTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (AssertionError | Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 新增或修改票类规则
     */
    @PostMapping(path = "/saveOrUpdate")
    @ApiOperation(value = "新增或修改票类规则")
    public Response<?> saveOrUpdate(@RequestBody List<PcxBasExpBizTypeQO> pcxBasExpBizTypeQOList) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.saveOrUpdate(pcxBasExpBizTypeQOList);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 删除票类规则
     */
    @PostMapping(path = "/deleteByIdList")
    @ApiOperation(value = "删除票类规则")
    public Response<?> deleteByIdList(@RequestBody PcxBasExpBizTypeQO PcxBasExpBizTypeQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.deleteByIdList(PcxBasExpBizTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 票类规则匹配费用编码（必传参数：ecs票据的id、类型、金额）
     */
    @PostMapping(path = "/expTypeList")
    @ApiOperation(value = "票类规则匹配费用类型成功")
    public Response<?> selectExpTypeList(@RequestBody List<PcxBasExpBizTypeQO> pcxBasExpBizTypeQOList) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.selectExpTypeList(pcxBasExpBizTypeQOList);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 查询电子凭证业务凭证种类
     */
    @PostMapping(path = "/ecsBizTypeList")
    @ApiOperation(value = "查询业务凭证种类选项")
    public Response<?> selectExpTypeList(@RequestBody PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.selectEcsBizType(pcxBasExpBizTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 查询票类规则的条件字段选项
     */
    @PostMapping(path = "/conditionFieldList")
    @ApiOperation(value = "查询票类规则的条件字段选项")
    public Response<?> selectConditionField(@RequestBody PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.selectConditionField(pcxBasExpBizTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 查询票类规则的条件字段选项
     */
    @PostMapping(path = "/operatorList")
    @ApiOperation(value = "查询运算符号选项")
    public Response<?> selectOperatorList(@RequestBody PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        try {
            CheckMsg<?> checkMsg = pcxBasExpBizTypeService.selectOperatorList(pcxBasExpBizTypeQO);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        } catch (Exception e) {
            return Response.fail().setMsg(e.getMessage());
        }
    }

}
