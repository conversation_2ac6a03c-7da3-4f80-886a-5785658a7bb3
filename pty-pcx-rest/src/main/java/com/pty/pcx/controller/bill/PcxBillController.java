package com.pty.pcx.controller.bill;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.CodeNameVO;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.bill.PcxBillTravelFellowsService;
import com.pty.pcx.api.ecs.EcsItemService;
import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.api.ecs.ExpenseStrategy;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.exception.ForbidTipsException;
import com.pty.pcx.common.exception.WarningTipsException;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ThreadLocalUtil;
import com.pty.pcx.common.valid.BillSave;
import com.pty.pcx.common.valid.BillView;
import com.pty.pcx.common.valid.Delete;
import com.pty.pcx.common.valid.Update;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.PcxBillDao;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.bill.*;
import com.pty.pcx.qo.bill.apportion.*;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.*;
import com.pty.pcx.qo.financeapproval.PcxFinanceApprovalBillQO;
import com.pty.pcx.service.impl.bill.balance.PcxBalanceCtrlService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.ecs.ExpenseStrategyContext;
import com.pty.pcx.util.FundSourceFlushUtil;
import com.pty.pcx.util.PcxBillViewWrapperUtil;
import com.pty.pcx.vo.bill.*;
import com.pty.pub.common.anno.IgnoreAuthe;
import com.pty.pub.common.anno.TicketAuth;
import com.pty.pub.common.anno.TxTraceable;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Indexed;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 报销流程controller
 * <AUTHOR>
 * @since 2024/11/25
 *
 */
@Api(value = "报销流程相关接口", tags = {"报销流程接口"})
@RestSchema(schemaId = "PcxBillController")
@RequestMapping("/pcx/bill")
@RestController
@Indexed
@Slf4j
public class PcxBillController {
    @Autowired
    private ExpenseStrategyContext expenseStrategyContext;

    @Autowired
    private PcxBillService pcxBillService;

    @Autowired
    private EcsProcessService ecsProcessService;

    @Autowired
    private EcsItemService ecsItemService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private PcxBillViewWrapperUtil pcxBillViewWrapperUtil;
    @Autowired
    private BillMainService billMainService;

    public static final String DOWNLOAD_ATTACH_CACHE = "PCX:DOWNLOAD:BILL:ATTACH";

    public static final String EXP_LEDGER_EHCACHE_EXCEL_NAME = "expLedgerExcel";

    @Autowired
    private PcxBalanceCtrlService pcxBalanceCtrlService;
    @Resource
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Resource
    private PcxBillAmtApportionService pcxBillAmtApportionService;
    @Resource
    private PcxBillDao pcxBillDao;
    @Resource
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @PostMapping(value = "/initExpenseAddition")
    public void initExpenseAddition(){
        pcxBasFormSettingService.initExpenseTypeAddition();
    }



    /**
     * 获取费用的无票原因
     * @return
     */
    @PostMapping(value = "/getNoEcsReason")
    @ApiOperation(value = "获取费用的无票原因")
    public Response<List<CodeNameVO>> getNoEcsReason(@RequestBody @Validated QueryNoEcsQO qo){
        CheckMsg<List<CodeNameVO>> expenseUserInfo = ecsProcessService.getNoEcsReason(qo);
        if (!expenseUserInfo.isSuccess()){
            return Response.commonResponse(Response.FAIL_CODE, expenseUserInfo.getMsgInfo());
        }
        return Response.success(expenseUserInfo.getData());
    }




    /**
     * 获取当前登录人的部门人员信息（不通过做不了单据）
     * @return
     */
    @PostMapping(value = "/getEmpInfo")
    @ApiOperation(value = "获取人员信息")
    public Response<MadEmployeeDTO> getExpenseUserInfo(@RequestBody EmpInfoQO empInfoQO){
        CheckMsg<MadEmployeeDTO> expenseUserInfo = pcxBillService.getExpenseUserInfo(empInfoQO);
        if (!expenseUserInfo.isSuccess()){
            return Response.commonResponse(Response.FAIL_CODE, expenseUserInfo.getMsgInfo());
        }
        return Response.success(expenseUserInfo.getData());
    }

    /**
     * 发起报销
     * @param expenseQO
     * @return
     */
    @PostMapping(value = "/startExpense")
    @ApiOperation(value = "发起报销")
    @TxTraceable(business = "发起报销")
    public Response<?> startExpense(@RequestBody @Validated(value = {StartEcs.class}) StartExpenseQO expenseQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.startExpense(expenseQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (Exception e){
            log.error("发起报销异常 req {}",expenseQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "发起报销异常");
        }
    }

    /**
     * 添加票
     * @param addInvoicesQO
     * @return
     */
    @PostMapping(value = "/addEcsBill")
    @ApiOperation(value = "添加票")
    @TxTraceable(business = "添加票")
    public Response<?> addEcsBill(@RequestBody  @Validated(value = {AddEcs.class}) AddInvoicesQO addInvoicesQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.addEcsBill(addInvoicesQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("添加票异常 req {}",addInvoicesQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "添加票异常");
        }
    }

    /**
     * 删除票
     * @param delExpenseQO
     * @return
     */
    @PostMapping(value = "/delEcsBillExpense")
    @ApiOperation(value = "删除票")
    @TxTraceable(business = "删除票")
    public Response<?> delEcsBillExpense(@RequestBody @Validated(value = {DelEcs.class}) DelExpenseQO delExpenseQO){
        try {
            if (StringUtil.isEmpty(delExpenseQO.getEcsBillId()) && StringUtil.isEmpty(delExpenseQO.getExpenseDetailId())){
                return Response.commonResponse(Response.FAIL_CODE, "参数:ecsBillId和expenseDetailId不能同时为空");
            }
            CheckMsg<?> voidCheckMsg = ecsProcessService.delEcsBillExpense(delExpenseQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
             log.error("删除票异常 req {}",delExpenseQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "删除异常");
        }
    }

    /**
     * 理票页面查看票的信息
     * @param updateEcsDetailAndPaymentQO
     * @return
     */
    @PostMapping(value = "/ecsExpView")
    @ApiOperation(value = "理票页面查看票的信息")
    public Response<?> ecsExpView(@RequestBody @Validated(value = {EcsExpView.class}) UpdateEcsDetailAndPaymentQO updateEcsDetailAndPaymentQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsExpView(updateEcsDetailAndPaymentQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("编辑票费用明细异常 req {}",updateEcsDetailAndPaymentQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "编辑票费用明细异常");
        }
    }

    /**
     * 编辑票费用明细和支付信息
     * @param updateEcsQO
     * @return
     */
    @PostMapping(value = "/updateEcs")
    @ApiOperation(value = "编辑票项目费用明细")
    @TxTraceable(business = "编辑票项目费用明细")
    public Response<?> updateEcs(@RequestBody @Validated(value = {UpdateEcsDetail.class}) UpdateEcsQO updateEcsQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateEcs(updateEcsQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("编辑票项目费用明细异常 req {}",updateEcsQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "编辑票项目费用明细异常");
        }
    }

    /**
     * 理票页面查看票的信息
     * @param updateEcsQO
     * @return
     */
    @PostMapping(value = "/ecsItemExpView")
    @ApiOperation(value = "理票页面查看票项目的信息")
    public Response<?> ecsItemExpView(@RequestBody @Validated(value = {EcsExpView.class}) UpdateEcsQO updateEcsQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsItemExpView(updateEcsQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("理票页面查看票项目的信息异常 req {}",updateEcsQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "理票页面查看票项目的信息异常");
        }
    }

    /**
     * 手动添加无票费用明细
     * @param updateNoEcsDetailQO
     * @return
     */
    @PostMapping(value = "/updateNoEcsDetail")
    @ApiOperation(value = "手动添加无票费用明细")
    @TxTraceable(business = "手动添加无票费用明细")
    public Response<?> updateNoEcsDetail(@RequestBody @Validated(value = {UpdateEcsDetail.class}) UpdateNoEcsDetailQO updateNoEcsDetailQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateNoEcsDetail(updateNoEcsDetailQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("手动添加无票费用明细异常 req {}",updateNoEcsDetailQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "手动添加无票费用明细异常");
        }
    }

    /**
     * 理票查看
     * @param billId
     * @return
     */
    @PostMapping(value = "/billView")
    @ApiOperation(value = "理票查看")
    public Response<?> billView(@RequestBody String billId){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsView(billId);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("理票查看异常 req {}",billId,e);
            return Response.commonResponse(Response.FAIL_CODE, "理票查看异常");
        }
    }

    /**
     * 选票查询事项列表
     * @param expenseQO
     * @return
     */
    @PostMapping(value = "/getItemList")
    @ApiOperation(value = "选票查询事项列表")
    public Response<?> getItemList(@RequestBody @Validated(value = {StartEcs.class}) ExpInvoiceQO expenseQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsItemService.getItemList(expenseQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查询事项列表异常 req {}",expenseQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询事项列表异常");
        }
    }

    /**
     * 查询报销单据费用标准
     */
    @PostMapping(value = "/getBillExpenseStand")
    @ApiOperation(value = "查询单据费用标准")
    public Response<List<ExpStandSnapshotsFrontVO>> getBillExpenseStand(@RequestBody @Validated PcxBillExpenseStandQO qo){
        try {
            CheckMsg<List<ExpStandSnapshotsFrontVO>> voidCheckMsg = pcxBillService.getBillExpenseStand(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查询单据费用标准异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询单据费用标准异常");
        }
    }

    /**
     * 查询申请单费用标准
     */
    @PostMapping(value = "/getReqBillExpenseStand")
    @ApiOperation(value = "查询申请单费用标准")
    public Response<List<ExpStandSnapshotsFrontVO>> getReqBillExpenseStand(@RequestBody @Validated PcxReqBillExpenseStandQO qo){
        try {
            CheckMsg<List<ExpStandSnapshotsFrontVO>> voidCheckMsg = pcxBillService.getReqBillExpenseStand(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查询申请单费用标准异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询申请单费用标准异常");
        }
    }

    /**
     * 暂存接口不进行校验只做数据的保存
     * @param qo
     * @return
     */
    @PostMapping(value = "/saveTemp")
    @TxTraceable(business = "单据数据保存Temp")
    @ApiOperation(value = "单据数据保存")
    public Response<Map<String,Object>> saveTempData(@RequestBody PcxBillQO qo){
        try {
            fillBizType(qo);
            //构建对象
            qo.buildInstance();
//            log.info("saveTempData:{}", JSON.toJSONString(qo));
            PcxBill pcxBill = pcxBillService.saveOrUpdate(qo);
            return Response.success(MapUtil.of("billId",pcxBill.getId()));
        }catch (WarningTipsException e){
            log.error("保存单据异常 req {}",qo,e);
            return Response.commonResponse(Response.STATUS_BIZ_NEED_CONFIRM, e.getMessage());
        } catch (ForbidTipsException e){
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("保存单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "保存单据异常," + e.getMessage());
        }
    }

    private void fillBizType(PcxBillQO qo){
        Integer bizType = null;
        PcxBill pcxBill = JSONObject.parseObject(JSONObject.toJSONString(qo.getBasicInfo()), PcxBill.class);
        if (Objects.isNull(pcxBill) && StringUtil.isNotEmpty(qo.getBillId())){
            PcxBill bill = pcxBillDao.selectById(qo.getBillId());
            if (Objects.nonNull(bill)){
                bizType = bill.getBizType();
            }
        }else if (StringUtil.isNotEmpty(qo.getFiscal(), qo.getAgyCode(), qo.getMofDivCode(), qo.getItemCode())){
            PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
            pcxBasExpTypeQO.setFiscal(qo.getFiscal());
            pcxBasExpTypeQO.setAgyCode(qo.getAgyCode());
            pcxBasExpTypeQO.setMofDivCode(qo.getMofDivCode());
            pcxBasExpTypeQO.setItemCode(qo.getItemCode());
            List<PcxBasExpType> pcxBasExpTypes = pcxBasExpTypeDao.selectItemExpenseTypeList(pcxBasExpTypeQO);
            ItemBizTypeEnum bizTypeEnum = ItemBizTypeEnum.COMMON;
            if (CollectionUtils.isNotEmpty(pcxBasExpTypes)) {
                bizTypeEnum = ItemBizTypeEnum.expenseTypeMatchBizType(pcxBasExpTypes.get(0).getExpenseCode());
            }
            bizType = bizTypeEnum.getCode();
        }
        qo.setBizType(bizType);
    }

    /**
     * 数据保存接口数据需要校验
     * @param qo
     * @return
     */
    @TxTraceable(business = "单据数据保存")
    @PostMapping(value = "/save")
    @ApiOperation(value = "单据数据保存")
    public Response<?> saveDataAndApproved(@RequestBody @Validated(BillSave.class) PcxBillQO qo){
        try {
            fillBizType(qo);
            //构建对象
            qo.buildInstance();
//            log.info("saveData:{}", JSON.toJSONString(qo));
            CheckMsg<Map<String,Object>> voidCheckMsg = pcxBillService.saveDataAndApproved(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(voidCheckMsg.getErrorCode().toString(), voidCheckMsg.getMsgInfo());
            }

            Map<String, Object> result = voidCheckMsg.getData();
            return Response.success(result);
        } catch (WarningTipsException e){
            log.error("保存单据异常 req {}",qo,e);
            return Response.commonResponse(Response.STATUS_BIZ_NEED_CONFIRM, e.getMessage());
        } catch (ForbidTipsException e){
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("保存单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "保存单据异常," + e.getMessage());

        }
    }

    /**
     * 数据保存接口数据需要校验
     * @param qo
     * @return
     */
    @PostMapping(value = "/approved")
    @ApiOperation(value = "单据送审")
    public Response<?> approved(@RequestBody PcxBillApprovedVO qo){
        try {
            CheckMsg<Map<String,Object>> voidCheckMsg = pcxBillService.approved(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (WarningTipsException e){
            log.error("送审单据异常 req {}",qo,e);
            return Response.commonResponse(Response.STATUS_BIZ_NEED_CONFIRM, e.getMessage());
        } catch (ForbidTipsException e){
            log.error("送审单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("送审单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "保存单据异常," + e.getMessage());
        }
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除单据")
    @TxTraceable(business = "删除单据")
    public Response<Void> delete(@RequestBody @Validated(Delete.class) PcxBillQO qo) {
        try {
            CheckMsg<Void> msg = pcxBillService.deleteData(qo);
            Assert.state(msg.isSuccess(), "单据删除失败, {}", msg.getMsgInfo());
            Response<Void> success = Response.success();
            success.setMsg(msg.getMsgInfo());
            return success;
        }catch (Exception e){
            log.error("删除单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }

    @ApiOperation(value = "获取出纳还款列表")
    @PostMapping(value = "/getRepaymentList")
    public Response<PageResult<PcxBillRepaymentVO>> getRepaymentList(@RequestBody PcxBillRepaymentQO qo) {
        try {
            return Response.success(pcxBillService.getPcxBillRepaymentList(qo));
        }catch (Exception e){
            log.error("获取出纳还款列表异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }

    @ApiOperation(value = "根据借款单ID获取还款单据")
    @PostMapping(value = "/getRepaymentListByBillId")
    public Response<List<PcxBillRepaymentVO>> getRepaymentListByBillId(@RequestBody String billId) {
        try {
            return Response.success(pcxBillService.getPcxBillRepaymentList(billId));
        }catch (Exception e){
            log.error("根据借款单ID获取还款单据异常 req {}",billId,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }


    @GetMapping("/downloadAllAttachZip/{attachId}")
    @ApiResponses({@ApiResponse(code = 200, response = File.class, message = "")})
    @IgnoreAuthe
    @TicketAuth
    @ApiOperation(value = "下载附件")
    public ResponseEntity<byte[]> downloadAllAttachZip(@PathVariable("attachId") String attachId, HttpServletResponse response) {
        try {

            ResponseEntity<byte[]> result = cacheManager.getCache(DOWNLOAD_ATTACH_CACHE).get(attachId, ResponseEntity.class);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=download_file.zip")
                    .body(result.getBody());
        }catch (Exception e) {
            log.error("下载附件异常 req {}",attachId,e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    @PostMapping("/zipAttachByBillIds")
    @ApiOperation(value = "根据单据ID批量生成下载文件")
    public Response<String> zipAttachByBillIds(@RequestBody List<String> billIds) {
        try {
            UUID uuid = UUID.fastUUID();
            ResponseEntity<byte[]> responseEntity = pcxBillService.downAllBillAttachZip(billIds);
            Cache cache = cacheManager.getCache(DOWNLOAD_ATTACH_CACHE);
            if (cache != null) {
                cache.put(uuid.toString(), responseEntity);
                return Response.success(uuid.toString());
            }
            return Response.commonResponse(Response.FAIL_CODE, "缓存附件信息异常");
        }catch (Exception e) {
            log.error("生成附件字节数据异常 req {}",billIds,e);
            return Response.commonResponse(Response.FAIL_CODE, "下载附件异常");
        }
    }


    /**
     * 单据数据回显
     * @param qo
     * @return
     */
    @PostMapping(value = "/view")
    @ApiOperation(value = "单据数据回显")
    public Response<PcxBillVO> view(@RequestBody @Validated(BillView.class) PcxBillViewQO qo){
        try {
            log.info("view:{}", JSON.toJSONString(qo));
            ThreadLocalUtil.set(qo.getPositionCode());
            CheckMsg<PcxBillVO> view = pcxBillService.view(qo.getBillId(), qo.getViewType());
            if (!view.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, view.getMsgInfo());
            }
            pcxBillViewWrapperUtil.wrap(qo,view.getData());
            return Response.success(view.getData());
        }catch (Exception e){
            log.error("回显单据数据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "回显单据数据异常");
        }finally {
            ThreadLocalUtil.remove();
        }
    }

    /**
     * 单据数据回显
     * @param qo
     * @return
     * @deprecated 废弃
     * @see com.pty.pcx.controller.financeapproval.PcxFinanceApprovalController#next(PcxFinanceApprovalBillQO)
     */
    @Deprecated
    @PostMapping(value = "/next")
    @ApiOperation(value = "下一待办单据详情")
    public Response<String> next(@RequestBody @Validated(BillView.class) PcxBillViewQO qo){
        try {
            log.info("next:{}", JSON.toJSONString(qo));
            CheckMsg<String> next = pcxBillService.next(qo);
            if (!next.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, next.getMsgInfo());
            }
            return Response.success(next.getData());
        }catch (Exception e){
            log.error("回显单据数据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "回显单据数据异常");
        }
    }

    @PostMapping(value = "selectPage")
    @ApiOperation(value = "查询单据列表")
    public Response<PcxBillPageResult<PcxBillListVO>> selectPage(@RequestBody PcxBillListQO qo){
        try {
            CheckMsg<PcxBillPageResult<PcxBillListVO>> msg = pcxBillService.selectWithPage(qo);
            Assert.state(msg.isSuccess(), "{}", msg.getMsgInfo());
            return Response.success(msg.getData());
        }catch (Exception e){
            log.error("查询单据列表异常 req {}", qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询单据列表异常");
        }
    }

    @PostMapping(value = "selectExpLedgerPage")
    @ApiOperation(value = "查询支出台账列表")
    public Response<PageResult<PcxBillExpLedgerVO>> selectExpLedgerPage(@RequestBody PcxBillListQO qo){
        try {
            CheckMsg<PageResult<PcxBillExpLedgerVO>> msg = pcxBillService.selectExpLedgerPage(qo);
            Assert.state(msg.isSuccess(), "{}", msg.getMsgInfo());
            return Response.success(msg.getData());
        } catch (IllegalArgumentException e){
            log.error("查询支出台账列表异常 req {}", qo, e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("查询支出台账列表异常 req {}", qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询支出台账列表异常");
        }
    }

    @ApiOperation(value = "缓存支出台账列表导出请求参数", notes = "缓存支出台账列表导出请求参数")
    @PostMapping(value = "/excelParams")
    public Response saveExcelParams(@RequestBody PcxBillExportQO qo) {
        String id = StringUtil.getUUID();
        cacheManager.getCache(EXP_LEDGER_EHCACHE_EXCEL_NAME).put(id, qo);
        return Response.success().setData(id);
    }

    @ApiOperation("支出台账列表导出")
    @GetMapping("/excel/export")
    @ApiResponses({@ApiResponse(code = 200, response = File.class, message = "")
    })
    @IgnoreAuthe
    @TicketAuth
    public ResponseEntity<byte[]> exportData(@RequestParam("id") String id) throws Exception {
        PcxBillExportQO qo = (PcxBillExportQO)cacheManager.getCache(EXP_LEDGER_EHCACHE_EXCEL_NAME).get(id).get();
        return pcxBillService.exportData(qo);
    }

    /**
     * 获取单据金额统计数据
     */
    @PostMapping(value = "getAmtSum")
    @ApiOperation(value = "获取单据金额统计数据")
    public Response<Map<String, BigDecimal>> getAmtSum(@RequestBody PcxBillListQO qo){
        try {
            CheckMsg<Map<String, BigDecimal>> msg = new CheckMsg<>();
            msg = pcxBillService.getAmtSum(qo);
            Assert.state(msg.isSuccess(), "{}", msg.getMsgInfo());
            return Response.success(msg.getData());
        } catch (IllegalArgumentException e){
            log.error("获取单据金额统计数据异常 req {}", qo, e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("获取单据金额统计数据异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "获取单据金额统计数据异常");
        }
    }

    /**
     * 我的待办数量
     */
    @PostMapping(value = "myUnAuditNum")
    @ApiOperation(value = "我的待办数量")
    public Response<Map<String, Long>> myUnAuditNum(@RequestBody PcxBillListQO qo){
        try {
            CheckMsg<Integer> msg = pcxBillService.todoTaskNum(qo);
            CheckMsg<Map<String, Long>> billFuncTaskMsg = pcxBillService.billFuncTaskNum(qo);
            Assert.state(msg.isSuccess(), "{}", msg.getMsgInfo());
            Assert.state(billFuncTaskMsg.isSuccess(), "{}", billFuncTaskMsg.getMsgInfo());
            billFuncTaskMsg.getData().put(PcxBillProcessConstant.BillIndexLabel.APPROVE.getCode(), msg.getData().longValue());
            return Response.success(billFuncTaskMsg.getData());
        }catch (Exception e){
            log.error("查询我的待办数量异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询我的待办数量异常");
        }
    }

    /**
     * 查询当前单位是否有启用申请/借款单据
     */
    @PostMapping(value = "/getEnableBillType")
    @ApiOperation(value = "查看当前单位是否有启用申请/借款单据")
    public Response getEnableBillType(@RequestBody PcxMadBaseQO qo){
        try {
            CheckMsg checkMsg = pcxBillService.getEnableBillType(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询当前单位是否有启用申请/借款单据异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询当前单位是否有启用申请/借款单据异常");
        }
    }

    @PostMapping(value = "/selectDefaultSettlement")
    @ApiOperation(value = "查询默认结算方式")
    public Response<Map<String, List<PcxBillSettlement>>> selectDefaultSettlement(@RequestBody DefaultSettlementQO qo){

        try {
            CheckMsg<Map<String, List<PcxBillSettlement>>> checkMsg = pcxBillService.selectDefaultSettlement(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询默认结算方式异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询当前单位是否有启用申请/借款单据异常");
        }
    }

    @PostMapping(value = "/defaultLabourSettlement")
    @ApiOperation(value = "查询默认劳务结算方式")
    public Response<Map<String, List<PcxBillSettlement>>> defaultLabourSettlement(@RequestBody DefaultSettlementQO qo){
        try {
            CheckMsg<Map<String, List<PcxBillSettlement>>> checkMsg = pcxBillService.defaultLabourSettlement(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询默认结算方式异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询当前单位是否有启用申请/借款单据异常");
        }
    }

    /**
     * 查询本人年度报销金额和笔数
     */
    @PostMapping(value = "/getMyFiscalExpenseInfo")
    @ApiOperation(value = "查询本人年度报销金额和笔数")
    public Response<PcxMyFiscalExpenseVO> getMyFiscalExpenseInfo(@RequestBody @Validated PcxMyFiscalExpenseQO qo){
        try {
            CheckMsg<PcxMyFiscalExpenseVO> checkMsg = pcxBillService.getMyFiscalExpenseInfo(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("查询本人年度报销金额和笔数异常 req {}",qo, e);
            return Response.commonResponse(Response.FAIL_CODE, "查询当前单位是否有启用申请/借款单据异常");
        }
    }


    @PostMapping(value = "/getFinalBill")
    @ApiOperation(value = "获取终审未结项申请单据")
    public Response<Map<String,Object>> getFinalBill(@RequestBody PcxBillQO qo){
        try {
            CheckMsg<Map<String,Object>> checkMsg = pcxBillService.getFinalBill(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("获取终审未结项申请单据异常 {} ",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }

    @PostMapping(value = "/saveRel")
    @ApiOperation(value = "保存关联单据")
    @TxTraceable(business = "保存关联单据")
    public Response saveRel(@RequestBody PcxBillRelationQO qo){
        try {
            CheckMsg voidCheckMsg = pcxBillService.saveRel(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success().setMsg("保存关联单据成功").setData(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("保存关联单据异常 {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "保存关联单据异常");
        }
    }

    @PostMapping(value = "/changeBalance")
    @ApiOperation(value = "更换指标")
    @TxTraceable(business = "更换指标")
    public Response changeBalance(@RequestBody @Validated(Update.class) PcxBillQO qo){
        try {
            CheckMsg voidCheckMsg = pcxBillService.changeBalance(qo, Objects.isNull(qo.getIsValidate())?null: qo.getIsValidate() == PubConstant.LOGIC_TRUE);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(voidCheckMsg.getErrorCode().toString(), voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (WarningTipsException e){
            log.error("更换指标异常 req {}",qo,e);
            return Response.commonResponse(Response.STATUS_BIZ_NEED_CONFIRM, e.getMessage());
        } catch (ForbidTipsException e){
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        } catch (Exception e){
            log.error("更换指标异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "更换指标异常," + e.getMessage());

        }
    }

    /**
     * 获取单据的基本信息
     * @param qo
     * @return
     */
    @PostMapping(value = "/basicInfo")
    @ApiOperation(value = "单据基础信息获取")
    public Response<PcxBillBasicVO> basicInfo(@RequestBody PcxBillQO qo) {
        try {
            PcxBillBasicVO vo = pcxBillService.basicInfo(qo.getBillId());
            if (Objects.isNull(vo)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }else {
                return Response.success(vo);
            }

        }catch (Exception e){
            log.error("获取单据基本信息异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }

    @PostMapping(path = "/setSettle")
    @ApiOperation(value = "结项(单据批量结项)")
    @TxTraceable(business = "结项(单据批量结项)")
    public Response setSettle(@RequestBody PcxBillQO qo){
        try {
            CheckMsg<?> check = pcxBillService.setSettle(qo);
            if (check.isSuccess()) {
                return Response.success().setMsg(check.getMsgInfo()).setData(check.getData());
            }
            return Response.fail().setMsg(check.getMsgInfo()).setData(check.getData());
        } catch (Exception e) {
            log.error("结项异常：", e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "还款")
    @PostMapping(value = "/addRepayment")
    @TxTraceable(business = "还款")
    public Response addRepayment(@RequestBody PcxBillAddRepaymentQO qo) {
        try {
            CheckMsg checkMsg = pcxBillService.addRepayment(qo);
            return Response.success(checkMsg.getData());
        } catch (Exception e){
            log.error("还款异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, e.getMessage());
        }
    }

    @ApiOperation(value = "费用办结列表查询")
    @PostMapping(value = "/getApplyBill")
    public Response<PageResult<PcxBill>> getApplyBill(@RequestBody PcxBillQueryQO qo) {
        try{
            CheckMsg<PageResult<PcxBill>> checkMsg = pcxBillService.getApplyBill(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("费用办结异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE,"查询失败");
        }
    }


    @ApiOperation(value = "费用办结")
    @PostMapping(value = "/finishBill")
    @TxTraceable(business = "费用办结")
    public Response finishBill(@RequestBody PcxBillExpenseCompletedQO qo) {

        CheckMsg<Map<String, Object>> checkMsg = pcxBillService.finishBill(qo);
        return Response.success(checkMsg.getData());
    }


    /**
     * 发起通用报销
     * @param expenseQO
     * @return
     */
    @PostMapping(value = "/startCommonExpense")
    @ApiOperation(value = "发起通用报销")
    @TxTraceable(business = "发起通用报销")
    public Response<?> startCommonExpense(@RequestBody @Validated(value = {StartEcsCommon.class}) StartExpenseQO expenseQO){
        try {
            String expenseCode = expenseStrategyContext.getExpenseCodeFromItemCode(expenseQO);
            ExpenseStrategy strategy = expenseStrategyContext.getStrategy(expenseCode);
            CheckMsg<?> voidCheckMsg = strategy.handleExpense(expenseQO);
            if (!voidCheckMsg.isSuccess()) {
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (Exception e) {
            log.error("发起通用报销异常 req {}", expenseQO, e);
            return Response.commonResponse(Response.FAIL_CODE, "发起通用报销异常");
        }
    }




    /**
     * 通用报销添加票
     * @param addInvoicesQO
     * @return
     */
    @PostMapping(value = "/addEcsCommonBill")
    @ApiOperation(value = "通用报销添加票")
    @TxTraceable(business = "通用报销添加票")
    public Response<?> addEcsCommonBill(@RequestBody  @Validated(value = {AddEcs.class}) AddInvoicesQO addInvoicesQO){
        try {
            String expenseCode = expenseStrategyContext.getExpenseCodeFromItemCode(addInvoicesQO);
            ExpenseStrategy strategy = expenseStrategyContext.getStrategy(expenseCode);
            CheckMsg<?> voidCheckMsg = strategy.handleAddBill(addInvoicesQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销添加票异常 req {}",addInvoicesQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销添加票异常");
        }
    }

    /**
     * 通用报销删除票
     * @param delEcsCommonQO
     * @return
     */
    @PostMapping(value = "/delEcsCommonExpense")
    @ApiOperation(value = "通用报销删除票")
    @TxTraceable(business = "通用报销删除票")
    public Response<?> delEcsCommonExpense(@RequestBody @Validated(value = {DelEcs.class}) DelEcsCommonQO delEcsCommonQO){
        try {
            String expenseCode = expenseStrategyContext.getExpenseCodeByBillId(delEcsCommonQO);
            ExpenseStrategy strategy = expenseStrategyContext.getStrategy(expenseCode);
            CheckMsg<?> voidCheckMsg = strategy.handleDelete(delEcsCommonQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销删除票异常 req {}",delEcsCommonQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销删除异常");
        }
    }

    /**
     * 通用报销编辑票
     * @param updateEcsCommonQO
     * @return
     */
    @PostMapping(value = "/updateEcsCommon")
    @ApiOperation(value = "通用报销编辑票")
    @TxTraceable(business = "通用报销编辑票")
    public Response<?> updateEcsCommon(@RequestBody @Validated(value = {UpdateEcsDetail.class}) UpdateEcsCommonQO updateEcsCommonQO){
        try {
            String expenseCode = expenseStrategyContext.getExpenseCodeFromItemCode(updateEcsCommonQO);
            ExpenseStrategy strategy = expenseStrategyContext.getStrategy(expenseCode);
            CheckMsg<?> voidCheckMsg = strategy.handUpdateBill(updateEcsCommonQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销编辑票异常 req {}",updateEcsCommonQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销编辑票异常");
        }
    }

    @PostMapping(value = "/ecsExpCommonView")
    @ApiOperation(value = "通用报销票明细")
    public Response<?> ecsExpCommonView(@RequestBody @Validated(value = {EcsExpView.class}) UpdateEcsCommonQO updateEcsCommonQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsExpCommonView(updateEcsCommonQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销票明细异常 req {}",updateEcsCommonQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销票明细异常");
        }
    }

    @PostMapping(value = "/commonBillExpenseTypeList")
    @ApiOperation(value = "通用报销费用类型")
    public Response<?> commonBillExpenseTypeList(@RequestBody @Valid CommonExpenseTypeQO qo){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.commonBillExpenseTypeList(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销费用类型查询异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销费用类型查询异常");
        }
    }
    /**
     * 添加无票通用费用
     * @param updateNoEcsCommonQO
     * @return
     */
    @PostMapping(value = "/updateNoEcsCommon")
    @ApiOperation(value = "添加无票通用费用")
    @TxTraceable(business = "添加无票通用费用")
    public Response<?> updateNoEcsCommon(@RequestBody @Validated(value = {AddNoEcsDetail.class}) UpdateNoEcsCommonQO updateNoEcsCommonQO){
        try {
            String expenseCode = expenseStrategyContext.getExpenseCodeNoEcs(updateNoEcsCommonQO);
            ExpenseStrategy strategy = expenseStrategyContext.getStrategy(expenseCode);
            CheckMsg<?> voidCheckMsg = strategy.updateNoEcs(updateNoEcsCommonQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("添加无票通用费用异常 req {}",updateNoEcsCommonQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "添加无票通用费用异常");
        }
    }

    /**
     * 审核岗编辑票费用类型
     * @param ecsRelSumQO
     * @return
     */
    @PostMapping(value = "/updateEcsExpType")
    @ApiOperation(value = "审核岗编辑票费用")
    @TxTraceable(business = "审核岗编辑票费用")
    public Response<?> updateEcsExpType(@RequestBody @Validated EcsRelSumQO ecsRelSumQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateEcsExpType(ecsRelSumQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("编辑票费用类型异常 req {}",ecsRelSumQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "编辑票费用类型异常");
        }
    }

    /**
     * 审核岗编辑票费用类型新
     * @param ecsRelSumQO
     * @return
     */
    @PostMapping(value = "/updateEcsExpTypeNew")
    @ApiOperation(value = "审核岗编辑票费用新")
    @TxTraceable(business = "审核岗编辑票费用新")
    public Response<?> updateEcsExpTypeNew(@RequestBody @Validated EcsRelSumQO ecsRelSumQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateEcsExpTypeNew(ecsRelSumQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("编辑票费用类型异常 req {}",ecsRelSumQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "编辑票费用类型异常");
        }
    }

    /**
     * 审核岗查看票列表
     * @param qo
     * @return
     */
    @PostMapping(value = "/ecsRelList")
    @ApiOperation(value = "审核岗查看票列表")
    public Response<?> ecsRelList(@RequestBody QueryEcsRelItemQO qo){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsRelList(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查看票列表异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查看票列表异常");
        }
    }

    /**
     * 审核岗查看票项目
     * @param qo
     * @return
     */
    @PostMapping(value = "/ecsRelItem")
    @ApiOperation(value = "审核岗查看票项目")
    public Response<?> ecsRelItem(@RequestBody @Validated QueryEcsRelItemQO qo){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.ecsRelItem(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查看票项目异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查看票项目异常");
        }
    }

    @PostMapping(value = "/calculateTaxAudit")
    @ApiOperation(value = "通用报销计算进项税")
    public Response<?> calculateTaxAudit(@RequestBody @Valid EcsCalculateTaxQO qo){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.calculateTaxAudit(qo);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("通用报销计算进项税异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "通用报销计算进项税异常");
        }
    }

    /**
     * 绑定合同
     * @param bindContractQO
     * @return
     */
    @PostMapping(value = "/bindContract")
    @ApiOperation(value = "报销单绑定合同")
    @TxTraceable(business = "报销单绑定合同")
    public Response<?> bindContract(@RequestBody @Validated(value = {BindContract.class}) BindContractQO bindContractQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.bindContract(bindContractQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("报销单绑定合同异常 req {}",bindContractQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "报销单绑定合同异常");
        }
    }

    /**
     * 解绑合同
     * @param bindContractQO
     * @return
     */
    @PostMapping(value = "/unbindContract")
    @ApiOperation(value = "报销单解绑合同")
    @TxTraceable(business = "报销单解绑合同")
    public Response<?> unbindContract(@RequestBody @Validated(value = {UnBindContract.class}) BindContractQO bindContractQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.unbindContract(bindContractQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("报销单解绑合同异常 req {}",bindContractQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "报销单解绑合同异常");
        }
    }

    /**
     * 更新票附件
     * @param updateEcsAttachQO
     * @return
     */
    @PostMapping(value = "/updateEcsAttach")
    @ApiOperation(value = "更新票附件")
    @TxTraceable(business = "更新票附件")
    public Response<?> updateEcsAttach(@RequestBody @Validated(value = {UpdateEcsDetail.class}) UpdateEcsAttachQO updateEcsAttachQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateEcsAttach(updateEcsAttachQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("更新票附件异常 req {}",updateEcsAttachQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新票附件异常");
        }
    }

    /**
     * 未匹配的票补充行程
     * @param unMatchEcsReplenishQO
     * @return
     */
    @PostMapping(value = "/unMatchEcsReplenishTrip")
    @ApiOperation(value = "未匹配的票补充行程")
    public Response<?> unMatchEcsReplenishTrip(@RequestBody @Valid UnMatchEcsReplenishQO unMatchEcsReplenishQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.unMatchEcsReplenishTrip(unMatchEcsReplenishQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("未匹配的票补充行程异常 req {}",unMatchEcsReplenishQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "未匹配的票补充行程异常");
        }
    }

    @Autowired
    private PcxBillTravelFellowsService pcxBillTravelFellowsService;

    /**
     * 外部人员列表
     * @param outerListQO
     * @return
     */
    @PostMapping(value = "/outerList")
    @ApiOperation(value = "外部人员列表")
    public Response<?> outerList(@RequestBody @Valid OuterListQO outerListQO){
        try {
            CheckMsg<?> voidCheckMsg = pcxBillTravelFellowsService.outerList(outerListQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("外部人员列表异常 req {}",outerListQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "未匹配的票补充行程异常");
        }
    }

    /**
     * 更新行程时间
     * @param updateTripTimeQO
     * @return
     */
    @PostMapping(value = "/updateTripTime")
    @ApiOperation(value = "更新行程时间")
    @TxTraceable(business = "更新行程时间")
    public Response<?> updateTripTime(@RequestBody @Valid UpdateTripTimeQO updateTripTimeQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateTripTime(updateTripTimeQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        } catch (Exception e){
            log.error("发起报销异常 req {}",updateTripTimeQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "发起报销异常");
        }
    }

    /**
     * 更新补充信息
     * @param updateEcsQO
     * @return
     */
    @PostMapping(value = "/updateAddition")
    @ApiOperation(value = "更新补充信息")
    @TxTraceable(business = "更新补充信息")
    public Response<?> updateAddition(@RequestBody @Valid UpdateAdditionQO updateEcsQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.updateAddition(updateEcsQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("更新补充信息异常 req {}",updateEcsQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新补充信息异常");
        }
    }

    /**
     * 查询行程节点补充信息
     * @param queryDetailAdditionQO
     * @return
     */
    @PostMapping(value = "/queryDetailAddition")
    @ApiOperation(value = "查询行程节点补充信息")
    public Response<?> queryDetailAddition(@RequestBody QueryDetailAdditionQO queryDetailAdditionQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.queryDetailAddition(queryDetailAdditionQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("查询行程节点补充信息异常 req {}",queryDetailAdditionQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询行程节点补充信息异常");
        }
    }

    /**
     * 费用查询补充信息
     * @param queryDetailAdditionQO
     * @return
     */
    @PostMapping(value = "/queryExpenseTypeAddition")
    @ApiOperation(value = "费用查询补充信息")
    public Response<?> queryExpenseTypeAddition(@RequestBody QueryExpenseTypeAdditionQO queryDetailAdditionQO){
        try {
            CheckMsg<?> voidCheckMsg = ecsProcessService.queryExpenseTypeAddition(queryDetailAdditionQO);
            if (!voidCheckMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, voidCheckMsg.getMsgInfo());
            }
            return Response.success(voidCheckMsg.getData());
        }catch (Exception e){
            log.error("费用查询补充信息异常 req {}",queryDetailAdditionQO,e);
            return Response.commonResponse(Response.FAIL_CODE, "费用查询补充信息异常");
        }
    }

    @PostMapping(value = "/loadBalanceDefault")
    @ApiOperation(value = "加载默认指标的其余数据")
    public Response<?> loadBalanceDefault(@RequestBody PcxBillBalanceMergeVO pcxBillBalanceMergeVO){
        try {
            PcxBill pcxBill = billMainService.view(pcxBillBalanceMergeVO.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            //岗位信息判断
            String positionCode;
            //组装数据
            List<PcxBillBalanceVO> emptyList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(pcxBillBalanceMergeVO.getBatchExpense())){
                positionCode = PositionEnum.FINANCE_AUDIT.getCode();
            } else {
                positionCode = PositionEnum.MAKE_BILL.getCode();
            }
            //根据费用分摊加载经费来源
            if (PositionEnum.MAKE_BILL.getCode().equals(positionCode)){
                if (!Objects.equals(ItemBizTypeEnum.COMMON.getCode(),pcxBill.getBizType())){
                    emptyList = pcxBillViewWrapperUtil.loadDefaultByExpense(pcxBill.getExpenseCodes(), pcxBill.getExpenseNames(), pcxBill);
                    pcxBillViewWrapperUtil.reLoadDefault(emptyList,pcxBill);
                }else {
                    pcxBillViewWrapperUtil.reLoadDefault(emptyList,pcxBill);
                }
            }else {
                for (PcxBillBalanceMergeVO.ExpenseCodeAndAmt expenseCodeAndAmt : pcxBillBalanceMergeVO.getBatchExpense()) {
                    List<PcxBillBalanceVO> pcxBillBalanceVOS = pcxBillViewWrapperUtil.loadDefaultByExpense(expenseCodeAndAmt.getExpenseCode(), expenseCodeAndAmt.getExpenseName(), pcxBill);
                    emptyList.addAll(pcxBillBalanceVOS);
                }
            }
            //转换对象并加载默认数据
            List<PcxBillBalanceMergeVO> pcxBillBalanceMergeVOS = FundSourceFlushUtil.balanceToMerge(emptyList,positionCode);
            List<PcxBillBalanceMergeVO> pcxBillBalanceMergeVOS1 = pcxBillViewWrapperUtil.loadDefaultBalance(pcxBillBalanceMergeVOS, positionCode, pcxBill);
            return Response.success(pcxBillBalanceMergeVOS1);
        }catch (Exception e){
            log.error("加载指标默认值失败 req {}",pcxBillBalanceMergeVO,e);
            return Response.commonResponse(Response.FAIL_CODE, "加载指标默认值失败");
        }
    }



    @PostMapping(value = "/queryTravelAmtApportionList")
    @ApiOperation(value = "查询差旅费费用分摊列表")
    public Response<?> queryTravelAmtApportionList(@RequestBody @Valid ApportionBaseQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.queryTravelAmtApportionList(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("查询差旅费费用分摊列表 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询差旅费费用分摊列表失败");
        }
    }

    @PostMapping(value = "/queryCommonAmtApportionList")
    @ApiOperation(value = "查询通用报销费用分摊列表")
    public Response<?> queryCommonAmtApportionList(@RequestBody @Valid ApportionBaseQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.queryCommonAmtApportionList(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("查询通用报销费用分摊列表失败 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询通用报销费用分摊列表失败");
        }
    }

    @PostMapping(value = "/updateTravelAmtApportion")
    @ApiOperation(value = "更新差旅费费用分摊列表")
    @TxTraceable(business = "更新差旅费费用分摊列表")
    public Response<?> updateTravelAmtApportion(@RequestBody @Valid TravelUpdateApportionQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.updateTravelAmtApportion(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("更新差旅费费用分摊列表异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新差旅费费用分摊列表失败");
        }
    }

    @PostMapping(value = "/updateCommonAmtApportion")
    @ApiOperation(value = "更新通用报销费用分摊列表")
    @TxTraceable(business = "更新通用报销费用分摊列表")
    public Response<?> updateCommonAmtApportion(@RequestBody @Valid CommonUpdateApportionQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.updateCommonAmtApportion(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("更新差旅费费用分摊列表异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新差旅费费用分摊列表失败");
        }
    }

    @PostMapping(value = "/switchApportionType")
    @ApiOperation(value = "切换分摊模式")
    @TxTraceable(business = "切换分摊模式")
    public Response<?> switchApportionType(@RequestBody @Valid SwitchApportionTypeQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.switchApportionType(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("切换分摊模式异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "切换分摊模式失败");
        }
    }

    @PostMapping(value = "/queryTravelApportionDetail")
    @ApiOperation(value = "查询差旅补充信息明细")
    public Response<?> queryTravelApportionDetail(@RequestBody @Valid QueryTravelApportionDetailQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.queryTravelApportionDetail(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("查询差旅补充信息明细异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询差旅补充信息明细失败");
        }
    }

    @PostMapping(value = "/updateTravelApportionDetail")
    @ApiOperation(value = "更新差旅补充信息明细")
    @TxTraceable(business = "更新差旅补充信息明细")
    public Response<?> updateTravelApportionDetail(@RequestBody @Valid UpdateTravelApportionDetailQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.updateTravelApportionDetail(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("更新差旅补充信息明细异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "更新差旅补充信息明细失败");
        }
    }

    @PostMapping(value = "/queryCommonApportionDetail")
    @ApiOperation(value = "查询通用补充信息明细")
    public Response<?> queryCommonApportionDetail(@RequestBody @Valid QueryCommonApportionDetailQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.queryCommonApportionDetail(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("查询通用补充信息明细异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询通用补充信息明细失败");
        }
    }

    @PostMapping(value = "/updateCommonApportionDetail")
    @ApiOperation(value = "更新通用补充信息明细")
    @TxTraceable(business = "更新通用补充信息明细")
    public Response<?> updateCommonApportionDetail(@RequestBody @Valid UpdateCommonApportionDetailQO qo){
        try {
            PcxBill pcxBill = billMainService.view(qo.getBillId());
            if (Objects.isNull(pcxBill)){
                return Response.commonResponse(Response.FAIL_CODE, "单据不存在");
            }
            CheckMsg checkMsg = pcxBillAmtApportionService.updateCommonApportionDetail(qo, pcxBill);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());

        }catch (Exception e){
            log.error("查询通用补充信息明细异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "查询通用补充信息明细失败");
        }
    }

    @PostMapping(value = "/selectClaimant")
    @ApiOperation(value = "加载推荐的代报人")
    public Response<List<MadEmployeeDTO>> selectClaimant(@RequestBody @Valid PcxBillQO qo){
        try {
            String billId = qo.getBillId();
            List<MadEmployeeDTO> baseDataVo = pcxBillService.selectClaimant(billId);
            return Response.success(baseDataVo);
        }catch (Exception e){
            log.error("加载推荐的代报人 req {}",qo,e);
            return Response.success(Lists.newArrayList());
        }
    }


    @PostMapping(value = "/reOpenBill")
    @ApiOperation(value = "重新打开单据")
    @TxTraceable(business = "重新打开单据")
    public Response<?> reOpenBill(@RequestBody @Valid PcxBillQO qo){
        try {
            pcxBillService.reOpenBill(qo);
            return Response.success().setMsg("重新打开单据成功");
        }catch (Exception e){
            log.error("重新打开单据 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "重新打开单据失败");
        }
    }

    @PostMapping(value = "/calculateBillAmt")
    @ApiOperation(value = "重新计算单据的各种金额")
    public Response<?> calculateBillAmt(@RequestBody PcxBillCalculateQO qo){
        try {
            PcxBillVO pcxBillVO = pcxBillService.calculateBillAmt(qo);
            return Response.success(pcxBillVO);
        }catch (Exception e){
            //报错则原封返回数据
            log.error("重新计算单据金额异常 req {}",qo,e);
            return Response.success(qo);
        }
    }

    @PostMapping(value = "/initBill")
    @ApiOperation(value = "初始化单据")
    public Response<?> initBill(@RequestBody @Valid InitBillQO qo){
        try {
            CheckMsg checkMsg = pcxBillService.initBill(qo);
            if (!checkMsg.isSuccess()){
                return Response.commonResponse(Response.FAIL_CODE, checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData());
        }catch (Exception e){
            log.error("初始化单据异常 req {}",qo,e);
            return Response.commonResponse(Response.FAIL_CODE, "初始化单据异常");
        }
    }
}
