package com.pty.pcx.controller.calculationrule;

import com.pty.pcx.api.calculationrule.ICalculationRuleService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.vo.setting.PaBizRuleVO;
import com.pty.pub.common.bean.Response;
import com.pty.rule.entity.PaBizRule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.*;

/**
 * 计算规则控制层
 */
@Indexed
@RequestMapping(value = "/pcx/calculation", method = {RequestMethod.POST})
@Api(value = "计算规则接口", tags = {"计算规则控制层"})
@RestSchema(schemaId = "/pcxCalculationRuleController")
@RestController
@Slf4j
public class PcxCalculationRuleController {

    @Autowired
    private ICalculationRuleService calculationRuleService;

    /**
     * 查询计算规则数据
     * @param
     * @return 单条数据
     */
    @PostMapping(value = "/getData")
    @ApiOperation(value = "查询计算规则数据")
    public Response getData(@RequestBody PaBizRuleVO paBizRuleVO) {
        try{
            CheckMsg<PaBizRule> bizRule = this.calculationRuleService.getData(paBizRuleVO);
            return Response.commonResponse(bizRule.isSuccess(), bizRule.getMsgInfo(), bizRule.getData());
        }catch (Exception e){
            log.error("获取计算规则数据：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

    /**
     * 保存计算规则数据
     * @param
     * @return 单条数据
     */
    @PostMapping(value = "/saveData")
    @ApiOperation(value = "查询计算规则数据")
    public Response saveData(@RequestBody PaBizRuleVO paBizRuleVO) {
        try{
            this.calculationRuleService.saveData(paBizRuleVO);
            return Response.success();
        }catch (Exception e){
            log.error("保存计算规则失败：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }

}
