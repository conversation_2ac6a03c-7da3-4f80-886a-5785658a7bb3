package com.pty.pcx.controller.setting;

import com.pty.pcx.api.setting.IPcxBaseDataService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.setting.BaseDataQO;
import com.pty.pub.common.bean.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.servicecomb.provider.rest.common.RestSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Indexed
@RequestMapping(value = "/pcx/baseData")
@Api(value = "报销基础数据接口", tags = {"报销基础数据接口控制层"})
@Slf4j
@RestController
@RestSchema(schemaId = "/pcxBaseDataController")
public class PcxBaseDataController {

    @Autowired
    private IPcxBaseDataService pcxBaseDataService;

    @PostMapping(value = "/getInfo")
    @ApiOperation(value = "报销基础数据接口信息")
    public Response<?> getInfo(@RequestBody BaseDataQO qo) {
        try{
            CheckMsg<?> checkMsg = pcxBaseDataService.getInfo(qo);
            if(!checkMsg.isSuccess()){
                return Response.fail().setMsg(checkMsg.getMsgInfo());
            }
            return Response.success(checkMsg.getData()).setMsg(checkMsg.getMsgInfo());
        }catch (Exception e){
            log.error("获取统一接口信息：{}", e.getMessage(),e);
            return Response.fail().setMsg(e.getMessage());
        }
    }
}