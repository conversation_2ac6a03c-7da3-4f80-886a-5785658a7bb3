<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pty.pcx.dao.transfer.PcxBillSyncDao">

    <select id="selectExtractDraftPcxBills" resultType="com.pty.pcx.entity.bill.PcxBill">
        SELECT
            b.*
        FROM
            pcx_bill b
            LEFT JOIN pcx_bill_sync s ON b.id = s.bill_id
        WHERE
          s.bill_id IS NULL
          AND b.biz_type in (${@<EMAIL>}, ${@<EMAIL>})
          AND b.approve_status = '${@<EMAIL>}'
          AND b.agy_code in(SELECT field04 FROM mad_ext_data WHERE atom_code = 'KINGDEE_ERP_ORGANIZATION')
    </select>

    <select id="selectUnsyncedBills" resultType="com.pty.pcx.entity.transfer.PcxBillSync">
        SELECT
            s.*
        FROM
            pcx_bill_sync s
            LEFT JOIN pcx_bill b ON s.bill_id = b.id
        WHERE
          s.sync_status IN (${@com.pty.pcx.common.enu.PcxBillSyncStatus@DRAFT_PENDING_SYNC.code}, ${@com.pty.pcx.common.enu.PcxBillSyncStatus@DRAFT_SYNCED.code}, ${@com.pty.pcx.common.enu.PcxBillSyncStatus@DRAFT_SYNC_FAILED.code})
          AND b.approve_status = '${@<EMAIL>}'
          AND b.agy_code in(SELECT field04 FROM mad_ext_data WHERE atom_code = 'KINGDEE_ERP_ORGANIZATION')
    </select>

    <select id="selectUselessBills" resultType="com.pty.pcx.entity.transfer.PcxBillSync">
        SELECT
            s.*
        FROM
            pcx_bill_sync s
        LEFT JOIN pcx_bill b ON s.bill_id = b.id
        WHERE
            b.id IS NULL
            AND s.sync_status != ${@com.pty.pcx.common.enu.PcxBillSyncStatus@DELETE_FAILED.code}
    </select>

</mapper>
