package com.pty.pcx.service.impl.bill;

import com.pty.pcx.api.bill.PcxBillExpDetailAttachRelService;
import com.pty.pcx.dao.bill.PcxBillExpAttachRelDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;


/**
 * 费用明细与附件关联表(PcxBillExpDetailAttachRel)表服务实现类
 * <AUTHOR>
 * @since 2024-12-20 10:21:46
 */
@Slf4j
@Indexed
@Service
public class PcxBillExpAttachRelServiceImpl implements PcxBillExpDetailAttachRelService {

	@Autowired
	private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;

}


