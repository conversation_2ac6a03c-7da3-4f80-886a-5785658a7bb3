package com.pty.pcx.service.impl.bas;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.api.bas.PcxSettlementRuleExpService;
import com.pty.pcx.api.bas.PcxSettlementRuleService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.dao.bas.PcxSettlementRuleDao;
import com.pty.pcx.entity.bas.PcxSettlementRule;
import com.pty.pcx.entity.bas.PcxSettlementRuleExp;
import com.pty.pcx.qo.bas.PcxSettlementRuleExpQO;
import com.pty.pcx.qo.bas.PcxSettlementRuleQO;
import com.pty.pcx.qo.bas.PcxSettlementRuleUpdateQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.bas.PcxSettlementRuleExpVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算规则设置(PcxBasSettlementSetting)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-09 18:07:02
 */
@Slf4j
@Indexed
@Service("PcxSettlementRuleService")
public class PcxSettlementRuleServiceImpl implements PcxSettlementRuleService {
    @Autowired
    private PcxSettlementRuleDao pcxSettlementRuleDao;

    @Autowired
    private PcxSettlementRuleExpService pcxSettlementRuleExpService;

    @Autowired
    private BatchServiceUtil batchServiceUtil;
    @Override
    public List<BaseDataVo> selectEnable(PcxSettlementRule qo) {
        List<PcxSettlementRule> pcxSettlementRules = pcxSettlementRuleDao.selectList(Wrappers.lambdaQuery(PcxSettlementRule.class)
                .eq(PcxSettlementRule::getAgyCode, qo.getAgyCode())
                .eq(PcxSettlementRule::getIsEnabled, 1)
                .eq(PcxSettlementRule::getFiscal, qo.getFiscal())
                .eq(PcxSettlementRule::getMofDivCode, qo.getMofDivCode())
                .in(ObjectUtils.isNotEmpty(qo.getSettlementTypes()), PcxSettlementRule::getSettlementType, qo.getSettlementTypes()));
        return pcxSettlementRules.stream().map(item -> {
            BaseDataVo vo = new BaseDataVo();
            vo.setId(item.getId());
            vo.setCode(item.getSettlementType());
            vo.setName(item.getSettlementName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BaseDataVo> selectSettlementByExpenses(PcxSettlementRuleQO qo) {
        if (ObjectUtils.isEmpty(qo)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(qo.getExpenseCodes()) || qo.getExpenseCodes().size() > 1) {
            return getSettlementRules(qo);
        }
        PcxSettlementRuleExpQO ruleExpParam = new PcxSettlementRuleExpQO();
        BeanUtils.copyProperties(qo, ruleExpParam);
        ruleExpParam.setTenantId(StringUtil.isNotBlank(qo.getTenantId()) ? qo.getTenantId() : PtyContext.getTenantId());
        ruleExpParam.setExpenseCode(qo.getExpenseCodes().get(0));
        Response<PcxSettlementRuleExpVO> pcxSettlementRuleExpVOResponse = pcxSettlementRuleExpService.selectByExpenseCode(ruleExpParam);
        if (pcxSettlementRuleExpVOResponse.isSuccess() && ObjectUtils.isNotEmpty(pcxSettlementRuleExpVOResponse.getData())) {
            PcxSettlementRuleExpVO data = pcxSettlementRuleExpVOResponse.getData();
            if (StringUtil.isNotBlank(data.getAllowSettlement())) {
                List<String> expenseCodes = Arrays.asList(data.getAllowSettlement().split(","));
                qo.setSettlementTypes(expenseCodes);
                return getSettlementRules(qo);
            }
        }
        return getSettlementRules(qo);
    }

    private List<BaseDataVo> getSettlementRules(PcxSettlementRuleQO qo) {
        PcxSettlementRule pcxSettlementRule = new PcxSettlementRule();
        BeanUtils.copyProperties(qo, pcxSettlementRule);
        return this.selectEnable(pcxSettlementRule);
    }
    /**
     * 查询所有结算方式
     * @param qo
     * @return
     */
    @Override
    public List<PcxSettlementRule> selectAll(PcxSettlementRule qo) {
        return pcxSettlementRuleDao.selectList(Wrappers.lambdaQuery(PcxSettlementRule.class)
                .eq(PcxSettlementRule::getAgyCode, qo.getAgyCode())
                .eq(PcxSettlementRule::getFiscal, qo.getFiscal())
                .eq(PcxSettlementRule::getMofDivCode, qo.getMofDivCode()));
    }

    /**
     * 更新结算方式启用状态
     * @param qo
     */
    @Override
    public void update(PcxSettlementRuleUpdateQO qo) {
        List<PcxSettlementRule> updatePcxSettlementRuleList = qo.getPcxSettlementRuleList();
        PcxSettlementRule ruleParam = updatePcxSettlementRuleList.get(0);

        //查询已开启的结算方式
        Map<String, String> enableMapDatas = this.selectEnable(ruleParam).stream()
                .collect(Collectors.toMap(BaseDataVo::getCode, BaseDataVo::getName, (v1, v2) -> v1));

        //找出本次更新为停用的结算方式
        List<String> stopDatas = updatePcxSettlementRuleList.stream()
                .filter(item -> item.getIsEnabled() == 0 && enableMapDatas.containsKey(item.getSettlementType()))
                .map(PcxSettlementRule::getSettlementType)
                .collect(Collectors.toList());

        //查询费用类型特殊设置
        PcxSettlementRuleExpQO ruleExpParam = new PcxSettlementRuleExpQO();
        BeanUtils.copyProperties(ruleParam, ruleExpParam);
        List<PcxSettlementRuleExp> pcxSettlementRuleExps = pcxSettlementRuleExpService.selectAll(ruleExpParam);

        // 检查特殊设置中是否包含要停用的结算方式
        boolean hasIllegalSettings = pcxSettlementRuleExps.stream()
                .anyMatch(item -> stopDatas.stream().anyMatch(item.getAllowSettlement()::contains));

        if (hasIllegalSettings) {
            throw new RuntimeException("存在费用类型特殊设置中包含停用的结算规则，请先取消设置相应的规则");
        }
        batchServiceUtil.batchProcess(updatePcxSettlementRuleList, PcxSettlementRuleDao.class, PcxSettlementRuleDao::updateById);
    }
}
