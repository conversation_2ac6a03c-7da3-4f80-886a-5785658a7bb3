package com.pty.pcx.service.impl.wit.func;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.pty.mad.common.DaysType;
import com.pty.pcx.mad.IHolidaysExternalService;
import com.pty.pub.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class FnIsWorkDay extends BFunction {
    public static final String FUNC_NAME = "isWorkDay";


    @Override
    public String getName() {
        return FUNC_NAME;
    }

    public String method() {
        return "isWorkDay(days)";
    }

    @Override
    public String methodName() {
        return "判断某个日期或者多个日期是否是工作日";
    }

    @Override
    public String desc() {
        return "判断某个日期或者月份或者时间在两个日期之间 如：isWorkDay(days)";
    }


    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        try {
            Object one = arg1.getValue(env);
            IHolidaysExternalService madHolidaysExternalService = SpringUtil.getBean(IHolidaysExternalService.class);
            if (one instanceof String) {
                DateTime parse = DateUtil.parse(one.toString());
                DaysType daysType = madHolidaysExternalService.getDaysType(parse.toLocalDateTime().toLocalDate());
                return AviatorBoolean.valueOf(Objects.equals(daysType,DaysType.WEEKDAYS));
            }
            if (one instanceof Collection){
                for (Object o : (Collection) one) {
                    DateTime parse = DateUtil.parse(o.toString());
                    DaysType daysType = madHolidaysExternalService.getDaysType(parse.toLocalDateTime().toLocalDate());
                    if (!Objects.equals(daysType,DaysType.WEEKDAYS)){
                        return AviatorBoolean.valueOf(false);
                    }
                }

            }
            return AviatorBoolean.valueOf(true);
        }catch (Exception e){
            log.warn("判断某个日期或者多个日期是否是工作日异常",e);
            return AviatorBoolean.valueOf(true);
        }
    }
}



