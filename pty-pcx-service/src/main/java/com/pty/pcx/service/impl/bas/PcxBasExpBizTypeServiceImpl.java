package com.pty.pcx.service.impl.bas;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.mad.common.BizConditionEnum;
import com.pty.pcx.api.bas.IPcxBasExpBizTypeService;
import com.pty.pcx.common.constant.*;
import com.pty.pcx.common.enu.PcxBizTypeCondFieldEnum;
import com.pty.pcx.common.enu.PcxOperatorEnum;
import com.pty.pcx.common.enu.wit.RuleTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpBizTypeDao;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dto.BatchProcessItem;
import com.pty.pcx.dto.rule.PaBizRuleDto;
import com.pty.pcx.dto.rule.PtyRuleDto;
import com.pty.pcx.ecs.IEcsBillBizTypeExternalService;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.pty.IPcxRuleService;
import com.pty.pcx.qo.bas.PcxBasExpBizTypeQO;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.rule.PaBizRuleQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.util.ValidUtil;
import com.pty.pcx.vo.bas.PcxBasExpBizTypeVO;
import com.pty.pcx.vo.bas.PcxBizTypeRuleDetailVO;
import com.pty.pcx.vo.bas.PcxBizTypeRuleHeadVO;
import com.pty.pcx.vo.setting.PaBizRuleVO;
import com.pty.pcx.vo.setting.PtyRuleVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Description: 票类规则ServiceImpl（"费用类型-电子凭证类型-业务规则"关系）
 * createdTime: 2024/12/6  上午10:58
 * creator: wangbao
 **/
@Slf4j
@Indexed
@Service
public class PcxBasExpBizTypeServiceImpl implements IPcxBasExpBizTypeService {

    @Autowired
    private PcxBasExpTypeDao pcxBasExpTypeDao;

    @Autowired
    private PcxBasExpBizTypeDao pcxBasExpBizTypeDao;

    @Autowired
    private IPcxRuleService pcxRuleService;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired
    private IEcsBillBizTypeExternalService ecsBillBizTypeExternalService;

    /**
     * 查询票类规则
     */
    @Override
    public CheckMsg<?> select(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        checkSelectParam(pcxBasExpBizTypeQO);
        PageInfo<PcxBasExpBizTypeVO> pageInfo = PageHelper.startPage(pcxBasExpBizTypeQO.getPageIndex(), pcxBasExpBizTypeQO.getPageSize())
                .doSelectPageInfo(() -> pcxBasExpBizTypeDao.select(pcxBasExpBizTypeQO));
        List<PcxBasExpBizTypeVO> basExpBizTypeVOList = pageInfo.getList();
        // 组装子类信息
        List<PaBizRuleDto> paBizRuleList = selectBizRuleList(pcxBasExpBizTypeQO, basExpBizTypeVOList);
        List<PtyRuleDto> ptyRuleList = selectPtyRuleList(paBizRuleList);
        buildBasExpBizTypeVOList(basExpBizTypeVOList, paBizRuleList, ptyRuleList);
        return CheckMsg.success().setData(new PageResult<PcxBasExpBizTypeVO>().setTotal(pageInfo.getTotal()).setResult(basExpBizTypeVOList));
    }

    /**
     * 校验查询动作入参
     */
    private void checkSelectParam(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        validBaseParam(pcxBasExpBizTypeQO);
        ValidUtil.checkEmptyObject(pcxBasExpBizTypeQO.getPageIndex(), "查询失败，页码不能为空");
        ValidUtil.checkEmptyObject(pcxBasExpBizTypeQO.getPageSize(), "查询失败，页容量不能为空");
    }

    /**
     * 参数为空则抛出异常：区划、单位、年度
     */
    private void validBaseParam(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        ValidUtil.checkEmptyObject(pcxBasExpBizTypeQO, "查询失败，参数校验异常，pcxBasExpBizTypeQO 不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getMofDivCode(), "查询失败，参数校验异常，mofDivCode 不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getAgyCode(), "查询失败，参数校验异常，agyCode 不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getFiscal(), "查询失败，参数校验异常，fiscal 不能为空");
    }

    /**
     * 查询 ptyRuleList
     */
    private List<PtyRuleDto> selectPtyRuleList(List<PaBizRuleDto> paBizRuleList) {
        List<String> ptyRuleIds = paBizRuleList.stream().map(PaBizRuleDto::getPtyRuleId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ptyRuleIds)) {
            return new ArrayList<>();
        }
        return pcxRuleService.selectByIds(ptyRuleIds);
    }

    /**
     * 查询 bizRuleList
     */
    private List<PaBizRuleDto> selectBizRuleList(PcxBasExpBizTypeQO pcxBasExpBizTypeQO, List<PcxBasExpBizTypeVO> basExpBizTypeVOList) {
        if(ObjectUtils.isEmpty(pcxBasExpBizTypeQO)){
            return Collections.emptyList();
        }
        List<String> paBizRuleCodeList = basExpBizTypeVOList.stream().map(PcxBasExpBizTypeVO::getBizRuleCode).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(paBizRuleCodeList)){
            return Collections.emptyList();
        }
        PaBizRuleQO paBizRuleQO = new PaBizRuleQO();
        paBizRuleQO.setRuleCodes(paBizRuleCodeList);
        paBizRuleQO.setMofDivCode(pcxBasExpBizTypeQO.getMofDivCode());
        paBizRuleQO.setAgyCode(pcxBasExpBizTypeQO.getAgyCode());
        paBizRuleQO.setFiscal(pcxBasExpBizTypeQO.getFiscal());
        paBizRuleQO.setTenantId(StringUtil.isNotBlank(pcxBasExpBizTypeQO.getTenantId()) ? pcxBasExpBizTypeQO.getTenantId() : PtyContext.getTenantId());
        return pcxRuleService.getRuleListByRuleCodes(paBizRuleQO);
    }

    /**
     * 组装 basExpBizTypeVOList
     */
    private void buildBasExpBizTypeVOList(List<PcxBasExpBizTypeVO> basExpBizTypeVOList, List<PaBizRuleDto> paBizRuleList, List<PtyRuleDto> ptyRuleList) {
        // 构建 paBizRuleVOList
        List<PtyRuleVO> ptyRuleVOList = BeanUtil.copyToList(ptyRuleList, PtyRuleVO.class);
        Map<String, PtyRuleVO> ptyRuleVOMap = ptyRuleVOList.stream().collect(Collectors.toMap(PtyRuleVO::getRuleId, Function.identity()));
        List<PaBizRuleVO> paBizRuleVOList = BeanUtil.copyToList(paBizRuleList, PaBizRuleVO.class);
        for(PaBizRuleVO paBizRuleVO : paBizRuleVOList){
            String ptyRuleId = paBizRuleVO.getPtyRuleId();
            if (StringUtil.isEmpty(ptyRuleId) || ObjectUtils.isEmpty(ptyRuleVOMap.get(ptyRuleId))) {
                break;
            }
            paBizRuleVO.setPtyRuleVO(ptyRuleVOMap.get(ptyRuleId));
            paBizRuleVO.setAtomRuleGroupVOList(analysisConditionJson(paBizRuleVO.getConditionJson()));
        }
        // 构建 basExpBizTypeVOList
        Map<String, PaBizRuleVO> paBizRuleVOMap = paBizRuleVOList.stream().collect(Collectors.toMap(PaBizRuleVO::getRuleCode, Function.identity()));
        for(PcxBasExpBizTypeVO basExpBizTypeVO : basExpBizTypeVOList){
            String bizRuleCode = basExpBizTypeVO.getBizRuleCode();
            if (StringUtil.isEmpty(bizRuleCode) || ObjectUtils.isEmpty(paBizRuleVOMap.get(bizRuleCode))) {
                break;
            }
            basExpBizTypeVO.setPaBizRuleDto(paBizRuleVOMap.get(bizRuleCode));
        }
    }

    /**
     * 新增或修改票类规则
     * scc：因需验重功能，经与王保确认，并未存在批量保存票据种类的功能。
     * 每个种类的批量规则，在QO的paBizRuleDto中
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> saveOrUpdate(List<PcxBasExpBizTypeQO> basExpBizTypeQOList) {
        if (CollectionUtil.isEmpty(basExpBizTypeQOList)){
            return CheckMsg.fail("保存失败，未选择票类规则");
        }
        PcxBasExpBizTypeQO pcxBasExpBizTypeQO = basExpBizTypeQOList.get(0);
        validSaveOrUpdateParam(pcxBasExpBizTypeQO);
        buildBatchSaveOrUpdateParam(pcxBasExpBizTypeQO);
        if (isExists(pcxBasExpBizTypeQO)){
            return CheckMsg.fail(String.format("票据种类 %s 已存在 ，请重新选择或编辑原有规则", pcxBasExpBizTypeQO.getEcsBizTypeName()));
        }
        List<String> idList = basExpBizTypeQOList.stream().map(PcxBasExpBizTypeQO::getId).collect(Collectors.toList());
        List<PcxBasExpBizTypeVO> basExpBizTypeVOList = pcxBasExpBizTypeDao.selectByIds(idList);
        List<PaBizRuleDto> paBizRuleList = selectBizRuleList(pcxBasExpBizTypeQO, basExpBizTypeVOList);
        if (CollectionUtil.isNotEmpty(paBizRuleList)) {
            pcxRuleService.deleteBizRule(paBizRuleList);
        }
        List<PaBizRuleVO> bizRuleVOList = basExpBizTypeQOList.stream().map(PcxBasExpBizTypeQO::getPaBizRuleDto).collect(Collectors.toList());
        List<PtyRuleVO> ruleVOList = basExpBizTypeQOList.stream().map(PcxBasExpBizTypeQO::getPtyRuleVo).collect(Collectors.toList());
        pcxRuleService.saveBizRule(bizRuleVOList,ruleVOList);
        batchServiceUtil.batchProcess(new BatchProcessItem<>(basExpBizTypeQOList, PcxBasExpBizTypeDao.class, this::saveOrUpdateBasExpBizType));
        return CheckMsg.success("保存成功");
    }


    /**
     * pcx_bas_exp_biz_type表中有唯一约束
     * 新增保存时校验是否已存在
     * @param pcxBasExpBizTypeQO
     * @return
     */
    private boolean isExists(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        if (OperationCNConstant.SAVE.equals(pcxBasExpBizTypeQO.getOperationType())){
            PcxBasExpBizTypeQO param = new PcxBasExpBizTypeQO();
            param.setAgyCode(pcxBasExpBizTypeQO.getAgyCode());
            param.setFiscal(pcxBasExpBizTypeQO.getFiscal());
            param.setMofDivCode(pcxBasExpBizTypeQO.getMofDivCode());
            param.setEcsBizTypeCode(pcxBasExpBizTypeQO.getEcsBizTypeCode());
            param.setExpenseCode(pcxBasExpBizTypeQO.getExpenseCode());
            param.setTenantId(pcxBasExpBizTypeQO.getTenantId());
            List<PcxBasExpBizTypeVO> basExpBizTypeList = pcxBasExpBizTypeDao.select(param);
            if (CollectionUtil.isNotEmpty(basExpBizTypeList)){
                return true;
            }
        }
        return false;
    }

    /**
     * 校验新增或修改动作的入参
     */
    private void validSaveOrUpdateParam(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        ValidUtil.checkEmptyObject(pcxBasExpBizTypeQO, "保存失败，参数校验异常，请求参数不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getExpenseCode(), "保存失败，参数校验异常，费用代码不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getExpenseName(), "保存失败，参数校验异常，费用名称不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getEcsBizTypeCode(), "保存失败，参数校验异常，电子凭证类型代码不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getEcsBizTypeName(), "保存失败，参数校验异常，电子凭证类型不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getMofDivCode(), "保存失败，参数校验异常，所属部门不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getFiscal(), "保存失败，参数校验异常，年度不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getAgyCode(), "保存失败，参数校验异常，单位代码不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getUserCode(), "保存失败，参数校验异常，用户编码不能为空");
        ValidUtil.checkEmptyStr(pcxBasExpBizTypeQO.getUserName(), "保存失败，参数校验异常，用户信息不能为空");
        PaBizRuleVO paBizRuleVO = pcxBasExpBizTypeQO.getPaBizRuleDto();
        ValidUtil.checkEmptyObject(paBizRuleVO, "保存失败，参数校验异常，paBizRuleVO 不能为空");
    }

    /**
     * 组装批量新增或修改操作的入参
     */
    private void buildBatchSaveOrUpdateParam(PcxBasExpBizTypeQO basExpBizTypeQO) {
        basExpBizTypeQO.setTenantId(StringUtil.isEmpty(basExpBizTypeQO.getTenantId()) ? PtyContext.getTenantId() : basExpBizTypeQO.getTenantId());
        PaBizRuleVO paBizRuleVO = basExpBizTypeQO.getPaBizRuleDto();
        PtyRuleVO ptyRuleVO = ObjectUtil.isEmpty(paBizRuleVO.getPtyRuleVO())? new PtyRuleVO() : paBizRuleVO.getPtyRuleVO();
        String curDate = DateUtil.getCurDate();
        Map<String, Object> map = buildConditionJson(paBizRuleVO.getAtomRuleGroupVOList());
        String conditionJson = StringUtil.getStringValue(map.get(PcxBizTypeConstant.MAP_KEY_CONDITION_JSON));
        String remark = StringUtil.getStringValue(map.get(PcxBizTypeConstant.MAP_KEY_REMARK));
        String ruleName = String.join(PcxDelimiterConstant.RULE_NAME_CONNECTOR,
                "费用明细", basExpBizTypeQO.getExpenseName(), "票据种类", basExpBizTypeQO.getEcsBizTypeName());
        if(StringUtil.isEmpty(basExpBizTypeQO.getId())){
            String id = IDGenerator.id();
            ptyRuleVO.setRuleId(id);
            ptyRuleVO.setRuleCreator(basExpBizTypeQO.getUserCode());
            ptyRuleVO.setRuleCreatedDate(curDate);
            ptyRuleVO.setRuleType(RuleTypeEnum.LEGAL.name());
            ptyRuleVO.setRuleModule(PcxConstant.SYS_ID);
            ptyRuleVO.setRuleAct(PtyRuleConstants.PtyRuleActionEnum.ACCESS.name());
            ptyRuleVO.setRuleGroup(PcxBizTypeConstant.RULE_ENGINE_GROUP_ID);
            ptyRuleVO.setEnabled(PubConstant.LOGIC_TRUE);
            ptyRuleVO.setRuleCorrect(PubConstant.LOGIC_TRUE);
            ptyRuleVO.setRuleIdentify(PubConstant.LOGIC_FALSE);

            paBizRuleVO.setId(IDGenerator.id());
            paBizRuleVO.setRuleCode(id);
            paBizRuleVO.setPtyRuleId(id);
            paBizRuleVO.setBizCode(basExpBizTypeQO.getExpenseCode());
            paBizRuleVO.setBizType(BizConditionEnum.BAS_EXP_BIZ_TYPE.getCode());
            paBizRuleVO.setCreator(basExpBizTypeQO.getUserCode());
            paBizRuleVO.setCreatorName(basExpBizTypeQO.getUserName());
            paBizRuleVO.setCreatedTime(curDate);
            paBizRuleVO.setRemark(remark);
            paBizRuleVO.setSeq(1);
            paBizRuleVO.setPtyRuleVO(ptyRuleVO);

            basExpBizTypeQO.setId(IDGenerator.id());
            basExpBizTypeQO.setBizRuleCode(id);
            basExpBizTypeQO.setCreator(basExpBizTypeQO.getUserCode());
            basExpBizTypeQO.setCreatorName(basExpBizTypeQO.getUserName());
            basExpBizTypeQO.setCreatedTime(curDate);
            basExpBizTypeQO.setOperationType(OperationCNConstant.SAVE);
            basExpBizTypeQO.setSeq(PcxBizTypeConstant.DEFAULT_SEQ);
            basExpBizTypeQO.setPaBizRuleDto(paBizRuleVO);
            basExpBizTypeQO.setPtyRuleVo(ptyRuleVO);
        }else{
            String id = IDGenerator.id();
            ptyRuleVO.setRuleId(id);

            paBizRuleVO.setId(IDGenerator.id());
            paBizRuleVO.setRuleCode(ptyRuleVO.getRuleId());
            paBizRuleVO.setPtyRuleId(ptyRuleVO.getRuleId());

            basExpBizTypeQO.setBizRuleCode(paBizRuleVO.getRuleCode());
            basExpBizTypeQO.setOperationType(OperationCNConstant.UPDATE);

        }
        ptyRuleVO.setRuleName(ruleName);
        ptyRuleVO.setRuleDesc(ruleName);
        ptyRuleVO.setRuleModifyDate(curDate);
        ptyRuleVO.setWhenCondition(PcxBizTypeConstant.RULE_ENGINE_TRUE);
        ptyRuleVO.setMustCondition(conditionJson);

        paBizRuleVO.setRuleName(ruleName);
        paBizRuleVO.setConditionJson(conditionJson);
        paBizRuleVO.setModifier(basExpBizTypeQO.getUserCode());
        paBizRuleVO.setModifiedTime(curDate);
        paBizRuleVO.setModifierName(basExpBizTypeQO.getUserName());
        paBizRuleVO.setMofDivCode(basExpBizTypeQO.getMofDivCode());
        paBizRuleVO.setAgyCode(basExpBizTypeQO.getAgyCode());
        paBizRuleVO.setTenantId(basExpBizTypeQO.getTenantId());
        paBizRuleVO.setFiscal(basExpBizTypeQO.getFiscal());
        paBizRuleVO.setPtyRuleVO(ptyRuleVO);

        basExpBizTypeQO.setBizRuleName(ruleName);
        basExpBizTypeQO.setModifier(basExpBizTypeQO.getUserCode());
        basExpBizTypeQO.setModifierName(basExpBizTypeQO.getUserName());
        basExpBizTypeQO.setModifiedTime(curDate);
        basExpBizTypeQO.setPaBizRuleDto(paBizRuleVO);
        basExpBizTypeQO.setPtyRuleVo(ptyRuleVO);
    }

    /**
     * 拼接票类规则的 conditionJson 以及中文描述 remark
     */
    private Map<String, Object> buildConditionJson(List<PcxBizTypeRuleHeadVO> ruleAtomGroupVOList) {
        Map<String, Object> resultMap = new HashMap<>();
        // 用户填写条件为空，设置默认值 true
        if(CollectionUtil.isEmpty(ruleAtomGroupVOList)){
            resultMap.put(PcxBizTypeConstant.MAP_KEY_CONDITION_JSON, PcxBizTypeConstant.RULE_ENGINE_TRUE);
            resultMap.put(PcxBizTypeConstant.MAP_KEY_REMARK, "暂无条件");
            return resultMap;
        }
        List<String> atomRuleGroupJsonList = new ArrayList<>();
        List<String> atomRuleGroupNameList = new ArrayList<>();
        for(PcxBizTypeRuleHeadVO ruleAtomGroupVO : ruleAtomGroupVOList){
            buildAtomRuleGroupJsonList(ruleAtomGroupVO, atomRuleGroupJsonList, atomRuleGroupNameList);
        }
        String conditionJson = String.join(PcxDelimiterConstant.OR, atomRuleGroupJsonList);
        ValidUtil.checkRegexMatcher(conditionJson, matchBizTypeRegex(conditionJson));
        String remark = String.join(PcxDelimiterConstant.HUO, atomRuleGroupNameList);
        resultMap.put(PcxBizTypeConstant.MAP_KEY_CONDITION_JSON, conditionJson);
        resultMap.put(PcxBizTypeConstant.MAP_KEY_REMARK, remark);
        return resultMap;
    }

    /**
     * 匹配票类规则的正则表达式
     */
    private static String matchBizTypeRegex(String conditionJson) {
        ValidUtil.checkEmptyStr(conditionJson, "matchBizTypeRegex ==> 匹配正则表达式失败，conditionJson 参数不能为空");
        String conditionRegex;
        if(conditionJson.contains(PcxDelimiterConstant.STRING_SYMBOL) ){
            conditionRegex = PcxRegexConstant.BIZ_TYPE_STRING_FIELD_RULE;
        }else{
            conditionRegex = PcxRegexConstant.BIZ_TYPE_NUMBER_FIELD_RULE;
        }
        return conditionRegex;
    }

    /**
     * 拼接一组原子规则的 json，组内用 and 连接
     */
    private static void buildAtomRuleGroupJsonList(PcxBizTypeRuleHeadVO ruleAtomGroupVO, List<String> conditionJsonList, List<String> atomRuleGroupNameList) {
        List<PcxBizTypeRuleDetailVO> pcxBizTypeRuleDetailVOList = ruleAtomGroupVO.getAtomRuleVOList();
        List<String> atomRuleJsonList = new ArrayList<>();
        List<String> atomRuleNameList = new ArrayList<>();
        for (PcxBizTypeRuleDetailVO ruleAtomVO : pcxBizTypeRuleDetailVOList){
            buildSingleAtomRuleJsonList(ruleAtomVO, atomRuleJsonList, atomRuleNameList);
        }
        String atomRuleGroupJson = String.format("(%s)", String.join(PcxDelimiterConstant.AND, atomRuleJsonList));
        String atomRuleGroupName = String.format("(%s)", String.join(PcxDelimiterConstant.QIE, atomRuleNameList));
        conditionJsonList.add(atomRuleGroupJson);
        atomRuleGroupNameList.add(atomRuleGroupName);

    }

    /**
     * 拼接单条原子规则的 json：原子规则 = 筛选条件 + 操作符 + 值
     */
    private static void buildSingleAtomRuleJsonList(PcxBizTypeRuleDetailVO atomRuleVO, List<String> atomRuleJsonList, List<String> atomRuleNameList) {
        StringBuilder jsonBuilder = new StringBuilder();
        StringBuilder nameBuilder = new StringBuilder();
        // 提取条件字段
        String fieldCode = atomRuleVO.getFieldCode();
        String fieldName = atomRuleVO.getFieldName();
        String fieldDataType = PcxBizTypeCondFieldEnum.getByCode(fieldCode).getDataType();
        // 提取操作符
        String optSymbol = atomRuleVO.getOperatorCode();
        String optName = atomRuleVO.getOperatorName();
        String optDataType = PcxOperatorEnum.getBySymbol(optSymbol).getDataType();
        // 校验"条件字段"和"操作符"
        ValidUtil.checkEmptyStr(fieldDataType, String.format("参数异常，fieldDataType 不能为空，字段名称：%s", fieldName));
        ValidUtil.checkEmptyStr(fieldDataType, String.format("参数异常，optDataType 不能为空，运算符名称：%s", optName));
        boolean isDataTypeEquals = optDataType.equalsIgnoreCase(fieldDataType);
        ValidUtil.checkBool(isDataTypeEquals, String.format("参数异常，运算符 %s 与字段 %s 的数据类型不匹配", optName, fieldName));
        // 提取条件值
        String conditionValueStr = StringUtil.getStringValue(atomRuleVO.getConditionValue());
        if(PcxDataTypeConstant.STRING.equalsIgnoreCase(fieldDataType)){
            conditionValueStr = String.join(PcxDelimiterConstant.STRING_SYMBOL,
                    PcxDelimiterConstant.LEFT_BRACKET, conditionValueStr, PcxDelimiterConstant.RIGHT_BRACKET);
        }
        // 拼接 conditionJson
        jsonBuilder.append(fieldCode).append(optSymbol).append(conditionValueStr);
        nameBuilder.append(fieldName).append(optName).append(conditionValueStr);
        atomRuleJsonList.add(jsonBuilder.toString());
        atomRuleNameList.add(nameBuilder.toString());
    }

    /**
     * 新增或修改 pcxBasExpBizType
     */
    public void saveOrUpdateBasExpBizType(PcxBasExpBizTypeDao pcxBasExpBizTypeDao, PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        if(OperationCNConstant.SAVE.equalsIgnoreCase(pcxBasExpBizTypeQO.getOperationType())){
            pcxBasExpBizTypeDao.insertSelective(pcxBasExpBizTypeQO);
        }else if(OperationCNConstant.UPDATE.equalsIgnoreCase(pcxBasExpBizTypeQO.getOperationType())){
            pcxBasExpBizTypeDao.updateById(pcxBasExpBizTypeQO);
        }
    }

    /**
     * 删除票类规则
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> deleteByIdList(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        checkDeleteByIdListParam(pcxBasExpBizTypeQO);
        List<PcxBasExpBizTypeVO> basExpBizTypeVOList = pcxBasExpBizTypeDao.selectByIds(pcxBasExpBizTypeQO.getIdList());
        if (CollectionUtil.isEmpty(basExpBizTypeVOList)) {
            return CheckMsg.success("删除成功");
        }
        batchServiceUtil.batchProcess(new BatchProcessItem<>(pcxBasExpBizTypeQO.getIdList(), PcxBasExpBizTypeDao.class, this::deletePcxBasExpBizType));
        List<PaBizRuleDto> paBizRuleList = selectBizRuleList(pcxBasExpBizTypeQO, basExpBizTypeVOList);
        if (CollectionUtil.isNotEmpty(paBizRuleList)) {
            pcxRuleService.deleteBizRule(paBizRuleList);
        }
        return CheckMsg.success("删除成功");
    }

    /**
     * 校验批量删除动作的入参
     */
    private void checkDeleteByIdListParam(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        ValidUtil.checkEmptyObject(pcxBasExpBizTypeQO, "删除失败，参数校验异常，pcxBasExpBizTypeQO 不能为空");
        ValidUtil.checkContainsEmptyStr(pcxBasExpBizTypeQO.getIdList(), "删除失败，参数校验异常，idList 中不能含有空元素");
    }

    /**
     * 删除 pcxBasExpBizType
     */
    public void deletePcxBasExpBizType(PcxBasExpBizTypeDao pcxBasExpBizTypeDao, String basExpBizTypeId) {
        pcxBasExpBizTypeDao.deleteById(basExpBizTypeId);
    }

    /**
     * 票类规则匹配费用编码
     */
    @Override
    public CheckMsg<?> selectExpTypeList(List<PcxBasExpBizTypeQO> pcxBasExpBizTypeQOList) {
        CheckMsg<?> paramCheckMsg = checkSelectExpTypeParam(pcxBasExpBizTypeQOList);
        if(!paramCheckMsg.isSuccess()){
            return paramCheckMsg;
        }
        List<PcxBasExpBizTypeVO> resultList = new ArrayList<>();
        for(PcxBasExpBizTypeQO pcxBasExpBizTypeQO : pcxBasExpBizTypeQOList){
            matchExpTypeForSingleBill(pcxBasExpBizTypeQO, resultList);
        }
        return CheckMsg.success(resultList);
    }

    /**
     * 检查入参：票类规则匹配费用编码
     */
    private CheckMsg<?> checkSelectExpTypeParam(List<PcxBasExpBizTypeQO> pcxBasExpBizTypeQOList) {
        if(CollectionUtil.isEmpty(pcxBasExpBizTypeQOList)){
            return CheckMsg.fail("查询失败，参数校验异常，pcxBasExpBizTypeQOList 不能为空");
        }
        for(PcxBasExpBizTypeQO pcxBasExpBizTypeQO : pcxBasExpBizTypeQOList){
            if(ObjectUtil.isEmpty(pcxBasExpBizTypeQO)){
                return CheckMsg.fail("查询失败，参数校验异常，pcxBasExpBizTypeQOList 不能为空");
            }
            if(StringUtil.isEmpty(pcxBasExpBizTypeQO.getMofDivCode())){
                return CheckMsg.fail("查询失败，参数校验异常，mofDivCode 不能为空");
            }
            if(StringUtil.isEmpty(pcxBasExpBizTypeQO.getAgyCode())){
                return CheckMsg.fail("查询失败，参数校验异常，agyCode 不能为空");
            }
            if(StringUtil.isEmpty(pcxBasExpBizTypeQO.getFiscal())){
                return CheckMsg.fail("查询失败，参数校验异常，fiscal 不能为空");
            }
        }
        return CheckMsg.success();
    }

    /**
     * 查询票类规则的条件字段选项
     */
    @Override
    public CheckMsg<?> selectConditionField(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        validBaseParam(pcxBasExpBizTypeQO);
        return CheckMsg.success(PcxBizTypeCondFieldEnum.getList());
    }

    /**
     * 查询票类规则的业务凭证种类选项
     */
    @Override
    public CheckMsg<?> selectEcsBizType(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        validBaseParam(pcxBasExpBizTypeQO);
        return ecsBillBizTypeExternalService.select(pcxBasExpBizTypeQO);
    }

    /**
     * 查询票类规则的运算符号选项
     */
    @Override
    public CheckMsg<?> selectOperatorList(PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        validBaseParam(pcxBasExpBizTypeQO);
        return CheckMsg.success(PcxOperatorEnum.getList());
    }

    /**
     * 对单张票匹配票类规则，返回对应的费用编码
     */
    private void matchExpTypeForSingleBill(PcxBasExpBizTypeQO pcxBasExpBizTypeQO, List<PcxBasExpBizTypeVO> resultList) {
        if(StringUtil.isEmpty(pcxBasExpBizTypeQO.getEcsBizTypeCode())){
            return;
        }
        List<PcxBasExpBizTypeVO> basExpBizTypeVOList = pcxBasExpBizTypeDao.select(pcxBasExpBizTypeQO);
        List<PaBizRuleDto> paBizRuleList = selectBizRuleList(pcxBasExpBizTypeQO, basExpBizTypeVOList);
        List<PtyRuleDto> ptyRuleList = selectPtyRuleList(paBizRuleList);
        buildBasExpBizTypeVOList(basExpBizTypeVOList, paBizRuleList, ptyRuleList);
        PcxBasExpBizTypeVO pcxBasExpBizTypeVO = BeanUtil.copyProperties(pcxBasExpBizTypeQO, PcxBasExpBizTypeVO.class);
        List<String> expCodeList = matchExpTypeCode(basExpBizTypeVOList, pcxBasExpBizTypeQO);
        if(CollectionUtil.isNotEmpty(expCodeList)){
            List<PcxBasExpType> pcxBasExpTypeList = selectPcxBasExpTypeList(expCodeList, pcxBasExpBizTypeQO);
            pcxBasExpBizTypeVO.setPcxBasExpTypeList(pcxBasExpTypeList);
        }
        resultList.add(pcxBasExpBizTypeVO);
    }

    /**
     * 查询费用信息
     */
    private List<PcxBasExpType> selectPcxBasExpTypeList(List<String> expCodeList, PcxBasExpBizTypeQO pcxBasExpBizTypeQO) {
        PcxBasExpTypeQO pcxBasExpTypeQO = new PcxBasExpTypeQO();
        pcxBasExpTypeQO.setExpTypeCodes(expCodeList);
        pcxBasExpTypeQO.setMofDivCode(pcxBasExpBizTypeQO.getMofDivCode());
        pcxBasExpTypeQO.setAgyCode(pcxBasExpBizTypeQO.getAgyCode());
        pcxBasExpTypeQO.setFiscal(pcxBasExpBizTypeQO.getFiscal());
        pcxBasExpTypeQO.setTenantId(pcxBasExpBizTypeQO.getTenantId());
        return pcxBasExpTypeDao.selectSimpleList(pcxBasExpTypeQO);
    }

    /**
     * 匹配票类规则
     */
    private List<String> matchExpTypeCode(List<PcxBasExpBizTypeVO> basExpBizTypeVOList, PcxBasExpBizTypeQO param) {
        List<String> expCodeList = new ArrayList<>();
        PaBizRuleQO paBizRuleQO = new PaBizRuleQO();
        BeanUtil.copyProperties(param, paBizRuleQO);
        for(PcxBasExpBizTypeVO basExpBizTypeVO : basExpBizTypeVOList){
            PaBizRuleVO paBizRuleVO = basExpBizTypeVO.getPaBizRuleDto();
            if (ObjectUtils.isEmpty(paBizRuleVO) || ObjectUtils.isEmpty(paBizRuleVO.getPtyRuleVO())) {
                expCodeList.add(basExpBizTypeVO.getExpenseCode());
                continue;
            }
            PtyRuleVO ptyRuleVO = paBizRuleVO.getPtyRuleVO();
            boolean isMatchSuccess = pcxRuleService.fireRule(ptyRuleVO, param);
            if(isMatchSuccess){
                expCodeList.add(basExpBizTypeVO.getExpenseCode());
            }
        }
        return expCodeList;
    }

    /**
     * 解析 conditionJson
     */
    private List<PcxBizTypeRuleHeadVO> analysisConditionJson(String conditionJson) {
        // 用conditionJson 为 "true" ， 说明用户没有填写条件
        if(StringUtil.isEmpty(conditionJson) || PcxBizTypeConstant.RULE_ENGINE_TRUE.equalsIgnoreCase(conditionJson)){
            return new ArrayList<>();
        }
        List<PcxBizTypeRuleHeadVO> atomRuleGroupVOList = new ArrayList<>();
        String[] groupRuleJsonList = conditionJson.split(PcxDelimiterConstant.OR);;
        int headSeq = 1;
        for(String groupRuleJson:groupRuleJsonList){
            PcxBizTypeRuleHeadVO atomRuleGroupVO = new PcxBizTypeRuleHeadVO();
            atomRuleGroupVO.setHeadSeq(headSeq++);
            List<PcxBizTypeRuleDetailVO> atomRuleVOList = new ArrayList<>();
            String[] atomRuleJsonList = groupRuleJson.split(PcxDelimiterConstant.AND);;
            int detailSeq = 1;
            for(String atomRuleJson:atomRuleJsonList){
                PcxBizTypeRuleDetailVO atomRuleVO = new PcxBizTypeRuleDetailVO();
                atomRuleVO.setDetailSeq(detailSeq++);
                atomRuleJson = excludeBracketBorder(atomRuleJson);
                // 若包含双引号则认为是"字符串类型字段的筛选规则"，否则认为是"数字类型字段的筛选规则"
                String conditionRegex = matchBizTypeRegex(atomRuleJson);
                analysisAtomRule(conditionRegex, atomRuleJson, atomRuleVO);
                atomRuleVOList.add(atomRuleVO);
            }
            atomRuleGroupVO.setAtomRuleVOList(atomRuleVOList);
            atomRuleGroupVOList.add(atomRuleGroupVO);
        }
        return atomRuleGroupVOList;
    }

    /**
     * 去除字符串首部和尾部的括号: ()
     */
    private static String excludeBracketBorder(String atomRuleJson) {
        if (atomRuleJson != null && !atomRuleJson.isEmpty()) {
            // 使用正则表达式去除最外层的括号，但不包括引号和括号
            atomRuleJson = atomRuleJson.replaceAll("^\\((?!\"|\\))|(?<!\"|\\))\\)$", "");
        }
        return atomRuleJson;
    }

    /**
     * 解析原子规则表达式：条件字段+操作符+条件值
     */
    public static void analysisAtomRule(String regex, String atomRuleJson, PcxBizTypeRuleDetailVO atomRuleVO) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(atomRuleJson);
        if (matcher.find()) {
            // 解析条件字段
            String fieldCode = matcher.group(1);
            atomRuleVO.setFieldCode(fieldCode);
            PcxBizTypeCondFieldEnum conditionFieldEnum = PcxBizTypeCondFieldEnum.getByCode(fieldCode);
            ValidUtil.checkEmptyObject(conditionFieldEnum, "票类规则的条件字段解析异常 ==> PcxConditionFieldEnum.fieldCode:"+fieldCode);
            String fieldName = conditionFieldEnum.getName();
            atomRuleVO.setFieldName(fieldName);
            // 解析操作符
            String operatorSymbol = matcher.group(2);
            atomRuleVO.setOperatorCode(operatorSymbol);
            PcxOperatorEnum operatorEnum = PcxOperatorEnum.getBySymbol(operatorSymbol);
            ValidUtil.checkEmptyObject(operatorEnum, "票类规则的操作符解析异常 ==> PcxOperator.operatorSymbol:"+operatorSymbol);
            String operatorName = operatorEnum.getName();
            atomRuleVO.setOperatorName(operatorName);
            // 解析条件值
            String conditionValue = matcher.group(3);
            atomRuleVO.setConditionValue(conditionValue);
        } else {
            log.error("票类规则的规则表达式解析异常 ==> {}" , atomRuleJson);
            throw new CommonException("票类规则的规则表达式解析异常 ==> " + atomRuleJson);
        }
    }

}
