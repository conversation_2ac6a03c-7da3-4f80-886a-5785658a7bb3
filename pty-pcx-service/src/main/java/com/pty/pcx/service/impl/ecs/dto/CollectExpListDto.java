package com.pty.pcx.service.impl.ecs.dto;

import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class CollectExpListDto {
    private List<PcxBillExpBase> expBaseList = new ArrayList<>();
    private List<PcxBillExpDetailBase> expDetailList = new ArrayList();
    private List<PcxBasExpType> expTypeList = new ArrayList<>();

    public void addExpBaseList(List<PcxBillExpBase> list){
        if (!CollectionUtils.isEmpty(list)){
            expBaseList.addAll(list);
        }
    }

    public void addExpDetailList(List<PcxBillExpDetailBase> list){
        if (!CollectionUtils.isEmpty(list)){
            expDetailList.addAll(list);
        }
    }

    public void addExpTypeList(List<PcxBasExpType> list){
        if (!CollectionUtils.isEmpty(list)){
            expTypeList.addAll(list);
        }
    }

}
