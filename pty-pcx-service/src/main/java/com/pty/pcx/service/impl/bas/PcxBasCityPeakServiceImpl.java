package com.pty.pcx.service.impl.bas;

import com.alibaba.fastjson.JSON;
import com.pty.pcx.api.bas.IPcxBasCityPeakService;
import com.pty.pcx.dao.bas.PcxBasCityPeakDao;
import com.pty.pcx.entity.bas.PcxBasCityPeak;
import com.pty.pcx.qo.bas.PcxBasCityPeakQO;
import com.pty.pcx.qo.bas.PcxBasCityPeakVO;
import com.pty.pcx.vo.BaseDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class PcxBasCityPeakServiceImpl implements IPcxBasCityPeakService {

    @Autowired
    private PcxBasCityPeakDao pcxBasCityPeakDao;
    @Override
    public List<BaseDataVo> selectAllCityPeakList(PcxBasCityPeakQO pcxBasCityPeakQO) {
        List<PcxBasCityPeak> pcxBasCityPeaks = pcxBasCityPeakDao.selectList(pcxBasCityPeakQO);
        if (CollectionUtils.isEmpty(pcxBasCityPeaks)){
            return new ArrayList<>();
        }
        List<BaseDataVo> baseDataVos = pcxBasCityPeaks.stream().map(pcxBasCityPeak -> {
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setId(pcxBasCityPeak.getId());
            baseDataVo.setCode(pcxBasCityPeak.getDataCode());
            baseDataVo.setName(pcxBasCityPeak.getDataName());
            return baseDataVo;
        }).collect(Collectors.toList());
        return baseDataVos;
    }

    @Override
    public List<PcxBasCityPeakVO> selectCityPeakList(PcxBasCityPeakQO pcxBasCityPeakQO) {
        List<PcxBasCityPeak> pcxBasCityPeaks = pcxBasCityPeakDao.selectList(pcxBasCityPeakQO);
        if (CollectionUtils.isEmpty(pcxBasCityPeaks)){
            return new ArrayList<>();
        }
        return pcxBasCityPeaks.stream().map(pcxBasCityPeak -> {
            PcxBasCityPeakVO pcxBasCityPeakVO = new PcxBasCityPeakVO();
            BeanUtils.copyProperties(pcxBasCityPeak,pcxBasCityPeakVO);
            return pcxBasCityPeakVO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveOrUpdate(List<PcxBasCityPeakQO> pcxBasCityPeakQOList) {
        if(CollectionUtils.isNotEmpty(pcxBasCityPeakQOList)){
            pcxBasCityPeakQOList.forEach(pcxBasCityPeakQO -> {
                PcxBasCityPeak pcxBasCityPeak = new PcxBasCityPeak();
                BeanUtils.copyProperties(pcxBasCityPeakQO,pcxBasCityPeak);
                pcxBasCityPeak.setPeakDateJson(JSON.toJSONString(pcxBasCityPeakQO.getPeakDateList()));
                if(StringUtils.isNotBlank(pcxBasCityPeak.getId())){
                    pcxBasCityPeakDao.updateById(pcxBasCityPeak);
                }else {
                    pcxBasCityPeakDao.insert(pcxBasCityPeak);
                }
            });
        }

    }

    @Override
    @Transactional
    public void deletePcxBasCityPeak(List<PcxBasCityPeakQO> pcxBasCityPeakQOList) {
        if(CollectionUtils.isNotEmpty(pcxBasCityPeakQOList)){
            List<String> ids = pcxBasCityPeakQOList.stream().map(PcxBasCityPeakQO::getId).collect(Collectors.toList());
            pcxBasCityPeakDao.deleteBatchIds(ids);
        }
    }
}
