package com.pty.pcx.service.impl.wit;

import com.pty.pcx.api.wit.IWitCommonService;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.rule.aviator.AviatorRulesEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
@Slf4j
public class WitCommonServiceImpl implements IWitCommonService {

    @Autowired(required = false)
    @RestClientReference(microServiceNames = "rule")
    private AviatorRulesEngine aviatorRulesEngine;

    @PostConstruct
    public void initService() {
        try {
            aviatorRulesEngine.addInstanceFunctions(PcxRuleDomainObject.class);
        } catch (Exception e) {
            log.error("实例初始化出错：", e);
            throw new CommonException("初始化" + e);
        }
    }
}
