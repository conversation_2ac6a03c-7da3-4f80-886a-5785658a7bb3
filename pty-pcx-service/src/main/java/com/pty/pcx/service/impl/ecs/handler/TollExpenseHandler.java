package com.pty.pcx.service.impl.ecs.handler;

import com.pty.mad.entity.MadArea;
import com.pty.pcx.dto.ecs.inv.PcxTollInvoiceDto;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.entity.bill.PcxBillExpTravel;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.service.impl.ecs.ProcessContext;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 定额发票quota、门票ticket
 */
@Indexed
@Component
@Slf4j
public class TollExpenseHandler extends CommonHandler implements Handler<InvoiceDtoWrapper<PcxTollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase>>{

    @Override
    public void handle(InvoiceDtoWrapper<PcxTollInvoiceDto, PcxBillExpBase, PcxBillExpDetailBase> invoiceDtoWrapper, ProcessContext context) {
        // 跟dto对应的费用/费用类型 进行费用处理
        PcxTollInvoiceDto toll = invoiceDtoWrapper.getDto();
        List<PcxBasExpType> myExpTypeList = new ArrayList<>(toll.getExpTypeList());

        //从票的费用类型中找出在事项的费用类型中共有的
        myExpTypeList = getMyExpTypeInItemExpTypeList(myExpTypeList, context);

        collectSettlDetails(invoiceDtoWrapper, PcxTollInvoiceDto::getBillSettlList, PcxTollInvoiceDto::getBillId,
                PcxTollInvoiceDto::getBillTypeCode, context);

        if (CollectionUtils.isEmpty(myExpTypeList) || myExpTypeList.size()>1){
            invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
            return;
        }
        PcxBasExpType pcxBasExpType = myExpTypeList.get(0);
        invoiceDtoWrapper.setMatchExpType(pcxBasExpType);
        if (pcxBasExpType.getIsRefine().equals(1)){
            transExpDetail(pcxBasExpType, toll, invoiceDtoWrapper, context);
        }else{
            transExpBase(pcxBasExpType, toll, invoiceDtoWrapper, context);
        }
    }

    private void transExpBase(PcxBasExpType pcxBasExpType, PcxTollInvoiceDto toll,
                              InvoiceDtoWrapper<PcxTollInvoiceDto, PcxBillExpBase,PcxBillExpDetailBase> invoiceDtoWrapper,
                              ProcessContext context) {
        PcxBillExpTravel expBase = new PcxBillExpTravel();
        expBase.setFiscal(context.getFiscal());
        expBase.setMofDivCode(context.getMofDivCode());
        expBase.setAgyCode(context.getAgyCode());
        expBase.setTenantId(context.getTenantId());
        expBase.setEcsRelItemName(toll.getBillDescpt());
        expBase.setExpenseCode(pcxBasExpType.getExpenseCode());
        expBase.setInputAmt(toll.getBillAmt());
        invoiceDtoWrapper.addExpense(expBase);
        invoiceDtoWrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
    }

    private void transExpDetail(PcxBasExpType basExpType, PcxTollInvoiceDto toll,
                                InvoiceDtoWrapper<PcxTollInvoiceDto, PcxBillExpBase,PcxBillExpDetailBase> invoiceDtoWrapper,
                                ProcessContext context) {
        PcxBillExpDetailTravel expDetailCommon = new PcxBillExpDetailTravel();
        expDetailCommon.setId(IDGenerator.id());
        expDetailCommon.setExpDetailCode(basExpType.getExpenseCode());
        expDetailCommon.setEcsAmt(toll.getBillAmt());
        expDetailCommon.setInputAmt(toll.getBillAmt());
        //新增其他额外的信息
        expDetailCommon.setFiscal(context.getFiscal());
        expDetailCommon.setMofDivCode(context.getMofDivCode());
        expDetailCommon.setAgyCode(context.getAgyCode());
        expDetailCommon.setEcsRelItemName(toll.getBillDescpt());
        expDetailCommon.setStartPlace(toll.getDeptStation());
        expDetailCommon.setEndPlace(toll.getDestStation());
        MadArea madArea = getMadAreaByCityName(toll.getCity(), context);
        if (Objects.nonNull(madArea)){
            expDetailCommon.setEndCity(madArea.getAreaName());
            expDetailCommon.setEndCityCode(madArea.getAreaCode());
        }
        String billDate = validAndGetDate(toll.getBillDate());
        if (StringUtil.isNotBlank(billDate)){
            String deptTime = validAndGetTimeSecond(toll.getTime());
            if (StringUtil.isNotBlank(deptTime)){
                expDetailCommon.setStartTime(toll.getBillDate() + " " + toll.getTime());
            }else{
                expDetailCommon.setStartTime(billDate);
            }
        }
        invoiceDtoWrapper.addExpDetail(expDetailCommon);
        fillDefaultClaimant(Collections.singletonList(expDetailCommon), context, toll);
        boolean isConfirm = checkIsConfirm(Collections.singletonList(expDetailCommon), context, invoiceDtoWrapper);
        invoiceDtoWrapper.setFlag(isConfirm?InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode() : InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
    }
}
