package com.pty.pcx.service.impl.msg.event;

import com.alibaba.fastjson.JSONObject;
import com.pty.message.sdk.anno.*;
import com.pty.message.sdk.constant.ModuleEnum;
import com.pty.message.sdk.event.RegistrableAbstractEvent;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@MessageFunc(module = ModuleEnum.PCX_APPROVAL, func = "PCX_APPROVAL_PRINT", desc = "审批打印")
public class PcxMsgApprovalPrintEvent extends RegistrableAbstractEvent {


    @MessageMiniUrl(attribute = "miniURL", desc = "小程序URL")
    @MessageReceivers(attribute = "receivers", desc = "接收者")
    @MessageAttribute(attribute = "claimantName", desc = "经办人")
    @MessageAttribute(attribute = "billType", desc = "单据类型")
    @MessageAttribute(attribute = "itemType", desc = "事项")
    @MessageAttribute(attribute = "reason", desc = "事由")
    @MessageAttribute(attribute = "money", desc = "金额")
    @MessageAttribute(attribute = "commitTime", desc = "提交日期")
    @MessageAttribute(attribute = "approvalNode", desc = "当前审批岗")
    @MessageAttribute(attribute = "tripTime", desc = "出差时间")
    @MessageAttribute(attribute = "tripPlaces", desc = "出差地")
    public PcxMsgApprovalPrintEvent(Object source) {
        super(source);
    }

    public PcxMsgApprovalPrintEvent(Object source, JSONObject transData) {
        super(source, transData);
    }
}
