package com.pty.pcx.service.impl.bas;


import com.pty.pcx.common.util.PcxUtil;
import com.pty.pcx.dao.bas.PcxBasCityClassifyDao;
import com.pty.pcx.dao.bas.PcxBasCityClassifyMainDao;
import com.pty.pcx.dao.costcontrollevel.PcxCostControlLevelDao;
import com.pty.pcx.dao.stand.PcxStandConditionDao;
import com.pty.pcx.dao.stand.PcxStandKeyDao;
import com.pty.pcx.dao.stand.PcxStandValueDao;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bas.PcxBasCityClassifyMain;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.entity.stand.PcxStandCondition;
import com.pty.pcx.entity.stand.PcxStandKey;
import com.pty.pcx.entity.stand.PcxStandValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.pty.mad.api.IPaValsetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 事务操作
 */
@Slf4j
@Indexed
@Service
public class TransOptService {

    @Autowired
    private PcxBasCityClassifyDao pcxBasCityClassifyDao;
    @Autowired
    private PcxBasCityClassifyMainDao pcxBasCityClassifyMainDao;
    @Autowired
    private PcxStandKeyDao pcxStandKeyDao;
    @Autowired
    private PcxStandValueDao pcxStandValueDao;
    @Autowired
    private PcxStandConditionDao pcxStandConditionDao;
    @Autowired
    private IPaValsetService selectByValsetCode;
    @Autowired
    private PcxCostControlLevelDao pcxCostControlLevelDao;



    /**
     * 主子表信息插入，同时更新支出标准
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void delAndInsertCityClassifyMain(Integer classifyType, String expenseTypeCode, Integer isSys, String agyCode, String fiscal, String mofDivCode,
                                         List<PcxBasCityClassify> list, List<PcxBasCityClassifyMain> mainList) {

        pcxBasCityClassifyMainDao.deleteClassify(classifyType, expenseTypeCode, isSys, agyCode, fiscal, mofDivCode);
        pcxBasCityClassifyDao.deleteClassify(classifyType, expenseTypeCode, agyCode, fiscal, mofDivCode);
        if(CollectionUtils.isNotEmpty(mainList)){
            PcxUtil.getPartitionBatchInsertList(mainList).forEach(o->pcxBasCityClassifyMainDao.batchInsert(o));
        }
        if(CollectionUtils.isNotEmpty(list)){
            PcxUtil.getPartitionBatchInsertList(list).forEach(o->pcxBasCityClassifyDao.batchInsert(o));
        }

//        //只有2或3个分类，才处理
//        if (mainList.size() == 2 || mainList.size() == 3){
//            //查询rowValueCode，判断是否要更新
//            PcxStandKey pcxStandKey = new PcxStandKey();
//            pcxStandKey.setRowKeyCode("cityClassify");
//            pcxStandKey.setFiscal(fiscal);
//            pcxStandKey.setAgyCode(agyCode);
//            pcxStandKey.setMofDivCode(mofDivCode);
//            List<String> rowValueCodeList = pcxStandKeyDao.selectListByAgyCode(pcxStandKey);
//            if(CollectionUtils.isNotEmpty(rowValueCodeList)){
//                String rowValueCode = rowValueCodeList.get(0);
//                String[] rowValueList = rowValueCode.split(",");
//                //城市分类级别有修改，需要更新支出标准
//                if (rowValueList.length != mainList.size()+1){
//                    if (rowValueList.length == 4){
//                        // 更新标准定义
//                        PcxStandKey pcxStandK = new PcxStandKey();
//                        pcxStandK.setRowKeyCode("cityClassify");
//                        pcxStandK.setRowValueCode("other,first,second");
//                        pcxStandK.setRowValueName("其他,一类地区,二类地区");
//                        pcxStandK.setUpdateTime(DateUtil.nowTime());
//                        pcxStandK.setAgyCode(agyCode);
//                        pcxStandK.setFiscal(fiscal);
//                        pcxStandK.setMofDivCode(mofDivCode);
//                        pcxStandKeyDao.updateByAgyCodeAndRowKeyCode(pcxStandK);
//                        // 删除三级分类
//                        PcxStandValue pcxStandValue = new PcxStandValue();
//                        pcxStandValue.setRowKeyCode("CITY_CLASSIFY");
//                        pcxStandValue.setRowValueCode("third");
//                        pcxStandValue.setAgyCode(agyCode);
//                        pcxStandValue.setFiscal(fiscal);
//                        pcxStandValue.setMofDivCode(mofDivCode);
//                        pcxStandValueDao.deleteByRowKeyValue(pcxStandValue);
//                    } else if (rowValueList.length == 3) {
//                        // 更新标准定义
//                        PcxStandKey pcxStandK = new PcxStandKey();
//                        pcxStandK.setRowKeyCode("cityClassify");
//                        pcxStandK.setRowValueCode("other,first,second,third");
//                        pcxStandK.setRowValueName("其他,一类地区,二类地区,三类地区");
//                        pcxStandK.setUpdateTime(DateUtil.nowTime());
//                        pcxStandK.setAgyCode(agyCode);
//                        pcxStandK.setFiscal(fiscal);
//                        pcxStandK.setMofDivCode(mofDivCode);
//                        pcxStandKeyDao.updateByAgyCodeAndRowKeyCode(pcxStandK);
//                        // 查费用控制级别
//                        PaValset paValset = new PaValset();
//                        paValset.setValsetCode("CONTROL_LEVEL");
//                        paValset.setAgyCode("*");
//                        paValset.setFiscal(Integer.valueOf(fiscal));
//                        paValset.setMofDivCode(mofDivCode);
//                        PageResult<PaValset> result = selectByValsetCode.selectByCode(paValset);
//                        List<PaValset> paValsets = result.getResult();
//                        // 查standcode
//                        PcxStandValue pcxStandValue = new PcxStandValue();
//                        pcxStandValue.setRowKeyCode("CITY_CLASSIFY");
//                        pcxStandValue.setAgyCode(agyCode);
//                        pcxStandValue.setFiscal(fiscal);
//                        pcxStandValue.setMofDivCode(mofDivCode);
//                        List<String> standCodes = pcxStandValueDao.selectStandCodesByAgy(pcxStandValue);
//
//                        List<PcxStandValueVO> pcxStandValueVOs = new ArrayList<>();
//                        for (String standCode : standCodes) {
//                            // 新增三级分类
//                            for (PaValset valset : paValsets) {
//                                PcxStandValue pcxStandVal = new PcxStandValue();
//                                pcxStandVal.setId(IDGenerator.id());
//                                pcxStandVal.setRowKeyCode("CITY_CLASSIFY");
//                                pcxStandVal.setRowKeyName("城市分类");
//                                pcxStandVal.setRowValueCode("third");
//                                pcxStandVal.setRowValueName("三类地区");
//                                pcxStandVal.setAgyCode(agyCode);
//                                pcxStandVal.setFiscal(fiscal);
//                                pcxStandVal.setMofDivCode(mofDivCode);
//                                pcxStandVal.setStandCode(standCode);
//                                pcxStandVal.setColumnKeyCode("controlLevel");
//                                pcxStandVal.setColumnKeyName("费用控制级别");
//                                pcxStandVal.setColumnValueCode(valset.getValCode());
//                                pcxStandVal.setColumnValueName(valset.getVal());
//                                pcxStandVal.setStandardValue("0");
//                                pcxStandVal.setTenantId(PtyContext.getTenantId());
//                                PcxStandValueVO pcxStandValueVO = new PcxStandValueVO();
//                                BeanUtils.copyProperties(pcxStandVal, pcxStandValueVO);
//                                pcxStandValueVOs.add(pcxStandValueVO);
//                            }
//                        }
//                        pcxStandValueDao.batchInsert(pcxStandValueVOs);
//                    }
//                }
//            }
//        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void delAndInsertCityPeakClassifyMain(Integer classifyType, String expenseTypeCode, Integer isSys, String agyCode, String fiscal, String mofDivCode,
                                                 List<PcxBasCityClassify> list, List<PcxBasCityClassifyMain> mainList) {

        pcxBasCityClassifyMainDao.deleteClassify(classifyType, expenseTypeCode, isSys, agyCode, fiscal, mofDivCode);
        pcxBasCityClassifyDao.deleteClassify(classifyType, expenseTypeCode, agyCode, fiscal, mofDivCode);
        if(CollectionUtils.isNotEmpty(mainList)){
            PcxUtil.getPartitionBatchInsertList(mainList).forEach(o->pcxBasCityClassifyMainDao.batchInsert(o));
        }
        if(CollectionUtils.isNotEmpty(list)){
            PcxUtil.getPartitionBatchInsertList(list).forEach(o->pcxBasCityClassifyDao.batchInsert(o));
        }


    }


    @Transactional(rollbackFor = Exception.class)
    public void carryOverStand(List<PcxStandKey> standKeys, List<PcxStandCondition> conditionList, List<PcxStandValue> standValues) {
        if (CollectionUtils.isNotEmpty(standKeys)){
            for (PcxStandKey standKey : standKeys) {
                pcxStandKeyDao.insert(standKey);
            }
        }
        if (CollectionUtils.isNotEmpty(conditionList)){
            for (PcxStandCondition pcxStandCondition : conditionList) {
                pcxStandConditionDao.insert(pcxStandCondition);
            }
        }
        if (CollectionUtils.isNotEmpty(standValues)){
            for (PcxStandValue pcxStandValue : standValues) {
                pcxStandValueDao.insert(pcxStandValue);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void carryOverCostControlLevel(List<PcxCostControlLevel> nextFiscalList) {
        if (CollectionUtils.isNotEmpty(nextFiscalList)){
            for (PcxCostControlLevel pcxCostControlLevel : nextFiscalList) {
                pcxCostControlLevelDao.insert(pcxCostControlLevel);
            }
        }
    }
}
