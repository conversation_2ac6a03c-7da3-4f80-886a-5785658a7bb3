package com.pty.pcx.service.impl.ecs.tax;

import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import lombok.Data;
import lombok.Setter;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Setter
@Data
@Indexed
@Component
public class RaiTaxCalculate implements EcsTaxCalculate{


    private EcsTaxCalculate next;
    @Override
    public boolean isMatch(PcxExpDetailEcsRel ecsRel, PcxBillExpDetailBase detail) {
        if (PcxConstant.TRAVEL_DETAIL_3021101.equals(detail.getExpDetailCode())){
            if (EcsEnum.BillType.RAI.getCode().equals(ecsRel.getEcsBillType())){
                if (detail.getInputAmt().compareTo(BigDecimal.ZERO) != 0){
                    BigDecimal taxAmt = detail.getInputAmt()
                            .divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("0.09"))
                            .setScale(2, RoundingMode.HALF_UP);;
                    detail.setTaxAmt(taxAmt);
                }
                detail.setTaxRate(new BigDecimal("0.09"));
                ecsRel.setTaxRate(new BigDecimal("0.09"));
                return true;
            }
        }
        return false;
    }

    @Override
    public EcsTaxCalculate next() {
        return next;
    }

}
