package com.pty.pcx.service.impl.ecs.ecstax;

import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
public class InvSpelTax implements EcsTaxCalculate{
    @Override
    public List<String> ecsBillType() {
        return Arrays.asList(EcsEnum.BillType.INV_ORD.getCode(), EcsEnum.BillType.INV_SPCL.getCode(), EcsEnum.BillType.EINV.getCode());
    }

    @Override
    public void childTaxCalculate(PcxExpDetailEcsRel ecsRel) {
        if (Objects.nonNull(ecsRel.getTaxRate())){
            BigDecimal taxAmt = ecsRel.getCheckAmt()
                    .divide(ecsRel.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP)
                    .multiply(ecsRel.getTaxRate())
                    .setScale(2, RoundingMode.HALF_UP);
            ecsRel.setTaxAmt(taxAmt);
        }
    }
}
