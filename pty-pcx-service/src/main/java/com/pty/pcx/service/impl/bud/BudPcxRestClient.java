package com.pty.pcx.service.impl.bud;

import com.pty.bud.entity.vo.BudPexBalanceVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Indexed
@Api(value = "pcxbud实现接口", tags = {"pcxbud实现接口表控制层"})
@Slf4j
@RestController
public class BudPcxRestClient {

    @Autowired
    private BudPcxService budPcxService;

    @PostMapping(value = "/api/bud/pex/selectPexByBalanceIds")
    Map<String, List<BudPexBalanceVo>> selectPexByBalanceIds(@RequestBody Map<String, Object> map){
        return budPcxService.selectPexByBalanceIds(map);
    }
}
