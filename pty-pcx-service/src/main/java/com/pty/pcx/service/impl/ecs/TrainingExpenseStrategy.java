package com.pty.pcx.service.impl.ecs;

import cn.bjca.sdk.ex.BusinessException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.pty.ecs.common.EcsServiceMsg;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.api.bill.PcxBillExpTrainingService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.ecs.ExpenseStrategy;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillStatusEnum;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.ecs.inv.InvoiceBaseDto;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.ecs.impl.EcsDtoTransHelper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.entity.contract.PcxBillContractRel;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.DelEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.util.PcxBillExpBasicUtils;
import com.pty.pcx.service.impl.ecs.dto.EcsListExpTypeCombine;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.ecs.EcsExpMatchVO;
import com.pty.pcx.vo.ecs.EcsWrongVO;
import com.pty.pub.common.exception.CommonException;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 培训费用报销策略实现类
 */
@Service
@Slf4j
public class TrainingExpenseStrategy implements ExpenseStrategy {
    @Resource
    private PcxBillExpDetailTrainingDao pcxBillExpDetailTrainingDao;
    @Resource
    private PcxEcsSettlDao pcxEcsSettlDao;
    @Resource
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    @Resource
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;
    @Resource
    private PcxBillExpTrainingDao pcxBillExpTrainingDao;
    @Resource
    private PcxBillExpTrainingService pcxBillExpTrainingService;
    @Resource
    private PcxBillService pcxBillService;
    @Resource
    private IEcsBillExternalService ecsBillExternalService;
    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private BillMainService billMainService;
    @Resource
    private IPcxBasItemExpService basItemExpService;

    @Resource
    private PcxBillAmtApportionService pcxBillAmtApportionService;

    @Autowired
    private PcxBillExpBasicUtils pcxBillExpBasicUtils;

    @Override
    public CheckMsg<?> handleExpense(StartExpenseQO expenseQO) {
        log.info("开始培训费用报销，单位：{}, 年度：{}", expenseQO.getAgyCode(), expenseQO.getFiscal());

        // 设置默认交易日期
        if (StringUtil.isEmpty(expenseQO.getTransDate())) {
            expenseQO.setTransDate(DateUtil.nowDate());
        }

        // 查询人员信息
        Optional.ofNullable(queryMadEmpDTO(expenseQO)).orElseThrow(() -> new CommonException("人员信息不存在"));

        // 预处理票据数据
        EcsProcessServiceImpl.InvoiceDtoCollect ecsCollect = new EcsProcessServiceImpl.InvoiceDtoCollect();
        if (CollectionUtils.isNotEmpty(expenseQO.getBillList())){
            ecsCollect = this.doProcessInvoices(expenseQO, expenseQO.getItemCode());
            //没有可以的票，只有无法报销的票
            if (CollectionUtils.isEmpty(ecsCollect.getWrappers())) {
                if (CollectionUtils.isNotEmpty(ecsCollect.getWrongEcs())){
                    log.info("错误的票,无法报销:{}", JSON.toJSONString(ecsCollect.getWrongEcs()));
                    return CheckMsg.fail("所选的票为假票或已报销的票");
                }
                return CheckMsg.fail("没有要处理的票");
            }
        }

        String billId;
        try {
            billId = pcxBillExpTrainingService.saveBillAndExpense(expenseQO, ecsCollect.getWrappers());
        } catch (RuntimeException e) {
            throw new BusinessException("单据保存失败: " + e.getMessage(), e);
        }

        return wrapperWrongEcs(billId, ecsCollect.getWrongEcs(), ecsCollect.getOtherExpTypeEcs());
    }

    @Override
    public CheckMsg<?> handleAddBill(AddInvoicesQO expenseQO) {
        log.info("addEcsTrainingBill:{}", JSON.toJSONString(expenseQO));
        // 2、查询报销单
        PcxBill view = billMainService.view(expenseQO.getBillId());
        if (Objects.isNull(view)) {
            return CheckMsg.fail("未查询到报销单");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())) {
            return CheckMsg.fail("报销单当前状态不可编辑");
        }

        // 预处理票据数据
        EcsProcessServiceImpl.InvoiceDtoCollect ecsCollect = new EcsProcessServiceImpl.InvoiceDtoCollect();
        if (CollectionUtils.isNotEmpty(expenseQO.getBillList())){
            ecsCollect = this.doProcessInvoices(expenseQO, expenseQO.getItemCode());
            log.info("handleAddBill wrappers:{}", JSON.toJSONString(ecsCollect));
            if (CollectionUtils.isEmpty(ecsCollect.getWrappers())
                    && CollectionUtils.isEmpty(ecsCollect.getWrongEcs())
                    && CollectionUtils.isEmpty(ecsCollect.getOtherExpTypeEcs())){
                return CheckMsg.fail("未查询到票信息");
            }
        }

        try {
            pcxBillExpTrainingService.addEcsBills(expenseQO, ecsCollect.getWrappers(), view);
        } catch (RuntimeException e) {
            log.error("addEcsCommonBill异常", e);
            return CheckMsg.fail(e.getMessage());
        }

        return wrapperWrongEcs(expenseQO.getBillId(), ecsCollect.getWrongEcs(), ecsCollect.getOtherExpTypeEcs());
    }

    @Override
    public CheckMsg<?> handUpdateBill(UpdateEcsCommonQO ecsCommonQO) {
        log.info("(training)handUpdateBill:{}", JSON.toJSONString(ecsCommonQO));
        PcxBill view = billMainService.view(ecsCommonQO.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        if (ecsCommonQO.getItemExpenseList()
                .stream()
                .allMatch(item->Objects.isNull(item.getExpenseList())
                        || item.getExpenseList().isEmpty())){
            return CheckMsg.fail("费用明细不能为空");
        }
        try {
            pcxBillExpTrainingService.updateEcsBill(ecsCommonQO, view);
        } catch (RuntimeException e) {
            log.error("(training)handUpdateBill", e);
            return CheckMsg.fail(e.getMessage());
        }
        return wrapperWrongEcs(ecsCommonQO.getBillId(), null, null);
    }


    /**
     * 查询发票信息，转换成报销dto，从dto中解析出费用明细，并取出错误的票和重复的票
     * @param invoiceQO
     * @param itemCode
     * @return
     */
    private EcsProcessServiceImpl.InvoiceDtoCollect doProcessInvoices(ExpInvoiceQO invoiceQO, String itemCode) {
        // 按事项查询匹配的费用类型
        PcxBasItemExpQO expQO = new PcxBasItemExpQO();
        expQO.setItemCode(itemCode);
        expQO.setAgyCode(invoiceQO.getAgyCode());
        expQO.setFiscal(invoiceQO.getFiscal());
        expQO.setMofDivCode(invoiceQO.getMofDivCode());
        List<String> expCodeList = basItemExpService.selectByItemCode(expQO).stream().map(
                PcxBasItemExp::getExpenseCode
        ).collect(Collectors.toList());
        log.info("理票选择事项关联的所有费用类型:{}", expCodeList);
        // 1.预处理
        Pair<Map<String, List>, List<InvoiceBaseDto>> mapListPair = queryEcsAndFilterFake(invoiceQO, expCodeList);
        // 2.非假票或者重复的票，但包含不是当前事项对应费用类型的票
        Map<String, List> invoiceMap = mapListPair.getLeft();

        //如果没有可用的票就返回空wrapper列表和错误的票
        List<InvoiceBaseDto> wrongEcsList = mapListPair.getRight();
        if (Objects.isNull(invoiceMap) || invoiceMap.isEmpty()){
            return EcsProcessServiceImpl.InvoiceDtoCollect.of(Lists.newArrayList(), wrongEcsList, Lists.newArrayList(), new HashMap<>(), new HashMap<>());
        }

        EcsProcessServiceImpl.InvoiceDtoCollect collect = pipelineDisposeEcs(invoiceMap, expCodeList, invoiceQO);

        return EcsProcessServiceImpl.InvoiceDtoCollect.of(collect.getWrappers(), wrongEcsList, collect.getOtherExpTypeEcs(), collect.getAuthClaimantMap(), collect.getInnerEmpMsgMap());
    }

    /**
     * 从票解析出费用明细，并表明票是否已匹配
     * @param invoiceMap
     * @param expCodeList
     * @param invoiceQO
     * @return
     */
    private EcsProcessServiceImpl.InvoiceDtoCollect pipelineDisposeEcs(Map<String, List> invoiceMap,
                                                                       List<String> expCodeList,
                                                                       ExpInvoiceQO invoiceQO) {
        // 2.打标识
        EcsProcessServiceImpl.InvoiceDtoCollect collect = this.wrapInvoice(invoiceMap, expCodeList);
        List<InvoiceDtoWrapper> wrappers = collect.getWrappers();
        if (CollectionUtils.isNotEmpty(wrappers)){
            // 3.组装处理链的上下文
            ProcessContext context = ProcessContextBuilder.build(wrappers, expCodeList, invoiceQO);
            // 4.处理链执行
            // 可多线程执行
            wrappers.forEach(w -> {
                if (!w.isSkipProcess()) { // 跳过则不进行处理
                    processInvoice(w, context);
                }else{
                    w.setFlag(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
                }
            });
            collect.setAuthClaimantMap(context.getAuthClaimantMap());
            collect.setInnerEmpMsgMap(context.getEmpMsgMap());
        }
        return collect;
    }

    /**
     * 处理单张发票
     * @param wrapper
     * @param context
     */
    private void processInvoice(InvoiceDtoWrapper wrapper, ProcessContext context) {
        InvoiceProcessChain chain = new InvoiceProcessChain(context);
        chain.process(wrapper);
    }

    /**
     * 对发票map进行包装
     * @param invoiceMap
     * @param expCodeList
     * @return
     */
    private EcsProcessServiceImpl.InvoiceDtoCollect wrapInvoice(Map<String, List> invoiceMap, Collection<String> expCodeList) {

        List<InvoiceDtoWrapper> wrappers = new ArrayList<>();
        List<InvoiceBaseDto> otherExpTypeEcs = new ArrayList<>();
        invoiceMap.forEach((k, v) -> {
            v.forEach(i -> {
                InvoiceBaseDto ecs = (InvoiceBaseDto) i;
                if (ecs.isOtherBizEcs()){
                    otherExpTypeEcs.add(ecs);
                }else {
                    InvoiceDtoWrapper dtoWrapper = InvoiceDtoWrapper.builder()
                            .type(k).dto(i).build();
                    // 按expList对wrapper的flag进行赋值，不在expList内的设置为不用处理
                    dtoWrapper.setSkipProcess(InvoiceDtoUtil.isSkipProcess(dtoWrapper, expCodeList));

                    wrappers.add(dtoWrapper);
                }
            });
        });
        return EcsProcessServiceImpl.InvoiceDtoCollect.ofWrapperAndOtherExpTypeEcs(wrappers, otherExpTypeEcs);
    }

    /**
     * 查询单据视图，处理理票过程中信息
     */
    private CheckMsg wrapperWrongEcs(String billId, List<InvoiceBaseDto> wrongEcsList, List<InvoiceBaseDto> otherExpTypeList) {
        CheckMsg<PcxBillVO> view = pcxBillService.view(billId, PositionBlockEnum.ECSEXPMATCH, PositionBlockEnum.SPECIFICITY);
        EcsExpMatchVO vo = view.getData().getEcsExpMatch();
        //错误费用类型的票数量提示
        if (CollectionUtils.isNotEmpty(otherExpTypeList)) {
            vo.setTidyEcsHint(String.format(PcxConstant.TIDY_ECS_HINT, otherExpTypeList.size()));
        }
        if (CollectionUtils.isNotEmpty(wrongEcsList)) {
            vo.setWrongEcsList(collectWrongEcsList(wrongEcsList));
        }
        return view;
    }

    private List<EcsWrongVO> collectWrongEcsList(List<InvoiceBaseDto> wrongEcsList) {
        List<EcsWrongVO> result = new ArrayList<>();
        for (InvoiceBaseDto invoiceBaseDto : wrongEcsList) {
            EcsWrongVO vo = EcsWrongVO
                    .builder()
                    .attachId(invoiceBaseDto.getAttachId())
                    .fileName(invoiceBaseDto.getBillDescpt())
                    .billDate(invoiceBaseDto.getBillDate())
                    .build();
            if (Objects.equals(invoiceBaseDto.getIsRelExp(),PcxConstant.HAS_EXPENSE)){
                vo.setWrongMessage("重复的票");
            } else{
                vo.setWrongMessage("查验为假");
            }
            result.add(vo);
        }
        return result;
    }
    /**
     * 预处理票据数据，过滤无效票据
     */
    private Map<String, List> preprocessEcsMap(StartExpenseQO expenseQO) {
        if (CollectionUtils.isEmpty(expenseQO.getBillList())) {
            return Collections.emptyMap();
        }
        Pair<Map<String, List>, List<InvoiceBaseDto>> result = queryEcsAndFilterFake(expenseQO, Lists.newArrayList());
        Map<String, List> ecsMap = result.getLeft();

        if (ecsMap == null || ecsMap.isEmpty()) {
            if (CollectionUtils.isNotEmpty(result.getRight())) {
                throw new CommonException("所选票为假票或已报销的票");
            }
            throw new CommonException("没有要处理的票");
        }

        return ecsMap;
    }

    /**
     * 查询并过滤无效发票
     */
    private Pair<Map<String, List>, List<InvoiceBaseDto>> queryEcsAndFilterFake(ExpInvoiceQO invoiceQO, List<String> itemExpTypeList) {
        EcsListExpTypeCombine combine = this.preprocessReal(invoiceQO, itemExpTypeList);
        Map<String, List> invoiceMap = combine.getEcsMap();

        if (Objects.isNull(invoiceMap) || invoiceMap.isEmpty()) {
            return Pair.of(new HashMap<>(), new ArrayList<>());
        }

        List<InvoiceBaseDto> invalidInvoices = filterInvalidInvoices(invoiceMap);
        return Pair.of(combine.getEcsMap(), invalidInvoices);
    }

    /**
     * 过滤掉已经报销或标记为假票的发票
     */
    private List<InvoiceBaseDto> filterInvalidInvoices(Map<String, List> invoiceMap) {
        Iterator<Map.Entry<String, List>> iterator = invoiceMap.entrySet().iterator();
        List<InvoiceBaseDto> invalidList = new ArrayList<>();

        while (iterator.hasNext()) {
            Map.Entry<String, List> entry = iterator.next();
            Iterator<?> invoiceIterator = entry.getValue().iterator();
            while (invoiceIterator.hasNext()) {
                InvoiceBaseDto invoice = (InvoiceBaseDto) invoiceIterator.next();

                // 合同可以多次报销
                if (!Objects.equals(invoice.getBillTypeCode(), EcsEnum.BillType.FILE_CONTRACT.getCode())
                        && (invoice.getIsRelExp() == PcxConstant.HAS_EXPENSE
                        || PcxConstant.FAKE_ECS.equals(invoice.getIsValid()))) {
                    invalidList.add(invoice);
                    invoiceIterator.remove();
                }
            }

            if (CollectionUtils.isEmpty(entry.getValue())) {
                iterator.remove();
            }
        }

        return invalidList;
    }

    /**
     * 调用 ECS 获取发票数据，并按票种进行费用类型匹配。
     * <p>
     * 主要流程：
     * 1. 查询发票数据；
     * 2. 对每张发票调用对应处理器获取关联的费用类型；
     * 3. 将费用类型统一收集用于后续校验；
     * 4. 若某发票所匹配的费用类型不在允许范围内，则标记为“非当前业务票据”。
     *
     * @param invoiceQO       发票查询参数
     * @param itemExpTypeList 允许的费用类型编码列表
     * @return EcsListExpTypeCombine 包含发票数据和费用类型的组合对象
     */
    private EcsListExpTypeCombine preprocessReal(ExpInvoiceQO invoiceQO, List<String> itemExpTypeList) {
        EcsListExpTypeCombine result = new EcsListExpTypeCombine();

        // 1. 查询发票数据
        Map<String, List> ecsMap = ecsBillExternalService.queryBillList2(invoiceQO);
        result.setEcsMap(ecsMap);

        if (ecsMap.isEmpty()) {
            return result;
        }

        // 2. 获取各票据类型的处理类映射
        Map<String, EcsDtoTransHelper.EcsBillDispose> disposeMap = EcsBillExternalServiceImpl.getEcsBillDisposeMap();

        // 3. 遍历所有票据类型及其对应的票据列表
        for (Map.Entry<String, List> entry : ecsMap.entrySet()) {
            String billType = entry.getKey();
            List<?> invoices = entry.getValue();

            EcsDtoTransHelper.EcsBillDispose handler = disposeMap.get(billType);
            if (handler == null) {
                log.warn("未找到票据类型[{}]的处理类", billType);
                continue;
            }

            // 4. 遍历该类型的每一张票据并处理
            for (Object invoice : invoices) {
                processSingleInvoice(invoice, handler, itemExpTypeList, result);
            }
        }

        return result;
    }

    /**
     * 处理单张发票：收集其关联的费用类型，并判断是否属于当前事项允许报销的范围。
     *
     * @param invoice         当前发票对象
     * @param handler         对应票据类型的处理类
     * @param itemExpTypeList 允许的费用类型编码列表
     * @param result          结果容器，用于存储收集到的数据
     */
    private void processSingleInvoice(Object invoice,
                                      EcsDtoTransHelper.EcsBillDispose handler,
                                      List<String> itemExpTypeList,
                                      EcsListExpTypeCombine result) {
        // 收集当前发票关联的所有费用类型
        EcsListExpTypeCombine expTypeCollect = new EcsListExpTypeCombine();
        handler.collectExpTypeSingle(invoice, expTypeCollect::addExpType);

        List<PcxBasExpType> expTypes = expTypeCollect.getExpTypes();
        if (CollectionUtils.isNotEmpty(expTypes)) {
            result.addExpType(expTypes); // 合并到全局费用类型集合中

            // 判断是否全部费用类型都不在允许范围内
            boolean allNotInAllowed = CollectionUtils.isNotEmpty(itemExpTypeList)
                    && expTypes.stream().allMatch(type -> isNotInItemExpType(type, itemExpTypeList));

            if (allNotInAllowed) {
                ((InvoiceBaseDto) invoice).setOtherBizEcs(true); // 标记为非当前业务票据
            }
        }
    }

    /**
     * 判断费用类型是否不在事项允许范围内
     */
    private boolean isNotInItemExpType(PcxBasExpType expType, List<String> itemExpTypeList) {
        String code = expType.getExpenseCode();
        if (expType.getIsRefine() == 1 && expType.getParentCode().equals(expType.getLastCode())){
            code = expType.getParentCode();
        }else if( expType.getIsRefine() == 1 && !expType.getParentCode().equals(expType.getLastCode())){
            code = expType.getLastCode();
        }
        return !itemExpTypeList.contains(code);
    }

    /**
     * 查询用户信息
     */
    private MadEmployeeDTO queryMadEmpDTO(ExpInvoiceQO expenseQO) {
        PcxBaseDTO dto = new PcxBaseDTO();
        dto.setFiscal(expenseQO.getFiscal());
        dto.setAgyCode(expenseQO.getAgyCode());
        dto.setMofDivCode(expenseQO.getMofDivCode());
        dto.setTenantId(expenseQO.getTenantId());
        return madEmployeeExternalService.selectEmployeeByUserCode(dto, expenseQO.getUserCode());
    }

    /**
     * 删除培训费关联票据的业务处理
     *
     * @param delEcsCommonQO 删除请求参数
     * @return CheckMsg 删除结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg<?> handleDelete(DelEcsCommonQO delEcsCommonQO) {
        log.info("开始处理培训费票删除，入参:{}", JSON.toJSONString(delEcsCommonQO));

        // 1. 查询报销单信息
        PcxBill pcxBill = billMainService.view(delEcsCommonQO.getBillId());
        if (Objects.isNull(pcxBill)) {
            return CheckMsg.fail("未查询到报销单");
        }
        if (!pcxBill.getBillStatus().equals(BillStatusEnum.SAVE.getCode())) {
            return CheckMsg.fail("报销单当前状态不可编辑");
        }

        // 2. 查询该单据下所有 ECS 票据关系
        List<PcxExpDetailEcsRel> ecsRelList = pcxExpDetailEcsRelDao.selectList(
                Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                        .eq(PcxExpDetailEcsRel::getBillId, delEcsCommonQO.getBillId())
        );

        // 3. 获取本次要删除的 ECS 票据关系
        PcxExpDetailEcsRel dbEcsRel = ecsRelList.stream()
                .filter(item -> Objects.equals(item.getId(), delEcsCommonQO.getEcsRelId()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到对应的票据关系"));

        String ecsBillId = dbEcsRel.getEcsBillId();

        // 4. 找出与本次删除相关的所有票据关系数据
        List<PcxExpDetailEcsRel> delEcsRelList;
        if (StringUtil.isNotEmpty(ecsBillId)) {
            delEcsRelList = ecsRelList.stream()
                    .filter(item -> item.getEcsBillId().equals(ecsBillId))
                    .collect(Collectors.toList());
        }else{
            //如果通过补充信息添加票据，没有票据id
            delEcsRelList = ecsRelList.stream()
                    .filter(item -> item.getId().equals(delEcsCommonQO.getEcsRelId()))
                    .collect(Collectors.toList());
        }

        // 5. 收集需要删除的明细 ID 和关系 ID
        List<String> delRelIds = delEcsRelList.stream()
                .map(PcxExpDetailEcsRel::getId)
                .collect(Collectors.toList());

        List<String> delDetailIds = delEcsRelList.stream()
                .map(PcxExpDetailEcsRel::getDetailId)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.toList());

        // 6. 获取剩余未删除的票据关系
        List<PcxExpDetailEcsRel> otherRelList = ecsRelList.stream()
                .filter(item -> !delRelIds.contains(item.getId()))
                .collect(Collectors.toList());

        // 7. 查询费用主表信息
        PcxBillExpTraining pcxBillExpTraining = pcxBillExpTrainingDao.selectOne(
                Wrappers.<PcxBillExpTraining>lambdaQuery()
                        .eq(PcxBillExpTraining::getBillId, delEcsCommonQO.getBillId())
        );

        // 8. 查询并过滤附件关系
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(
                        Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                                .eq(PcxBillExpAttachRel::getBillId, pcxBill.getId())
                ).stream()
                .filter(item -> !item.getRelId().equals(ecsBillId))
                .collect(Collectors.toList());

        // 9. 更新单据金额
        collectBillAmt(pcxBill, pcxBillExpTraining, otherRelList);
        // 确保有明确的更新条件
        pcxBillExpTrainingDao.update(pcxBillExpTraining,
                Wrappers.<PcxBillExpTraining>lambdaUpdate()
                        .eq(PcxBillExpTraining::getBillId, pcxBillExpTraining.getBillId())
                        .eq(PcxBillExpTraining::getExpenseCode, pcxBillExpTraining.getExpenseCode()));

        pcxBill = billMainService.saveOrUpdate(pcxBill);

        Pair<List<PcxBillAmtApportion>, List<PcxBillAmtApportionDepartment>> apportionPair = pcxBillAmtApportionService.initApportion(pcxBill, Collections.singletonList(pcxBillExpTraining));

        // 保存分摊信息
        pcxBillExpBasicUtils.batchInsertBillAmtApportion(pcxBill, apportionPair.getLeft(), apportionPair.getRight());

        // 10. 构建 ECS 关系（同步更新外部系统）
        PcxBillContractRel contractRel = new PcxBillContractRel();
        buildEcsRel(pcxBill, otherRelList, attachRelList, contractRel);

        // 11. 删除数据库记录
        pcxExpDetailEcsRelDao.deleteBatchIds(delRelIds); // 删除票据关系
        pcxBillExpAttachRelDao.delete(
                Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                        .eq(PcxBillExpAttachRel::getBillId, pcxBill.getId())
                        .eq(PcxBillExpAttachRel::getRelId, ecsBillId)
        );
        pcxEcsSettlDao.delete(
                Wrappers.lambdaQuery(PcxEcsSettl.class)
                        .eq(PcxEcsSettl::getBillId, pcxBill.getId())
                        .eq(PcxEcsSettl::getEcsBillId, ecsBillId)
        );

        // 12. 删除明细数据
        if (CollectionUtils.isNotEmpty(delDetailIds)) {
            pcxBillExpDetailTrainingDao.deleteBatchIds(delDetailIds);
        }

        log.info("培训费票删除成功，billId={}", delEcsCommonQO.getBillId());
        return wrapperWrongEcs(delEcsCommonQO.getBillId(), null, null);
    }

    @Override
    public CheckMsg<?> updateNoEcs(UpdateNoEcsCommonQO qo) {
        log.info("updateNoEcs:{}", JSON.toJSONString(qo));
        PcxBill view = billMainService.view(qo.getBillId());
        if (Objects.isNull(view)){
            return CheckMsg.fail("未查询到报销单信息");
        }
        if (!view.getBillStatus().equals(BillStatusEnum.SAVE.getCode())){
            return CheckMsg.fail("报销单当前状态不可编辑");
        }
        pcxBillExpTrainingService.updateNoEcs(qo, view);
        return wrapperWrongEcs(qo.getBillId(),null,null);
    }

    /**
     * 根据剩余的票据关系重新计算报销单和费用明细的总金额
     */
    private void collectBillAmt(PcxBill bill, PcxBillExpTraining pcxBillExpTraining, List<PcxExpDetailEcsRel> allEcsRel) {
        Map<String, BigDecimal> inputAmtMap = new HashMap<>();
        Map<String, BigDecimal> checkAmtMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(allEcsRel)) {
            for (PcxExpDetailEcsRel rel : allEcsRel) {
                String expTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ?
                        PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();

                inputAmtMap.merge(expTypeCode, rel.getInputAmt(), BigDecimal::add);
                checkAmtMap.merge(expTypeCode, rel.getCheckAmt(), BigDecimal::add);
            }
        }

        String expenseCode = pcxBillExpTraining.getExpenseCode();
        BigDecimal totalInputAmt = inputAmtMap.getOrDefault(expenseCode, BigDecimal.ZERO);
        BigDecimal totalCheckAmt = checkAmtMap.getOrDefault(expenseCode, BigDecimal.ZERO);

        pcxBillExpTraining.setInputAmt(totalInputAmt);
        pcxBillExpTraining.setCheckAmt(totalCheckAmt);

        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);
    }

    /**
     * 构建或更新 ECS 系统中的票据关联关系（用于外部接口调用）
     */
    private void buildEcsRel(PcxBill bill, List<PcxExpDetailEcsRel> allEcsRel,
                             List<PcxBillExpAttachRel> allAttachRelList,
                             PcxBillContractRel contractRel) {
        BuildExpRelQO qo = new BuildExpRelQO();
        qo.setAgencyCode(bill.getAgyCode());
        qo.setTargetBillId(bill.getId());
        qo.setTargetBillNo(bill.getBillNo());
        qo.setTargetFiscalYear(Integer.valueOf(bill.getFiscal()));
        qo.setMofDivCode(bill.getMofDivCode());
        qo.setUserCode(bill.getClaimantCode());
        qo.setUserName(bill.getClaimantName());

        List<BuildExpRelQO.InvoiceQO> billList = new ArrayList<>();
        Map<String, List<PcxBillExpAttachRel>> attachRelMap = allAttachRelList.stream()
                .collect(Collectors.groupingBy(PcxBillExpAttachRel::getRelId));

        // 按照 ecsBillId 分组，构建发票请求对象
        Map<String, List<PcxExpDetailEcsRel>> grouped = allEcsRel.stream()
                .filter(rel -> StringUtil.isNotEmpty(rel.getEcsBillId()))
                .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillId));

        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : grouped.entrySet()) {
            BuildExpRelQO.InvoiceQO invoiceQO = new BuildExpRelQO.InvoiceQO();
            invoiceQO.setBillId(entry.getKey());
            invoiceQO.setBillAttachType("0");
            invoiceQO.setAgencyCode(bill.getAgyCode());
            invoiceQO.setFiscalYear(Integer.parseInt(bill.getFiscal()));
            invoiceQO.setMofDivCode(bill.getMofDivCode());
            billList.add(invoiceQO);

            // 添加附件关系
            List<PcxBillExpAttachRel> attachments = attachRelMap.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(attachments)) {
                for (PcxBillExpAttachRel attach : attachments) {
                    BuildExpRelQO.InvoiceQO attachQO = new BuildExpRelQO.InvoiceQO();
                    attachQO.setBillId(attach.getAttachId());
                    attachQO.setBillAttachType("1");
                    attachQO.setAgencyCode(bill.getAgyCode());
                    attachQO.setFiscalYear(Integer.parseInt(bill.getFiscal()));
                    attachQO.setMofDivCode(bill.getMofDivCode());
                    billList.add(attachQO);
                }
            }
        }

        // 添加合同关系（如果存在）
        if (contractRel != null && StringUtil.isNotEmpty(contractRel.getContractId())) {
            BuildExpRelQO.InvoiceQO attachQO = new BuildExpRelQO.InvoiceQO();
            attachQO.setBillId(contractRel.getContractId());
            attachQO.setBillAttachType("1");
            attachQO.setAgencyCode(bill.getAgyCode());
            attachQO.setFiscalYear(Integer.parseInt(bill.getFiscal()));
            attachQO.setMofDivCode(bill.getMofDivCode());
            billList.add(attachQO);
        }

        qo.setBillList(billList);

        // 调用外部服务更新 ECS 数据
        EcsServiceMsg msg = CollectionUtils.isNotEmpty(billList)
                ? ecsBillExternalService.buildExpRel(qo)
                : ecsBillExternalService.deleteExpRel(qo);

        if (!msg.isSuccess()) {
            throw new RuntimeException("更新ECS票据状态失败：" + msg.getMsgInfo());
        }
    }
}
