package com.pty.pcx.service.impl.bas;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.bas.IPcxBasItemAuthService;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.common.constant.OperationCNConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.ResponseErrorCode;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemDao;
import com.pty.pcx.dao.bas.PcxBasItemExpDao;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemAuth;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.qo.bas.PcxBasItemAuthQO;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.treasurypay.detail.BillFuncCodeItemRelQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.bas.BasItemVO;
import com.pty.pcx.vo.treasurypay.detail.BillFuncCodeItemRelVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (PcxBasItem)表服务实现类
 * <AUTHOR>
 * @since 2024-10-24 21:23:38
 */
@Slf4j
@Indexed
@Service
public class PcxBasItemServiceImpl implements IPcxBasItemService {

	@Autowired
	private PcxBasItemDao pcxBasItemDao;

	@Autowired
	private IPcxBasItemExpService pcxBasItemExpService;

	@Autowired
	private BatchServiceUtil batchServiceUtil;

	@Autowired
	private IPcxBasItemAuthService pcxBasItemAuthService;

	@Autowired
	private IPcxMadEmployeeService pcxMadEmployeeService;

	@Autowired
	private PcxBasItemExpDao pcxBasItemExpDao;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Response<?> save(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()) {
			return Response.fail().setMsg(valided.getMsgInfo());
		}
		if(StringUtil.isEmpty(pcxBasItemQO.getItemCode())){
			return Response.fail().setMsg("事项类型编码不能为空");
		}
		CheckMsg<?> checkedLevel = isFirstLevel(pcxBasItemQO);
		if(!checkedLevel.isSuccess()){
			return Response.fail().setMsg(checkedLevel.getMsgInfo());
		}
		CheckMsg<?> checkedExist = checkItemExists(pcxBasItemQO);
		if(!checkedExist.isSuccess()){
			return Response.fail().setMsg(checkedExist.getMsgInfo());
		}
		pcxBasItemQO.setCreator(pcxBasItemQO.getUserCode());
		pcxBasItemQO.setCreatorName(pcxBasItemQO.getUserName());
		pcxBasItemQO.setCreatedTime(DateUtil.getCurDate());
		pcxBasItemQO.setId(StringUtil.getUUID());
		if(StringUtil.isNotEmpty(pcxBasItemQO.getParentCode())){
			pcxBasItemQO.setIsLeaf(PubConstant.LOGIC_TRUE);
		}
		pcxBasItemDao.insertSelective(pcxBasItemQO);
		// 保存事项与费用的关联关系
		if(CollectionUtil.isNotEmpty(pcxBasItemQO.getPcxBasExpTypes())){
			CheckMsg<?> checkMsg = saveOrUpdateExpReleation(pcxBasItemQO, Boolean.TRUE);
			if(!checkMsg.isSuccess()){
				throw new RuntimeException(checkMsg.getMsgInfo());
			}
		}
		// 保存事项权限关系
		saveOrUpdateAuthRel(pcxBasItemQO,Boolean.TRUE);
		return Response.success("事项类型新增成功");
	}

	private void saveOrUpdateAuthRel(PcxBasItemQO pcxBasItemQO, Boolean isSave) {
		boolean isEmptyDepartmentsOrEmployees = CollectionUtil.isEmpty(pcxBasItemQO.getDepartments()) || CollectionUtil.isEmpty(pcxBasItemQO.getEmployees());
		if (isEmptyDepartmentsOrEmployees && isSave) {
			return;
		}
		if (!isSave){
			PcxBasItemAuthQO pcxBasItemAuthQO = new PcxBasItemAuthQO();
			pcxBasItemAuthQO.setItemCode(pcxBasItemQO.getItemCode());
			pcxBasItemAuthQO.setMofDivCode(pcxBasItemQO.getMofDivCode());
			pcxBasItemAuthQO.setFiscal(pcxBasItemQO.getFiscal());
			pcxBasItemAuthQO.setAgyCode(pcxBasItemQO.getAgyCode());
			pcxBasItemAuthQO.setTenantId(StringUtil.isEmpty(pcxBasItemQO.getTenantId())? PtyContext.getTenantId():pcxBasItemQO.getTenantId());
			pcxBasItemAuthService.deleteByItemCode(pcxBasItemAuthQO);
		}
		saveAuthRel(pcxBasItemQO);
	}

	private void saveAuthRel(PcxBasItemQO pcxBasItemQO) {
		if (CollectionUtil.isEmpty(pcxBasItemQO.getDepartments()) && CollectionUtil.isEmpty(pcxBasItemQO.getEmployees())) {
			return;
		}
		String fiscal = pcxBasItemQO.getFiscal();
		String agyCode = pcxBasItemQO.getAgyCode();
		String mofDivCode = pcxBasItemQO.getMofDivCode();
		String tenantId = pcxBasItemQO.getTenantId();
		String itemCode = pcxBasItemQO.getItemCode();
		String userCode = pcxBasItemQO.getUserCode();
		String userName = pcxBasItemQO.getUserName();
		setAuthFields(pcxBasItemQO.getDepartments(), fiscal, agyCode, mofDivCode, tenantId, itemCode, userCode, userName, PcxBasItemAuthServiceImpl.DEPARTMENT);
		setAuthFields(pcxBasItemQO.getEmployees(), fiscal, agyCode, mofDivCode, tenantId, itemCode, userCode, userName, PcxBasItemAuthServiceImpl.EMPLOYEE);
		List<PcxBasItemAuth> pcxBasItemAuths = new ArrayList<>(pcxBasItemQO.getDepartments());
		pcxBasItemAuths.addAll(pcxBasItemQO.getEmployees());
		pcxBasItemAuthService.saveBatch(pcxBasItemAuths);
	}

	private void setAuthFields(List<PcxBasItemAuth> authList, String fiscal, String agyCode, String mofDivCode, String tenantId, String itemCode, String userCode, String userName, String authType) {
		for (PcxBasItemAuth auth : authList) {
			auth.setAuthCode(auth.getAuthCode().replace("DEPT", ""));
			auth.setFiscal(fiscal);
			auth.setAgyCode(agyCode);
			auth.setMofDivCode(mofDivCode);
			auth.setTenantId(tenantId);
			auth.setId(StringUtil.getUUID());
			auth.setAuthType(authType);
			auth.setItemCode(itemCode);
			auth.setCreator(userCode);
			auth.setCreatedTime(DateUtil.getCurDate());
			auth.setCreatorName(userName);
			auth.setModifier(userCode);
			auth.setModifiedTime(DateUtil.getCurDate());
			auth.setModifierName(userName);
		}
	}

	/**
	 * 修改数据
	 * @param pcxBasItemQO 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Response<?> updateById( PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()) {
			return Response.fail().setMsg(valided.getMsgInfo());
		}
		if(StringUtil.isEmpty(pcxBasItemQO.getId())){
			return Response.fail().setMsg("请选择需要修改的事项类型");
		}
		pcxBasItemQO.setModifier(pcxBasItemQO.getUserCode());
		pcxBasItemQO.setModifiedTime(DateUtil.getCurDate());
		pcxBasItemQO.setModifierName(pcxBasItemQO.getUserName());
		pcxBasItemDao.updateById(pcxBasItemQO);
		CheckMsg<?> checkMsg = saveOrUpdateExpReleation(pcxBasItemQO, Boolean.FALSE);
		if(!checkMsg.isSuccess()){
			throw new RuntimeException(checkMsg.getMsgInfo());
		}
		// 保存事项权限关系
		saveOrUpdateAuthRel(pcxBasItemQO,Boolean.FALSE);
		return Response.success("事项类型修改成功");
	}


	@Override
	public Response<?> selectWithPage(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()){
			return Response.fail().setMsg(valided.getMsgInfo());
		}
		PageInfo<PcxBasItem> pageInfo = PageHelper.startPage(pcxBasItemQO.getPageIndex(), pcxBasItemQO.getPageSize())
				.doSelectPageInfo(() -> {
					pcxBasItemDao.selectSimpleList(pcxBasItemQO);
				});
		return Response.success(pageInfo);
	}

	private PcxBasItemVO convertToVO(PcxBasItem pcxBasItem,String billFuncCode) {
		PcxBasItemVO pcxBasItemVO = new PcxBasItemVO();
		BeanUtils.copyProperties(pcxBasItem, pcxBasItemVO);
		List<String> expenseCodes = getExpenseCodes(pcxBasItem,billFuncCode);
		pcxBasItemVO.setExpenseCodes(expenseCodes);
		assembleItemAuth(pcxBasItem, pcxBasItemVO);
		return pcxBasItemVO;
	}

	@Override
	public PcxBasItemVO selectById(PcxBasItemQO pcxBasItemQO) {
		if (StringUtil.isEmpty(pcxBasItemQO.getId())) {
			return new PcxBasItemVO();
		}
		PcxBasItem pcxBasItem = pcxBasItemDao.selectByQO(pcxBasItemQO);
		return convertToVO(pcxBasItem,pcxBasItemQO.getBilltypeCode());
	}

	@Override
	public PcxBasItemVO selectByItemCode(PcxBasItemQO pcxBasItemQO) {
		if (StringUtil.isEmpty(pcxBasItemQO.getItemCode())) {
			return new PcxBasItemVO();
		}

		PcxBasItem pcxBasItem = pcxBasItemDao.selectByQO(pcxBasItemQO);
		return convertToVO(pcxBasItem,pcxBasItemQO.getBilltypeCode());
	}

	private void assembleItemAuth(PcxBasItem pcxBasItem, PcxBasItemVO pcxBasItemVO) {
		if (ObjectUtils.isEmpty(pcxBasItem)|| ObjectUtils.isEmpty(pcxBasItemVO)) {
			return;
		}
		PcxBasItemAuthQO pcxBasItemAuthQO = new PcxBasItemAuthQO();
		pcxBasItemAuthQO.setMofDivCode(pcxBasItem.getMofDivCode());
		pcxBasItemAuthQO.setAgyCode(pcxBasItem.getAgyCode());
		pcxBasItemAuthQO.setFiscal(pcxBasItem.getFiscal());
		pcxBasItemAuthQO.setTenantId(pcxBasItem.getTenantId());
		pcxBasItemAuthQO.setItemCode(pcxBasItem.getItemCode());
		List<PcxBasItemAuth> pcxBasItemAuths = pcxBasItemAuthService.selectByQO(pcxBasItemAuthQO);
		if (pcxBasItemAuths == null) {
			pcxBasItemVO.setDepartments(new ArrayList<>());
			pcxBasItemVO.setEmployees(new ArrayList<>());
			return;
		}
		Map<String, List<PcxBasItemAuth>> pcxBasItemGroup = pcxBasItemAuths.stream()
				.collect(Collectors.groupingBy(PcxBasItemAuth::getAuthType));
		pcxBasItemVO.setDepartments(CollectionUtil.isEmpty(pcxBasItemGroup.get(PcxBasItemAuthServiceImpl.DEPARTMENT))
				? new ArrayList<>()
				: pcxBasItemGroup.get(PcxBasItemAuthServiceImpl.DEPARTMENT));
		pcxBasItemVO.setEmployees(CollectionUtil.isEmpty(pcxBasItemGroup.get(PcxBasItemAuthServiceImpl.EMPLOYEE))
				? new ArrayList<>()
				: pcxBasItemGroup.get(PcxBasItemAuthServiceImpl.EMPLOYEE));
	}

	private List<String> getExpenseCodes(PcxBasItem pcxBasItem,String billFuncCode) {
		PcxBasItemExpQO pcxBasItemExpQO = new PcxBasItemExpQO();
		pcxBasItemExpQO.setMofDivCode(pcxBasItem.getMofDivCode());
		pcxBasItemExpQO.setAgyCode(pcxBasItem.getAgyCode());
		pcxBasItemExpQO.setFiscal(pcxBasItem.getFiscal());
		pcxBasItemExpQO.setTenantId(pcxBasItem.getTenantId());
		pcxBasItemExpQO.setItemCode(pcxBasItem.getItemCode());
		pcxBasItemExpQO.setBilltypeCode(billFuncCode);
		List<PcxBasItemExp> pcxBasItemExpList = pcxBasItemExpService.selectByItemCode(pcxBasItemExpQO);
		if (CollectionUtil.isNotEmpty(pcxBasItemExpList)) {
			return pcxBasItemExpList.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	@Override
	public Response<?> disEnableOrEnableByQO(PcxBasItemQO pcxBasItemQO, Integer isEnable) {
		String action = PubConstant.LOGIC_TRUE==isEnable  ? OperationCNConstant.ENABLE : OperationCNConstant.DISABLE;
		CheckMsg<?> checkFlag = checkParams(pcxBasItemQO,action);
		if(!checkFlag.isSuccess()){
			return Response.fail().setMsg(checkFlag.getMsgInfo());
		}
		PcxBasItem pcxBasItems = pcxBasItemDao.selectByQO(pcxBasItemQO);
		if(ObjectUtils.isEmpty(pcxBasItems)){
			return Response.fail().setMsg("事项类型不存在");
		}
		// 禁用或启用下级
		// todo 缺少对业务使用的判断，即单据使用了则不能禁用
		List<PcxBasItem> childList = getChildList(pcxBasItems);
		if(PubConstant.LOGIC_FALSE == isEnable){
			// 如果为停用操作,需要有一层交互确认
			CheckMsg<?> isNeedConfirm = isNeedConfirm(pcxBasItemQO, childList, action);
			if (!isNeedConfirm.isSuccess()) {
				return Response.fail().setCode(ResponseErrorCode.STATUS_BIZ_NEED_CONFIRM).setMsg(isNeedConfirm.getMsgInfo());
			}
		}
		List<PcxBasItem> updateData = new ArrayList<>();
		if(!CollectionUtil.isEmpty(childList)){
			updateData.addAll(childList);
		}
		// 当是启用时，启用下级需要把上级启用了
		if(PubConstant.LOGIC_TRUE==isEnable && StringUtil.isNotEmpty(pcxBasItems.getParentCode())){
			PcxBasItemQO parentQO = new PcxBasItemQO();
			parentQO.setItemCode(pcxBasItems.getParentCode());
			parentQO.setMofDivCode(pcxBasItems.getMofDivCode());
			parentQO.setAgyCode(pcxBasItems.getAgyCode());
			parentQO.setFiscal(pcxBasItems.getFiscal());
			parentQO.setTenantId(pcxBasItems.getTenantId());
			PcxBasItem parentItem = pcxBasItemDao.selectByQO(parentQO);
			updateData.add(parentItem);
		}
		updateData.add(pcxBasItemQO);
		updateData.forEach(item->{
			item.setIsEnabled(isEnable);
			item.setModifier(pcxBasItemQO.getUserCode());
			item.setModifiedTime(DateUtil.getCurDate());
			item.setModifierName(pcxBasItemQO.getUserName());
		});
		batchServiceUtil.batchProcess(updateData,PcxBasItemDao.class, PcxBasItemDao::disEnableOrEnableId);
		return Response.success(String.format("事项类型%s成功",action));
	}

	private CheckMsg<?> isNeedConfirm(PcxBasItemQO pcxBasItemQO, List<PcxBasItem> childList, String action) {
		if (ObjectUtils.isEmpty(pcxBasItemQO.getIsConfirm()) ||
				(PubConstant.LOGIC_FALSE == pcxBasItemQO.getIsConfirm() && CollectionUtil.isNotEmpty(childList))) {
			return CheckMsg.fail().setErrorCode(CheckMsg.STATUS_BIZ_NEED_CONFIRM).setMsgInfo(String.format("此操作会%s当前费用类型的下级数据，是否同意", action));
		}
		return CheckMsg.success();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Response<?> deleteById(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> checkMsg = checkParams(pcxBasItemQO,OperationCNConstant.DELETE);
		if(!checkMsg.isSuccess()){
			return Response.fail().setMsg(checkMsg.getMsgInfo());
		}

		PcxBasItem pcxBasItems = pcxBasItemDao.selectByQO(pcxBasItemQO);
		if(ObjectUtils.isEmpty(pcxBasItems)){
			return Response.fail().setMsg("事项类型不存在");
		}
		// 删除下级
		List<PcxBasItem> subItem = getChildList(pcxBasItems);
		if(!CollectionUtil.isEmpty(subItem)){
			batchServiceUtil.batchProcess(subItem,PcxBasItemDao.class, PcxBasItemDao::deleteById);
		}
		pcxBasItemDao.deleteById(pcxBasItemQO);
		deleteBasItemExpReleation(pcxBasItemQO);
		return Response.commonResponse("删除成功");
	}

	private List<PcxBasItem> getChildList(PcxBasItem pcxBasItems){
		PcxBasItemQO param = new PcxBasItemQO();
		param.setParentCode(pcxBasItems.getItemCode());
		param.setMofDivCode(pcxBasItems.getMofDivCode());
		param.setAgyCode(pcxBasItems.getAgyCode());
		param.setFiscal(pcxBasItems.getFiscal());
		param.setTenantId(pcxBasItems.getTenantId());
        return pcxBasItemDao.selectSimpleList(param);
	}

	@Override
	public CheckMsg<?> getAll(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()){
			return valided;
		}

		List<PcxBasItemVO> treeData = pcxBasItemDao.getTreeData(pcxBasItemQO);
		return CheckMsg.success(treeData);
	}

	@Override
	public Response<?> getTopLevelItem(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()){
			return Response.fail().setMsg(valided.getMsgInfo());
		}
		pcxBasItemQO.setIsLeaf(PubConstant.LOGIC_FALSE);

		List<PcxBasItem> pcxBasItems = pcxBasItemDao.selectSimpleList(pcxBasItemQO);
		pcxBasItems = pcxBasItems.stream().filter(item->StringUtil.isEmpty(item.getParentCode())).collect(Collectors.toList());
		return Response.success(pcxBasItems);

	}

	@Override
	public List<BasItemVO> getOwnItem(PcxBasItemQO pcxBasItemQO) {
		MadEmployeeDTO employeeInfo = this.getEmployeeInfo(pcxBasItemQO);
		if(ObjectUtils.isEmpty(employeeInfo)){
			throw new RuntimeException("当前用户未关联员工信息，请先到部门员工管理中关联");
		}
        PcxBasItemAuthQO pcxBasItemAuthQO = new PcxBasItemAuthQO();
        pcxBasItemAuthQO.setFiscal(pcxBasItemQO.getFiscal());
        pcxBasItemAuthQO.setMofDivCode(pcxBasItemQO.getMofDivCode());
        pcxBasItemAuthQO.setAgyCode(pcxBasItemQO.getAgyCode());
        pcxBasItemAuthQO.setBilltypeCode(pcxBasItemQO.getBilltypeCode());
		pcxBasItemAuthQO.setTenantId(StringUtil.isEmpty(pcxBasItemQO.getTenantId())?PtyContext.getTenantId():pcxBasItemQO.getTenantId());
		pcxBasItemAuthQO.setDepartmentCode(employeeInfo.getDepartmentCode());
		pcxBasItemAuthQO.setEmployeeCode(employeeInfo.getEmployeeCode());
		List<PcxBasItem> ownItem = pcxBasItemAuthService.getOwnItem(pcxBasItemAuthQO);
		ownItem = ownItem.stream().peek(item->{
			if (Objects.isNull(item.getSeq())){
				item.setSeq(1);
			}
		}).sorted(Comparator.comparingInt(PcxBasItem::getSeq)).collect(Collectors.toList());
		return disposeItemBizType(ownItem, pcxBasItemQO.getFiscal(), pcxBasItemQO.getMofDivCode(), pcxBasItemQO.getAgyCode());
	}

	/**
	 * 处理事项的业务类型
	 * 如果关联了差旅费，则走差旅费流程
	 * 否则则通用报销流程
	 * @param ownItem
	 */
	private List<BasItemVO> disposeItemBizType(List<PcxBasItem> ownItem, String fiscal, String mofDivCode, String agyCode) {
		List<BasItemVO> result = new ArrayList<>();
		if (CollectionUtil.isEmpty(ownItem)){
			return result;
		}
		List<String> itemCodeList = ownItem.stream()
				.map(PcxBasItem::getItemCode).collect(Collectors.toList());
		PcxBasItemExpQO pcxBasItemExpQO = new PcxBasItemExpQO();
		pcxBasItemExpQO.setItemCodes(itemCodeList);
		pcxBasItemExpQO.setFiscal(fiscal);
		pcxBasItemExpQO.setAgyCode(agyCode);
		pcxBasItemExpQO.setMofDivCode(mofDivCode);
		List<PcxBasItemExp> pcxBasItemExps = pcxBasItemExpDao.selectByQO(pcxBasItemExpQO);
		Map<String, List<String>> itemExpMap = pcxBasItemExps.stream()
				.collect(Collectors.groupingBy(PcxBasItemExp::getItemCode,
						Collectors.mapping(PcxBasItemExp::getExpenseCode, Collectors.toList())));
		ownItem.forEach(item->{
			BasItemVO vo = new BasItemVO();
			BeanUtils.copyProperties(item, vo);
			List<String> expTypeCode = itemExpMap.get(item.getItemCode());
			// 该处判断应该调整
			if (CollectionUtil.isNotEmpty(expTypeCode) && expTypeCode.contains(PcxConstant.TRAVEL_EXPENSE_30211)){
				vo.setBizType(ItemBizTypeEnum.TRAVEL.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.TRAVEL.getName());
			}else if(CollectionUtil.isNotEmpty(expTypeCode) && expTypeCode.contains(PcxConstant.ABROAD_EXPENSE_30212)){
				vo.setBizType(ItemBizTypeEnum.ABROAD.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.ABROAD.getName());
			}else if(CollectionUtil.isNotEmpty(expTypeCode) && expTypeCode.contains(PcxConstant.MEETING_EXPENSE_30215)){
				vo.setBizType(ItemBizTypeEnum.MEETING.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.MEETING.getName());
			}else if(CollectionUtil.isNotEmpty(expTypeCode) && expTypeCode.contains(PcxConstant.TRAINING_EXPENSE_30216)){
				vo.setBizType(ItemBizTypeEnum.TRAINING.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.TRAINING.getName());
			}else if(CollectionUtil.isNotEmpty(expTypeCode) && expTypeCode.contains(PcxConstant.TREAT_EXPENSE_30217)){
				vo.setBizType(ItemBizTypeEnum.INLANDFEE.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.INLANDFEE.getName());
			}
			else{
				vo.setBizType(ItemBizTypeEnum.COMMON.getCode());
				vo.setBizTypeName(ItemBizTypeEnum.COMMON.getName());
			}
			result.add(vo);
		});
		return result;
	}

	@Override
	public List<PcxBasItem> selectByParentCodes(List<String> itemCodeList, @NotBlank(message = "单位编码不能为空") String agyCode, @NotBlank(message = "区划不能为空") String mofDivCode, @NotBlank(message = "年度不能为空") String fiscal) {
		PcxBasItemQO param = new PcxBasItemQO();
		param.setMofDivCode(mofDivCode);
		param.setAgyCode(agyCode);
		param.setFiscal(fiscal);
		param.setParentCodes(itemCodeList);
		return pcxBasItemDao.selectSimpleList(param);
	}

	@Override
	public CheckMsg<?> getRootItem(PcxBasItemQO pcxBasItemQO) {
		CheckMsg<?> valided = isValided(pcxBasItemQO);
		if(!valided.isSuccess()){
			return CheckMsg.fail().setMsgInfo(valided.getMsgInfo());
		}
		List<PcxBasItem> pcxBasItems = pcxBasItemDao.selectSimpleList(pcxBasItemQO);
		if(CollectionUtil.isEmpty(pcxBasItems)){
			return CheckMsg.fail().setMsgInfo("未查询到一级事项类型,请联系管理员预制数据");
		}
		Map<String, List<PcxBasItem>> result = new LinkedHashMap<>();
		result.put(BillFuncCodeEnum.APPLY.getCode(),pcxBasItems.stream().filter(item-> StringUtil.isNotEmpty(item.getBilltypeCode()) && item.getBilltypeCode().contains(BillFuncCodeEnum.APPLY.getCode())).collect(Collectors.toList()));
		result.put(BillFuncCodeEnum.EXPENSE.getCode(),pcxBasItems.stream().filter(item-> StringUtil.isNotEmpty(item.getBilltypeCode()) && item.getBilltypeCode().contains(BillFuncCodeEnum.EXPENSE.getCode())).collect(Collectors.toList()));
		result.put(BillFuncCodeEnum.LOAN.getCode(),pcxBasItems.stream().filter(item-> StringUtil.isNotEmpty(item.getBilltypeCode()) && item.getBilltypeCode().contains(BillFuncCodeEnum.LOAN.getCode())).collect(Collectors.toList()));
		return CheckMsg.success().setData(result);
	}

	/***
	 * 获取启用了申请单的事项
	 * @param pcxBasItemQO
	 * @param pcxBasItems
	 * @return
	 */
	private List<PcxBasItem> getApplyItem(PcxBasItemQO pcxBasItemQO,List<PcxBasItem> pcxBasItems ){
		List<PcxBasItem> applyItem = pcxBasItemDao.getApplyItem(pcxBasItemQO);
		if(CollectionUtil.isEmpty(applyItem)){
			return new ArrayList<>();
		}
		List<String> parentItem = applyItem.stream().map(PcxBasItem::getParentCode).collect(Collectors.toList());
		return pcxBasItems.stream().filter(item -> parentItem.contains(item.getItemCode())).collect(Collectors.toList());
	}

	private MadEmployeeDTO getEmployeeInfo(PcxBasItemQO pcxBasItemQO) {
		PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
		pcxMadBaseQO.setFiscal(pcxBasItemQO.getFiscal());
		pcxMadBaseQO.setMofDivCode(pcxBasItemQO.getMofDivCode());
		// 是否查询本部门
		if(!ObjectUtils.isEmpty(pcxBasItemQO.getIsCurDept()) && PubConstant.LOGIC_TRUE == pcxBasItemQO.getIsCurDept()){
			pcxMadBaseQO.setAgyCode(pcxBasItemQO.getAgyCode());
		}
		pcxMadBaseQO.setTenantId(StringUtil.isEmpty(pcxBasItemQO.getTenantId())?PtyContext.getTenantId():pcxBasItemQO.getTenantId());
		pcxMadBaseQO.setUserCode(pcxBasItemQO.getUserCode());
		List<MadEmployeeDTO> select = pcxMadEmployeeService.select(pcxMadBaseQO);
		return select.stream().findFirst().orElse(null);
	}

	private CheckMsg<?> isFirstLevel(PcxBasItemQO qo){
		PcxBasItemQO param = new PcxBasItemQO();
		param.setMofDivCode(qo.getMofDivCode());
		param.setAgyCode(qo.getAgyCode());
		param.setFiscal(qo.getFiscal());
		param.setItemCode(qo.getParentCode());
		param.setTenantId(qo.getTenantId());
		List<PcxBasItem> pcxBasItems = pcxBasItemDao.selectSimpleList(qo);
		if(CollectionUtil.isEmpty(pcxBasItems)){
			return CheckMsg.fail("父级代码不存在");
		}
		PcxBasItem pcxBasItem = pcxBasItems.get(0);
		return StringUtil.isEmpty(pcxBasItem.getParentCode())?CheckMsg.success():CheckMsg.fail("父级代码不是一级代码");
	}

	/***
	 * 保存或修改事项与费用的关联关系
	 * @param qo
	 * @return
	 */
	private CheckMsg<?> saveOrUpdateExpReleation(PcxBasItemQO qo, Boolean isSave) {
		CheckMsg<?> checkMsg = CheckMsg.success();
		if(CollectionUtil.isEmpty(qo.getPcxBasExpTypes()) && isSave){
			return checkMsg;
		}else if (CollectionUtil.isEmpty(qo.getPcxBasExpTypes()) && !isSave){
			return deleteBasItemExpReleation(qo);
		}
		List<PcxBasItemExpQO> pcxBasItemExpQOList = new ArrayList<>();
		for (PcxBasExpType pcxBasExpType : qo.getPcxBasExpTypes()) {
			PcxBasItemExpQO pcxBasItemExpQO = new PcxBasItemExpQO();
			pcxBasItemExpQO.setSeq(pcxBasExpType.getSeq());
			pcxBasItemExpQO.setAgyCode(qo.getAgyCode());
			pcxBasItemExpQO.setMofDivCode(qo.getMofDivCode());
			pcxBasItemExpQO.setFiscal(qo.getFiscal());
			pcxBasItemExpQO.setItemCode(qo.getItemCode());
			pcxBasItemExpQO.setItemName(qo.getItemName());
			pcxBasItemExpQO.setExpenseCode(pcxBasExpType.getExpenseCode());
			pcxBasItemExpQO.setExpenseName(pcxBasExpType.getExpenseName());
			pcxBasItemExpQO.setTenantId(StringUtil.isEmpty(qo.getTenantId())? PtyContext.getTenantId():qo.getTenantId());
			pcxBasItemExpQO.setCreatorCode(qo.getUserCode());
			pcxBasItemExpQO.setCreatorName(qo.getUserName());
			pcxBasItemExpQO.setCreatedTime(DateUtil.getCurDate());
			pcxBasItemExpQO.setModifiedTime(DateUtil.getCurDate());
			pcxBasItemExpQO.setModifier(qo.getUserCode());
			pcxBasItemExpQO.setModifierName(qo.getUserName());
			pcxBasItemExpQO.setCreatedTime(DateUtil.getCurDate());
			pcxBasItemExpQO.setId(StringUtil.getUUID());
			pcxBasItemExpQOList.add(pcxBasItemExpQO);
		}
		if(isSave){
			checkMsg = pcxBasItemExpService.batchSave(pcxBasItemExpQOList);
		}else{
			checkMsg = updateBasItemExpReleation(qo, pcxBasItemExpQOList);
		}
		return checkMsg;
	}

	private CheckMsg<?> deleteBasItemExpReleation(PcxBasItemQO qo) {
		PcxBasItemExpQO deleteParam = new PcxBasItemExpQO();
		deleteParam.setItemCode(qo.getItemCode());
		deleteParam.setMofDivCode(qo.getMofDivCode());
		deleteParam.setAgyCode(qo.getAgyCode());
		deleteParam.setFiscal(qo.getFiscal());
		deleteParam.setTenantId(qo.getTenantId());
		return pcxBasItemExpService.deleteByItemCode(deleteParam);
	}

	@Override
	public Response<?> billFuncCodeItemRel(BillFuncCodeItemRelQO qo) {
		PcxBasItemQO bas = new PcxBasItemQO();
		bas.setAgyCode(qo.getAgyCode());
		bas.setMofDivCode(qo.getMofDivCode());
		bas.setFiscal(qo.getFiscal());
		Response<?> topLevelItem = this.getTopLevelItem(bas);
		if (!topLevelItem.isSuccess()) {
			return Response.businessFailResponse(topLevelItem.getMsg());
		}
		if (ObjectUtils.isEmpty(topLevelItem.getData())){
			return Response.success(new ArrayList<>());
		}
		List<PcxBasItem> items = (List<PcxBasItem>) topLevelItem.getData();
		List<BillFuncCodeItemRelVO> vos = new ArrayList<>();
		for (BillFuncCodeEnum funcCodeEnum : BillFuncCodeEnum.values()) {
			BillFuncCodeItemRelVO relVO = new BillFuncCodeItemRelVO();
			vos.add(relVO);
			relVO.setBillFuncCode(funcCodeEnum.getCode());
			relVO.setBillFuncName(funcCodeEnum.getName());
			if (funcCodeEnum == BillFuncCodeEnum.EXPENSE) {
				relVO.setItems(items.stream().map(item -> {
					BillFuncCodeItemRelVO.ItemSimpleVO itemSimpleVO = new BillFuncCodeItemRelVO.ItemSimpleVO();
					itemSimpleVO.setPFuncCode(funcCodeEnum.getCode());
					itemSimpleVO.setBillFuncCode(item.getItemCode());
					itemSimpleVO.setBillFuncName(item.getItemName());
					return itemSimpleVO;
				}).collect(Collectors.toList()));
			}
		}
		return Response.success(vos);
	}

	@Override
	public boolean isIsCtrlBudget(PcxBasItemQO qo) {
		PcxBasItemVO itemVO = this.selectByItemCode(qo);
		Assert.state(itemVO != null, "事项不存在");
        assert itemVO != null;
        return BudgetCtrlUtil.isBudgetCtrlEnabled(itemVO.getBudgetCtrl(), qo.getBilltypeCode());
	}

	private CheckMsg<?> updateBasItemExpReleation(PcxBasItemQO qo, List<PcxBasItemExpQO> pcxBasItemExpQOList) {
		CheckMsg<?> checkMsg = deleteBasItemExpReleation(qo);
		if (checkMsg.isSuccess()) {
			checkMsg = pcxBasItemExpService.batchSave(pcxBasItemExpQOList);
		} else {
			throw new RuntimeException(checkMsg.getMsgInfo());
		}
		return checkMsg;
	}

	private CheckMsg<?> isValided(PcxBasItemQO qo) {
		if(StringUtil.isEmpty(qo.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if(StringUtil.isEmpty(qo.getMofDivCode())){
			return CheckMsg.fail("区划编码不能为空");
		}
		if(StringUtil.isEmpty(qo.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg<?> checkItemExists(PcxBasItemQO pcxBasItemQO) {
		List<PcxBasItem> pcxBasItems = pcxBasItemDao.selectSimpleList(pcxBasItemQO);
		if (CollectionUtil.isNotEmpty(pcxBasItems)) {
			boolean exists = pcxBasItems.stream()
					.anyMatch(item -> item.getItemCode().equals(pcxBasItemQO.getItemCode()) ||
							item.getItemName().equals(pcxBasItemQO.getItemName()));
			if (exists) {
				return CheckMsg.fail().setMsgInfo("事项类型编码或名称已存在");
			}
		}
		return CheckMsg.success();
	}

	private CheckMsg<?> checkParams(PcxBasItemQO qo,String action) {
		if(StringUtil.isEmpty(qo.getId())){
			return CheckMsg.fail(String.format("请选择需要%s的数据",action));
		}
		CheckMsg<?> valided = isValided(qo);
		if(!valided.isSuccess()){
			return valided;
		}
		return CheckMsg.success();
	}
}
