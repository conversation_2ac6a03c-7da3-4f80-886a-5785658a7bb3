package com.pty.pcx.service.impl.ecs;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.mad.qo.MadExpendQo;
import com.pty.mad.vo.MadExpendVo;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillOuterEmpService;
import com.pty.pcx.api.calculationrule.ICalculationRuleService;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.TripTypeEnum;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dto.bill.CityDaysAndExtraDataResult;
import com.pty.pcx.dto.bill.CityInfo;
import com.pty.pcx.dto.bill.EmpInfo;
import com.pty.pcx.dto.bill.SubsidyExtraDTO;
import com.pty.pcx.dto.ecs.CalculateSubsidyResult;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.dto.ecs.ValsetDTO;
import com.pty.pcx.dto.ecs.inv.ExpDetailDTO;
import com.pty.pcx.dto.ecs.settlement.EcsBillSettleDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.ecs.impl.EcsDtoTransHelper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.mad.IMadExtDataExternalService;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bill.extrasubsidy.ExtraAttachQO;
import com.pty.pcx.qo.bill.extrasubsidy.ExtraSubsidyQO;
import com.pty.pcx.qo.bill.extrasubsidy.UpdateExtraSubsidyQO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.service.impl.bill.PcxBillExtraSubsidyServiceImpl;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.service.impl.ecs.dto.*;
import com.pty.pcx.service.impl.ecs.handler.GeneralExpenseHandler;
import com.pty.pcx.service.impl.ecs.handler.Handler;
import com.pty.pcx.service.impl.ecs.handler.HandlerFactory;
import com.pty.pcx.service.impl.ecs.tax.TaxCalculate;
import com.pty.pcx.service.impl.ecs.trip.EcsItemNameHelper;
import com.pty.pcx.service.impl.ecs.trip.MatchTripResult;
import com.pty.pcx.service.impl.ecs.trip.TravelTripProcessor;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pcx.vo.ecs.*;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

//理票过后组装保存数据，报销单组装反显数据
@Indexed
@Service
@Slf4j
public class EcsExpOptService{

    @Resource
    private IPcxBasItemExpService basItemExpService;
    @Resource
    private PcxBasExpTypeDao basExpTypeDao;
    @Resource
    private EcsExpTransService ecsExpTransService;
    @Resource
    private PcxBillTravelTripDao billTravelTripDao;
    @Resource
    private PcxExpDetailEcsRelDao expDetailEcsRelDao;
    @Resource
    private PcxBillExpStandResultDao pcxBillExpStandResultDao;
    @Resource
    private TravelTripProcessor travelTripProcessor;

    @Autowired
    private IEcsBillExternalService ecsBillExternalService;
    @Autowired
    private StandMatchProcessor standMatchProcessor;
    @Autowired
    private PcxEcsSettlDao pcxEcsSettlDao;
    @Autowired
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;
    @Autowired
    private TaxCalculate taxCalculate;
    @Autowired
    private PcxCostControlLevelService pcxCostControlLevelService;
    @Autowired
    private EcsExpCommonService ecsExpCommonService;

    @Resource
    private PcxBillOuterEmpService pcxBillOuterEmpService;

    @Autowired
    private EcsProcessService ecsProcessService;
    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Resource
    private PcxBillTravelTripDao pcxBillTravelTripDao;
    @Resource
    private SubsidyService subsidyService;
    @Resource
    private PcxBasExpTypeDao pcxBasExpTypeDao;
    @Resource
    private IMadExtDataExternalService iMadExtDataExternalService;

    @Resource
    private PcxBudBalanceService pcxBudBalanceService;

    @Resource
    private PcxBillTripCityDayDao pcxBillTripCityDayDao;
    @Resource
    private ICalculationRuleService calculationRuleService;
    @Resource
    private PcxBillExtraAttachDao pcxBillExtraAttachDao;
    @Resource
    private PcxBillExpDetailTravelDao pcxBillExpDetailTravelDao;


    public BaseDataVo queryEmpBudDepartment(PcxBill bill) {
        PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
        pcxBalancesQO.setFiscal(bill.getFiscal());
        pcxBalancesQO.setAgyCode(bill.getAgyCode());
        pcxBalancesQO.setMofDivCode(bill.getMofDivCode());
        pcxBalancesQO.setDepartmentCode(bill.getDepartmentCode());
        return pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
    }

    /**
     * 发起报销
     * 根据选择的事项生成事项包含的费用
     * 根据票生成的费用明细填充费用金额
     * 保存票与费用或费用明细关联信息
     * 规划行程，可能会生成补充的无票费用明细
     * 如果有行程，根据城市驻留时间，住宿舍的天数计算补助
     * 保存理票时，跟随生成费用明细或费用一起生成的支出标准结果
     * 保存票关联的附件信息
     * @param expenseQO
     * @param wrappers
     * @return
     */
    public String saveBillAndExpense(StartExpenseQO expenseQO, List<InvoiceDtoWrapper> wrappers) {
        //查询事项的费用类型
        List<PcxBasItemExp> baseTypeList = getItemExpenseType(expenseQO);
        //创建报销单对象
        PcxBill bill = buildBill(expenseQO, baseTypeList);
        //电子凭证带过来的支付信息
        List<PcxEcsSettl> settlList = new ArrayList<>();
        //标准结果
        List<PcxBillExpStandResult> standList = new ArrayList<>();
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        //根据费用类型生成对应的费用实体类对象
        List<PcxBillExpBase> baseExpList = buildBaseExpByExpType(baseTypeList);
        //把每个费用与费用类型进行映射，方便后面明细找费用使用
        Map<String, PcxBillExpBase> baseExpMap = baseExpList.stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode,
                        Function.identity(), (key1, key2)->key1));
        //所有票生成的费用明细
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();
        //所有票关联费用明细
        //确认的票会生成票和费用明细的关联关系，一张票多个费用明细会生成多个关联关系，一张票多个项目，它的明细都挂到第一个项目上
        //未确认的票，会生成未确认的关联关系，如果有不完整的费用明细，会把明细列表转成jsonArray放到rel的ecsContent里面
        List<PcxExpDetailEcsRel> allEcsRel = new ArrayList<>();
        //票关联的附件列表
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();

        //收集所有票生成的费用，明细，并建立票与明细的关联关系
        collectAllExpBaseAndDetailAndEcsRelAndStandResult(expenseQO, allExpDetailBase, allEcsRel, wrappers, standList, settlList, attachRelList, Lists.newArrayList());

        //初始化外部人员信息，把生成的外部人员编码填充进明细里面
        List<PcxBillOuterEmp> outerEmps = pcxBillOuterEmpService.initOuterEmp(allExpDetailBase);

        TripAndEcsIntercityTraffic judged = judgeTripAndEcsIntercityTraffic(allExpDetailBase);
        //匹配行程
        MatchTripResult matchTripResult = travelTripProcessor.matchTripByDetail(allExpDetailBase, bill.getClaimantCode(), !judged.ecsIntercityTraffic);
        //转换成行程dto
        CollectTripDto tripDto = collectTrip(expenseQO, matchTripResult);
        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            allExpDetailBase.addAll(tripDto.getNoEcsDetailBaseList());
        }
        //计算补助
        CalculateSubsidyResult calculateSubsidyResult = calculateSubsidyByHotel(bill, tripDto, allExpDetailBase, standList, bill.getClaimantCode(), tripCityDays);
        if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
            allExpDetailBase.addAll(calculateSubsidyResult.getSubsidyList());
        }
        //所有费用明细的明细代码
        List<String> allDetailCodes = allExpDetailBase.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        //查询出明细对于的费用代码
        Map<String, String> parentMap = getExpTypeParentMap(expenseQO, allDetailCodes);

        //票生成的明细，查出费用明细对应的费用关系，给费用汇总加上金额，并把费用id打到费用明细上面
        summaryExpBaseAmtAndMarkDetail(baseExpMap, allExpDetailBase, parentMap);

        BigDecimal totalAmt = BigDecimal.ZERO;
        for (PcxBillExpBase pcxBillExpBase : baseExpList) {
            totalAmt = totalAmt.add(pcxBillExpBase.getInputAmt());
        }
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(allEcsRel);
        bill.setInputAmt(totalAmt.add(unMatchEcsAmt));

        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(allExpDetailBase, baseExpList);

        disposeExpDepartment(baseExpList, bill);

        collectTripSegment(bill, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(baseExpList, allExpDetailBase, tripDto, allEcsRel, bill.getId(), true));

        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(bill)
                .baseExpList(baseExpList)
                .tripDto(tripDto)
                .insertOrUpdateDetailList(allExpDetailBase)
                .parentMap(parentMap)
                .addEcsRel(allEcsRel)
                .allEcsList(allEcsRel)
                .settlList(settlList)
                .standList(standList)
                .allAttachRelList(attachRelList)
                .addAttachRelList(attachRelList)
                .fellowsList(fellows)
                .addOuterEmp(outerEmps)
                .tripCityDays(tripCityDays)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .build();
        return ecsExpTransService.createExpenseBill(dto);
    }

    /**
     * 收集行程分段
     * @param tripSegments
     * @param billParamsGet
     */
    private void collectTripSegment(PcxBill bill,
                                    List<PcxBillTripSegment> tripSegments,
                                    List<PcxBillAmtApportion> amtApportions,
                                    List<PcxBillAmtApportionDepartment> amtApportionDepartments,
                                    ParamsGetter billParamsGet) {
        BigDecimal totalAmt = bill.getInputAmt();
        if (totalAmt.compareTo(BigDecimal.ZERO) <= 0){
            return;
        }
        List<PcxBillExpDetailBase> detailBases = billParamsGet.expenseDetailGet.get();
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(detailBases);
        Map<String, PcxBillExpDetailTravel> travelDetailMap = travelDetailList.stream()
                .peek(item->item.setField01(""))
                .collect(Collectors.toMap(PcxBillExpDetailTravel::getId, Function.identity(), (key1, key2) ->key1));
        Pair<EcsExpMatchVO, List<BillTripVO>> pair = ecsExpMatchAndTripVo(bill, true, billParamsGet);

        List<String> noIncludedTripTaxiDetailIds = pair.getLeft().getNoIncludedTripTaxiDetailIds();
        if (CollectionUtils.isNotEmpty(noIncludedTripTaxiDetailIds)){
            for (String detailId : noIncludedTripTaxiDetailIds) {
                PcxBillExpDetailTravel detailTravel = travelDetailMap.get(detailId);
                if (Objects.nonNull(detailTravel)){
                    detailTravel.setField01("noInTrip");
                }
            }
        }
        PcxBillAmtApportion amtApportion = new PcxBillAmtApportion();
        amtApportion.setId(IDGenerator.id());
        amtApportion.setFiscal(bill.getFiscal());
        amtApportion.setAgyCode(bill.getAgyCode());
        amtApportion.setMofDivCode(bill.getMofDivCode());
        PcxBillAmtApportionDepartment amtApportionDepartment = new PcxBillAmtApportionDepartment();
        amtApportionDepartment.setFiscal(bill.getFiscal());
        amtApportionDepartment.setAgyCode(bill.getAgyCode());
        amtApportionDepartment.setMofDivCode(bill.getMofDivCode());
        amtApportionDepartment.setId(IDGenerator.id());
        amtApportionDepartment.setApportionId(amtApportion.getId());
        //费用承担部门改成直接用填报人部门
        amtApportionDepartment.setDepartmentCode(bill.getDepartmentCode());
        amtApportionDepartment.setDepartmentName(bill.getDepartmentName());

        BaseDataVo baseDataVo = queryEmpBudDepartment(bill);
        if (Objects.nonNull(baseDataVo)){
            amtApportionDepartment.setBudDepartmentCode(baseDataVo.getCode());
            amtApportionDepartment.setBudDepartmentName(baseDataVo.getName());
        }
        List<PcxBillExpBase> expBases = billParamsGet.expenseBaseGet.get();
        for (PcxBillExpBase expBase : expBases) {
            expBase.setDepartmentCode(bill.getDepartmentCode());
            expBase.setDepartmentName(bill.getDepartmentName());
        }

        //如果没有行程，则记录一个默认整单分摊
        if (CollectionUtils.isNotEmpty(pair.getRight())){
            totalAmt = BigDecimal.ZERO;
            List<BillTripVO> tripVOS = pair.getRight();
            for (BillTripVO tripVO : tripVOS) {
                int index = 1;
                boolean more = tripVO.getEcsList().size()>1;
                int i = more ? 1 : 0;
                for (; i < tripVO.getEcsList().size(); i++) {
                    BillTripNodeVO nodeVO = tripVO.getEcsList().get(i);
                    PcxBillTripSegment tripSegment = new PcxBillTripSegment();
                    tripSegment.setId(IDGenerator.id());
                    tripSegment.setBillId(bill.getId());
                    tripSegment.setTripId(tripVO.getTripId());
                    tripSegment.setStartTime(nodeVO.getPreFinishTime());
                    tripSegment.setFinishTime(nodeVO.getStartTime());
                    tripSegment.setFiscal(bill.getFiscal());
                    tripSegment.setAgyCode(bill.getAgyCode());
                    tripSegment.setMofDivCode(bill.getMofDivCode());
                    tripSegment.setSeq(index++);
                    tripSegment.setCityCode(nodeVO.getCityCode());
                    tripSegment.setCityName(nodeVO.getCityName());
                    List<String> tripDetailIds = nodeVO.getTripDetailIds();
                    BigDecimal inputAmt = BigDecimal.ZERO;
                    for (String tripDetailId : tripDetailIds) {
                        if (StringUtil.isNotEmpty(tripDetailId)){
                            PcxBillExpDetailTravel travel = travelDetailMap.get(tripDetailId);
                            if (Objects.nonNull(travel)){
                                travel.setTripId(tripVO.getTripId());
                                travel.setTripSegmentId(tripSegment.getId());
                                inputAmt = inputAmt.add(travel.getInputAmt());
                            }
                        }
                    }
                    tripSegment.setInputAmt(inputAmt);
                    totalAmt = totalAmt.add(inputAmt);
                    if (tripSegment.getInputAmt().compareTo(BigDecimal.ZERO) > 0){
                        tripSegment.setApportionId(amtApportion.getId());
                    }
                    tripSegments.add(tripSegment);
                }
            }
        }

        amtApportion.setApportionAmt(totalAmt);
        amtApportion.setInputAmt(totalAmt);
        amtApportion.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
        amtApportion.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
        amtApportionDepartment.setDepartmentAmt(totalAmt);
        amtApportionDepartment.setInputAmt(totalAmt);
        amtApportionDepartment.setDepartmentRate(new BigDecimal(100));
        amtApportionDepartment.setExpenseTypeCode(PcxConstant.TRAVEL_EXPENSE_30211);
        amtApportionDepartment.setExpenseTypeName(PcxConstant.TRAVEL_EXPENSE_30211_NAME);
        amtApportionDepartments.add(amtApportionDepartment);
        amtApportions.add(amtApportion);
    }

    /**
     * 组装回显查询数据的参数
     * @param baseExpList
     * @param allExpDetailBase
     * @param tripDto
     * @param allEcsRel
     * @return
     */
    private ParamsGetter getBillParamsGet(List<PcxBillExpBase> baseExpList,
                                          List<PcxBillExpDetailBase> allExpDetailBase,
                                          CollectTripDto tripDto,
                                          List<PcxExpDetailEcsRel> allEcsRel,
                                          String billId,
                                          Boolean isDoTrip) {
        List<PcxBillTravelTrip> tripList = new ArrayList<>();
        if(isDoTrip){
            if (CollectionUtils.isNotEmpty(tripDto.getTripList())){
                tripList = tripDto.getTripList();
                Map<String, String> detailTripIdMap = new HashMap<>();
                for (Map.Entry<String, Set<String>> entry : tripDto.getTripDetailIdMap().entrySet()) {
                    for (String detailId : entry.getValue()) {
                        detailTripIdMap.put(detailId, entry.getKey());
                    }
                }
                for (PcxBillExpDetailBase detailBase : allExpDetailBase) {
                    PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detailBase;
                    travel.setTripId(detailTripIdMap.getOrDefault(travel.getId(),""));
                }
            }
        }else if(StringUtil.isNotEmpty(billId)){
            tripList = getBillTripList(billId);
        }
        List<PcxBillTravelTrip> tripFinalList = new ArrayList<>(tripList);
        return new ParamsGetter(()->baseExpList, ()->allExpDetailBase, ()->allEcsRel,()->tripFinalList, Lists::newArrayList, Lists::newArrayList);
    }

    private boolean isIntercityTrafficVO(EcsExpenseVO travel){
        if (Objects.isNull(travel)){
            return false;
        }
        return (PcxConstant.TRAVEL_DETAIL_3021101.equals(travel.getExpenseTypeCode()) && StringUtil.isEmpty(travel.getChangeDetailId()))
                || PcxConstant.TRAVEL_DETAIL_3021110.equals(travel.getExpenseTypeCode());
    }

    public static boolean isIntercityTraffic(PcxBillExpDetailTravel travel){
        if (Objects.isNull(travel)){
            return false;
        }
        return (PcxConstant.TRAVEL_DETAIL_3021101.equals(travel.getExpDetailCode())
                && StringUtil.isEmpty(travel.getOriginId())) ||
                PcxConstant.TRAVEL_DETAIL_3021110.equals(travel.getExpDetailCode());
    }

    public static boolean isIntercityTrafficBase(PcxBillExpDetailBase travel){
        if (Objects.isNull(travel)){
            return false;
        }
        return (PcxConstant.TRAVEL_DETAIL_3021101.equals(travel.getExpDetailCode())
                || PcxConstant.TRAVEL_DETAIL_3021110.equals(travel.getExpDetailCode()));
    }

    private void disposeExpDepartment(List<PcxBillExpBase> baseExpList, PcxBill bill) {
        for (PcxBillExpBase billExpBase : baseExpList) {
            billExpBase.setDepartmentCode(bill.getDepartmentCode());
            billExpBase.setDepartmentName(bill.getDepartmentName());
        }
    }

    /**
     * 处理差旅费信息
     * 开始时间，结束时间，出发城市，目的地多个城市
     */
    private List<PcxBillTravelFellows> disposeTravelMessage(List<PcxBillExpDetailBase> insertDetailList,
                                      List<PcxBillExpBase> baseExpList) {
        List<PcxBillTravelFellows> fellows = new ArrayList<>();
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(insertDetailList);
        travelDetailList = travelDetailList.stream()
                .filter(EcsExpOptService::isIntercityTraffic)
                .collect(Collectors.toList());
        PcxBillExpBase expBase = baseExpList.stream()
                .filter(item -> Objects.equals(item.getExpenseCode(), PcxConstant.TRAVEL_EXPENSE_30211))
                .findFirst().orElse(null);
        if (CollectionUtils.isEmpty(travelDetailList) || Objects.isNull(expBase)){
            return fellows;
        }
        PcxBillExpTravel expTravel = (PcxBillExpTravel) expBase;
        if(CollectionUtils.isNotEmpty(travelDetailList)){
            travelDetailList.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
            String startTime = travelDetailList.get(0).getStartTime().substring(0,10);
            String finishTime = travelDetailList.get(travelDetailList.size()-1).getFinishTime().substring(0,10);

            expTravel.setStartTime(startTime);
            expTravel.setFinishTime(finishTime);
            collectFellows(travelDetailList, expTravel, fellows);
            collectTravelPlaces(travelDetailList, expTravel);
        }else{
            expTravel.setStartTime("");
            expTravel.setFinishTime("");
            expTravel.setStartPlaceCode("");
            expTravel.setStartPlaceName("");
            expTravel.setTravelPlaceCode("");
            expTravel.setTravelPlaceName("");
        }
        return fellows;
    }

    /**
     * 收集差旅出发城市，目的地多个城市
     */
    private void collectTravelPlaces(List<PcxBillExpDetailTravel> travelDetailList, PcxBillExpTravel expTravel) {
        Map<String, List<PcxBillExpDetailTravel>> empMap = travelDetailList.stream()
                .collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEmpCode));
        List<CityInfo> startPlace = new ArrayList<>();
        List<CityInfo> endPlace = new ArrayList<>();
        Set<String> startCitySet = new HashSet<>();
        Set<String> endCitySet = new HashSet<>();
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : empMap.entrySet()) {

            List<PcxBillExpDetailTravel> value = getPcxBillExpDetailTravels(entry);
            CityInfo start = new CityInfo(value.get(0).getStartCityCode(), value.get(0).getStartCity());
            checkCityExistsAndAdd(start, startCitySet, startPlace);

            boolean hasEnd = false;
            if (value.size() == 1){
                checkCityExistsAndAdd(start, endCitySet, endPlace);
            }else{
                for (int i = 0; i < value.size()-1; i++) {
                    CityInfo end = new CityInfo(value.get(i).getEndCityCode(), value.get(i).getEndCity());
                    if (!Objects.equals(end.getCode(), start.getCode())){
                        hasEnd = true;
                        checkCityExistsAndAdd(end, endCitySet, endPlace);
                    }
                }
                if (!hasEnd){
                    checkCityExistsAndAdd(start, endCitySet, endPlace);
                }
            }
        }
        StringBuilder startCityCode = new StringBuilder();
        StringBuilder startCityName = new StringBuilder();
        StringBuilder finishCityCode = new StringBuilder();
        StringBuilder finishCityName = new StringBuilder();
        for (CityInfo cityInfo : startPlace) {
            startCityCode.append(cityInfo.getCode()).append(",");
            startCityName.append(cityInfo.getName()).append(",");
        }
        for (CityInfo cityInfo : endPlace) {
            finishCityCode.append(cityInfo.getCode()).append(",");
            finishCityName.append(cityInfo.getName()).append(",");
        }
        expTravel.setStartPlaceCode(getCityData(startCityCode.toString()));
        expTravel.setStartPlaceName(getCityData(startCityName.toString()));
        expTravel.setTravelPlaceCode(getCityData(finishCityCode.toString()));
        expTravel.setTravelPlaceName(getCityData(finishCityName.toString()));
    }

    private String getCityData(String codeOrName){
        return !codeOrName.isEmpty() ? codeOrName.substring(0, codeOrName.length()-1) : "";
    }

    private void checkCityExistsAndAdd(CityInfo start, Set<String> startCitySet, List<CityInfo> startPlace) {
        if (!startCitySet.contains(start.getCode())){
            startPlace.add(start);
            startCitySet.add(start.getCode());
        }
    }

    private static List<PcxBillExpDetailTravel> getPcxBillExpDetailTravels(Map.Entry<String, List<PcxBillExpDetailTravel>> entry) {
        List<PcxBillExpDetailTravel> value = entry.getValue();
        value.sort(new Comparator<PcxBillExpDetailTravel>() {
            @Override
            public int compare(PcxBillExpDetailTravel o1, PcxBillExpDetailTravel o2) {
                int compareStartTime = o1.getStartTime().compareTo(o2.getStartTime());
                if (compareStartTime ==0){
                    if (Objects.nonNull(o1.getTripSeq()) && Objects.nonNull(o2.getTripSeq())){
                        return o1.getTripSeq().compareTo(o2.getTripSeq());
                    }
                }
                return compareStartTime;
            }
        });
        return value;
    }

    /**
     * 收集出差人信息
     */
    private void collectFellows(List<PcxBillExpDetailTravel> travelDetailList,
                                                                      PcxBillExpTravel expTravel,
                                                                      List<PcxBillTravelFellows> fellows) {
        Map<String, List<PcxBillExpDetailTravel>> empTravelMap = travelDetailList.stream()
                .filter(item->StringUtil.isNotEmpty(item.getEmpCode()))
                .collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEmpCode));
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : empTravelMap.entrySet()) {
            PcxBillExpDetailTravel travelDetail = entry.getValue().get(0);
            PcxBillTravelFellows travelFellows = new PcxBillTravelFellows();
            travelFellows.setExpenseId(expTravel.getId());
            travelFellows.setEmpCode(travelDetail.getEmpCode());
            travelFellows.setEmpName(travelDetail.getEmpName());
            travelFellows.setEmpType(travelDetail.getEmpType());
            travelFellows.setBudLevel(travelDetail.getBudLevel());
            travelFellows.setIsSubsidy(travelDetail.getIsSubsidy());
            travelFellows.setFiscal(expTravel.getFiscal());
            travelFellows.setAgyCode(expTravel.getAgyCode());
            travelFellows.setMofDivCode(expTravel.getMofDivCode());
            fellows.add(travelFellows);
        }
    }


    /**
     * 计算伙食补助，如果有行程数据，则取行程中的住宿费去计算伙食补助
     */
    private CalculateSubsidyResult calculateSubsidyByHotel(PcxBill pcxBill,
                                                           CollectTripDto tripDto,
                                                           List<PcxBillExpDetailBase> allExpDetailBase,
                                                           List<PcxBillExpStandResult> standResultList,
                                                           String claimantCode,
                                                           List<PcxBillTripCityDay> tripCityDaysCollect) {
        return subsidyService.calculateSubsidyByHotel(pcxBill, tripDto, allExpDetailBase, standResultList, claimantCode, tripCityDaysCollect);
    }

    /**
     * 未匹配的票补充行程
     * 查询票信息，查询费用明细
     * 校验票是否可以编辑，票的费用明细和明细的费用明细是否一致
     * 除了住宿费可以多张票，其余费用不能多张票
     * 住宿费多张票特殊处理
     * 重新里行程，保存报销单，费用，费用明细，票，行程等信息
     * @param view
     * @param replenishQO
     */
    public void unMatchEcsReplenishTrip(PcxBill view, UnMatchEcsReplenishQO replenishQO) {
        //查询出报销单的费用数据
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(view);
        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        PcxBillExpDetailBase replenishDetail = oldDetailList.stream().filter(item -> item.getId().equals(replenishQO.getDetailId())).findFirst().orElse(null);
        if (Objects.isNull(replenishDetail)){
            throw new RuntimeException("未查询到费用明细");
        }
        if (!isTripDetail(replenishDetail)){
            throw new RuntimeException("只有住宿费和城市间交通费明细可以操作");
        }
        //查询出报销单的票关联关系
        //找到票的关联信息，可能有多条，飞机票或者增值税发票多个item，住宿费多个人，会生成多个费用明细或费用，所以一个票会有多个关联关系
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(replenishQO.getBillId(), null);
        List<PcxExpDetailEcsRel> optEcsList = ecsRelList.stream()
                .filter(item -> replenishQO.getEcsBillIds().contains(item.getEcsBillId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(optEcsList)){
            throw new RuntimeException("未查询到票信息");
        }
        if (!Objects.equals(replenishDetail.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)
            && replenishQO.getEcsBillIds().size()>1){
            throw new RuntimeException("住宿费明细可以选择多张票");
        }
        for (PcxExpDetailEcsRel rel : optEcsList) {
            if (isTransExpense(rel)){
                throw new RuntimeException("已匹配的票不能操作");
            }
            if (StringUtil.isEmpty(rel.getExpenseTypeCode())){
                throw new RuntimeException("没有匹配到费用类型的票不能操作");
            }
            if (!rel.getExpenseTypeCode().equals(replenishDetail.getExpDetailCode())){
                throw new RuntimeException("与补充行程费用类型不同的票不能操作");
            }
        }
        ecsRelList.removeAll(optEcsList);

        Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailBase>> pair = ecsReplenishExpenseDetail(optEcsList, replenishDetail);

        List<PcxExpDetailEcsRel> addEcsRelList = pair.getLeft();
        ecsRelList.addAll(addEcsRelList);
        List<PcxBillExpDetailBase> addTravelDetailList = pair.getRight();


        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息
        boolean isDoTrip = false;
        MatchTripResult matchTripResult = null;
        List<PcxBillExpDetailBase> reTripDelDetail = new ArrayList<>();

        if (addTravelDetailList.stream().anyMatch(EcsExpOptService::isTripDetail)){
            DoTripResultDTO tripResult = doMatchTrip(addTravelDetailList, delOldDetailList, oldDetailList, view.getClaimantCode(), false);
            isDoTrip = tripResult.isDoTrip();
            reTripDelDetail = tripResult.getReTripDelDetail();
            matchTripResult = tripResult.getMatchTripResult();
        }
        //把历史费用明细的金额从费用上和报销单上去掉
        delOldDetailList.addAll(reTripDelDetail);

        ExpInvoiceQO expenseQO = new ExpInvoiceQO();
        expenseQO.setFiscal(replenishQO.getFiscal());
        expenseQO.setAgyCode(replenishQO.getAgyCode());
        expenseQO.setMofDivCode(replenishQO.getMofDivCode());
        CollectTripDto tripDto = collectTrip(expenseQO, matchTripResult);

        List<String> oldNeedDelDetailIds  = delOldDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = oldDetailList.stream().filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());
        needSaveOrUpdateDetailList.addAll(addTravelDetailList);
        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            needSaveOrUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
        }
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        CalculateSubsidyResult calculateSubsidyResult = new CalculateSubsidyResult();
        if (isDoTrip){
            //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
            calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needSaveOrUpdateDetailList, standResultList, view.getClaimantCode(), tripCityDays);
            if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
                needSaveOrUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
            }
        }

        List<String> otherExpTypeCode = delOldDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        //查询出明细对于的费用代码
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(expenseQO, view, billExpList, needSaveOrUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);
        //删除旧的行程，删除旧的补充明细
        //保存新的行程，保存新的补充明细
        //把付款信息保存
        //把付款信息和费用明细同步给ecs
        //删除历史的补助信息
        //保存新生成的补助信息
        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(needSaveOrUpdateDetailList, billExpList);
        List<PcxBillExpStandResult> delOldStandResultList = getDelDetailStandResult(delOldDetailList);

        standResultList.addAll(calculateNewDetailStandResult(addTravelDetailList));
        List<PcxBillExpAttachRel> allAttachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, replenishQO.getBillId()));

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, needSaveOrUpdateDetailList, tripDto, ecsRelList, view.getId(), isDoTrip));

        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .delEcsBillIds(replenishQO.getEcsBillIds())
                .addEcsRel(addEcsRelList)
                .tripDto(tripDto)
                .insertOrUpdateDetailList(needSaveOrUpdateDetailList)
                .isDoTrip(isDoTrip)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .delOldDelDetail(delOldDetailList)
                .fellowsList(fellows)
                .standList(standResultList)
                .delStandResultList(delOldStandResultList)
                .allAttachRelList(allAttachRelList)
                .tripCityDays(tripCityDays)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .build();
        ecsExpTransService.unMatchEcsReplenishTrip(dto);
    }

    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailBase>> ecsReplenishExpenseDetail(List<PcxExpDetailEcsRel> optEcsList,
                                                                                                 PcxBillExpDetailBase replenishDetail) {
        List<PcxExpDetailEcsRel> newEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailBase> addTravelDetailList = new ArrayList<>();
        String ecsContent = "";
        for (PcxExpDetailEcsRel rel : optEcsList) {
            PcxExpDetailEcsRel newEcsRel = new PcxExpDetailEcsRel();
            BeanUtils.copyProperties(rel, newEcsRel);
            if (StringUtil.isNotEmpty(rel.getEcsContent())){
                ecsContent = rel.getEcsContent();
            }
            newEcsRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            newEcsRel.setEcsContent("");
            newEcsRelList.add(newEcsRel);
        }
        PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) replenishDetail;
        if (Objects.equals(replenishDetail.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)){
            Map<String, List<PcxExpDetailEcsRel>> ecsMap = newEcsRelList.stream().collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillId));
            List<DateVO> dateList = calculateHotelDate(travel.getStartTime(), travel.getFinishTime(), ecsMap.keySet().size());
            int idnex = 0;
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsMap.entrySet()) {
                DateVO dateVO = dateList.get(idnex++);
                List<PcxExpDetailEcsRel> ecsRelList = entry.getValue();
                PcxExpDetailEcsRel rel = ecsRelList.get(0);
                rel.setInputAmt(rel.getEcsAmt());
                PcxBillExpDetailTravel newTravel = new PcxBillExpDetailTravel();
                BeanUtils.copyProperties(travel, newTravel);
                newTravel.setRemark("");
                newTravel.setSource(BillExpDetailSourceEnum.ECS.getCode());
                newTravel.setId(IDGenerator.id());
                newTravel.setEcsAmt(rel.getEcsAmt());
                newTravel.setInputAmt(rel.getEcsAmt());
                newTravel.setCheckAmt(rel.getEcsAmt());
                newTravel.setStartTime(dateVO.getStartDate());
                newTravel.setFinishTime(dateVO.getEndDate());

                disposeDetailTax(Collections.singletonList(newTravel), rel);

                rel.setDetailId(newTravel.getId());
                rel.setEmpCode(newTravel.getEmpCode());
                addTravelDetailList.add(newTravel);
            }
        }else{
            PcxBillExpDetailTravel newTravel = new PcxBillExpDetailTravel();
            BeanUtils.copyProperties(travel, newTravel);
            newTravel.setRemark("");
            newTravel.setSource(BillExpDetailSourceEnum.ECS.getCode());
            newTravel.setId(IDGenerator.id());
            PcxExpDetailEcsRel rel = newEcsRelList.get(0);
            rel.setDetailId(newTravel.getId());
            rel.setEmpCode(newTravel.getEmpCode());
            newTravel.setEcsAmt(rel.getEcsAmt());
            newTravel.setInputAmt(rel.getEcsAmt());
            newTravel.setCheckAmt(rel.getEcsAmt());
            if (StringUtil.isNotEmpty(ecsContent)){
                List<PcxBillExpDetailTravel> travels = JSON.parseArray(ecsContent, PcxBillExpDetailTravel.class);
                if (CollectionUtils.isNotEmpty(travels)){
                    PcxBillExpDetailTravel detailTravel = travels.get(0);
                    newTravel.setTrafficToolName(detailTravel.getTrafficToolName());
                    newTravel.setTrafficToolCode(detailTravel.getTrafficToolCode());
                }
            }

            disposeDetailTax(Collections.singletonList(newTravel), rel);
            addTravelDetailList.add(newTravel);
        }

        return Pair.of(newEcsRelList, addTravelDetailList);
    }

    /**
     * 把开始日期到结束日期平分成num个日期段
     * 用例1 2025-01-01到2025-01-05，num=3，返回2025-01-01到2025-01-03，2025-01-03到2025-01-04，2025-01-04到2025-01-05
     * 用例2 2025-01-01到2025-01-05，num=4，返回2025-01-01到2025-01-02，2025-01-02到2025-01-03，2025-01-03到2025-01-04，2025-01-04到2025-01-05
     * 用例3 2025-01-01到2025-01-05，num=5，直接报异常平分不了
     * @param startDate
     * @param endDate
     * @param num
     * @return
     */
    private List<DateVO> calculateHotelDate(String startDate, String endDate, int num) {
        List<DateVO> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(start, end);
        if (daysBetween + 1 == num) {
            throw new RuntimeException(num + "张票无法平分住宿费日期段");
        }

        if (daysBetween < num - 1) {
            throw new RuntimeException("住宿费日期段不足以平分成指定数量");
        }

        LocalDate currentStart = start;
        for (int i = 0; i < num; i++) {
            LocalDate currentEnd = currentStart.plusDays((daysBetween + 1) / num);
            if (i == num - 1) {
                currentEnd = end;
            }
            dateList.add(new DateVO(currentStart.format(formatter), currentEnd.format(formatter)));
            currentStart = currentEnd;
        }

        return dateList;
    }

    /**
     * 更新只有住宿票的行程的开始或结束时间
     * @param view
     * @param updateTripTimeQO
     */
    public void updateTripTime(PcxBill view, UpdateTripTimeQO updateTripTimeQO) {
        List<PcxBillExpDetailBase> detailBases = queryBillExpDetailList(view);
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(detailBases);
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        PcxBillExpDetailTravel travel = travelDetailList.stream().filter(item -> Objects.equals(item.getId(), updateTripTimeQO.getDetailId())).findFirst().orElse(null);
        if (Objects.isNull(travel)){
            throw new RuntimeException("未查询到明细信息");
        }
        //解析出附件关联信息
        List<PcxBillExpAttachRel> attachRelList = analysisDetailRelList(Collections.singletonList(travel), updateTripTimeQO.getAttachList());

        travel.setRemark(updateTripTimeQO.getRemark());
        travel.setStartTime(GeneralExpenseHandler.getRemarkAirportStartTime(updateTripTimeQO.getStartOrFinishTime(), GeneralExpenseHandler.defaultStartTime));
        travel.setFinishTime(GeneralExpenseHandler.getRemarkAirportFinishTime(travel.getStartTime(), ""));
        travel.setNoEcsReason(updateTripTimeQO.getNoEcsReason());
        travel.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
        DoTripResultDTO doTripResultDTO = doMatchTrip(Lists.newArrayList(), Lists.newArrayList(), detailBases,view.getClaimantCode(), true);
        //费用类型与费用映射
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);

        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        BeanUtils.copyProperties(updateTripTimeQO, invoiceQO);
        CollectTripDto tripDto = collectTrip(invoiceQO, doTripResultDTO.getMatchTripResult());

        List<PcxBillExpDetailBase> needUpdateDetailList = new ArrayList<>(detailBases);
        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            needUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
        }
        List<PcxBillExpStandResult> standList = new ArrayList<>();

        //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
        CalculateSubsidyResult calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needUpdateDetailList, standList, view.getClaimantCode(), tripCityDays);
        if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
            needUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
        }

        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), null);

        List<PcxBillExpStandResult> delStandResultList = getDelDetailStandResult(doTripResultDTO.getReTripDelDetail());
        needUpdateDetailList.removeAll(doTripResultDTO.getReTripDelDetail());

        List<String> otherExpTypeCode = doTripResultDTO.getReTripDelDetail().stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        //所有费用明细的明细代码
        //查询出明细对应的费用代码
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        //汇总金额更新费用和报销单
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(invoiceQO, view, billExpList, needUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);

        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(needUpdateDetailList, billExpList);

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, needUpdateDetailList, tripDto, ecsRelList, view.getId(), true));
        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .insertOrUpdateDetailList(needUpdateDetailList)
                .tripDto(tripDto)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .standList(standList)
                .delStandResultList(delStandResultList)
                .isDoTrip(true)
                .delOldDelDetail(doTripResultDTO.getReTripDelDetail())
                .fellowsList(fellows)
                .addAttachRelList(attachRelList)
                .delAttachRelId(travel.getId())
                .tripCityDays(tripCityDays)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .build();
        ecsExpTransService.updateTripTime(dto);
    }

    private Map<String, String> getExpTypeParentMap(ExpInvoiceQO invoiceQO, List<String> allDetailCodes) {
        if (CollectionUtils.isNotEmpty(allDetailCodes)){
            List<PcxBasExpTypeVO> pcxBasExpTypes = getExpTypeByCodes(invoiceQO, allDetailCodes);
            return pcxBasExpTypes.stream().collect(Collectors.toMap(PcxBasExpTypeVO::getExpenseCode, PcxBasExpTypeVO::getParentCode));
        }else {
            return new HashMap<>();
        }
    }

    private List<PcxBasExpTypeVO> getExpTypeByCodes(ExpInvoiceQO invoiceQO, List<String> allDetailCodes){
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setFiscal(invoiceQO.getFiscal());
        qo.setAgyCode(invoiceQO.getAgyCode());
        qo.setMofDivCode(invoiceQO.getMofDivCode());
        qo.setExpTypeCodes(allDetailCodes);
        return basExpTypeDao.getTreeData(qo);
    }

    private CollectTripDto collectTrip(ExpInvoiceQO expenseQO, MatchTripResult matchTripResult) {
        CollectTripDto tripDto = new CollectTripDto();
        //组装生成行程po
        Map<String, Set<String>> tripDetailIdMap = new HashMap<>();
        List<PcxBillTravelTrip> tripList = new ArrayList<>();
        if (Objects.nonNull(matchTripResult)){
            if (CollectionUtils.isNotEmpty(matchTripResult.getTripDTOList())){
                tripDto.setTripDTOList(matchTripResult.getTripDTOList());
                for (EcsExpTripDTO tripDTO : matchTripResult.getTripDTOList()) {
                    PcxBillTravelTrip trip = new PcxBillTravelTrip();
                    BeanUtils.copyProperties(tripDTO, trip);
                    BeanUtils.copyProperties(expenseQO, trip);
                    trip.setId(tripDTO.getId());
                    tripDetailIdMap.put(trip.getId(), tripDTO.getDetailIds());
                    tripList.add(trip);
                }
                //系统补充行程生成的明细
                if (Objects.nonNull(matchTripResult.getNoEcsDetail()) && Objects.nonNull(matchTripResult.getNoEcsDetail().getExpDetailList())){
                    tripDto.setNoEcsDetailBaseList(matchTripResult.getNoEcsDetail().getExpDetailList());
                }
            }
        }
        tripDto.setTripList(tripList);
        tripDto.setTripDetailIdMap(tripDetailIdMap);

        return tripDto;
    }

    private void summaryExpBaseAmtAndMarkDetail(Map<String, PcxBillExpBase> baseExpMap, List<PcxBillExpDetailBase> allExpDetailBase,
                                                Map<String, String> parentMap) {
        //从费用明细中收集金额，更新到对应的费用上面
        Map<String, List<PcxBillExpDetailBase>> detailMap = allExpDetailBase
                .stream().collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode())));
        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            PcxBillExpBase expBase = baseExpMap.get(entry.getKey());
            BigDecimal total = BigDecimal.ZERO;
            for (PcxBillExpDetailBase detailBase : entry.getValue()) {
                detailBase.setExpenseId(expBase.getId());
                total = total.add(detailBase.getInputAmt());
            }
            expBase.setInputAmt(expBase.getInputAmt().add(total));
        }

    }

    private void collectAllExpBaseAndDetailAndEcsRelAndStandResult(ExpInvoiceQO invoiceQO,
                                                                   List<PcxBillExpDetailBase> allExpDetailBase,
                                                                   List<PcxExpDetailEcsRel> allEcsRel,
                                                                   List<InvoiceDtoWrapper> wrappers,
                                                                   List<PcxBillExpStandResult> standList,
                                                                   List<PcxEcsSettl> settlList,
                                                                   List<PcxBillExpAttachRel> attachRelList,
                                                                   List<PcxBillExpDetailBase> existsDetailList) {
        if (CollectionUtils.isEmpty(wrappers)){
            return;
        }
        compensationEmpMsg(wrappers, invoiceQO.getDefaultClaimantDTO(), existsDetailList);

        for (InvoiceDtoWrapper wrapper : wrappers) {
            //整理票和费用，票和明细的关联关系
            List<PcxExpDetailEcsRel> ecsRel = collectEcsRel(wrapper, attachRelList, invoiceQO, settlList);
            allEcsRel.addAll(ecsRel);
            //整理票生成的所有费用，票生成的所有明细
            CollectExpListDto expDto = collectExpBase(wrapper);
            allExpDetailBase.addAll(expDto.getExpDetailList());
            //费用的支出标准列表
            if (CollectionUtils.isNotEmpty(wrapper.getStandList())){
                //特殊处理住宿费的支出标准，关联的id为hotelGroup,因为住宿费是用一张票计算的标准，而不是单条明细
                if (PcxConstant.TRAVEL_DETAIL_3021102.equals(ecsRel.get(0).getExpenseTypeCode())){
                    disposeHotelStandResult(expDto.getExpDetailList().get(0), wrapper.getStandList());
                }
                standList.addAll(wrapper.getStandList());
            }
            if (CollectionUtils.isNotEmpty(wrapper.getSettlList())){
                settlList.addAll(wrapper.getSettlList());
            }
        }
    }

    /**
     * 补偿人员信息，如果城市间交通费和住宿费都是一个人的，则把缺少人员信息的明细补充上人员信息
     * @param wrappers
     */
    private void compensationEmpMsg(List<InvoiceDtoWrapper> wrappers,
                                    DefaultClaimantDTO defaultClaimantDTO,
                                    List<PcxBillExpDetailBase> existsDetailList) {
        List<InvoiceDtoWrapper> collect = wrappers.stream().filter(item -> item.getLackEmpCode() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            return;
        }
        List<InvoiceDtoWrapper> travelWrappers = wrappers.stream().filter(this::isMatchTravelDetail).collect(Collectors.toList());
        Map<String, List<PcxBillExpDetailTravel>> travelMap = new HashMap<>();
        for (InvoiceDtoWrapper travelWrapper : travelWrappers) {
            List expDetailList = travelWrapper.getExpDetailList();
            if (CollectionUtils.isNotEmpty(expDetailList)){
                for (Object expDetail : expDetailList) {
                    PcxBillExpDetailTravel detail = (PcxBillExpDetailTravel) expDetail;
                    if (Objects.equals(detail.getEmpType(), PcxConstant.EMP_TYPE_INNER)){
                        List<PcxBillExpDetailTravel> travels = travelMap.computeIfAbsent(detail.getEmpName(), k -> new ArrayList<>());
                        travels.add(detail);
                    }
                }
            }
        }
        //添加票时，把库里的费用明细页拿出来匹配一下人员信息
        if (CollectionUtils.isNotEmpty(existsDetailList)){
            List<PcxBillExpDetailTravel> travelDetails = getTravelDetailList(existsDetailList);
            travelDetails = travelDetails.stream().filter(item -> isTripDetail(item)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(travelDetails)){
                for (PcxBillExpDetailTravel travelDetail : travelDetails) {
                    if (Objects.equals(travelDetail.getEmpType(), PcxConstant.EMP_TYPE_INNER)){
                        List<PcxBillExpDetailTravel> travels = travelMap.computeIfAbsent(travelDetail.getEmpName(), k -> new ArrayList<>());
                        travels.add(travelDetail);
                    }
                }
            }
        }
        if (travelMap.isEmpty() && Objects.nonNull(defaultClaimantDTO)){
            for (InvoiceDtoWrapper wrapper : collect) {
                List list = wrapper.getExpDetailList();
                if (CollectionUtils.isNotEmpty(list)) {
                    for (Object o : list) {
                        PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) o;
                        if (isNeedEmpCode(travel)){
                            travel.setEmpCode(defaultClaimantDTO.getEmpCode());
                            travel.setEmpName(defaultClaimantDTO.getEmpName());
                            travel.setEmpType(PcxConstant.EMP_TYPE_INNER);
                            travel.setBudLevel(defaultClaimantDTO.getBudLevel());
                            travel.setIsSubsidy(1);
                        }

                    }
                }
                //如果只缺少人员信息，则把票改成已确认
                if (wrapper.getLackEmpCode() == 2) {
                    wrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
                    Handler handler = HandlerFactory.getStandHandler(wrapper);
                    if (handler != null) {
                        handler.handle(wrapper, ProcessContext.DEFAULT_CONTEXT);
                    }
                }
            }
        }else if (travelMap.keySet().size()==1){
            String key = travelMap.keySet().iterator().next();
            List<PcxBillExpDetailTravel> travels = travelMap.get(key);
            if (CollectionUtils.isNotEmpty(travels)){
                PcxBillExpDetailTravel detailTravel = travels.get(0);
                for (InvoiceDtoWrapper wrapper : collect) {
                    List list = wrapper.getExpDetailList();
                    if (CollectionUtils.isNotEmpty(list)){
                        for (Object o : list) {
                            PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) o;
                            if (isNeedEmpCode(travel)){
                                travel.setEmpCode(detailTravel.getEmpCode());
                                travel.setEmpName(detailTravel.getEmpName());
                                travel.setEmpType(detailTravel.getEmpType());
                                travel.setBudLevel(detailTravel.getBudLevel());
                                travel.setIsSubsidy(1);
                            }

                        }
                    }
                    //如果只缺少人员信息，则把票改成已确认
                    if (wrapper.getLackEmpCode()==2){
                        wrapper.setFlag(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
                    }
                }
            }
        }
    }

    public static boolean isNeedEmpCode(PcxBillExpDetailTravel travel) {
        if (StringUtil.isEmpty(travel.getEmpType())){
            return true;
        }else{
            if (Objects.equals(PcxConstant.EMP_TYPE_OUTER, travel.getEmpType()) && StringUtil.isEmpty(travel.getEmpName())){
                return true;
            }else if (Objects.equals(PcxConstant.EMP_TYPE_INNER, travel.getEmpType()) && StringUtil.isEmpty(travel.getEmpCode())){
                return true;
            }
        }
        return false;
    }

    private boolean isMatchTravelDetail(InvoiceDtoWrapper invoiceDtoWrapper){
        if (!Objects.equals(invoiceDtoWrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            return false;
        }
        if (Objects.isNull(invoiceDtoWrapper.getMatchExpType())){
            return false;
        }
        return Objects.equals(invoiceDtoWrapper.getMatchExpType().getExpenseCode(), PcxConstant.TRAVEL_DETAIL_3021101)
                || Objects.equals(invoiceDtoWrapper.getMatchExpType().getExpenseCode(), PcxConstant.TRAVEL_DETAIL_3021102)
                || Objects.equals(invoiceDtoWrapper.getMatchExpType().getExpenseCode(), PcxConstant.TRAVEL_DETAIL_3021110);
    }

    public MadExpendVo getCostItemData(String fiscal, String agyCode, String mofDivCode, String expenseTypeCode){
        MadExpendQo qo = new MadExpendQo();
        qo.setFiscal(Integer.valueOf(fiscal));
        qo.setAgyCode(agyCode);
        qo.setMofDivCode(mofDivCode);
        qo.setExpenseCode(PcxConstant.TRAVEL_EXPENSE_30211);
        try {
            MadExpendVo costItemData = iMadExtDataExternalService.getCostItemData(qo);
            if (Objects.nonNull(costItemData)){
                return costItemData;
            }
        } catch (Exception e) {
            log.error("disposeAddition err", e);
        }
        return new MadExpendVo();
    }

    private void disposeHotelStandResult(PcxBillExpDetailBase pcxBillExpDetailBase, List<PcxBillExpStandResult> standList) {
        PcxBillExpDetailTravel hotelDetail = (PcxBillExpDetailTravel) pcxBillExpDetailBase;
        for (PcxBillExpStandResult standResult : standList) {
            standResult.setEcsBillId(hotelDetail.getHotelGroup());
        }
    }

    private List<PcxBillExpBase> buildBaseExpByExpType(List<PcxBasItemExp> baseTypeList) {
        List<PcxBillExpBase> expList = new ArrayList<>();
        for (PcxBasItemExp basItemExp : baseTypeList) {
            PcxBillExpBase entityBean = ExpenseBeanUtil.getEntityBean(basItemExp.getExpenseCode());
            BeanUtils.copyProperties(basItemExp, entityBean);
            entityBean.setId(IDGenerator.id());
            entityBean.setInputAmt(BigDecimal.ZERO);
            expList.add(entityBean);
        }
        return expList;
    }

    private List<PcxBasItemExp> getItemExpenseType(StartExpenseQO expenseQO) {
        PcxBasItemExpQO expQO = new PcxBasItemExpQO();
        expQO.setItemCode(expenseQO.getItemCode());
        expQO.setAgyCode(expenseQO.getAgyCode());
        expQO.setFiscal(expenseQO.getFiscal());
        expQO.setMofDivCode(expenseQO.getMofDivCode());
        return basItemExpService.selectByItemCode(expQO);
    }


    private PcxBill buildBill(StartExpenseQO expenseQO, List<PcxBasItemExp> baseTypeList) {
        MadEmployeeDTO madEmployeeDTO = expenseQO.getMadEmployeeDTO();
        PcxBill bill = PcxBill.builder()
                .agyCode(expenseQO.getAgyCode())
                .fiscal(expenseQO.getFiscal())
                .mofDivCode(expenseQO.getMofDivCode())
                .itemCode(expenseQO.getItemCode())
                .itemName(expenseQO.getItemName())
                .claimantCode(madEmployeeDTO.getEmployeeCode())
                .claimantName(madEmployeeDTO.getEmployeeName())
                .departmentCode(madEmployeeDTO.getDepartmentCode())
                .departmentName(madEmployeeDTO.getDepartmentName())
                .billFuncCode(BillFuncCodeEnum.EXPENSE.getCode())
                .billFuncName(BillFuncCodeEnum.EXPENSE.getName())
                .transDate(expenseQO.getTransDate())
                .createdTime(DateUtil.nowTime())
                .creator(expenseQO.getUserCode())
                .creatorName(expenseQO.getUserName())
                .modifiedTime(DateUtil.nowTime())
                .modifier(expenseQO.getUserCode())
                .modifierName(expenseQO.getUserName())
                .bizType(ItemBizTypeEnum.TRAVEL.getCode())
                .bizTypeName(ItemBizTypeEnum.TRAVEL.getName())
                .reason("")
                .expenseCodes(baseTypeList.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.joining(",")))
                .expenseNames(baseTypeList.stream().map(PcxBasItemExp::getExpenseName).collect(Collectors.joining(",")))
                .build();
        //因公出差发起的无票报销直接打上仅报补助的标
        if (expenseQO.isNoEcs() && Objects.equals(expenseQO.getItemCode(), PcxConstant.BUSINESS_TRIP_ITEM)){
            bill.setOnlySubsidy(true);
        }
        //票的代报人只有一个
        if (Objects.nonNull(expenseQO.getDefaultClaimant())){
            bill.setClaimantCode(expenseQO.getDefaultClaimant().getEmpCode());
            bill.setClaimantName(expenseQO.getDefaultClaimant().getEmpName());
        }
        return bill;
    }

    private CollectExpListDto collectExpBase(InvoiceDtoWrapper wrapper) {
        CollectExpListDto dto = new CollectExpListDto();
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            dto.addExpBaseList(wrapper.getExpenseList());
            dto.addExpDetailList(wrapper.getExpDetailList());
        }
        return dto;
    }

    private List<PcxExpDetailEcsRel> collectEcsRel(InvoiceDtoWrapper wrapper,
                                                   List<PcxBillExpAttachRel> attachRelList,
                                                   ExpInvoiceQO invoiceQO,
                                                   List<PcxEcsSettl> settlList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        //给票建立一个基础的关联，因为票有可能关联多个费用明细，后面直接copy它
        List<PcxExpDetailEcsRel> copyRel = getEcsRel(wrapper, attachRelList, settlList);
        for (PcxBillExpAttachRel attachRel : attachRelList) {
            attachRel.setFiscal(invoiceQO.getFiscal());
            attachRel.setAgyCode(invoiceQO.getAgyCode());
            attachRel.setMofDivCode(invoiceQO.getMofDivCode());
            attachRel.setTenantId(invoiceQO.getTenantId());
        }
        for (PcxEcsSettl ecsSettl : settlList) {
            ecsSettl.setFiscal(invoiceQO.getFiscal());
            ecsSettl.setAgyCode(invoiceQO.getAgyCode());
            ecsSettl.setMofDivCode(invoiceQO.getMofDivCode());
        }
        if (CollectionUtils.isEmpty(copyRel)){
            return result;
        }
        copyRel.forEach(item->{
            item.setFiscal(invoiceQO.getFiscal());
            item.setAgyCode(invoiceQO.getAgyCode());
            item.setMofDivCode(invoiceQO.getMofDivCode());
            item.setExpenseTypeCode("");
            item.setExpenseTypeName("");
            item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
        });

        if (Objects.nonNull(wrapper.getMatchExpType())){
            //未匹配的票有可能是匹配的费用类型，但是生成明细时数据不完整，保存费用类型，和费用明细
            copyRel.forEach(item->{
                item.setExpenseTypeCode(wrapper.getMatchExpType().getExpenseCode());
                item.setExpenseTypeName(wrapper.getMatchExpType().getExpenseName());
            });
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                copyRel.forEach(item->{
                    item.setInputAmt(BigDecimal.ZERO);
                    item.setCheckAmt(BigDecimal.ZERO);
                });
                copyRel.get(0).setEcsContent(JSON.toJSONString(wrapper.getExpDetailList()));
                BigDecimal inputAmt = BigDecimal.ZERO;
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    if (Objects.nonNull(detailBase.getInputAmt())){
                        inputAmt = inputAmt.add(detailBase.getInputAmt());
                    }
                }
                copyRel.get(0).setInputAmt(inputAmt);
            }
        }
        if (Objects.equals(wrapper.getFlag(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            //如果是匹配的票，则把上面保存的费用明细去掉，不需要保存在票里面
            copyRel.forEach(item->{
                item.setEcsContent("");
                item.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            });
            //解析出的费用明细都挂到第一个项目上
            PcxExpDetailEcsRel firstEcsRel = copyRel.get(0);
            if (CollectionUtils.isNotEmpty(wrapper.getExpDetailList())){
                for (Object o : wrapper.getExpDetailList()) {
                    PcxBillExpDetailBase detailBase = (PcxBillExpDetailBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(firstEcsRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setDetailId(detailBase.getId());
                    rel.setExpenseTypeCode(detailBase.getExpDetailCode());
                    rel.setInputAmt(detailBase.getInputAmt());
                    rel.setCheckAmt(detailBase.getInputAmt());
                    rel.setEmpCode(detailBase.getEmpCode());
                    //计算进项税
                    taxCalculate.calculate(rel, detailBase);
                    result.add(rel);
                }
            }
            if (CollectionUtils.isNotEmpty(wrapper.getExpenseList())){
                for (Object o : wrapper.getExpenseList()) {
                    PcxBillExpBase expBase = (PcxBillExpBase) o;
                    PcxExpDetailEcsRel rel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(copyRel, rel);
                    rel.setId(IDGenerator.id());
                    rel.setExpenseTypeCode(expBase.getExpenseCode());
                    rel.setItemName(expBase.getEcsRelItemName());
                    result.add(rel);
                }
            }
            //如果条数大于1，则进行了copy，把第一条去掉
            if (copyRel.size()>1){
                result.addAll(copyRel.subList(1, copyRel.size()));
            }
        }else{
            //未匹配的票记录一下票信息
            result.addAll(copyRel);
        }
        return result;
    }


    private List<PcxExpDetailEcsRel> getEcsRel(InvoiceDtoWrapper wrapper, List<PcxBillExpAttachRel> attachRelList, List<PcxEcsSettl> settlList) {
        EcsDtoTransHelper.EcsBillDispose ecsBillDispose = EcsBillExternalServiceImpl.getEcsBillDisposeMap().get(wrapper.getType());
        if (Objects.nonNull(ecsBillDispose)){
            return ecsBillDispose.initEcsRelListTravel(wrapper.getDto(), attachRelList, settlList, Lists.newArrayList(), new HashMap<>());
        }
        return Lists.newArrayList();
    }

    public void updateExtraSubsidy(UpdateExtraSubsidyQO qo, PcxBill view) {

        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        //查询出报销单费用
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);
        //查询出报销单票关联关系
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), "");
        //查询出费用明细
        List<PcxBillExpDetailBase> detailBases = queryBillExpDetailList(view);
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(detailBases);
        //把职员的补助明细筛选出来
        List<String> delDetailIds = new ArrayList<>();
        List<PcxBillExpDetailTravel> oldSubsidyDetailList = travelDetailList.stream()
                .filter(item->Objects.equals(PcxConstant.TRAVEL_DETAIL_3021103,item.getExpDetailCode())
                    && Objects.equals(qo.getEmpCode(), item.getEmpCode()))
                .peek(item->{delDetailIds.add(item.getId());})
                .collect(Collectors.toList());
        travelDetailList.removeAll(oldSubsidyDetailList);
        List<PcxBillExpDetailBase> delOldDetailList = detailBases.stream()
                .filter(item->delDetailIds.contains(item.getId())).collect(Collectors.toList());
        detailBases.removeAll(delOldDetailList);
        //查询出职员的行程城市驻留数据
        List<PcxBillTripCityDay> cityDays = queryCityDays(view.getId());
        List<PcxBillTripCityDay> empCityDays = cityDays.stream()
                .filter(item -> Objects.equals(qo.getEmpCode(), item.getEmpCode()))
                .collect(Collectors.toList());
        //把补助日期转换成单个日期，重新匹配城市驻留数据，把日期分配到每个城市中的周末加班天数和项目经理天数
        //组装额外补助信息
        CityDaysAndExtraDataResult extraDataResult = transDataAndCollectNewCityDays(qo.getExtraSubsidyList(), empCityDays, view);
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        List<PcxBillExpStandResult> delOldStandResultList = getDelDetailStandResult(delOldDetailList);
        List<PcxBillExpDetailTravel> empTravelList = travelDetailList.stream().filter(item -> Objects.equals(item.getEmpCode(), qo.getEmpCode())).collect(Collectors.toList());
        //使用新生成的城市驻留天数，计算职员的补助信息
        List<PcxBillExpDetailTravel> newSubsidyList = subsidyService.generateSubsidyByExtra(view, empTravelList, standResultList, extraDataResult.getCityDays());
        detailBases.addAll(newSubsidyList);
        //重新计算报销单金额，费用金额
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        //查询出明细对于的费用代码
        ExpInvoiceQO expInvoiceQO = new ExpInvoiceQO();
        expInvoiceQO.setFiscal(view.getFiscal());
        expInvoiceQO.setAgyCode(view.getAgyCode());
        expInvoiceQO.setMofDivCode(view.getMofDivCode());
        //查询出明细对于的费用代码
        List<String> otherExpTypeCode = delOldDetailList.stream()
                .map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(expInvoiceQO, view, billExpList, detailBases, otherExpTypeCode, unMatchEcsAmt);
        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, detailBases, null, ecsRelList, view.getId(), false));
        List<PcxBillExtraAttach> allExtraAttachList = queryExtraAttachList(view.getId());
        allExtraAttachList = allExtraAttachList.stream().filter(item->!Objects.equals(item.getEmpCode(), qo.getEmpCode())).collect(Collectors.toList());
        allExtraAttachList.addAll(extraDataResult.getExtraAttaches());
        List<PcxBillExpAttachRel> attachRelList = getAttachRelList(view, (a) -> true);
        //事务中更新报销单，删除补助明细，插入新的补助明细，更新费用，删除旧的城市驻留数据，插入新的城市驻留数据，插入补助数据
        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .insertOrUpdateDetailList(detailBases)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .delOldDelDetail(delOldDetailList)
                .standList(standResultList)
                .delStandResultList(delOldStandResultList)
                .tripCityDays(extraDataResult.getCityDays())
                .delTripCityDays(empCityDays)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .addExtraList(extraDataResult.getExtraSubsidies())
                .addExtraAttachList(extraDataResult.getExtraAttaches())
                .allExtraAttachList(allExtraAttachList)
                .allAttachRelList(attachRelList)
                .updateExtraSubsidyQO(qo)
                .build();
        ecsExpTransService.updateExtraSubsidy(dto);
    }

    private CityDaysAndExtraDataResult transDataAndCollectNewCityDays(List<ExtraSubsidyQO> extraSubsidyList,
                                                                      List<PcxBillTripCityDay> empCityDays,
                                                                      PcxBill view) {
        List<PcxBillTripCityDay> newCityDays = Lists.newArrayList();
        List<PcxBillExtraSubsidy> extraSubsidies = Lists.newArrayList();
        List<PcxBillExtraAttach> extraAttaches = Lists.newArrayList();
        String empCode = empCityDays.get(0).getEmpCode();
        for (PcxBillTripCityDay empCityDay : empCityDays) {
            PcxBillTripCityDay newCityDay = new PcxBillTripCityDay();
            BeanUtils.copyProperties(empCityDay, newCityDay);
            newCityDay.setId(IDGenerator.id());
            newCityDay.setWorkExtraDays(0);
            newCityDay.setManagerDays(0);
            newCityDays.add(newCityDay);
        }
        for (ExtraSubsidyQO extraSubsidyQO : extraSubsidyList) {
            ExtraSubsidyTypeEnum extraEnum = ExtraSubsidyTypeEnum.getEnumByCode(extraSubsidyQO.getExtraType());
            if (Objects.isNull(extraEnum)){
                throw new RuntimeException("额外补助类型不存在");
            }
            int days = 0;
            if(CollectionUtils.isNotEmpty(extraSubsidyQO.getDateList())){
                Set<String> dateSet = transExtraDate(extraSubsidyQO.getDateList(), extraEnum);
                days = dateSet.size();
                Iterator<String> iterator = dateSet.iterator();
                while (iterator.hasNext()){
                    String date = iterator.next();
                    for (PcxBillTripCityDay newCityDay : newCityDays) {
                        if (newCityDay.getStartTime().compareTo(date) <=0  && newCityDay.getFinishTime().compareTo(date) >=0){
                            switch (extraEnum){
                                case EXTRA_WORK:
                                    newCityDay.setWorkExtraDays(newCityDay.getWorkExtraDays()+1);
                                    break;
                                case MANAGE:
                                    newCityDay.setManagerDays(newCityDay.getManagerDays()+1);
                                    break;
                            }
                            iterator.remove();
                            break;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(dateSet)){
                    throw new RuntimeException(dateSet+ "不在行程里");
                }
            }

            PcxBillExtraSubsidy extraSubsidy = new PcxBillExtraSubsidy();
            extraSubsidy.setId(IDGenerator.id());
            extraSubsidy.setBillId(view.getId());
            extraSubsidy.setExtraType(extraSubsidyQO.getExtraType());
            extraSubsidy.setExtraDays(days);
            if (CollectionUtils.isNotEmpty(extraSubsidyQO.getDateList())){
                extraSubsidy.setExtraDate(String.join(",", extraSubsidyQO.getDateList()));
            }else{
                extraSubsidy.setExtraDate("");
            }
            extraSubsidy.setEmpCode(empCode);
            extraSubsidy.setFiscal(view.getFiscal());
            extraSubsidy.setAgyCode(view.getAgyCode());
            extraSubsidy.setMofDivCode(view.getMofDivCode());
            extraSubsidies.add(extraSubsidy);

            if (CollectionUtils.isNotEmpty(extraSubsidyQO.getAttachList())){
                for (ExtraAttachQO attachQO : extraSubsidyQO.getAttachList()) {
                    PcxBillExtraAttach extraAttach = new PcxBillExtraAttach();
                    extraAttach.setId(IDGenerator.id());
                    extraAttach.setBillId(view.getId());
                    extraAttach.setExtraType(extraSubsidyQO.getExtraType());
                    extraAttach.setFileName(attachQO.getFileName());
                    extraAttach.setFileId(attachQO.getFileId());
                    extraAttach.setEmpCode(empCode);
                    extraAttach.setFiscal(view.getFiscal());
                    extraAttach.setAgyCode(view.getAgyCode());
                    extraAttach.setMofDivCode(view.getMofDivCode());
                    extraAttaches.add(extraAttach);
                }
            }

        }
        return CityDaysAndExtraDataResult.of(newCityDays, extraSubsidies, extraAttaches);
    }

    private Set<String> transExtraDate(List<String> dateRangeList, ExtraSubsidyTypeEnum extraEnum) {
        Set<String> dateSet = Sets.newHashSet();
        switch (extraEnum){
            case EXTRA_WORK:
                for (String dateRange : dateRangeList) {
                    String[] split = dateRange.split(",");
                    dateSet.addAll(Arrays.asList(split));
                }
                break;
            case MANAGE:
                for (String s : dateRangeList) {
                    String[] splitItem = s.split(PcxBillExtraSubsidyServiceImpl.DATE_SPECIAL);
                    String startDate = splitItem[0];
                    String endDate = splitItem[1];
                    if (startDate.compareTo(endDate) > 0){
                        throw new RuntimeException("开始时间不能晚于结束时间");
                    }
                    dateSet.addAll(getBetweenDays(startDate, endDate));
                }
                break;
        }
        return dateSet;
    }

    public static Set<String> getBetweenDays(String startDate, String endDate) {
        Set<String> result = Sets.newHashSet();
        result.add(startDate);
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        while (start.isBefore(end)){
            start = start.plusDays(1);
            result.add(start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        return result;
    }


    private List<PcxBillTripCityDay> queryCityDays(String billId) {
        return pcxBillTripCityDayDao.selectList(Wrappers.lambdaQuery(PcxBillTripCityDay.class)
                .eq(PcxBillTripCityDay::getBillId, billId));
    }


    public NoEcsDetailVO queryNoEcsDetail(QueryNoEcsDetailQO qo, PcxBill view) {
        NoEcsDetailVO noEcsDetailVO = new NoEcsDetailVO();
        PcxBillExpDetailTravel detailTravel = pcxBillExpDetailTravelDao.selectById(qo.getDetailId());
        detailTravel.setEmpInfoList(detailTravel.getEmpInfo());
        noEcsDetailVO.setDetail(JSON.parseObject(JSON.toJSONString(detailTravel)));
        String relKey = getDetailAttachRelKey(detailTravel);
        List<PcxBillExpAttachRel> attachRelList = getAttachRelList(view,
                attachRel -> Objects.equals(attachRel.getRelType(), PcxExpAttachRelType.EXP_DETAIL.getCode())
                && Objects.equals(relKey, attachRel.getRelId()));
        if (CollectionUtils.isNotEmpty(attachRelList)){
            for (PcxBillExpAttachRel attachRel : attachRelList) {
                DetailAttachVO relQO = new DetailAttachVO();
                relQO.setAttachId(attachRel.getAttachId());
                relQO.setFileName(attachRel.getFileName());
                noEcsDetailVO.getFileList().add(relQO);
            }
        }
        return noEcsDetailVO;
    }

    public void detailMountTrip(DetailMountTripQO qo, PcxBill view) {
        List<PcxBillExpDetailBase> needUpdateDetailList = queryBillExpDetailList(view);
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(needUpdateDetailList);

        PcxBillExpDetailTravel detailTravel = travelDetailList.stream()
                .filter(item->Objects.equals(item.getId(), qo.getDetailId())).findFirst().orElseThrow(()->new RuntimeException("未查询到费用明细"));

        List<PcxBillTravelTrip> tripList = getBillTripList(view.getId());
        PcxBillTravelTrip travelTrip =  tripList.stream()
                .filter(item->Objects.equals(item.getId(), qo.getTripId())).findFirst().orElseThrow(()->new RuntimeException("未查询到行程信息"));
        String mountTripKey = getMountTripKey(travelTrip.getStartTime(), travelTrip.getEndTime(), travelTrip.getEmpCode());

        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), null);

        List<PcxBillExpDetailTravel> detailTravelList = matchBrotherDetail(detailTravel, travelDetailList, ecsRelList);
        for (PcxBillExpDetailTravel travel : detailTravelList) {
            travel.setMountTrip(mountTripKey);
            if (StringUtil.isNotEmpty(qo.getRemark())){
                travel.setRemark(travel.getRemark() + qo.getRemark());
            }
        }

        //报销单的所有费用
        List<PcxBillExpBase> baseExpList = queryBillExpBaseList(view);

        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(baseExpList, needUpdateDetailList, null, ecsRelList, view.getId(), false));

        List<String> expTypeCodes = needUpdateDetailList.stream()
                .map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        invoiceQO.setFiscal(view.getFiscal());
        invoiceQO.setAgyCode(view.getAgyCode());
        invoiceQO.setMofDivCode(view.getMofDivCode());
        Map<String, String> newParentMap = getExpTypeParentMap(invoiceQO, expTypeCodes);

        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(baseExpList)
                .insertOrUpdateDetailList(needUpdateDetailList)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .build();
        ecsExpTransService.detailMountTrip(dto);
    }

    /**
     * 住宿费找出同住的明细
     * 市内交通费找出同一张票的明细
     * 这两种情况这些明细在理票页面是在一起展示的
     * @param detailTravel
     * @param travelDetailList
     * @return
     */
    private List<PcxBillExpDetailTravel> matchBrotherDetail(PcxBillExpDetailTravel detailTravel, List<PcxBillExpDetailTravel> travelDetailList, List<PcxExpDetailEcsRel> ecsRelList) {
        if (Objects.equals(detailTravel.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)){
            return travelDetailList.stream().filter(item->{
                return Objects.equals(item.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)
                        && Objects.equals(item.getHotelGroup(), detailTravel.getHotelGroup())
                        && Objects.equals(item.getHotelSeq(), detailTravel.getHotelSeq());
            }).collect(Collectors.toList());
        }
        if (Objects.equals(detailTravel.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021112)){
            PcxExpDetailEcsRel rel = ecsRelList.stream().filter(item -> Objects.equals(item.getDetailId(), detailTravel.getId())).findFirst().orElse(null);
            if (Objects.nonNull(rel) && StringUtil.isNotEmpty(rel.getEcsBillId())){
                List<String> detailIds = ecsRelList.stream()
                        .filter(item->Objects.equals(item.getEcsBillId(), rel.getEcsBillId()) && StringUtil.isNotEmpty(item.getDetailId()))
                        .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
                return travelDetailList.stream().filter(item->detailIds.contains(item.getId())).collect(Collectors.toList());
            }
        }
        return Collections.singletonList(detailTravel);
    }

    @AllArgsConstructor
    private static class ParamsGetter {
        Supplier<List<PcxBillExpBase>> expenseBaseGet;
        Supplier<List<PcxBillExpDetailBase>> expenseDetailGet;
        Supplier<List<PcxExpDetailEcsRel>> ecsRelGet;
        Supplier<List<PcxBillTravelTrip>> tripGet;
        Supplier<List<PcxBillExpStandResult>> standResultGet;
        Supplier<List<PcxBillExpAttachRel>> attachRelGet;
    }

    private ParamsGetter getEcsViewParamsGetterByBill(PcxBill bill){
        return new ParamsGetter(
                ()->queryBillExpBaseList(bill),
                ()->queryBillExpDetailList(bill),
                ()->queryEcsRelList(bill.getId(),""),
                ()->getBillTripList(bill.getId()),
                ()->getBillStandResult(bill),
                ()->getAttachRelList(bill,  attachRel -> PcxExpAttachRelType.EXP_DETAIL.getCode()
                        .equals(attachRel.getRelType()))
        );
    }

    /**
     * 报销单票展示页面回显
     * 根据billId查询报销单主信息
     * 查询报销单关联的费用
     * 查询报销单费用的所有费用明细
     * 查询报销单行程
     * 查询报销单支出标准结果
     * 如果有行程，则填充行程票信息
     * 如果有剩余匹配的票，则填充匹配的票信息
     * 如果有未匹配的票，则填充未匹配的票信息
     * 如果有补助，则填充补助信息
     * @param bill
     * @return
     */
    public EcsExpMatchVO ecsViewNew(PcxBill bill, boolean isAudit, ParamsGetter paramsGetter) {
        EcsExpMatchVO vo = new EcsExpMatchVO();
        if (Objects.isNull(bill)){
            return vo;
        }
        vo.setDepartmentCode(bill.getDepartmentCode());
        vo.setDepartmentName(bill.getDepartmentName());
        vo.setExpenseTypeCodeList(Arrays.asList(StringUtil.getStringValue(bill.getExpenseCodes()).split(",")));
        //通用报销、会议、培训返回通用报销的票列表
        if (Objects.equals(bill.getBizType(), ItemBizTypeEnum.COMMON.getCode())){
            return ecsExpCommonService.ecsView(bill);
        }
        //查询费用
        List<PcxBillExpBase> expBaseList = paramsGetter.expenseBaseGet.get();
        //查询明细
        List<PcxBillExpDetailBase> detailBases = paramsGetter.expenseDetailGet.get();
        //查询票关联关系
        List<PcxExpDetailEcsRel> ecsRelList = paramsGetter.ecsRelGet.get();
        //费用code对于name
        List<String> allExpTypeCodeList = expBaseList.stream()
                .map(PcxBillExpBase::getExpenseCode)
                .collect(Collectors.toList());
        allExpTypeCodeList.addAll(detailBases.stream()
                .map(PcxBillExpDetailBase::getExpDetailCode)
                .collect(Collectors.toList()));
        //票id结合，查询票信息使用
        List<String> ecsBillIds = ecsRelList.stream()
                .filter(item->StringUtil.isNotEmpty(item.getEcsBillId()))
                .map(PcxExpDetailEcsRel::getEcsBillId).distinct().collect(Collectors.toList());

        //获取票信息，是否验真，票类信息，附件数量
        //审核岗pc端查看行程不需要查询票信息
        Map<String, EcsMsgDTO> ecsMsgMap = new HashMap<>();
        if (!isAudit){
            Pair<Map<String, EcsMsgDTO>, List<JSONObject>> ecsBillMsgPair = getEcsMsgMap(ecsBillIds, bill);
            ecsMsgMap = ecsBillMsgPair.getKey();
            List<JSONObject> billJsonList = ecsBillMsgPair.getRight();
            vo.setEcsbills(billJsonList);
        }

        allExpTypeCodeList.addAll(ecsRelList.stream()
                .filter(item->!isTransExpense(item) && StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                .map(PcxExpDetailEcsRel::getExpenseTypeCode)
                .collect(Collectors.toList()));
        Map<String, String> expTypeNameMap = getExpTypeNameMap(allExpTypeCodeList, bill);

        //查询行程
        List<PcxBillTravelTrip> tripList = paramsGetter.tripGet.get();
        if (CollectionUtils.isNotEmpty(tripList)){
            vo.setTripType(tripList.get(0).getTripType());
        }
        Map<String, String> tripEmpNameMap = new HashMap<>();
        for (PcxBillTravelTrip value : tripList) {
            tripEmpNameMap.put(value.getId(), value.getEmpName());
        }
        //查询标准结果
        List<PcxBillExpStandResult> standResultList = paramsGetter.standResultGet.get();

        //查询出明细与附件的关联关系
        List<PcxBillExpAttachRel> attachRelList = paramsGetter.attachRelGet.get();

        Map<Boolean, List<PcxBillExpDetailBase>> subsidyOrNoMap = detailBases.stream()
                .collect(Collectors.partitioningBy(this::isSubsidyDetail));

        List<PcxBillExpDetailBase> noSubsidyList = subsidyOrNoMap.get(false);
        List<PcxBillExpDetailBase> subsidyList = subsidyOrNoMap.get(true);

        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(noSubsidyList);
        List<PcxBillExpDetailTravel> allTrafficList = new ArrayList<>();
        List<PcxBillExpDetailTravel> hotelList = new ArrayList<>();
        //城市间交通费的可以，用于退票匹配正常行程
        Map<String, List<String>>  intercityKeyStartTimeMap = new HashMap<>();
        for (PcxBillExpDetailTravel detailTravel : travelDetailList) {
            if (isHotelDetail(detailTravel)){
                hotelList.add(detailTravel);
            }
            if (isIntercityTraffic(detailTravel)){
                String key = String.format("%s-%s-%s", detailTravel.getEmpCode(), detailTravel.getStartCityCode(), detailTravel.getEndCityCode());
                List<String> startTimeList = intercityKeyStartTimeMap.computeIfAbsent(key, key1 -> new ArrayList<>());
                startTimeList.add(detailTravel.getStartTime());
                allTrafficList.add(detailTravel);
            }
        }
        //手动添加的明细给前端带json数据，住宿费要特殊处理一下
        getHotelDetailGroup(hotelList);
        //网约车行程单，只展示一个，把票下的多个金额合并展示
        Map<String, BigDecimal> taxiInputAmtMap = new HashMap<>();
        Map<String, BigDecimal> taxiCheckAmtMap = new HashMap<>();
        Map<String, List<String>> taxiDetailIdMap = new HashMap<>();
        Map<String, PcxExpDetailEcsRel> detailEcsRelMap = ecsRelList.stream()
                .peek(item->{
                    if (isSingleShow(item.getExpenseTypeCode())){
                        List<String> detailIds = taxiDetailIdMap.computeIfAbsent(item.getEcsBillId(), key -> new ArrayList<>());
                        detailIds.add(item.getDetailId());
                        BigDecimal taxiAmt = taxiInputAmtMap.computeIfAbsent(item.getEcsBillId(), key->BigDecimal.ZERO);
                        BigDecimal taxiCheckAmt = taxiCheckAmtMap.computeIfAbsent(item.getEcsBillId(), key->BigDecimal.ZERO);
                        taxiInputAmtMap.put(item.getEcsBillId(), taxiAmt.add(item.getInputAmt()));
                        taxiCheckAmtMap.put(item.getEcsBillId(), taxiCheckAmt.add(item.getCheckAmt()));
                    }
                })
                .filter(EcsExpOptService::isTransExpense)
                .filter(item->StringUtil.isNotEmpty(item.getDetailId()))
                .collect(Collectors.toMap(PcxExpDetailEcsRel::getDetailId, Function.identity()));
        //明细转成票形式
        List<EcsExpenseVO> ecsExpList = transAllEcsExpList(noSubsidyList, subsidyList, standResultList,
                attachRelList, expTypeNameMap, ecsMsgMap, tripEmpNameMap, detailEcsRelMap, taxiInputAmtMap,taxiCheckAmtMap, taxiDetailIdMap);
        vo.setAllEcsList(ecsExpList);
        //已处理的票
        Set<String> usedDetailId = new HashSet<>();
        //网约车票只需要展示一次
        List<String> usedEcsBillId = new ArrayList<>();
        List<String> noIncludedTripTaxiDetailIds = new ArrayList<>();
        //填充行程的票
        fillTripByEcxExpense(ecsExpList, tripList, usedDetailId, vo, usedEcsBillId, intercityKeyStartTimeMap, noIncludedTripTaxiDetailIds);
        vo.setNoIncludedTripTaxiDetailIds(noIncludedTripTaxiDetailIds);
        //填充剩余已匹配的票
        fillMatchByEcxExpense(ecsExpList, usedDetailId, vo, usedEcsBillId, ecsRelList);
        //未匹配的票
        List<PcxExpDetailEcsRel> unMatchEcs = ecsRelList.stream().filter(item -> !isTransExpense(item))
                .collect(Collectors.toList());
        //填充未匹配的票
        fillUnMatchEcsViewNew(unMatchEcs, vo, ecsMsgMap, bill);

        List<PcxBillExpDetailTravel> subsidyDetailList = getTravelDetailList(subsidyList);
        //填充补助
        fillSubsidyView(subsidyDetailList, bill, vo, allTrafficList);

        //培训费/会议费的劳务人员信息处理
        ecsExpCommonService.buildLabourDetailVos(bill, vo);

        vo.setBillId(bill.getId());
        return vo;
    }

    private final static Map<String, String> provinceShortMap;

    static {
        String provinceShortStr = "{\n" +
                "\"11\": \"\",\n" +
                "\"12\": \"\",\n" +
                "\"13\": \"冀\",\n" +
                "\"14\": \"晋\",\n" +
                "\"15\": \"蒙\",\n" +
                "\"21\": \"辽\",\n" +
                "\"22\": \"吉\",\n" +
                "\"23\": \"黑\",\n" +
                "\"31\": \"\",\n" +
                "\"32\": \"苏\",\n" +
                "\"33\": \"浙\",\n" +
                "\"34\": \"皖\",\n" +
                "\"35\": \"闽\",\n" +
                "\"36\": \"赣\",\n" +
                "\"37\": \"鲁\",\n" +
                "\"41\": \"豫\",\n" +
                "\"42\": \"鄂\",\n" +
                "\"43\": \"湘\",\n" +
                "\"44\": \"粤\",\n" +
                "\"45\": \"桂\",\n" +
                "\"46\": \"琼\",\n" +
                "\"50\": \"\",\n" +
                "\"51\": \"川\",\n" +
                "\"52\": \"贵\",\n" +
                "\"53\": \"云\",\n" +
                "\"54\": \"藏\",\n" +
                "\"61\": \"陕\",\n" +
                "\"62\": \"甘\",\n" +
                "\"63\": \"青\",\n" +
                "\"64\": \"宁\",\n" +
                "\"65\": \"新\",\n" +
                "\"81\": \"港\",\n" +
                "\"82\": \"澳\",\n" +
                "\"71\": \"台\"\n" +
                "}";
        provinceShortMap = JSON.parseObject(provinceShortStr, new TypeReference<Map<String, String>>() {
        });
    }

    /**
     * 从理票行程里面的行程列表中复制一份出来
     * 只保留城市间交通费节点，并且每个节点保存关联的费用明细id集合，给前端点击节点筛选费用明细使用
     * A-B-C-D-A1(A1为最后返回出发地A，实在行程最后手动添加的一条)
     * A包含行程的所有明细id
     * B包含A-B的城市间交通费，和A-B之间发生的其他费用明细id
     * C包含B-C的城市间交通费，和B-C之间发生的其他费用明细id
     * D一样
     * A1包含D-A1的城市间交通费，A1发生的市内交通费
     * @param bill
     * @param isAudit
     * @return
     */
    public Pair<EcsExpMatchVO, List<BillTripVO>> ecsExpMatchAndTripVo(PcxBill bill, boolean isAudit, ParamsGetter paramsGetter){
        //TODO 申请不涉及行程处理 兼容出国、会议、培训、招待等业务判断处理
        if (BillFuncCodeEnum.APPLY.getCode().equals(bill.getBillFuncCode())){
            return Pair.of(new EcsExpMatchVO(), new ArrayList<>());
        }
        if (Objects.isNull(paramsGetter)){
            paramsGetter = getEcsViewParamsGetterByBill(bill);
        }
        EcsExpMatchVO ecsExpMatchVO = ecsViewNew(bill, isAudit, paramsGetter);
        List<EcsBillTripVO> tripList = ecsExpMatchVO.getTripList();
        if (CollectionUtils.isEmpty(tripList)){
            return Pair.of(ecsExpMatchVO, new ArrayList<>());
        }
        List<BillTripVO> result = new ArrayList<>();
        List<List<EcsExpenseVO>> tripEcsList = new ArrayList<>();
        tripList = tripList.stream().filter(item->CollectionUtils.isNotEmpty(item.getEcsList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tripList)){
            return Pair.of(ecsExpMatchVO, new ArrayList<>());
        }
        //在取每个节点的关联的费用明细，最后一个节点也需要关联
        for (EcsBillTripVO tripVO : tripList) {
            BillTripVO billTripVO = new BillTripVO();
            BeanUtils.copyProperties(tripVO, billTripVO);

            List<EcsExpenseVO> ecsList = new ArrayList<>(tripVO.getEcsList());
            tripEcsList.add(ecsList);
            EcsExpenseVO head = ecsList.get(0);
            //第一个节点可以查看所有明细
            Set<String> allDetailIds = new HashSet<>();
            allDetailIds.add(head.getDetailId());
            //第一个节点可以被下一个节点点击查看
            head.getNextDetailIds().add(head.getDetailId());
            EcsExpenseVO preCity = head;
            if (ecsList.size()>1){
                Map<String, Set<String>> sectionCityDetailMap = new HashMap<>();
                Iterator<EcsExpenseVO> iterator = ecsList.subList(1, ecsList.size()).iterator();
                while (iterator.hasNext()){
                    EcsExpenseVO ecsExpenseVO = iterator.next();
                    String matchKey = getMatchKey(preCity, ecsExpenseVO);
                    Set<String> detailIdList = sectionCityDetailMap.computeIfAbsent(matchKey, key -> new HashSet<>());
                    detailIdList.addAll(ecsExpenseVO.getBrotherDetails());
                    allDetailIds.addAll(ecsExpenseVO.getBrotherDetails());
                    allDetailIds.add(ecsExpenseVO.getDetailId());

                    if (isIntercityTrafficVO(ecsExpenseVO)){
                        //出发城市的明细，或没有城市的自己展示
                        // 其他城市的下一节点展示
                        String cityCode = ecsExpenseVO.getCityCode();
                        for (Map.Entry<String, Set<String>> entry : sectionCityDetailMap.entrySet()) {
                            if (Objects.equals(entry.getKey(), CHANGE_OR_RETURN_KEY)){
                                ecsExpenseVO.getMyDetailIds().addAll(entry.getValue());
                                continue;
                            }
                            String[] split = entry.getKey().split("#");
                            String city = "";
                            if (split.length>0){
                                city = split[0];
                            }
                            String time = "";
                            if (split.length>1){
                                time = split[1];
                            }

                            if ("".equals(city) //没有城市
                                    || Objects.equals(city, cityCode) // 或者是当前城市
                            || (Objects.equals(preCity.getCityCode(), city) && Objects.equals(time, preCity.getStartTime()))){ //或者是前一个城市的其他费用
                                ecsExpenseVO.getMyDetailIds().addAll(entry.getValue());
                            }else {
                                ecsExpenseVO.getNextDetailIds().addAll(entry.getValue());
                            }
                        }
                        //自己也是下一节点展示
                        ecsExpenseVO.getNextDetailIds().add(ecsExpenseVO.getDetailId());
                        sectionCityDetailMap.clear();
                        preCity = ecsExpenseVO;
                    }else{
                        detailIdList.add(ecsExpenseVO.getDetailId());
                        iterator.remove();
                    }
                }
            }
            head.getTripDetailIds().addAll(allDetailIds);
            //把补助vo从行程里面删除掉，补助vo临时放到行程里面只是为了收集行程节点可以更新的明细id集合
            removeAllSubsidy(tripVO.getEcsList());
        }
        for (int i = 0; i < tripList.size(); i++) {
            BillTripVO billTripVO = new BillTripVO();
            EcsBillTripVO tripVO = tripList.get(i);
            List<EcsExpenseVO> ecsList = tripEcsList.get(i);
            BeanUtils.copyProperties(tripVO, billTripVO);
            List<BillTripNodeVO> nodeVOS = new ArrayList<>();

            EcsExpenseVO preVo = null;
            for (int j = 0; j < ecsList.size(); j++) {
                BillTripNodeVO nodeVO = new BillTripNodeVO();
                EcsExpenseVO vo = ecsList.get(j);
                BeanUtils.copyProperties(vo, nodeVO);
                nodeVO.setPreFinishTime(Objects.nonNull(preVo) ? preVo.getFinishTime() : "");
                nodeVO.setCityCode(vo.getStartCityCode());
                nodeVO.setCityName(vo.getStartCity());
                if (j>0){
                    EcsExpenseVO pre = ecsList.get(j-1);
                    vo.getTripDetailIds().addAll(pre.getNextDetailIds());
                    vo.getTripDetailIds().addAll(ecsList.get(j).getMyDetailIds());
                    double days = TravelTripProcessor.getDiffDays(pre.getFinishTime(), vo.getStartTime());
                    nodeVO.setDays(days);
                }
                disposeNodeCityName(nodeVO);
                nodeVOS.add(nodeVO);
                preVo = vo;
            }
            BillTripNodeVO last = new BillTripNodeVO();
            if (Objects.nonNull(preVo)){
                last.setCityName(preVo.getEndCity());
                last.setCityCode(preVo.getEndCityCode());
                last.setPreFinishTime(preVo.getFinishTime());
                last.setStartTime(preVo.getFinishTime());
                last.setFinishTime(preVo.getFinishTime());
                last.getTripDetailIds().addAll(preVo.getNextDetailIds());
            }
            disposeNodeCityName(last);
            nodeVOS.add(last);
            //合并城市节点
            nodeVOS = margeCityNode(nodeVOS);
            //处理同一天到达多个城市，前面的城市显示0天
            disposeSameDateDays(nodeVOS);
            disposeAdditionQueryId(nodeVOS);
            billTripVO.setEcsList(nodeVOS);
            result.add(billTripVO);
        }
        return Pair.of(ecsExpMatchVO, result);
    }

    /**
     * A-B-C-D-E-A
     * @param nodeVOS
     */
    private void disposeSameDateDays(List<BillTripNodeVO> nodeVOS) {
        double lastDays = 0;
        for (int i = 0; i < nodeVOS.size()-1; i++) {
            BillTripNodeVO current = nodeVOS.get(i);
            BillTripNodeVO next = nodeVOS.get(i+1);
            String preFinishTime = current.getPreFinishTime();
            String startTime = current.getStartTime();
            //B节点时，B的到达时间和B出发去C的时间
            //如果不是同一天则不用处理
            if (!Objects.equals(preFinishTime, startTime)){
                lastDays = current.getDays();
                continue;
            }
            //C的到达时间，和C出发去D的时间
            String nextPreFinishTime = next.getPreFinishTime();
            String nextStartTime = next.getStartTime();
            //如果B和C的到达时间相同，则就属于同一天去了多个地方，前面的节点算0天，同一天最后的节点算日期
//            if (Objects.equals(preFinishTime, nextPreFinishTime)){
//                current.setDays(0);
//                if (!Objects.equals(startTime, nextStartTime)){
//                    next.setDays(next.getDays()+1);
//                }
//            }
            //如果是A-B-A这种当天往返的，B不用清0
            if (nodeVOS.size() > 3 && Objects.equals(preFinishTime, nextPreFinishTime)){
                current.setDays(0);
                if (lastDays == 0 && i == nodeVOS.size()-2){
                    current.setDays(1);
                }else{
                    //如果C去D的出发时间不是当天，则C把B的1天也加上
                    if (!Objects.equals(startTime, nextStartTime)){
                        next.setDays(next.getDays()+1);
                    }
                }
            }
            lastDays = current.getDays();
        }
    }

    private static final String CHANGE_OR_RETURN_KEY = "CHANGE_OR_RETURN";
    private String getMatchKey(EcsExpenseVO preCity, EcsExpenseVO ecsExpenseVO) {
        //退票和改签票直接归类到下一个城市间交通费上
        if (Objects.equals(ecsExpenseVO.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021106)
        || (Objects.equals(ecsExpenseVO.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021101) && StringUtil.isNotEmpty(ecsExpenseVO.getChangeDetailId()))){
            return CHANGE_OR_RETURN_KEY;
        }else{
            return String.format("%s#%s",ecsExpenseVO.getCityCode(), ecsExpenseVO.getStartTime());
        }
    }

    /**
     * 同一城市的节点进行合并
     * @param nodeVOS
     * @return
     */
    private List<BillTripNodeVO> margeCityNode(List<BillTripNodeVO> nodeVOS) {
        if (CollectionUtils.isEmpty(nodeVOS) || nodeVOS.size() == 1){
            return nodeVOS;
        }
        BillTripNodeVO nodeVO = nodeVOS.get(0);
        nodeVOS = nodeVOS.subList(1, nodeVOS.size());
        Collections.reverse(nodeVOS);
        Iterator<BillTripNodeVO> it = nodeVOS.iterator();
        BillTripNodeVO cur = it.next();
        List<BillTripNodeVO> result = new ArrayList<>();
        while (it.hasNext()){
            BillTripNodeVO next = it.next();
            if (Objects.equals(cur.getPreFinishTime(), next.getPreFinishTime())
                    && Objects.equals(cur.getCityCode(), next.getCityCode())){
                cur.getTripDetailIds().addAll(next.getTripDetailIds());
                cur.setPreFinishTime(next.getPreFinishTime());
                it.remove();
            }else{
                result.add(cur);
                cur = next;
            }
        }
        result.add(cur);
        Collections.reverse(result);
        result.add(0, nodeVO);
        return result;
    }

    //第一版补充信息使用，已经没用了
    @Deprecated
    private void disposeAdditionQueryId(List<BillTripNodeVO> nodeVOS) {
        for (BillTripNodeVO nodeVO : nodeVOS) {
            if (CollectionUtils.isNotEmpty(nodeVO.getTripDetailIds())){
                nodeVO.setQueryAdditionId(nodeVO.getTripDetailIds().get(0));
            }else{
                nodeVO.setQueryAdditionId(nodeVO.getDetailId());
            }
        }
    }

    /**
     * 把补助vo从行程里面删除掉
     * @param ecsList
     */
    private void removeAllSubsidy(List<EcsExpenseVO> ecsList) {
        ecsList.removeIf(this::isSubsidyVo);
    }

    private boolean isSubsidyVo(EcsExpenseVO next) {
        return Objects.equals(PcxConstant.TRAVEL_DETAIL_3021103, next.getExpenseTypeCode())
                || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021104, next.getExpenseTypeCode());
    }

    private void disposeNodeCityName(BillTripNodeVO last) {
        if (StringUtil.isNotEmpty(last.getCityCode())){
            String shortName = provinceShortMap.get(last.getCityCode().substring(0, 2));
            if (StringUtil.isNotEmpty(shortName)){
                last.setCityName(String.format("%s/%s", last.getCityName(), shortName));
            }
        }
    }

    private void tidyEcsVoSeq(List<EcsExpenseVO> ecsList) {
        int start = 0;
        for (int i = 0; i < ecsList.size(); i++) {
            EcsExpenseVO node = ecsList.get(i);
            if (isIntercityTrafficVO(node)){
                if(start > 0){
                    List<EcsExpenseVO> section = ecsList.subList(start, i);
                    if (CollectionUtils.isNotEmpty(section)){
                        section.sort(new Comparator<EcsExpenseVO>() {
                            @Override
                            public int compare(EcsExpenseVO o1, EcsExpenseVO o2) {
                                int typeCompare = o2.getExpenseTypeCode().compareTo(o1.getExpenseTypeCode());
                                if (typeCompare == 0){
                                    if (StringUtil.isNotEmpty(o1.getStartTime(), o2.getStartTime())){
                                        return o1.getStartTime().compareTo(o2.getStartTime());
                                    }
                                    if (StringUtil.isNotEmpty(o1.getFinishTime(), o2.getFinishTime())){
                                        return o1.getFinishTime().compareTo(o2.getFinishTime());
                                    }
                                    return 0;
                                }
                                return typeCompare;
                            }
                        });
                    }
                }
                start = i+1;
            }
        }
    }

    private Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMap(List<String> ecsBillIds, PcxBill view) {
        return ecsBillExternalService.getEcsMsgMapByEcsIds(ecsBillIds, view.getFiscal(), view.getAgyCode(), view.getMofDivCode());
    }

    /**
     * 手动添加的明细，要把明细转成json带给前端
     * 住宿费需要特殊处理，因为保存的时候住宿费多个人被拆成了多条detail，这一起的多个人的明细给前端的时候要组装成一条
     * @param allHotelDetailList
     * @return
     */
    private void getHotelDetailGroup(List<PcxBillExpDetailTravel> allHotelDetailList) {
        if (CollectionUtils.isNotEmpty(allHotelDetailList)){
            Map<String, List<PcxBillExpDetailTravel>> map = allHotelDetailList.stream()
                    .collect(Collectors.groupingBy(this::getHotelGroupSeq));
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : map.entrySet()) {
                List<EmpInfo> infoList = new ArrayList<>();
                String tripId = "";
                for (PcxBillExpDetailTravel item : entry.getValue()) {
                    if (StringUtil.isEmpty(tripId)){
                        tripId = item.getTripId();
                    }
                    EmpInfo info = new EmpInfo();
                    info.setEmpCode(item.getEmpCode());
                    info.setEmpName(item.getEmpName());
                    info.setBudLevel(item.getBudLevel());
                    info.setEmpType(item.getEmpType());
                    info.setIsSubsidy(item.getIsSubsidy());
                    infoList.add(info);
                }
                for (PcxBillExpDetailTravel travel : entry.getValue()) {
                    travel.setTripId(tripId);
                    travel.setEmpInfoList(infoList);
                }
            }
        }
    }

    private void fillUnMatchEcsViewNew(List<PcxExpDetailEcsRel> unMatchEcs,
                                       EcsExpMatchVO resultVo,
                                       Map<String, EcsMsgDTO> ecsMsgMap,
                                       PcxBill bill) {
        Map<String, List<PcxExpDetailEcsRel>> ecsMap = unMatchEcs.stream().collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillId));
        List<String> umMatchEcsExpenseType = unMatchEcs.stream()
                .filter(item->StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                .map(PcxExpDetailEcsRel::getExpenseTypeCode)
                .collect(Collectors.toList());
        Map<String, List<PcxBasFormSetting>> detailFormSettingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(umMatchEcsExpenseType)){
            List<PcxBasFormSetting> list = pcxBasFormSettingService.selectValidNotNullSettings(bill.getFiscal(), bill.getAgyCode(), bill.getMofDivCode());
            detailFormSettingMap = list.stream().collect(Collectors.groupingBy(PcxBasFormSetting::getFormCode));
        }
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsMap.entrySet()) {
            BigDecimal unmatchEcsAmt = BigDecimal.ZERO;
            Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = Optional.ofNullable(entry.getValue())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(rel ->
                            Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                    ));
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> listEntry : ecsDetailMap.entrySet()) {
                unmatchEcsAmt = unmatchEcsAmt.add(listEntry.getValue().get(0).getEcsAmt());
            }

            EcsExpenseVO vo = new EcsExpenseVO();

            PcxExpDetailEcsRel item = entry.getValue().get(0);
            vo.setExpenseTypeCode(item.getExpenseTypeCode());
            vo.setExpenseTypeName(item.getExpenseTypeName());
            vo.setEcsBillId(item.getEcsBillId());
            vo.setEcsBillType(item.getEcsBillType());
            vo.setEcsBillDate(item.getEcsBillDate());
            vo.setEcsBillNo(item.getEcsBillNo());
            vo.setTicketAmt(unmatchEcsAmt);
            vo.setInputAmt(unmatchEcsAmt);
            vo.setEmpCode(item.getEmpCode());
            vo.setItemName(item.getEcsBillDesc());
            vo.setSource(BillExpDetailSourceEnum.ECS.getCode());
            vo.setHintMessage(disposeUnMatchEcsHint(entry.getValue().get(0), detailFormSettingMap));
            vo.setEcsRelId(item.getId());
            EcsMsgDTO ecsMsgDTO = ecsMsgMap.get(item.getEcsBillId());
            if (ecsMsgDTO != null){
                vo.setIsValid(ecsMsgDTO.getIsValid());
                vo.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
                vo.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
                vo.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
                vo.setDisplayBizTypeName(EcsItemNameHelper.getDisplayBizTypeName(ecsMsgDTO.getBizTypeName()));
            }

            resultVo.getUnMatchEcsList().add(vo);
        }
    }

    private String disposeUnMatchEcsHint(PcxExpDetailEcsRel pcxExpDetailEcsRel, Map<String, List<PcxBasFormSetting>> detailFormSettingMap) {
        if (StringUtil.isEmpty(pcxExpDetailEcsRel.getExpenseTypeCode())){
            return "请确认费用类型";
        }
        Set<String> hintSet = new HashSet<>();
        if (StringUtil.isNotEmpty(pcxExpDetailEcsRel.getEcsContent())){
            List<PcxBillExpDetailTravel> detailList = JSON.parseArray(pcxExpDetailEcsRel.getEcsContent(), PcxBillExpDetailTravel.class);
            pcxBasFormSettingService.isConfirm(detailList, detailFormSettingMap, hintSet, Sets.newHashSet());
        }else{
            hintSet = detailFormSettingMap.get(pcxExpDetailEcsRel.getExpenseTypeCode()).stream().map(PcxBasFormSetting::getFieldName).collect(Collectors.toSet());
        }
        if (hintSet.isEmpty()){
            return "";
        }else if (hintSet.size()==1){
            return String.format("待补充%s", hintSet.iterator().next());
        }else{
            return String.format("待补充%s 等%d项信息", hintSet.iterator().next(), hintSet.size());
        }
    }

    private void fillMatchByEcxExpense(List<EcsExpenseVO> ecsExpList,
                                       Set<String> usedDetailId,
                                       EcsExpMatchVO vo,
                                       List<String> usedEcsBillId,
                                       List<PcxExpDetailEcsRel> ecsRelList) {
        //找出行程处理完的票，然后看票是否关联的费用或者明细，这些票是需要处理的剩余匹配的票
        List<EcsExpenseVO> matchEcsRel = ecsExpList.stream()
                .filter(item -> !usedDetailId.contains(item.getDetailId())
                && (StringUtil.isEmpty(item.getEcsBillId()) || !usedEcsBillId.contains(item.getEcsBillId()))
                && !isSubsidyDetailVo(item))
                .collect(Collectors.toList());
        //市内交通费值展示一次
        List<EcsExpenseVO> trafficList = matchEcsRel.stream()
                .filter(item->isSingleShow(item.getExpenseTypeCode())
                        && StringUtil.isNotEmpty(item.getEcsBillId()))
                .collect(Collectors.toList());
        matchEcsRel.removeAll(trafficList);
        for (EcsExpenseVO ecsExpenseVO : trafficList) {
            if (!usedEcsBillId.contains(ecsExpenseVO.getEcsBillId())){
                matchEcsRel.add(ecsExpenseVO);
                usedEcsBillId.add(ecsExpenseVO.getEcsBillId());
            }
        }
        if(CollectionUtils.isNotEmpty(matchEcsRel)){
            //根据detailId进行分组
            Map<String, List<PcxExpDetailEcsRel>> detailEcsMap = ecsRelList.stream()
                    .filter(item->StringUtil.isNotEmpty(item.getDetailId()))
                    .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getDetailId));
            for (EcsExpenseVO ecsExpenseVO : matchEcsRel) {
                String detailId = ecsExpenseVO.getDetailId();
                List<PcxExpDetailEcsRel> rels = detailEcsMap.get(detailId);
                if (CollectionUtil.isNotEmpty(rels)) {
                    ecsExpenseVO.setEcsRelId(rels.get(0).getId());
                }
            }
        }
        vo.getEcsExpList().addAll(matchEcsRel);
    }

    /**
     * 填充行程的票
     */
    private void fillTripByEcxExpense(List<EcsExpenseVO> ecsExpList,
                                      List<PcxBillTravelTrip> tripList,
                                      Set<String> usedDetailId,
                                      EcsExpMatchVO vo,
                                      List<String> usedEcsBillId,
                                      Map<String, List<String>>  intercityKeyStartTimeMap,
                                      List<String> noIncludedTripTaxiDetailIds) {
        //没有行程则不需要处理
        if (CollectionUtils.isEmpty(tripList)){
            return;
        }
        tripList.sort(Comparator.comparing(PcxBillTravelTrip::getStartTime));
        Map<String, List<EcsExpenseVO>> returnEcsMap = new HashMap<>();
        Map<String, EcsExpenseVO> ecsChangeMap = new HashMap<>();
        //匹配不到行程的退票
        List<EcsExpenseVO> noInercityEcsList = new ArrayList<>();
        List<EcsExpenseVO> taxiDetails = new ArrayList<>();
        for (EcsExpenseVO item : ecsExpList) {
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021106, item.getExpenseTypeCode())
                && StringUtil.isNotEmpty(item.getEmpCode(), item.getStartCityCode(), item.getEndCityCode())){
                String intercityKey = returnEcsKey(item);
                List<String> startTimeList = intercityKeyStartTimeMap.get(intercityKey);
                if (CollectionUtils.isNotEmpty(startTimeList)
                        && startTimeList.stream().anyMatch(startTime->isInOneWeek(item.getStartTime(), startTime))){
                    List<EcsExpenseVO> returnEcsList = returnEcsMap.computeIfAbsent(intercityKey, key -> new ArrayList<>());
                    returnEcsList.add(item);
                }else{
                    noInercityEcsList.add(item);
                }
            }
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021101, item.getExpenseTypeCode())
                && StringUtil.isNotEmpty(item.getChangeDetailId())){
                ecsChangeMap.put(item.getChangeDetailId(), item);
            }
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021112, item.getExpenseTypeCode())
                && Objects.equals(BillExpDetailSourceEnum.ECS.getCode(), item.getSource())){
                taxiDetails.add(item);
            }
        }

        //遍历每个行程
        for (PcxBillTravelTrip value : tripList) {
            EcsBillTripVO tripVO = new EcsBillTripVO();
            tripVO.setStartTime(value.getStartTime().substring(0,10));
            tripVO.setEndTime(value.getEndTime().substring(0,10));
            String tripEmpName = value.getEmpName();
            tripVO.setEmpName(disposeTripEmpName(value.getEmpName()));
            tripVO.setEmpCode(value.getEmpCode());
            tripVO.setTripId(value.getId());
            //填充行程的票，包括差旅的票，和行程人的其他票
            tripVO.setEcsList(collectTripEmpEcsListByExpense(value, ecsExpList, usedDetailId, usedEcsBillId, returnEcsMap, ecsChangeMap, tripEmpName, value.getTripType(), noInercityEcsList, taxiDetails));
            vo.getTripList().add(tripVO);
            for (EcsExpenseVO ecsExpenseVO : tripVO.getEcsList()) {
                if (StringUtil.isNotEmpty(ecsExpenseVO.getStartTime())){
                    ecsExpenseVO.setStartTime(ecsExpenseVO.getStartTime().substring(0,10));
                }
                if (StringUtil.isNotEmpty(ecsExpenseVO.getFinishTime())){
                    ecsExpenseVO.setFinishTime(ecsExpenseVO.getFinishTime().substring(0,10));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(taxiDetails)){
            noIncludedTripTaxiDetailIds.addAll(taxiDetails.stream().map(EcsExpenseVO::getDetailId).collect(Collectors.toList()));
        }
        //外挂的票
        Map<String, List<EcsExpenseVO>> mountListMap = ecsExpList.stream().filter(item->{
            return !usedDetailId.contains(item.getDetailId()) && !usedEcsBillId.contains(item.getEcsBillId()) && StringUtil.isNotEmpty(item.getMountTrip());
        }).collect(Collectors.groupingBy(EcsExpenseVO::getMountTrip));
        if (!mountListMap.isEmpty()){
            //处理一下外挂的票
            for (EcsBillTripVO tripVO : vo.getTripList()) {
                String mountTripKey = getMountTripKey(tripVO.getStartTime(), tripVO.getEndTime(), tripVO.getEmpCode());
                List<EcsExpenseVO> mountList = mountListMap.get(mountTripKey);
                //把行程外挂载的票包含进去
                if (CollectionUtils.isNotEmpty(mountList)){
                    List<EcsExpenseVO> sortResult = tripVO.getEcsList();
                    EcsExpenseVO last = sortResult.remove(sortResult.size() - 1);
                    for (EcsExpenseVO ecsExpenseVO : mountList) {
                        if (isSingleShow(ecsExpenseVO.getExpenseTypeCode())
                                && StringUtil.isNotEmpty(ecsExpenseVO.getEcsBillId())) {
                            if (!usedEcsBillId.contains(ecsExpenseVO.getEcsBillId())) {
                                usedEcsBillId.add(ecsExpenseVO.getEcsBillId());
                                usedDetailId.add(ecsExpenseVO.getDetailId());
                                sortResult.add(ecsExpenseVO);
                            }
                        }else{
                            sortResult.add(ecsExpenseVO);
                            usedDetailId.add(ecsExpenseVO.getDetailId());
                        }
                    }

                    sortResult.add(last);
                }
            }
        }

    }

    private String disposeTripEmpName(String empName) {
        String[] split = empName.split(",");
        //多余2个人，只展示两个人的名字
        if (split.length>2){
            return String.format("%s,%s等%d人", split[0], split[1], split.length);
        }
        return empName;
    }

    //改签费按照城市间交通费处理
    public static String transTripDetailExpenseTypeCode(String expenseTypeCode){
        if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021101, expenseTypeCode)
            ||Objects.equals(PcxConstant.TRAVEL_DETAIL_3021110, expenseTypeCode)){
            return PcxConstant.TRAVEL_DETAIL_3021101;
        }
        return expenseTypeCode;
    }

    private List<EcsExpenseVO> collectTripEmpEcsListByExpense(PcxBillTravelTrip trip,
                                                              List<EcsExpenseVO> ecsExpList,
                                                              Set<String> usedDetailId,
                                                              List<String> usedEcsBillId,
                                                              Map<String, List<EcsExpenseVO>> returnEcsMap,
                                                              Map<String, EcsExpenseVO> ecsChangeMap,
                                                              String tripEmpName,
                                                              String tripType,
                                                              List<EcsExpenseVO> noInercityEcsList,
                                                              List<EcsExpenseVO> taxiDetails) {

        //找到行程的费用明细
        //先按费用明细类型排序，再按开始时间排序
        List<EcsExpenseVO> tripDetail = ecsExpList.stream()
                .filter(item -> trip.getId().equals(item.getTripId())
                    && StringUtil.isEmpty(item.getMountTrip()))//挂载的票不在这里处理
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tripDetail)){
            return Lists.newArrayList();
        }
        List<EcsExpenseVO> result = new ArrayList<>(tripDetail);
        result = result.stream().filter(item->StringUtil.isEmpty(item.getChangeDetailId())).collect(Collectors.toList());
        //行程的明细id集合
        List<String> travelDetailIds = new ArrayList<>();
        //开始时间结束时间
        List<EcsExpenseVO> travelList = tripDetail.stream()
                .peek(item->{
                    travelDetailIds.add(item.getDetailId());
                })
                .filter(this::isIntercityTrafficVO)
                .sorted(Comparator.comparing(EcsExpenseVO::getSortDate)).collect(Collectors.toList());

        String startAdjustTime = getAdjustTime(travelList.get(0), -12);
        String endAdjustTime = getAdjustFinishTime(travelList.get(travelList.size()-1), 12);

        String startTime = travelList.get(0).getStartTime();
        String endTime = travelList.get(travelList.size()-1).getFinishTime();

        String tripId = trip.getId();

        List<String> empCodeList = Arrays.asList(trip.getEmpCode().split(","));
        //行程人，在行程时间内的其他票
        List<EcsExpenseVO> empOtherEcsRel = ecsExpList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getSortDate())
                        && StringUtil.isEmpty(item.getChangeDetailId())
                        && StringUtil.isEmpty(item.getTripId())
                        && (
                            (TravelTripProcessor.transDate(item.getSortDate()).compareTo(TravelTripProcessor.transDate(startAdjustTime)) >= 0
                                && TravelTripProcessor.transDate(item.getSortDate()).compareTo(TravelTripProcessor.transDate(endAdjustTime)) <= 0
                                && !isSubsidyVo(item))
                        || (
                                TravelTripProcessor.transDate(item.getSortDate()).compareTo(startTime)>=0
                                && TravelTripProcessor.transDate(item.getSortDate()).compareTo(endTime)<=0
                                && isSubsidyVo(item)
                            )
                        )
                        && (empCodeList.contains(item.getEmpCode()) || StringUtil.isEmpty(item.getEmpCode()))
                        && !travelDetailIds.contains(item.getDetailId())
                        && !usedEcsBillId.contains(item.getEcsBillId()))
                .collect(Collectors.toList());

        result.addAll(empOtherEcsRel);

        //使用城市间交通费和住宿费作为排序的框架
        //先展示城市间交通费，再展示住宿费
        //其他费用往里面插
        List<EcsExpenseVO> sortResult = result.stream()
                .filter(item->isTripExpenseVo(item)
                && item.getTripId().equals(trip.getId()))
                .sorted(new Comparator<EcsExpenseVO>() {
                    @Override
                    public int compare(EcsExpenseVO o1, EcsExpenseVO o2) {
                        int startTimeCompare = o1.getStartTime().compareTo(o2.getStartTime());
                        if (startTimeCompare == 0){
                            String o1ExpenseTypeCode = transTripDetailExpenseTypeCode(o1.getExpenseTypeCode());
                            String o2ExpenseTypeCode = transTripDetailExpenseTypeCode(o2.getExpenseTypeCode());
                            int typeCode = o1ExpenseTypeCode.compareTo(o2ExpenseTypeCode);
                            if (typeCode == 0){
                                if (PcxConstant.TRAVEL_DETAIL_3021101.equals(o1ExpenseTypeCode)){
                                    return o1.getTripSeq().compareTo(o2.getTripSeq());
                                }
                            }else{
                                return typeCode;
                            }

                        }
                        return startTimeCompare;
                    }
                })
                .collect(Collectors.toList());
        //保证最后一个是城市间交通费
        sortLastTraffic(sortResult);
        List<EcsExpenseVO> other = result.stream()
                .filter(item->!(Objects.equals(PcxConstant.TRAVEL_DETAIL_3021101, item.getExpenseTypeCode())
                        || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021102, item.getExpenseTypeCode())
                        || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021106, item.getExpenseTypeCode())
                        || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021110, item.getExpenseTypeCode())))
                .sorted((o1,o2)->{
                    //同一类型的票集中展示
                    int compare = o1.getExpenseTypeCode().compareTo(o2.getExpenseTypeCode());
                    if (compare == 0){
                        return o1.getSortDate().compareTo(o2.getSortDate());
                    }else{
                        return compare;
                    }
                })
                .collect(Collectors.toList());
        String startCityCode = "";
        //排序框架是城市间交通费和住宿费
        //循环框架元素，把其他费用往里面插
        //如果有城市看城市和两端的节点城市是否相同
        //市内交通费只展示一次
        String lastDetailId = sortResult.get(sortResult.size()-1).getDetailId();
        for (int i = 0; i < sortResult.size()-1;) {
            String sortSTime = TravelTripProcessor.transDate(sortResult.get(i).getSortDate());
            //出发行程时间往前推12小时，返程结束时间往后推12小时，以便能把出发搭车，返程搭车的票包含进去
            String itemStartAdjustTime = "";
            if (i == 0 ){
                startCityCode = sortResult.get(i).getCityCode();
                itemStartAdjustTime = getAdjustTime(sortResult.get(i), -12);
            }
            EcsExpenseVO endCity = sortResult.get(i+1);
            String itemEndAdjustTime = "";
            if (i == sortResult.size() - 2){
                itemEndAdjustTime = getAdjustFinishTime(endCity, 12);
            }
            String sortETime = getSortEndTime(endCity);
            String endCityCode = endCity.getCityCode();
            String arriveCityCode = getArriveCityCode(endCity, lastDetailId);
            int addNum = i+1;
            Iterator<EcsExpenseVO> otherIt = other.iterator();
            while (otherIt.hasNext()){
                EcsExpenseVO next = otherIt.next();
                //其他票的发生时间是否在城市驻留时间内，城市驻留时间就是前一个城市的到达时间和后一个行程的出发时间之间
                //如果是第一个出发行程，则出发时间会往前调整12个小时有一个调整时间
                //如果是最后一个到达行程，则到达时间会往后调整12个小时有一个调整时间
                //如果票时间在城市到达时间后，或调整的时间后
                if ((TravelTripProcessor.transDate(next.getSortDate()).compareTo(sortSTime) >= 0 ||
                        (StringUtil.isNotEmpty(itemStartAdjustTime) && TravelTripProcessor.transDate(next.getSortDate()).compareTo(itemStartAdjustTime) >= 0))
                        //如果票时间在离开城市时间前，或调整时间前
                    &&(TravelTripProcessor.transDate(next.getSortDate()).compareTo(sortETime) <= 0 ||
                        (StringUtil.isNotEmpty(itemEndAdjustTime) && TravelTripProcessor.transDate(next.getSortDate()).compareTo(itemEndAdjustTime) <= 0))){
                    if (StringUtil.isEmpty(next.getCityCode()) ||
                            (endCityCode.equals(next.getCityCode()) || (startCityCode.equals(next.getCityCode()))) ||
                            (StringUtil.isNotEmpty(arriveCityCode) && arriveCityCode.equals(next.getCityCode()))){
                        //如果是网约车票，则只展示一次，就把票id记录到已使用的票id集合中，这张票就展示一次
                        if (isSingleShow(next.getExpenseTypeCode())
                            && StringUtil.isNotEmpty(next.getEcsBillId())){
                            if (!usedEcsBillId.contains(next.getEcsBillId())){
                                sortResult.add(addNum ++, next);
                                otherIt.remove();
                                usedEcsBillId.add(next.getEcsBillId());
                            }
                        }else{
                            sortResult.add(addNum ++, next);
                            otherIt.remove();
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(taxiDetails)){
                Iterator<EcsExpenseVO> iterator = taxiDetails.iterator();
                while (iterator.hasNext()){
                    EcsExpenseVO next = iterator.next();
                    if (StringUtil.isNotEmpty(next.getEmpCode()) && !empCodeList.contains(next.getEmpCode())){
                        continue;
                    }
                    if ((TravelTripProcessor.transDate(next.getSortDate()).compareTo(sortSTime) >= 0 ||
                            (StringUtil.isNotEmpty(itemStartAdjustTime) && TravelTripProcessor.transDate(next.getSortDate()).compareTo(itemStartAdjustTime) >= 0))
                            &&(TravelTripProcessor.transDate(next.getSortDate()).compareTo(sortETime) <= 0 ||
                            (StringUtil.isNotEmpty(itemEndAdjustTime) && TravelTripProcessor.transDate(next.getSortDate()).compareTo(itemEndAdjustTime) <= 0))){
                        if (StringUtil.isEmpty(next.getCityCode()) ||
                                (endCityCode.equals(next.getCityCode()) || (startCityCode.equals(next.getCityCode()))) ||
                                (StringUtil.isNotEmpty(arriveCityCode) && arriveCityCode.equals(next.getCityCode()))){
                            iterator.remove();
                        }
                    }
                }
            }
            addNum -= i;
            i += addNum;
            startCityCode = endCityCode;
        }

        //区间内排序
        tidyEcsVoSeq(sortResult);

        //把退票费加进去
        //把改签费对应的原城市间交通费加进去
        for (int i = 0; i < sortResult.size()-1;) {
            EcsExpenseVO startCity = sortResult.get(i);
            EcsExpenseVO endCity = sortResult.get(i+1);
            int addNum = i+1;
            addNum += insertReturnEcs(startCity, returnEcsMap, sortResult, addNum, usedDetailId, noInercityEcsList);
            addNum += insertEcsChangeOrigin(endCity, ecsChangeMap, sortResult, addNum, usedDetailId);
            addNum += insertEcsChangeOrigin(startCity, ecsChangeMap, sortResult, addNum, usedDetailId);
            addNum += insertReturnEcs(endCity, returnEcsMap, sortResult, addNum, usedDetailId, noInercityEcsList);
            addNum -= i;
            i += addNum;
        }

        for (EcsExpenseVO vo : sortResult) {
            //使用过的票的费用明细id
            //外面过滤行程外匹配的票使用
            removeEmpName(vo, tripEmpName);
            usedDetailId.add(vo.getDetailId());
            //行程内的票都表上行程id，前端点击行程节点筛选数据使用
            vo.setTripId(tripId);
        }
        return sortResult;
    }

    private String getMountTripKey(String startTime, String finishTime, String empCode) {
        String key = String.format("%s#%s#%s", TravelTripProcessor.transDate(startTime), TravelTripProcessor.transDate(finishTime), empCode);
        return MD5.create().digestHex(key);
    }

    private String getAdjustTime(EcsExpenseVO ecsExpenseVO, int hours) {
        String sortDate = ecsExpenseVO.getSortDate();
        return LocalDateTime.parse(sortDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")).plusHours(hours).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

    private String getAdjustFinishTime(EcsExpenseVO ecsExpenseVO, int hours) {
        String sortDate = ecsExpenseVO.getFinishDate();
        return LocalDateTime.parse(sortDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")).plusHours(hours).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

    private void sortLastTraffic(List<EcsExpenseVO> sortResult) {
        EcsExpenseVO vo = sortResult.get(sortResult.size() - 1);
        if (!Objects.equals(vo.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021101)
                && !Objects.equals(vo.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021110)){
            for (int i = sortResult.size()-1 ; i >0; i--) {
                EcsExpenseVO vo1 = sortResult.get(i);
                if (Objects.equals(vo1.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021101) || Objects.equals(vo1.getExpenseTypeCode(), PcxConstant.TRAVEL_DETAIL_3021110)){
                    sortResult.remove(i);
                    sortResult.add(vo1);
                    break;
                }
            }
        }
    }

    private boolean isSingleShow(String expenseTypeCode){
        return Objects.equals(PcxConstant.TRAVEL_DETAIL_3021111, expenseTypeCode)
                || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021112, expenseTypeCode);
    }

    private String getArriveCityCode(EcsExpenseVO endCity, String lastDetailId) {
        if (Objects.equals(endCity.getDetailId(), lastDetailId)){
            return endCity.getEndCityCode();
        }
        return "";
    }

    private void removeEmpName(EcsExpenseVO vo, String tripEmpName) {
        if (StringUtil.isNotEmpty(vo.getEmpName()) && vo.getEmpName().equals(tripEmpName)){
            vo.setEmpName("");
        }
    }

    private String getSortEndTime(EcsExpenseVO endCity) {
        if (PcxConstant.TRAVEL_DETAIL_3021102.equals(endCity.getExpenseTypeCode())){
            return TravelTripProcessor.transDate(endCity.getFinishTime());
        }else{
            return TravelTripProcessor.transDate(endCity.getSortDate());
        }
    }

    private int insertEcsChangeOrigin(EcsExpenseVO ecsExpenseVO, Map<String, EcsExpenseVO> ecsChangeMap,
                                      List<EcsExpenseVO> sortResult, int addNum, Set<String> usedDetailId) {
        int num = 0;
        EcsExpenseVO ecsChangeOrigin = ecsChangeMap.get(ecsExpenseVO.getDetailId());
        if (Objects.nonNull(ecsChangeOrigin)){
            if (!usedDetailId.contains(ecsChangeOrigin.getDetailId())){
                sortResult.add(addNum, ecsChangeOrigin);
                num ++;
                usedDetailId.add(ecsChangeOrigin.getDetailId());
            }
        }
        return num;
    }

    private int insertReturnEcs(EcsExpenseVO ecsExpenseVO,
                                Map<String, List<EcsExpenseVO>> returnEcsMap,
                                List<EcsExpenseVO> sortResult,
                                int addNum,
                                Set<String> usedDetailId,
                                List<EcsExpenseVO> noInercityEcsList) {
        int num = 0;
        if (isIntercityTrafficVO(ecsExpenseVO)){
            String returnEcsKey = returnEcsKey(ecsExpenseVO);
            List<EcsExpenseVO> returnList = returnEcsMap.get(returnEcsKey);
            if (CollectionUtils.isNotEmpty(returnList)){
                for (EcsExpenseVO vo : returnList) {
                    if (isInOneWeek(vo.getStartTime(), ecsExpenseVO.getStartTime())
                        && !usedDetailId.contains(vo.getDetailId())){
                        sortResult.add(addNum ++, vo);
                        num ++;
                        usedDetailId.add(vo.getDetailId());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(noInercityEcsList)){
                for (EcsExpenseVO vo : noInercityEcsList) {
                    //如果是不完全匹配的退票，则人员+出发地点相同就认为是该行程的退票
                    if (Objects.equals(vo.getEmpCode(), ecsExpenseVO.getEmpCode())
                            && Objects.equals(vo.getStartCityCode(), ecsExpenseVO.getStartCityCode())
                             && isInOneWeek(vo.getStartTime(), ecsExpenseVO.getStartTime())){
                        if (!usedDetailId.contains(vo.getDetailId())){
                            sortResult.add(addNum ++, vo);
                            num ++;
                            usedDetailId.add(vo.getDetailId());
                        }
                    }
                }
            }
        }
        return num;
    }

    private String returnEcsKey(EcsExpenseVO ecsExpenseVO){
        return String.format("%s-%s-%s", ecsExpenseVO.getEmpCode(), ecsExpenseVO.getStartCityCode(), ecsExpenseVO.getEndCityCode());
    }

    /**
     * 判断是否在两周内
     * @param baseTime
     * @param compareTime
     * @return
     */
    private boolean isInOneWeek(String baseTime, String compareTime){
        if (StringUtil.isEmpty(baseTime) || StringUtil.isEmpty(compareTime)){
            return false;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate baseDate = LocalDate.parse(baseTime, formatter);
            LocalDate compareDate = LocalDate.parse(compareTime, formatter);

            // 计算两个日期之间的天数差
            long daysBetween = Math.abs(java.time.temporal.ChronoUnit.DAYS.between(baseDate, compareDate));

            // 如果相差天数小于等于 7 天，则视为在两周内
            return daysBetween <= 7;
        } catch (Exception e) {
            // 解析失败，认为不在两周内
            return false;
        }

    }

    /**
     * 把费用明细转换成EcsExpenseVO给理票页面展示
     */
    private List<EcsExpenseVO> transAllEcsExpList(List<PcxBillExpDetailBase> noSubsidyDetailList,
                                                  List<PcxBillExpDetailBase> subsidyDetailList,
                                                  List<PcxBillExpStandResult> standResultList,
                                                  List<PcxBillExpAttachRel> attachRelList,
                                                  Map<String, String> expTypeCodeNameMap,
                                                  Map<String, EcsMsgDTO> ecsMsgMap,
                                                  Map<String, String> tripEmpNameMap,
                                                  Map<String, PcxExpDetailEcsRel> detailEcsRelMap,
                                                  Map<String, BigDecimal> taxiInputAmtMap,
                                                  Map<String, BigDecimal> taxiCheckAmtMap,
                                                  Map<String, List<String>> taxiDetailIdMap ) {

        List<EcsExpenseVO> result = new ArrayList<>();
        //明细的支出标准
        Map<String, PcxBillExpStandResult> standResultMap = standResultList.stream()
                .collect(Collectors.toMap(this::getStandResultKeyByEcsThenDetail,
         Function.identity(), (key1, key2) -> key1));
        //明细或票的附件
        //如果是票relId是ecsBillId
        //如果是住宿费relId是hotelGroup
        //如果是其他明细relId是detail的id
        Map<String, List<PcxBillExpAttachRel>> attachRelMap = attachRelList.stream()
                .collect(Collectors.groupingBy(PcxBillExpAttachRel::getRelId));

        Set<String> hotelGroupSet = new HashSet<>();
        Map<String, List<String>> hotelGroupDetailMap = new HashMap<>();
        for (PcxBillExpDetailBase detailBase : noSubsidyDetailList) {
            if (Objects.equals(detailBase.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)){
                PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) detailBase;
                String hotelGroupSeq = getHotelGroupSeq(hotel);
                List<String> detailList = hotelGroupDetailMap.computeIfAbsent(hotelGroupSeq, k -> new ArrayList<>());
                //记录一下展示的这个住宿费的所有明细id
                detailList.add(detailBase.getId());
            }
        }

        List<PcxBillExpDetailBase> allDetailList = new ArrayList<>(noSubsidyDetailList);
        //补助也生成vo方便后面放到行程中，行程节点收集补助费用明细使用，在整理行程节点时，会把补助的vo删除掉
        allDetailList.addAll(subsidyDetailList);

        for (PcxBillExpDetailBase detailBase : allDetailList) {
            if (Objects.equals(detailBase.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)){
                //多人住宿费只展示一条
                PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) detailBase;
                String hotelGroupSeq = getHotelGroupSeq(hotel);
                if (hotelGroupSet.contains(hotelGroupSeq)){
                    continue;
                }
                hotelGroupSet.add(hotelGroupSeq);
            }
            EcsExpenseVO ecsExpenseVO = getDetailExpenseVo(detailBase, detailEcsRelMap,
                    expTypeCodeNameMap, attachRelMap, standResultMap, ecsMsgMap, taxiInputAmtMap,
                    tripEmpNameMap, taxiDetailIdMap, hotelGroupDetailMap, taxiCheckAmtMap);
            if (null != ecsExpenseVO) {
                result.add(ecsExpenseVO);
            }
        }
        return getEcsExpenseVOS(result);
    }

    /**
     *
     * 显示票据后置处理，如果一张票内存在多个明细，会生成多条展示数据，将这些数据合并成一条数据,用于界面展示
     * @param result
     * @return
     */
    private List<EcsExpenseVO> getEcsExpenseVOS(List<EcsExpenseVO> result) {
        List<EcsExpenseVO> resultCollect = new ArrayList<>();

        List<EcsExpenseVO> noEcsCollect = result.stream().filter(e -> StringUtil.isEmpty(e.getEcsBillId())).collect(Collectors.toList());
        resultCollect.addAll(noEcsCollect);
        Map<String, List<EcsExpenseVO>> collect = result.stream().filter(e -> StringUtil.isNotEmpty(e.getEcsBillId())).collect(Collectors.groupingBy(EcsExpenseVO::getEcsBillId));
        for (Map.Entry<String, List<EcsExpenseVO>> entry : collect.entrySet()) {
            List<EcsExpenseVO> list = entry.getValue();
            if (list.size() > 1) {
                EcsExpenseVO ecsExpenseVO = list.get(0);
                ecsExpenseVO.setTicketAmt(list.stream().map(EcsExpenseVO::getTicketAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                ecsExpenseVO.setInputAmt(list.stream().map(EcsExpenseVO::getInputAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                ecsExpenseVO.setCheckAmt(list.stream().map(EcsExpenseVO::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                resultCollect.add(ecsExpenseVO);
            }else{
                resultCollect.add(list.get(0));
            }
        }
        return resultCollect;
    }

    private String getStandResultKeyByEcsThenDetail(PcxBillExpStandResult standResult){
        if (StringUtil.isNotEmpty(standResult.getEcsBillId())){
            return standResult.getEcsBillId();
        } else if (StringUtil.isNotEmpty(standResult.getDetailId())) {
            return standResult.getDetailId();
        }else {
            return standResult.getExpenseId();
        }
    }

    private String getHotelGroupSeq(PcxBillExpDetailTravel hotel){
        return String.format("%s-%d", hotel.getHotelGroup(), hotel.getHotelSeq());
    }

    private String getDetailStandResultKey(PcxBillExpDetailBase detailBase){
        if (Objects.equals(detailBase.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)){
            PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) detailBase;
            return hotel.getHotelGroup();
        }
        return detailBase.getId();
    }

    /**
     * 费用明细转换票
     * 包括ecs解析的费用明细
     * 手动添加的费用明细
     */
    private EcsExpenseVO getDetailExpenseVo(PcxBillExpDetailBase detailBase,
                                            Map<String, PcxExpDetailEcsRel> detailEcsRelMap,
                                            Map<String, String> expTypeCodeNameMap,
                                            Map<String, List<PcxBillExpAttachRel>> attachRelMap,
                                            Map<String, PcxBillExpStandResult> standResultMap,
                                            Map<String, EcsMsgDTO> ecsMsgMap,
                                            Map<String, BigDecimal> taxiInputAmtMap,
                                            Map<String, String> tripEmpNameMap,
                                            Map<String, List<String>> taxiDetailIdMap,
                                            Map<String, List<String>> hotelGroupDetailMap,
                                            Map<String, BigDecimal> taxiCheckAmtMap) {
        String ecsBillId = "";
        String ecsBillType = "";
        String ecsBillDate = "";
        String ecsBillNo = "";
        String itemName = "";
        //明细时ecs票生成的可以找到票信息
        EcsMsgDTO ecsMsgDTO = null;
        Set<String> detailIds = new HashSet<>();
        //TODO 兼容字段不同赋值
        if(StringUtil.isEmpty(detailBase.getSource())){
           if(detailBase instanceof PcxBillExpDetailMeeting) {
               PcxBillExpDetailMeeting detailMeeting = (PcxBillExpDetailMeeting) detailBase;
               detailBase.setSource(detailMeeting.getExpSource());
           }else if(detailBase instanceof PcxBillExpDetailTraining) {
               PcxBillExpDetailTraining training = (PcxBillExpDetailTraining) detailBase;
               detailBase.setSource(training.getExpSource());
           }else if(detailBase instanceof PcxBillExpDetailInlandfee){
               PcxBillExpDetailInlandfee inlandfee = (PcxBillExpDetailInlandfee) detailBase;
               detailBase.setSource(inlandfee.getExpSource());
           }
        }
        if (Objects.equals(detailBase.getSource(), BillExpDetailSourceEnum.ECS.getCode())
                //正常的会有票，但是有个情况，前端删除票后页面没刷新就进行了保存，把历史的费用明细有给加上了，这时候就没有票信息了，避免出错，加个判断
                && detailEcsRelMap.containsKey(detailBase.getId())) {
            PcxExpDetailEcsRel rel = detailEcsRelMap.get(detailBase.getId());
            ecsBillId = rel.getEcsBillId();
            ecsBillType = rel.getEcsBillType();
            ecsBillDate = rel.getEcsBillDate();
            ecsBillNo = rel.getEcsBillNo();
            itemName = rel.getEcsBillDesc();
            ecsMsgDTO = ecsMsgMap.get(rel.getEcsBillId());
            List<String> ids = taxiDetailIdMap.get(rel.getEcsBillId());
            if (CollectionUtils.isNotEmpty(ids)) {
                detailIds.addAll(ids);
            }
        }

        String standResultKey = getDetailStandResultKey(detailBase);
        PcxBillExpStandResult standResult = standResultMap.get(standResultKey);
        EcsExpenseVO trafficVO = new EcsExpenseVO();

        if (detailBase instanceof PcxBillExpDetailTravel) {
            PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detailBase;
            ecsBillDate = getEcsBillDate(ecsBillDate, travel);
            trafficVO.setBrotherDetails(detailIds);
            switch (travel.getExpDetailCode()) {
                case PcxConstant.TRAVEL_DETAIL_3021101:
                case PcxConstant.TRAVEL_DETAIL_3021102:
                case PcxConstant.TRAVEL_DETAIL_3021106:
                case PcxConstant.TRAVEL_DETAIL_3021110:
                case PcxConstant.TRAVEL_DETAIL_3021112:
                    trafficVO.setExpenseId(travel.getExpenseId());
                    trafficVO.setDetailId(travel.getId());
                    trafficVO.setEcsBillId(ecsBillId);
                    trafficVO.setEcsBillType(ecsBillType);
                    trafficVO.setEcsBillDate(ecsBillDate);
                    trafficVO.setSortDate(travel.getStartTime());
                    trafficVO.setFinishDate(travel.getFinishTime());
                    trafficVO.setEcsBillNo(ecsBillNo);
                    trafficVO.setStartTime(TravelTripProcessor.transDate(travel.getStartTime()));
                    trafficVO.setFinishTime(TravelTripProcessor.transDate(travel.getFinishTime()));
                    trafficVO.setTicketAmt(travel.getEcsAmt());
                    trafficVO.setInputAmt(getInputAmt(travel, taxiInputAmtMap, ecsBillId));
                    trafficVO.setCheckAmt(getCheckAmt(travel, taxiCheckAmtMap, ecsBillId));
                    trafficVO.setStartPlace(StringUtil.isEmpty(travel.getStartPlace()) ? travel.getStartCity() : travel.getStartPlace());
                    trafficVO.setEndPlace(StringUtil.isEmpty(travel.getEndPlace()) ? travel.getEndCity() : travel.getEndPlace());
                    trafficVO.setInputSeatLevel(travel.getInputSeatLevel());
                    trafficVO.setEmpCode(travel.getEmpCode());
                    trafficVO.setEmpName(getEmpName(travel, tripEmpNameMap));
                    trafficVO.setEmpTempName(travel.getEmpName());
                    trafficVO.setExpenseTypeCode(travel.getExpDetailCode());
                    trafficVO.setExpenseTypeName(expTypeCodeNameMap.getOrDefault(travel.getExpDetailCode(), ""));
                    trafficVO.setTripId(travel.getTripId());
                    trafficVO.setTripSeq(travel.getTripSeq());
                    trafficVO.setNoEcsReason(travel.getNoEcsReason());
                    trafficVO.setNoEcsReasonName(NoEcsReasonEnum.getDescByCode(travel.getExpDetailCode(), travel.getNoEcsReason()));
                    travel.setNoEcsReasonName(trafficVO.getNoEcsReasonName());
                    trafficVO.setSource(travel.getSource());
                    trafficVO.setCityCode(PcxConstant.TRAVEL_DETAIL_3021101.equals(travel.getExpDetailCode()) ? travel.getStartCityCode() : travel.getEndCityCode());
                    trafficVO.setCityName(PcxConstant.TRAVEL_DETAIL_3021101.equals(travel.getExpDetailCode()) ? travel.getStartCity() : travel.getEndCity());
                    trafficVO.setStartCityCode(travel.getStartCityCode());
                    trafficVO.setStartCity(travel.getStartCity());
                    trafficVO.setEndCityCode(travel.getEndCityCode());
                    trafficVO.setEndCity(travel.getEndCity());
                    trafficVO.setCarType(travel.getCarType());
                    trafficVO.setMileage(travel.getMileage());
                    trafficVO.setMountTrip(travel.getMountTrip());
                    //被改签的明细记录改签的明细id
                    trafficVO.setChangeDetailId(travel.getOriginId());
                    trafficVO.setTripFlag(travel.getTripFlag());
                    if (Objects.nonNull(standResult)) {
                        trafficVO.setStandResult(standResult);
                    }
                    fillManualDetail(travel, trafficVO);
                    fillReplenishDetailJson(trafficVO, travel);
                    fillAttachRel(ecsBillId, travel, attachRelMap, trafficVO);
                    fillHotelPerson(travel, trafficVO);
                    trafficVO.setTransNo(getTransNo(travel, ecsBillDate));
                    trafficVO.setRemark(getRemark(travel, trafficVO.getEmpTempName(), tripEmpNameMap));
                    if (Objects.equals(travel.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021102)) {
                        String hotelGroupSeq = getHotelGroupSeq(travel);
                        List<String> ids = hotelGroupDetailMap.get(hotelGroupSeq);
                        if (CollectionUtils.isNotEmpty(ids)) {
                            detailIds.addAll(ids);
                        }
                    }
                    break;
                default:
                    trafficVO.setExpenseId(detailBase.getExpenseId());
                    trafficVO.setExpenseTypeCode(detailBase.getExpDetailCode());
                    trafficVO.setExpenseTypeName(expTypeCodeNameMap.getOrDefault(detailBase.getExpDetailCode(), ""));
                    trafficVO.setDetailId(detailBase.getId());
                    trafficVO.setEcsBillId(ecsBillId);
                    trafficVO.setEcsBillType(ecsBillType);
                    trafficVO.setEcsBillDate(ecsBillDate);
                    trafficVO.setSortDate(getOtherSortDate(travel, ecsBillDate));
                    trafficVO.setFinishDate(travel.getFinishTime());
                    trafficVO.setStartTime(TravelTripProcessor.transDate(travel.getStartTime()));
                    trafficVO.setFinishTime(TravelTripProcessor.transDate(travel.getFinishTime()));
                    trafficVO.setEcsBillNo(ecsBillNo);
                    trafficVO.setTicketAmt(detailBase.getEcsAmt());
                    trafficVO.setInputAmt(getInputAmt(detailBase, taxiInputAmtMap, ecsBillId));
                    trafficVO.setCheckAmt(getCheckAmt(detailBase, taxiCheckAmtMap, ecsBillId));
                    trafficVO.setCityCode(travel.getEndCityCode());
                    trafficVO.setCityName(travel.getEndCity());
                    trafficVO.setEmpName(getEmpName(travel, tripEmpNameMap));
                    trafficVO.setEmpCode(detailBase.getEmpCode());
                    trafficVO.setSource(detailBase.getSource());
                    trafficVO.setNoEcsReason(detailBase.getNoEcsReason());
                    trafficVO.setNoEcsReasonName(NoEcsReasonEnum.getDescByCode(detailBase.getExpDetailCode(), detailBase.getNoEcsReason()));
                    trafficVO.setMountTrip(travel.getMountTrip());
                    detailBase.setNoEcsReasonName(trafficVO.getNoEcsReasonName());
                    if (Objects.nonNull(standResult)) {
                        trafficVO.setStandResult(standResult);
                    }
                    fillManualDetail(detailBase, trafficVO);
                    fillAttachRel(ecsBillId, detailBase, attachRelMap, trafficVO);
                    trafficVO.setTransNo(TravelTripProcessor.transDate(trafficVO.getSortDate()));
            }
            String bizTypeName = "";
            if (Objects.nonNull(ecsMsgDTO)) {
                bizTypeName = ecsMsgDTO.getBizTypeName();
                trafficVO.setIsValid(ecsMsgDTO.getIsValid());
                trafficVO.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
                trafficVO.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
                trafficVO.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
                trafficVO.setDisplayBizTypeName(EcsItemNameHelper.getDisplayBizTypeName(bizTypeName));
                trafficVO.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
                trafficVO.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
                trafficVO.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
            }
            trafficVO.setItemName(disposeItemName(bizTypeName, (PcxBillExpDetailTravel) detailBase, ecsBillType, expTypeCodeNameMap));
            return trafficVO;

        }else {
            //TODO 非差旅费用逻辑处理，如果匹配不到票则不转成票明细
            trafficVO.setExpenseId(detailBase.getExpenseId());
            trafficVO.setExpenseTypeCode(detailBase.getExpDetailCode());
            trafficVO.setExpenseTypeName(expTypeCodeNameMap.getOrDefault(detailBase.getExpDetailCode(), ""));
            trafficVO.setDetailId(detailBase.getId());
            trafficVO.setEcsBillId(ecsBillId);
            trafficVO.setEcsBillType(ecsBillType);
            trafficVO.setEcsBillDate(ecsBillDate);
            trafficVO.setEcsBillNo(ecsBillNo);
            //项目名称
            trafficVO.setItemName(itemName);
            //发票说明
            trafficVO.setEcsBillDesc(itemName);
            trafficVO.setTicketAmt(detailBase.getEcsAmt());
            trafficVO.setInputAmt(getInputAmt(detailBase, taxiInputAmtMap, ecsBillId));
            trafficVO.setCheckAmt(getCheckAmt(detailBase, taxiCheckAmtMap, ecsBillId));
            trafficVO.setEmpCode(detailBase.getEmpCode());
            trafficVO.setSource(detailBase.getSource());
            trafficVO.setNoEcsReason(detailBase.getNoEcsReason());
            trafficVO.setNoEcsReasonName(NoEcsReasonEnum.getDescByCode(detailBase.getExpDetailCode(), detailBase.getNoEcsReason()));
            detailBase.setNoEcsReasonName(trafficVO.getNoEcsReasonName());
            if (Objects.nonNull(standResult)) {
                trafficVO.setStandResult(standResult);
            }
            fillManualDetail(detailBase, trafficVO);
            fillAttachRel(ecsBillId, detailBase, attachRelMap, trafficVO);
            trafficVO.setTransNo(TravelTripProcessor.transDate(trafficVO.getSortDate()));
            String bizTypeName = "";

            if (Objects.equals(detailBase.getSource(), BillExpDetailSourceEnum.MANUAL.getCode())) {
                return trafficVO;
            }
            if (Objects.isNull(ecsMsgDTO)) {
                return null;
            }
            bizTypeName = ecsMsgDTO.getBizTypeName();
            trafficVO.setIsValid(ecsMsgDTO.getIsValid());
            trafficVO.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
            trafficVO.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
            trafficVO.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
            trafficVO.setDisplayBizTypeName(EcsItemNameHelper.getDisplayBizTypeName(bizTypeName));
            trafficVO.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
            trafficVO.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
            trafficVO.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
            return trafficVO;
        }
    }

    private String getOtherSortDate(PcxBillExpDetailTravel travel, String ecsBillDate) {
        return StringUtil.isNotEmpty(travel.getStartTime()) ? travel.getStartTime() : StringUtil.isNotEmpty(travel.getFinishTime()) ? travel.getFinishTime() : ecsBillDate;
    }

    private String getOtherFinishDate(PcxBillExpDetailTravel travel, String ecsBillDate) {
        return StringUtil.isNotEmpty(travel.getFinishTime()) ? travel.getFinishTime() : StringUtil.isNotEmpty(travel.getStartTime()) ? travel.getStartTime() : ecsBillDate;
    }

    private String getEmpName(PcxBillExpDetailTravel travel, Map<String, String> tripEmpNameMap) {
        if (StringUtil.isNotEmpty(travel.getTripId())){
            if (travel.getEmpName().equals(tripEmpNameMap.get(travel.getTripId()))){
                return "";
            }
        }
        return travel.getEmpName();
    }

    private BigDecimal getInputAmt(PcxBillExpDetailBase travel, Map<String, BigDecimal> taxiInputAmtMap, String ecsBillId) {
        if (isSingleShow(travel.getExpDetailCode()) && StringUtil.isNotEmpty(ecsBillId)){
            return taxiInputAmtMap.getOrDefault(ecsBillId, travel.getInputAmt());
        }
        return travel.getInputAmt();
    }

    private BigDecimal getCheckAmt(PcxBillExpDetailBase travel, Map<String, BigDecimal> taxiCheckAmtMap, String ecsBillId) {
        if (isSingleShow(travel.getExpDetailCode()) && StringUtil.isNotEmpty(ecsBillId)){
            return taxiCheckAmtMap.getOrDefault(ecsBillId, travel.getCheckAmt());
        }
        return travel.getCheckAmt();
    }

    private String getRemark(PcxBillExpDetailTravel travel, String empTempName, Map<String, String> tripEmpNameMap) {
        if (BillExpDetailSourceEnum.REPLENISH.getCode().equals(travel.getSource())){
            if(empTempName.equals(tripEmpNameMap.get(travel.getTripId()))){
                return travel.getRemark();
            }
            return String.format("%s | %s", empTempName, travel.getRemark());
        }
        return "";
    }

    private String getEcsBillDate(String ecsBillDate, PcxBillExpDetailTravel travel) {
        if (StringUtil.isNotEmpty(ecsBillDate)){
            return ecsBillDate;
        }
        if (StringUtil.isNotEmpty(travel.getStartTime())){
            return travel.getStartTime().substring(0,10);
        }
        if (StringUtil.isNotEmpty(travel.getFinishTime())){
            return travel.getFinishTime().substring(0,10);
        }
        return "";
    }

    private String disposeItemName(String bizTypeName, PcxBillExpDetailTravel travel, String ecsBillType, Map<String, String> expenseTypeMap) {
        return EcsItemNameHelper.disposeItemName(bizTypeName, travel, ecsBillType, expenseTypeMap);
    }

    private void fillReplenishDetailJson(EcsExpenseVO trafficVO, PcxBillExpDetailTravel travel) {
        if (BillExpDetailSourceEnum.REPLENISH.getCode().equals(travel.getSource())){
            trafficVO.setDetailJson(JSON.toJSONString(travel));
        }
    }

    private String getTransNo(PcxBillExpDetailTravel travel, String ecsBillDate) {
        List<String> list = new ArrayList<>();
        if (BillExpDetailSourceEnum.REPLENISH.getCode().equals(travel.getSource())){
            if (PcxConstant.TRAVEL_DETAIL_3021102.equals(travel.getExpDetailCode())){
                list.add(travel.getStartTime().substring(5) + "至" + travel.getFinishTime().substring(5));
            }else{
                if (StringUtil.isNotEmpty(travel.getStartTime())){
                    list.add(travel.getStartTime().substring(0,10));
                }
            }
        }else{
            if (PcxConstant.TRAVEL_DETAIL_3021102.equals(travel.getExpDetailCode())){
                list.add(travel.getStartTime() + "至" + travel.getFinishTime());
                list.add(travel.getTravelDays()+"天");
            }else{
                if (StringUtil.isNotEmpty(travel.getStartTime())){
                    list.add(travel.getStartTime().substring(0,10));
                }
                if (StringUtil.isNotEmpty(travel.getInputSeatLevel())){
                    list.add(travel.getInputSeatLevel());
                }
            }
        }
        if (list.isEmpty()){
            list.add(ecsBillDate);
        }
        return String.join(" | ", list);
    }


    private List<PcxBillExpAttachRel> getAttachRelList(PcxBill view, Predicate<PcxBillExpAttachRel> filter) {
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, view.getId()));
        return attachRelList.stream().filter(filter).collect(Collectors.toList());
    }

    private Map<String, String> getExpTypeNameMap(List<String> allExpTypeCodeList, PcxBill view) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(allExpTypeCodeList)){
            return result;
        }
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setFiscal(view.getFiscal());
        qo.setMofDivCode(view.getMofDivCode());
        qo.setAgyCode(view.getAgyCode());
        qo.setExpTypeCodes(new ArrayList<>(allExpTypeCodeList));
        List<PcxBasExpType> itemExps = basExpTypeDao.selectSimpleList(qo);
        Map<String, String> expTypeMap = itemExps.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, PcxBasExpType::getExpenseName));
        result.putAll(expTypeMap);
        return result;
    }

    private List<PcxBillExpStandResult> getBillStandResult(PcxBill view) {
        return pcxBillExpStandResultDao.selectListByBillId(view.getId());
    }

    private void fillManualDetail(PcxBillExpDetailBase travel, EcsExpenseVO trafficVO){
        if (BillExpDetailSourceEnum.MANUAL.getCode().equals(travel.getSource())){

            if (isHotelDetail(travel)){
                PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) travel;
                trafficVO.setDetailJson(JSON.toJSONString(hotel));
            }else{
                trafficVO.setDetailJson(JSON.toJSONString(travel));
            }
        }
    }

    private void fillHotelPerson(PcxBillExpDetailBase travel, EcsExpenseVO trafficVO){
        PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) travel;
        if (Objects.nonNull(hotel) && CollectionUtils.isNotEmpty(hotel.getEmpInfoList())){
            String empCodes = hotel.getEmpInfoList().stream().map(EmpInfo::getEmpCode).collect(Collectors.joining(","));
            String empNames = hotel.getEmpInfoList().stream().map(EmpInfo::getEmpName).collect(Collectors.joining(","));
            trafficVO.setEmpCode(empCodes);
            trafficVO.setEmpName(empNames);
        }
    }

    private void fillAttachRel(String ecsBillId, PcxBillExpDetailBase travel,
                                              Map<String, List<PcxBillExpAttachRel>> detailAttachMap,
                                              EcsExpenseVO trafficVO) {
        List<PcxBillExpAttachRel> attachRelList;
        if (StringUtil.isNotEmpty(ecsBillId)){
            attachRelList = detailAttachMap.get(ecsBillId);
        }else if (PcxConstant.TRAVEL_DETAIL_3021102.equals(travel.getExpDetailCode())){
            PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) travel;
            attachRelList = detailAttachMap.get(hotel.getHotelGroup());
        }else{
            attachRelList = detailAttachMap.get(travel.getId());
        }
        if (CollectionUtils.isNotEmpty(attachRelList)){
            //手动加票的把附件列表带上
            if (travel.getSource().equals(BillExpDetailSourceEnum.MANUAL.getCode())){
                List<PcxAttachRelVO> attachRelVOS = new ArrayList<>();
                for (PcxBillExpAttachRel attachRel : attachRelList) {
                    attachRelVOS.add(new PcxAttachRelVO(attachRel.getAttachId(), attachRel.getFileName()));
                }
                trafficVO.setAttachRelList(attachRelVOS);
            }
        }
    }

    private void fillSubsidyView(List<PcxBillExpDetailTravel> subsidyList, PcxBill view, EcsExpMatchVO vo, List<PcxBillExpDetailTravel> allTrafficList) {

        if (CollectionUtils.isEmpty(subsidyList)){
            return;
        }
        Map<String, List<PcxBillExpDetailTravel>> empCityMap = allTrafficList.stream().collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEmpCode));
        //处理3021103和3021104的补助信息，按人员分组，进行整理，人员所在城市分组天数，金额
        Map<String, List<PcxBillExpDetailTravel>> empMap = subsidyList.stream()
                .filter(this::isSubsidyDetail)
                .collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEmpCode));

        List<SubsidyVO> list = new ArrayList<>();
        PcxBasExpTypeQO query = new PcxBasExpTypeQO();
        query.setFiscal(view.getFiscal());
        query.setMofDivCode(view.getMofDivCode());
        query.setAgyCode(view.getAgyCode());
        query.setExpTypeCodes(Arrays.asList(PcxConstant.TRAVEL_DETAIL_3021103, PcxConstant.TRAVEL_DETAIL_3021104));
        List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(query);
        Map<String, String> expNameMap = expTypeList.stream()
                .collect(Collectors.toMap(PcxBasExpType::getExpenseCode, PcxBasExpType::getExpenseName, (key1, key2) -> key1));
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : empMap.entrySet()) {
            PcxBillExpDetailTravel empTravel = entry.getValue().get(0);
            List<PcxBillExpDetailTravel> travels = empCityMap.get(empTravel.getEmpCode());
            travels.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
            Map<String, Integer> citySortMap = new HashMap<>();
            int index = 0;
            for (PcxBillExpDetailTravel travel : travels) {
                if (!citySortMap.containsKey(travel.getEndCityCode())){
                    citySortMap.put(travel.getEndCityCode(), index ++);
                }
            }
            SubsidyVO so = new SubsidyVO();
            so.setEmpCode(empTravel.getEmpCode());
            so.setEmpName(empTravel.getEmpName());
            so.setSubsidyItems(fillSubsidyItem(entry.getValue(), expNameMap, citySortMap));
            BigDecimal checkAmt = BigDecimal.ZERO;
            BigDecimal inputAmt = BigDecimal.ZERO;
            for (SubsidyVO.SubsidyItem subsidyItem : so.getSubsidyItems()) {
                checkAmt = checkAmt.add(subsidyItem.getCheckAmt());
                inputAmt = inputAmt.add(subsidyItem.getInputAmt());
            }
            so.setTotalAmt(checkAmt);
            so.setCheckAmt(checkAmt);
            so.setInputAmt(inputAmt);
            list.add(so);
        }
        vo.setSubsidyList(list);
    }

    private List<SubsidyVO.SubsidyItem> fillSubsidyItem(List<PcxBillExpDetailTravel> detailTravelList,
                                                        Map<String, String> expNameMap,
                                                        Map<String, Integer> citySortMap) {
        Map<String, List<PcxBillExpDetailTravel>> expTypeMap = detailTravelList.stream().collect(Collectors.groupingBy(PcxBillExpDetailBase::getExpDetailCode));
        List<SubsidyVO.SubsidyItem> itemList = new ArrayList<>();
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : expTypeMap.entrySet()) {
            PcxBillExpDetailTravel detailTravel = entry.getValue().get(0);
            SubsidyVO.SubsidyItem item = new SubsidyVO.SubsidyItem();
            item.setTypeCode(detailTravel.getExpDetailCode());
            item.setTypeName(expNameMap.get(detailTravel.getExpDetailCode()));
            itemList.add(item);
            Map<String, List<PcxBillExpDetailTravel>> cityMap = entry.getValue().stream().collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEndCityCode));
            List<SubsidyVO.SubsidyDetail> detailList = new ArrayList<>();
            BigDecimal totalInputAmt = BigDecimal.ZERO;
            BigDecimal totalCheckAmt = BigDecimal.ZERO;
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> listEntry : cityMap.entrySet()) {
                PcxBillExpDetailTravel value = listEntry.getValue().get(0);
                SubsidyVO.SubsidyDetail detail = new SubsidyVO.SubsidyDetail();

                if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021103, entry.getKey())){
                    disposeSubsidyAmtAndDay(detail, listEntry.getValue());
                } else if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021104, entry.getKey())) {
                    disposeSubsidyAmtAndDay3021104(detail, listEntry.getValue());
                }

                detail.setCity(value.getEndCity());
                detail.setCityCode(value.getEndCityCode());
                BigDecimal checkAmt = BigDecimal.ZERO;
                BigDecimal inputAmt = BigDecimal.ZERO;
                for (PcxBillExpDetailTravel travel : listEntry.getValue()) {
                    checkAmt = checkAmt.add(travel.getCheckAmt());
                    inputAmt = inputAmt.add(travel.getInputAmt());
                }
                detail.setTotalAmt(checkAmt);
                detail.setInputAmt(inputAmt);
                detail.setCheckAmt(checkAmt);
                totalInputAmt = totalInputAmt.add(inputAmt);
                totalCheckAmt = totalCheckAmt.add(checkAmt);
                detailList.add(detail);
            }
            int defaultIndex = citySortMap.keySet().size();
            detailList.sort((o1, o2) -> {
                int o1Index = citySortMap.getOrDefault(o1.getCityCode(), defaultIndex);
                int o2Index = citySortMap.getOrDefault(o2.getCityCode(), defaultIndex);
                return o1Index - o2Index;
            });


            List<SubsidyVO.ExtraDetail> extraList = new ArrayList<>();
            item.setExtraList(extraList);
            disposeExtraSubsidy(extraList, entry, detailTravel);

            item.setDetailList(detailList);
            item.setCheckAmt(totalCheckAmt);
            item.setTotalAmt(totalCheckAmt);
            item.setInputAmt(totalInputAmt);
        }
        return itemList;
    }

    /**
     * 处理人员的其他补助信息
     * @param extraList
     * @param entry
     * @param detailTravel
     */
    private void disposeExtraSubsidy(List<SubsidyVO.ExtraDetail> extraList, Map.Entry<String, List<PcxBillExpDetailTravel>> entry, PcxBillExpDetailTravel detailTravel) {
        if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021103, entry.getKey())){
            List<ValsetDTO> valset = queryExtraConfig(detailTravel.getFiscal(), detailTravel.getAgyCode(), detailTravel.getMofDivCode());
            if (CollectionUtils.isNotEmpty(valset)){
                double extraWorkDays = 0;
                double managerDays = 0;
                BigDecimal extraWorkAmt = BigDecimal.ZERO;
                BigDecimal managerAmt = BigDecimal.ZERO;
                BigDecimal extraWorkUnit = BigDecimal.ZERO;
                BigDecimal managerUnit = BigDecimal.ZERO;
                for (PcxBillExpDetailTravel travel : entry.getValue()) {
                    extraWorkDays += travel.getWorkExtraDays();
                    managerDays += travel.getManagerDays();
                    extraWorkAmt = extraWorkAmt.add(travel.getWorkExtraAmt());
                    managerAmt = managerAmt.add(travel.getManagerAmt());
                }
                PcxBillExpDetailTravel oneDetail = entry.getValue().get(0);
                String subsidyExtra = oneDetail.getSubsidyExtra();
                if (StringUtil.isNotEmpty(subsidyExtra)){
                    SubsidyExtraDTO subsidyExtraDTO = JSON.parseObject(subsidyExtra, SubsidyExtraDTO.class);
                    extraWorkUnit = subsidyExtraDTO.getHolidaySubsidy();
                    managerUnit = subsidyExtraDTO.getManagerSubsidy();
                }
                for (ValsetDTO valsetDTO : valset) {
                    ExtraSubsidyTypeEnum enumByValsetCode = ExtraSubsidyTypeEnum.getEnumByValsetCode(valsetDTO.getValcode());
                    if (Objects.isNull(enumByValsetCode)){
                        continue;
                    }
                    SubsidyVO.ExtraDetail extraDetail = new SubsidyVO.ExtraDetail();
                    extraDetail.setExtraName(valsetDTO.getValName());
                    extraDetail.setDays(Objects.equals(ExtraSubsidyTypeEnum.EXTRA_WORK, enumByValsetCode) ? extraWorkDays : managerDays);
                    extraDetail.setAmt(Objects.equals(ExtraSubsidyTypeEnum.EXTRA_WORK, enumByValsetCode) ? extraWorkAmt : managerAmt);
                    if (StringUtil.isNotEmpty(subsidyExtra)){
                        if (Objects.equals(ExtraSubsidyTypeEnum.EXTRA_WORK, enumByValsetCode)){
                            extraDetail.setUnit(extraWorkUnit);
                        }else{
                            extraDetail.setUnit(managerUnit);
                        }
                    }else{
                        if (Objects.equals(ExtraSubsidyTypeEnum.EXTRA_WORK, enumByValsetCode)){
                            try {
                                extraWorkUnit = extraWorkAmt.divide(BigDecimal.valueOf(extraWorkDays), 2, RoundingMode.HALF_UP);
                            }catch (Exception e){

                            }
                            extraDetail.setUnit(extraWorkUnit);
                        }else{
                            try {
                                managerUnit = managerAmt.divide(BigDecimal.valueOf(managerDays), 2, RoundingMode.HALF_UP);
                            }catch (Exception e){

                            }
                            extraDetail.setUnit(managerUnit);
                        }
                    }

                    if (extraDetail.getDays()>0){
                        extraList.add(extraDetail);
                    }
                }
            }
        }
    }

    /**
     * 处理城市的补助明细，基础补助，住宿舍补助
     * @param detail
     * @param value
     */
    private void disposeSubsidyAmtAndDay(SubsidyVO.SubsidyDetail detail, List<PcxBillExpDetailTravel> value) {
        double travelDays = 0;
        double dormDays = 0;
        BigDecimal baseSubsidy = BigDecimal.ZERO;
        BigDecimal dormSubsidy = BigDecimal.ZERO;
        BigDecimal baseSubsidyUnit = BigDecimal.ZERO;
        BigDecimal dormSubsidyUnit = BigDecimal.ZERO;
        String travelDayType = PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY;
        String dormDayType = PcxConstant.SUBSIDY_DORM_DAY_TYPE_WORK_DORM_DAY;
        PcxBillExpDetailTravel detailTravel = value.get(0);
        if (StringUtil.isNotEmpty(detailTravel.getSubsidyExtra())){
            SubsidyExtraDTO subsidyExtraDTO = JSON.parseObject(detailTravel.getSubsidyExtra(), SubsidyExtraDTO.class);
            travelDayType = subsidyExtraDTO.getDayType();
            dormDayType = subsidyExtraDTO.getDormType();
            baseSubsidyUnit = subsidyExtraDTO.getBaseSubsidy();
            dormSubsidyUnit = subsidyExtraDTO.getDormSubsidy();
        }
        for (PcxBillExpDetailTravel item : value) {
            baseSubsidy = baseSubsidy.add(item.getBaseAmt());
            dormSubsidy = dormSubsidy.add(item.getDormAmt());
            if (Objects.equals(travelDayType, PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY)){
                travelDays += item.getWorkDays();
            }else{
                travelDays += item.getTravelDays();
            }
            if (Objects.equals(dormDayType, PcxConstant.SUBSIDY_DORM_DAY_TYPE_WORK_DORM_DAY)){
                dormDays += item.getWorkDormDays();
            }else{
                dormDays += item.getDormDays();
            }
        }
        if (StringUtil.isEmpty(detailTravel.getSubsidyExtra())){
            if (baseSubsidy.compareTo(BigDecimal.ZERO)>0 && travelDays>0){
                baseSubsidyUnit = baseSubsidy.divide(new BigDecimal(travelDays), 2, RoundingMode.HALF_UP);
            }
            if (dormSubsidy.compareTo(BigDecimal.ZERO)>0 && dormDays>0){
                dormSubsidyUnit = dormSubsidy.divide(new BigDecimal(dormDays), 2, RoundingMode.HALF_UP);
            }
        }
        List<SubsidyVO.SubsidyExplain> explains = new ArrayList<>();
        if (baseSubsidy.compareTo(BigDecimal.ZERO)>0){
            SubsidyVO.SubsidyExplain explain = new SubsidyVO.SubsidyExplain();
            explain.setName("出差天数");
            explain.setDays(travelDays);
            explain.setUnit(baseSubsidyUnit);
            explain.setAmt(baseSubsidy);
            explains.add(explain);
        }
        if (dormSubsidy.compareTo(BigDecimal.ZERO)>0){
            SubsidyVO.SubsidyExplain explain = new SubsidyVO.SubsidyExplain();
            explain.setName("住宿舍天数");
            explain.setDays(dormDays);
            explain.setUnit(dormSubsidyUnit);
            explain.setAmt(dormSubsidy);
            explains.add(explain);
        }
        detail.setExplainList(explains);
    }

    /**
     * 处理城市的补助明细，基础补助，住宿舍补助
     * @param detail
     * @param value
     */
    private void disposeSubsidyAmtAndDay3021104(SubsidyVO.SubsidyDetail detail, List<PcxBillExpDetailTravel> value) {
        double travelDays = 0;
        BigDecimal baseSubsidy = BigDecimal.ZERO;
        BigDecimal baseSubsidyUnit = BigDecimal.ZERO;
        String travelDayType = PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY;
        PcxBillExpDetailTravel detailTravel = value.get(0);
        if (StringUtil.isNotEmpty(detailTravel.getSubsidyExtra())){
            SubsidyExtraDTO subsidyExtraDTO = JSON.parseObject(detailTravel.getSubsidyExtra(), SubsidyExtraDTO.class);
            travelDayType = subsidyExtraDTO.getDayType();
            baseSubsidyUnit = subsidyExtraDTO.getBaseSubsidy();
        }
        for (PcxBillExpDetailTravel item : value) {
            baseSubsidy = baseSubsidy.add(item.getBaseAmt());
            if (Objects.equals(travelDayType, PcxConstant.SUBSIDY_DAY_TYPE_WORK_DAY)){
                travelDays += item.getWorkDays();
            }else{
                travelDays += item.getTravelDays();
            }
        }
        if (StringUtil.isEmpty(detailTravel.getSubsidyExtra())){
            if (baseSubsidy.compareTo(BigDecimal.ZERO)>0 && travelDays>0){
                baseSubsidyUnit = baseSubsidy.divide(new BigDecimal(travelDays), 2, RoundingMode.HALF_UP);
            }
        }
        List<SubsidyVO.SubsidyExplain> explains = new ArrayList<>();
        if (baseSubsidy.compareTo(BigDecimal.ZERO)>0){
            SubsidyVO.SubsidyExplain explain = new SubsidyVO.SubsidyExplain();
            explain.setName("出差天数");
            explain.setDays(travelDays);
            explain.setUnit(baseSubsidyUnit);
            explain.setAmt(baseSubsidy);
            explains.add(explain);
        }
        detail.setExplainList(explains);
    }

    private List<ValsetDTO> queryExtraConfig(String fiscal, String agyCode, String mofDivCode){
        return calculationRuleService.getExtraSubsidyValset(fiscal, agyCode, mofDivCode, PcxConstant.TRAVEL_DETAIL_3021103);
    }

    private List<PcxBillTravelTrip> getBillTripList(String billId) {
        return billTravelTripDao.selectByBillId(billId);
    }

    /**
     * 添加票
     * 取出添加票转换的费用，或者费用明细
     * 如果时城市间交通费或住宿费，在外面会出发重新规划行程
     * 重新规划行程会删除原来系统补充的无票费用明细
     * 统一把所有的明细金额统计，更新到报销单和费用上
     * @param invoiceQO
     * @param wrappers
     * @param view
     * @return
     */
    public String addEcsBills(AddInvoicesQO invoiceQO, List<InvoiceDtoWrapper> wrappers, PcxBill view) {

        //标准结果
        List<PcxBillExpStandResult> standList = new ArrayList<>();
        //报销单的所有费用
        List<PcxBillExpBase> baseExpList = queryBillExpBaseList(view);
        //所有票生成的费用明细
        List<PcxBillExpDetailBase> allExpDetailBase = new ArrayList<>();
        //所有票关联费用明细
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        //票的支付信息
        List<PcxEcsSettl> settlList = new ArrayList<>();
        //收集票的附件，包括票自己的附件和他关联的酒店流水单等附件
        List<PcxBillExpAttachRel> addAttachRelList = new ArrayList<>();

        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        List<PcxBillExpDetailBase> needUpdateDetailList = queryBillExpDetailList(view);

        //收集所有票生成的费用，明细，并建立票与费用和明细的关联关系
        collectAllExpBaseAndDetailAndEcsRelAndStandResult(invoiceQO, allExpDetailBase, addEcsRel, wrappers, standList, settlList, addAttachRelList, needUpdateDetailList);
        //补充外部人员的编码信息
        Pair<List<PcxBillOuterEmp>, List<PcxBillOuterEmp>> outerEmp = pcxBillOuterEmpService.initBillOuterEmp(allExpDetailBase, view, false);
        List<PcxBillOuterEmp> addOuterEmp = outerEmp.getLeft();
        //新生成的费用明细
        List<PcxBillExpDetailBase> newDetailList = new ArrayList<>(allExpDetailBase);

        //重新规划整个单据的行程
        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息
        //抛出重新规划行程删除的需要删除的明细
        boolean isDoTrip = false;
        MatchTripResult matchTripResult = null;
        List<PcxBillExpDetailBase> reTripDelDetail = new ArrayList<>();
        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息

        TripAndEcsIntercityTraffic judged = judgeTripAndEcsIntercityTraffic(newDetailList);
        if (judged.getTripDetail()){
            //当前报销单的行程是住宿费行程，并且添加的票是住宿费，则按照住宿费理行程，系统补充的大交通不需要删除，系统补充的住宿可以删除
            //如果添加的是城市间交通费，则正常理票，删除全部系统补充的明细
            boolean isHotelTrip = !judged.ecsIntercityTraffic && isHotelTrip(view);
            DoTripResultDTO tripResult = doMatchTrip(newDetailList, Lists.newArrayList(), needUpdateDetailList, view.getClaimantCode(), isHotelTrip);
            isDoTrip = tripResult.isDoTrip();
            reTripDelDetail = tripResult.getReTripDelDetail();
            matchTripResult = tripResult.getMatchTripResult();
        }
        needUpdateDetailList.addAll(newDetailList);

        List<String> oldNeedDelDetailIds  = reTripDelDetail.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        needUpdateDetailList = needUpdateDetailList.stream().filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());

        CollectTripDto tripDto = collectTrip(invoiceQO, matchTripResult);

        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            needUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
        }

        CalculateSubsidyResult calculateSubsidyResult = new CalculateSubsidyResult();
        if (isDoTrip){
            //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
            calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needUpdateDetailList, standList, view.getClaimantCode(), tripCityDays);
            if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
                needUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
            }
        }

        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), null);
        ecsRelList.addAll(addEcsRel);

        List<PcxBillExpStandResult> delStandResultList = getDelDetailStandResult(reTripDelDetail);

        List<String> otherExpTypeCode = reTripDelDetail.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        //所有费用明细的明细代码
        //查询出明细对应的费用代码
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        //汇总金额更新费用和报销单
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(invoiceQO, view, baseExpList, needUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);

        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(needUpdateDetailList, baseExpList);

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(view, (a)->true);
        allAttachRel.addAll(addAttachRelList);

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(baseExpList, needUpdateDetailList, tripDto, ecsRelList, view.getId(), isDoTrip));

        List<PcxBillExtraAttach> allExtraAttachList = queryExtraAttachList(view.getId());

        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(baseExpList)
                .insertOrUpdateDetailList(needUpdateDetailList)
                .tripDto(tripDto)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .addEcsRel(addEcsRel)
                .standList(standList)
                .delStandResultList(delStandResultList)
                .isDoTrip(isDoTrip)
                .settlList(settlList)
                .delOldDelDetail(reTripDelDetail)
                .addAttachRelList(addAttachRelList)
                .fellowsList(fellows)
                .allAttachRelList(allAttachRel)
                .tripCityDays(tripCityDays)
                .addOuterEmp(addOuterEmp)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .allExtraAttachList(allExtraAttachList)
                .build();
        return ecsExpTransService.addEcsBill(dto);
    }

    private boolean isHotelTrip(PcxBill view) {
        List<PcxBillTravelTrip> tripList = pcxBillTravelTripDao.selectList(new LambdaQueryWrapper<PcxBillTravelTrip>()
                .eq(PcxBillTravelTrip::getBillId, view.getId()));
        return CollectionUtils.isNotEmpty(tripList)
                && tripList.stream().allMatch(item->Objects.equals(item.getTripType(), TripTypeEnum.HOTEL.getName()));
    }

    private BigDecimal getUnMatchEcsAmt(List<PcxExpDetailEcsRel> ecsRelList) {
        Map<String, List<PcxExpDetailEcsRel>> ecsRelMap = ecsRelList.stream()
                .filter(item ->StringUtil.isNotEmpty(item.getEcsBillId())
                        && !isTransExpense(item))
                .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillId));
        BigDecimal unmatchEcsAmt = BigDecimal.ZERO;
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsRelMap.entrySet()) {
            Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = Optional.ofNullable(entry.getValue())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(rel ->
                            Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                    ));
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> listEntry : ecsDetailMap.entrySet()) {
                unmatchEcsAmt = unmatchEcsAmt.add(listEntry.getValue().get(0).getEcsAmt());
            }

        }
        return unmatchEcsAmt;
    }

    private Map<String, String> calculateBillAndExpAmtAndCollectExpTypeMap(ExpInvoiceQO invoiceQO,
                                                                           PcxBill view,
                                                                           List<PcxBillExpBase> baseExpList,
                                                                           List<PcxBillExpDetailBase> needUpdateDetailList,
                                                                           List<String> otherExpTypeCode,
                                                                           BigDecimal unMatchEcsAmt) {
        List<String> expTypeCodes = needUpdateDetailList.stream()
                .map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        expTypeCodes.addAll(otherExpTypeCode);
        Map<String, String> newParentMap = getExpTypeParentMap(invoiceQO, expTypeCodes);

        calculateBillAndExpAmt(view, baseExpList, needUpdateDetailList, newParentMap, unMatchEcsAmt);
        return newParentMap;
    }

    public static boolean isTripDetail(PcxBillExpDetailBase item) {
        //城市间交通费，住宿费，改签费，只有住宿费的话会走住宿费理行程
        return PcxConstant.TRAVEL_DETAIL_3021101.equals(item.getExpDetailCode())
                || PcxConstant.TRAVEL_DETAIL_3021102.equals(item.getExpDetailCode())
                ||PcxConstant.TRAVEL_DETAIL_3021110.equals(item.getExpDetailCode());
    }

    public static boolean isTripExpenseVo(EcsExpenseVO item) {
        return PcxConstant.TRAVEL_DETAIL_3021101.equals(item.getExpenseTypeCode())
                || PcxConstant.TRAVEL_DETAIL_3021102.equals(item.getExpenseTypeCode())
                ||PcxConstant.TRAVEL_DETAIL_3021110.equals(item.getExpenseTypeCode());
    }

    private boolean isSubsidyDetail(PcxBillExpDetailBase item) {
        return PcxConstant.TRAVEL_DETAIL_3021103.equals(item.getExpDetailCode())
                || PcxConstant.TRAVEL_DETAIL_3021104.equals(item.getExpDetailCode());
    }

    private boolean isSubsidyDetailVo(EcsExpenseVO item) {
        return PcxConstant.TRAVEL_DETAIL_3021103.equals(item.getExpenseTypeCode())
                || PcxConstant.TRAVEL_DETAIL_3021104.equals(item.getExpenseTypeCode());
    }

    /**
     * 获取报销单关联的费用数据
     * @param view
     * @return
     */
    public List<PcxBillExpBase> queryBillExpBaseList(PcxBill view) {
        String[] expTypeCodeArr = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpBase> result = new ArrayList<>();
        for (String expType : expTypeCodeArr) {
            BillExpenseService<PcxBillExpBase> bean = ExpenseBeanUtil.getBean(expType, view.getBizType());
            PcxBillExpBase expBase = bean.view(expType, view);
            if (Objects.nonNull(expBase)){
                result.add(expBase);
            }
        }
        return result;
    }

    /**
     * 删除票操作
     * 有可能是删除票，或者删除手动添加的费用明细
     * 如果是票，则找出票关联的费用明细
     * 如果是删除费用明细，则直接记录费用明细，如果是住宿费的明细，则找出hotelGroup相同的明细一起删除
     * 查看费用明细是否有tripId，如果有则整体报销单重新规划行程，把补助和系统补充的行程费用明细全都删除
     * 重新生成的行程数据，去计算补助
     * 把删除的费用明细，包括调用删除，和重新规划行程删除的金额从报销单和费用上去掉
     * 把新生成的补助加到报销单和费用上
     * 查询删除的费用明细关联的支出标准结果
     * 事务中处理数据
     *     如果有删除票，则调ecs更新票信息， 删除票关联关系
     *     更新报销单，更新费用，插入或更新费用明细
     *     如果从新规划了行程，则删除历史的行程，插入新的行程，更新费用明细和行程的关系
     *     删除支出标准结果
     * @param invoiceQO
     * @return
     */
    public String delEcsBillExpense(DelExpenseQO invoiceQO, PcxBill view){

        // 1、删除费用或费用明细
        //查询报销单所有的费用
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(view);
        //费用类型与费用映射
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);

        List<String> delDetailIds = new ArrayList<>();
        List<PcxExpDetailEcsRel> delEcsRelList = new ArrayList<>();
        List<PcxExpDetailEcsRel> otherRelList = new ArrayList<>();
        boolean isDelEcsRel = false;
        String delAttachRelId;
        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        //删除票
        if (StringUtil.isNotEmpty(invoiceQO.getEcsBillId())){
            delAttachRelId = invoiceQO.getEcsBillId();
            isDelEcsRel = true;
            //找到删除票的关联信息，可能有多条，飞机票或者增值税发票多个item，会生成多个费用明细或费用，所以一个票会有多个关联关系
            List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(invoiceQO.getBillId(), null);

            //需要删除的票关联关系
            delEcsRelList = ecsRelList.stream()
                    .filter(item->item.getEcsBillId().equals(invoiceQO.getEcsBillId())
                            && item.getEcsBillType().equals(invoiceQO.getEcsBillType())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(delEcsRelList)){
                throw new RuntimeException("未查询到票信息");
            }
            //需要删除的票关联关系id
            List<String> delRelIDS = delEcsRelList.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList());

            //除了当前删除的其他的匹配的票，调ecs解除票报销状态，需要传报销单剩余关联的票
            otherRelList = ecsRelList.stream().filter(
                    item->!delRelIDS.contains(item.getId())).collect(Collectors.toList());

            //如果票关联了费用明细，则找出费用明细，并按照分类，把费用明细的费用数据金额减掉，并把报销单金额减掉
            //并收集需要删除的费用明细
            //要删除的费用明细列表和费用类型映射关系  expTypeCode-> [detailId...]
            delDetailIds.addAll(delEcsRelList.stream()
                    .filter(item->StringUtil.isNotEmpty(item.getDetailId()))
                    .map(PcxExpDetailEcsRel::getDetailId)
                    .collect(Collectors.toList()));
        }else{
            PcxBillExpDetailBase detailBase = oldDetailList.stream()
                    .filter(item -> item.getId().equals(invoiceQO.getExpenseDetailId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(detailBase)){
                throw new RuntimeException("未查询到要删除的明细");
            }
            if (isHotelDetail(detailBase)){
                //删除明细肯定时手动添加的，如果是住宿费，需要把相同的hotelGroup的明细全部删除
                PcxBillExpDetailTravel manualHotel = (PcxBillExpDetailTravel) detailBase;
                delAttachRelId = manualHotel.getHotelGroup();
                List<PcxBillExpDetailBase> hotelList = oldDetailList.stream().filter(EcsExpOptService::isHotelDetail).collect(Collectors.toList());
                List<PcxBillExpDetailTravel> hoteDetaillList = getTravelDetailList(hotelList);
                List<String> sameHotelGroupIds = hoteDetaillList.stream().filter(item->item.getHotelGroup().equals(manualHotel.getHotelGroup())).map(PcxBillExpDetailTravel::getId).collect(Collectors.toList());
                delDetailIds.addAll(sameHotelGroupIds);
            }else{
                delDetailIds.add(detailBase.getId());
                delAttachRelId = detailBase.getId();
            }
        }


        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = new ArrayList<>();
        boolean isDoTrip = false;
        MatchTripResult matchTripResult = null;
        List<PcxBillExpDetailBase> reTripDelDetail = new ArrayList<>();
        CollectTripDto tripDto = new CollectTripDto();
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        CalculateSubsidyResult calculateSubsidyResult = new CalculateSubsidyResult();
        if (CollectionUtils.isNotEmpty(delDetailIds)){
            delOldDetailList = oldDetailList.stream()
                    .filter(item->delDetailIds.contains(item.getId()))
                    .collect(Collectors.toList());

            oldDetailList = oldDetailList.stream()
                    .filter(item->!delDetailIds.contains(item.getId()))
                    .collect(Collectors.toList());

            List<PcxBillExpDetailBase> delTripDetail = delOldDetailList.stream()
                    .filter(EcsExpOptService::isTripDetail)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delTripDetail)){
                List<PcxBillExpDetailTravel> delTravelDetailList = getTravelDetailList(delTripDetail);
                if (delTravelDetailList.stream().anyMatch(item->StringUtil.isNotEmpty(item.getTripId()))){
                    boolean isHotelTrip = isHotelTrip(view);
                    DoTripResultDTO tripResult = doMatchTrip(Lists.newArrayList(), delOldDetailList, oldDetailList, view.getClaimantCode(), isHotelTrip);
                    isDoTrip = tripResult.isDoTrip();
                    reTripDelDetail = tripResult.getReTripDelDetail();
                    matchTripResult = tripResult.getMatchTripResult();
                }
            }

            //重新规划行程需要删掉的明细加进去
            delOldDetailList.addAll(reTripDelDetail);

            tripDto = collectTrip(invoiceQO, matchTripResult);

            List<String> oldNeedDelDetailIds  = delOldDetailList
                    .stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
            needSaveOrUpdateDetailList = oldDetailList.stream()
                    .filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());
            //行程补充的无票费用明细
            if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
                needSaveOrUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
            }

            if (isDoTrip){
                //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
                calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needSaveOrUpdateDetailList, standResultList, view.getClaimantCode(), tripCityDays);
                if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
                    needSaveOrUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
                }
            }
        }else{
            needSaveOrUpdateDetailList = oldDetailList;
        }

        //查询出明细对于的费用代码
        List<String> otherExpTypeCode = delOldDetailList.stream()
                .map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());

        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(otherRelList);
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(invoiceQO, view, billExpList, needSaveOrUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);

        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(oldDetailList, billExpList);

        List<PcxBillExpStandResult> delStandResultList = getDelDetailStandResult(delOldDetailList);

        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, view.getId()));
        attachRelList = attachRelList.stream().filter(item->!item.getRelId().equals(delAttachRelId)).collect(Collectors.toList());

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, needSaveOrUpdateDetailList, tripDto, otherRelList, view.getId(), isDoTrip));

        List<PcxBillExtraAttach> allExtraAttachList = queryExtraAttachList(view.getId());
        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .insertOrUpdateDetailList(needSaveOrUpdateDetailList)
                .delOldDelDetail(delOldDetailList)
                .isDoTrip(isDoTrip)
                .tripDto(tripDto)
                .parentMap(newParentMap)
                .delEcsRelList(delEcsRelList)
                .allEcsList(otherRelList)
                .isDelEcsRel(isDelEcsRel)
                .delStandResultList(delStandResultList)
                .standList(standResultList)
                .allAttachRelList(attachRelList)
                .delAttachRelId(delAttachRelId)
                .fellowsList(fellows)
                .tripCityDays(tripCityDays)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .allExtraAttachList(allExtraAttachList)
                .build();
        return ecsExpTransService.delDetailAndEcsBill(dto);
    }

    private List<PcxBillExpStandResult> getDelDetailStandResult(List<PcxBillExpDetailBase> delOldDetailList) {
        List<PcxBillExpStandResult> result = new ArrayList<>();
        List<PcxBillExpDetailBase> haveStandSourceDetailList = delOldDetailList.stream()
                .filter(item->!BillExpDetailSourceEnum.REPLENISH.getCode().equals(item.getSource())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(haveStandSourceDetailList)){
            return result;
        }
        String billId = haveStandSourceDetailList.get(0).getBillId();
        Map<Boolean, List<PcxBillExpDetailBase>> map = haveStandSourceDetailList.stream()
                .collect(Collectors.partitioningBy(EcsExpOptService::isHotelDetail));
        if (!map.isEmpty()){
            for (Map.Entry<Boolean, List<PcxBillExpDetailBase>> entry : map.entrySet()) {
                List<String> relIds = entry.getValue().stream().map(item -> {
                    if (entry.getKey()) {
                        PcxBillExpDetailTravel hotelDetail = (PcxBillExpDetailTravel) item;
                        return hotelDetail.getHotelGroup();
                    } else {
                        return item.getId();
                    }
                }).collect(Collectors.toList());
                relIds = relIds.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(relIds)){
                    continue ;
                }
                if (entry.getKey()){
                    result.addAll(pcxBillExpStandResultDao.selectList(Wrappers.lambdaQuery(PcxBillExpStandResult.class)
                            .eq(PcxBillExpStandResult::getBillId, billId)
                            .in(PcxBillExpStandResult::getEcsBillId, relIds)));
                }else{
                    result.addAll(pcxBillExpStandResultDao.selectList(Wrappers.lambdaQuery(PcxBillExpStandResult.class)
                            .eq(PcxBillExpStandResult::getBillId, billId)
                            .in(PcxBillExpStandResult::getDetailId, relIds)));
                }
            }
        }
        return result;
    }

    public static boolean isTransExpense(PcxExpDetailEcsRel item) {
        return Objects.equals(item.getIsConfirm(), InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
    }


    public static List<PcxBillExpDetailTravel> getTravelDetailList(List<PcxBillExpDetailBase> baseList){
        List<PcxBillExpDetailTravel> detailTravelList = new ArrayList<>();
        for (PcxBillExpDetailBase detail : baseList) {
            if (detail instanceof PcxBillExpDetailTravel) {
                PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detail;
                detailTravelList.add(travel);
            }
        }
        return detailTravelList;
    }


    /**
     * 移动端查看票详情时
     * 编辑费用明细和支付信息
     * @param updateQO
     * @param view
     */
    public void updateEcs(UpdateEcsQO updateQO, PcxBill view) {
        //转换出费用明细列表
        Map<String, List<PcxBillExpDetailBase>> itemDetailMap = analysisDetailListItemMap(updateQO.getItemExpenseList(), view,
                updateQO.getExpenseTypeCode(), BillExpDetailSourceEnum.ECS.getCode());
        List<PcxBillExpDetailBase> newDetailList = itemDetailMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        validDetail(newDetailList);

        Pair<List<PcxBillOuterEmp>, List<PcxBillOuterEmp>> outerEmp = pcxBillOuterEmpService.initBillOuterEmp(newDetailList, view, true);
        List<PcxBillOuterEmp> addOuterEmp = outerEmp.getLeft();
        List<PcxBillOuterEmp> updateOuterEmp = outerEmp.getRight();
        Map<String, PcxBillOuterEmp> outerUpdateSubsidy = updateOuterEmp.stream().collect(Collectors.toMap(PcxBillOuterEmp::getEmpCode, Function.identity(), (key1, key2)->key1));
        //转换出付款信息列表
        List<PcxEcsSettl> ecsSettlList = analysisEcsSettlEcsItem(updateQO.getSettlList(), updateQO.getBillId(), updateQO.getEcsBillId(), updateQO.getEcsBillType(), updateQO);
        //转换出附件信息列表
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRelList(updateQO.getEcsBillClassRelList(), view, updateQO.getEcsBillId());
        //转换出同步ecs数据
        UpdateEcsBillDTO updateEcsBillDTO = analysisUpdateEcsBillDTOEcsItem(newDetailList, ecsSettlList, updateQO);

        //查询出报销单的费用数据
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(view);

        boolean updateOuterSubsidy = !outerUpdateSubsidy.isEmpty();
        //如果编辑票修改了外部人员的是否有补助，则把外部人员所有的明细是否有补助字段都进行更新
        if (updateOuterSubsidy){
            disposeOuterUpdateSubsidy(outerUpdateSubsidy, oldDetailList);
        }

        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        //查询出报销单的票关联关系
        //找到票的关联信息，可能有多条，飞机票或者增值税发票多个item，住宿费多个人，会生成多个费用明细或费用，所以一个票会有多个关联关系
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(updateQO.getBillId(), null);

        List<PcxExpDetailEcsRel> optEcsRelList = ecsRelList.stream().filter(item->item.getEcsBillId().equals(updateQO.getEcsBillId())
                && item.getEcsBillType().equals(updateQO.getEcsBillType())).collect(Collectors.toList());
        ecsRelList.removeAll(optEcsRelList);
        if (CollectionUtils.isEmpty(optEcsRelList)){
            throw new RuntimeException("未找到匹配的票");
        }

        //根据编辑票明细页面修改的项目的票金额，更新ecsRel里面的ecsAmt
        changeEcsAmt(optEcsRelList, updateQO.getItemExpenseList());

        //需要绑定的票，如果当前操作的的是未匹配的票，则需要调ecs修改票状态

        //如果是未匹配的票，则直接生成新的票和明细关联关系，删除未匹配的票关联关系,还要去调ecs更新票接口
        if (isTransExpense(optEcsRelList.get(0))){
            //匹配的票，编辑明细时，把旧的明细都删掉
            List<String> oldEcsRelDetailIds = optEcsRelList.stream()
                    .filter(item -> StringUtil.isNotEmpty(item.getDetailId()))
                    .map(PcxExpDetailEcsRel::getDetailId)
                    .collect(Collectors.toList());
            delOldDetailList = oldDetailList.stream()
                    .filter(item->oldEcsRelDetailIds.contains(item.getId()))
                    .collect(Collectors.toList());
        }
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);

        disposeDetailTax(newDetailList, rel);

        List<PcxExpDetailEcsRel> newRelList = analysisNewEcsRelEcsItem(optEcsRelList, itemDetailMap, updateQO.getExpenseTypeCode());
        ecsRelList.addAll(newRelList);

        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息
        boolean isDoTrip = false;
        MatchTripResult matchTripResult = null;
        List<PcxBillExpDetailBase> reTripDelDetail = new ArrayList<>();
        TripAndEcsIntercityTraffic judged = judgeTripAndEcsIntercityTraffic(newDetailList);
        if (judged.tripDetail){
            boolean isHotelTrip = !judged.ecsIntercityTraffic && isHotelTrip(view);
            DoTripResultDTO tripResult = doMatchTrip(newDetailList, delOldDetailList, oldDetailList, view.getClaimantCode(), isHotelTrip);
            isDoTrip = tripResult.isDoTrip();
            reTripDelDetail = tripResult.getReTripDelDetail();
            matchTripResult = tripResult.getMatchTripResult();
        }
        //把历史费用明细的金额从费用上和报销单上去掉
        delOldDetailList.addAll(reTripDelDetail);

        CollectTripDto tripDto = collectTrip(updateQO, matchTripResult);

        List<String> oldNeedDelDetailIds  = delOldDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = oldDetailList.stream().filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());
        needSaveOrUpdateDetailList.addAll(newDetailList);
        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            needSaveOrUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
        }
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        CalculateSubsidyResult calculateSubsidyResult = new CalculateSubsidyResult();
        if (isDoTrip){
            //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
            calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needSaveOrUpdateDetailList, standResultList, view.getClaimantCode(), tripCityDays);
            if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
                needSaveOrUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
            }
        }else if (updateOuterSubsidy){
            //如果没有重新理行程，并且外部人员的是否有补助还发生了变化，则把外部人员的补助都删掉，如果有必要的话，重新生成补助
            List<PcxBillExpDetailTravel> travels = getTravelDetailList(oldDetailList);
            List<String> delSubsidyIds = new ArrayList<>();
            List<PcxBillExpDetailTravel> delSubsidy = travels.stream().filter(item->isSubsidyDetail(item)
                    && Objects.equals(item.getEmpType(), PcxConstant.EMP_TYPE_OUTER)
                    && outerUpdateSubsidy.containsKey(item.getEmpCode()))
                    .peek(item->{delSubsidyIds.add(item.getId());})
                    .collect(Collectors.toList());
            delOldDetailList.addAll(delSubsidy);
            needSaveOrUpdateDetailList = needSaveOrUpdateDetailList.stream()
                    .filter(item->!delSubsidyIds.contains(item.getId()))
                    .collect(Collectors.toList());
            List<PcxBillOuterEmp> subsidyOuter = new ArrayList<>();
            for (Map.Entry<String, PcxBillOuterEmp> entry : outerUpdateSubsidy.entrySet()) {
                if (entry.getValue().getIsSubsidy() == 1){
                    subsidyOuter.add(entry.getValue());
                }
            }
            if (CollectionUtils.isNotEmpty(subsidyOuter)){
                List<PcxBillExpDetailBase> outerTravelDetail = needSaveOrUpdateDetailList.stream().filter(item -> subsidyOuter.contains(item.getEmpCode())
                        && Objects.equals(item.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021101))
                        .collect(Collectors.toList());
                List<PcxBillExpDetailTravel> newSubsidy = generateSubsidy(subsidyOuter, view, outerTravelDetail, standResultList);
                needSaveOrUpdateDetailList.addAll(newSubsidy);
            }
        }

        List<String> otherExpTypeCode = delOldDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        //查询出明细对于的费用代码
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(updateQO, view, billExpList, needSaveOrUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);
        //删除旧的行程，删除旧的补充明细
        //保存新的行程，保存新的补充明细
        //把付款信息保存
        //把付款信息和费用明细同步给ecs
        //删除历史的补助信息
        //保存新生成的补助信息
        //处理差旅费信息，解析出出差人，出发地点，目的地点，出差时间
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(needSaveOrUpdateDetailList, billExpList);
        List<PcxBillExpStandResult> delOldStandResultList = getDelDetailStandResult(delOldDetailList);

        standResultList.addAll(calculateNewDetailStandResult(newDetailList));
        List<PcxBillExpAttachRel> allAttachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, updateQO.getBillId()));
        allAttachRelList = allAttachRelList.stream()
                .filter(item-> !item.getRelId().equals(rel.getEcsBillId()))
                .collect(Collectors.toList());
        allAttachRelList.addAll(attachRelList);

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, needSaveOrUpdateDetailList, tripDto, ecsRelList, view.getId(), isDoTrip));

        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .ecsRel(rel)
                .addEcsRel(newRelList)
                .tripDto(tripDto)
                .insertOrUpdateDetailList(needSaveOrUpdateDetailList)
                .isDoTrip(isDoTrip)
                .parentMap(newParentMap)
                .allEcsList(ecsRelList)
                .settlList(ecsSettlList)
                .delOldDelDetail(delOldDetailList)
                .fellowsList(fellows)
                .standList(standResultList)
                .delStandResultList(delOldStandResultList)
                .allAttachRelList(allAttachRelList)
                .addAttachRelList(attachRelList)
                .updateEcsBillDTO(updateEcsBillDTO)
                .tripCityDays(tripCityDays)
                .addOuterEmp(addOuterEmp)
                .updateOuterEmp(updateOuterEmp)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .build();
        ecsExpTransService.updateDetailAndPayment(dto);
    }

    private List<PcxBillExpDetailTravel> generateSubsidy(List<PcxBillOuterEmp> subsidyOuter,
                                                         PcxBill bill,
                                                         List<PcxBillExpDetailBase> outerTravelDetail,
                                                         List<PcxBillExpStandResult> standResultList) {
        return subsidyService.generateSubsidy(subsidyOuter, bill, outerTravelDetail, standResultList);
    }

    private void disposeOuterUpdateSubsidy(Map<String, PcxBillOuterEmp> outerUpdateSubsidy, List<PcxBillExpDetailBase> oldDetailList) {
        for (PcxBillExpDetailBase detailBase : oldDetailList) {
            PcxBillExpDetailTravel detailTravel = (PcxBillExpDetailTravel) detailBase;
            if (Objects.equals(detailTravel.getEmpType(), PcxConstant.EMP_TYPE_OUTER)){
                PcxBillOuterEmp outerEmp = outerUpdateSubsidy.get(detailTravel.getEmpCode());
                if (Objects.nonNull(outerEmp)){
                    detailTravel.setIsSubsidy(outerEmp.getIsSubsidy());
                    detailTravel.setBudLevel(outerEmp.getBudLevel());
                }
            }
        }
    }

    private void changeEcsAmt(List<PcxExpDetailEcsRel> optEcsRelList, List<EcsItemExpense> itemExpenseList) {
        Map<String, BigDecimal> itemEcsMap = itemExpenseList.stream().collect(Collectors.toMap(EcsItemExpense::getEcsDetailId, EcsItemExpense::getEcsAmt, (key1, key2) -> key1));
        optEcsRelList.forEach(item->{
            BigDecimal ecsAmt = itemEcsMap.get(item.getEcsDetailId());
            if (ecsAmt != null){
                item.setEcsAmt(ecsAmt);
            }
        });
    }

    private List<PcxEcsSettl> analysisEcsSettlEcsItem(List<EcsSettlQO> settlList,
                                                      String billId,
                                                      String ecsBillId,
                                                      String ecsBillType,
                                                      ExpInvoiceQO invoiceQO) {
        List<PcxEcsSettl> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settlList)){
            for (EcsSettlQO ecsSettlQO : settlList) {
                PcxEcsSettl ecsSettl = new PcxEcsSettl();
                BeanUtils.copyProperties(ecsSettlQO, ecsSettl);
                ecsSettl.setPayAmt(Objects.isNull(ecsSettl.getPayAmt())? BigDecimal.ZERO: ecsSettl.getPayAmt());
                ecsSettl.setBillId(billId);
                ecsSettl.setEcsBillId(ecsBillId);
                ecsSettl.setEcsTypeCode(ecsBillType);
                ecsSettl.setFiscal(invoiceQO.getFiscal());
                ecsSettl.setAgyCode(invoiceQO.getAgyCode());
                ecsSettl.setMofDivCode(invoiceQO.getMofDivCode());
                result.add(ecsSettl);
            }
        }
        return result;
    }
    private void disposeDetailTax(List<PcxBillExpDetailBase> newDetailList, PcxExpDetailEcsRel rel) {
        for (PcxBillExpDetailBase detailBase : newDetailList) {
            taxCalculate.calculate(rel, detailBase);
        }
    }

    //匹配行程
    private DoTripResultDTO doMatchTrip(List<PcxBillExpDetailBase> newDetailList,
                                        List<PcxBillExpDetailBase> delOldDetailList,
                                        List<PcxBillExpDetailBase> oldDetailList,
                                        String claimantCode,
                                        Boolean isHotelTrip) {
        boolean delTraffic = false;
        if (isHotelTrip){
            //住宿费行程是否全部都是系统生成的城市间交通费，如果是，则可以删除
            delTraffic =  oldDetailList.stream().allMatch(item-> !isIntercityTrafficBase(item)
                   || !Objects.equals(BillExpDetailSourceEnum.ECS.getCode(), item.getSource()));
        }
        DoTripResultDTO resultDTO = new DoTripResultDTO();
        //整个报销单重新规划行程，把系统补充的无票明细，和补助明细，都全部删掉
        //如果是住宿费理行程，则系统补充的城市间交通费不需要删除
        for (PcxBillExpDetailBase item : oldDetailList) {
            if (isNoEcsDetail(item)) {
                //如果是住宿费理行程，只把系统补充的住宿费删除
                if (isHotelTrip){
                    if (isHotelDetail(item) || delTraffic){
                        resultDTO.getReTripDelDetail().add(item);
                    }
                }else{
                    resultDTO.getReTripDelDetail().add(item);
                }
            }
            if (isSubsidyDetail(item)){
                resultDTO.getReTripDelDetail().add(item);
            }
        }
        List<String> delNoEcsDetailIds = resultDTO.getReTripDelDetail().stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<String> delOldDetailIds = delOldDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        oldDetailList = oldDetailList.stream()
                .filter(item -> !delNoEcsDetailIds.contains(item.getId())
                        && !delOldDetailIds.contains(item.getId())).collect(Collectors.toList());
        List<PcxBillExpDetailBase> allTravelDetail = oldDetailList.stream()
                .filter(EcsExpOptService::isTripDetail).collect(Collectors.toList());
        allTravelDetail.addAll(newDetailList);
        resultDTO.setMatchTripResult(travelTripProcessor.matchTripByDetail(allTravelDetail, claimantCode, isHotelTrip));
        return resultDTO;
    }

    private List<PcxBillExpAttachRel> analysisAttachRelList(List<AttachRelQO> attachRelList, PcxBill view,
                                                            String ecsBillId) {

        List<PcxBillExpAttachRel> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachRelList)){
            for (AttachRelQO attachRelQO : attachRelList) {
                PcxBillExpAttachRel attachRel = new PcxBillExpAttachRel();
                attachRel.setBillId(view.getId());
                attachRel.setFiscal(view.getFiscal());
                attachRel.setAgyCode(view.getAgyCode());
                attachRel.setMofDivCode(view.getMofDivCode());
                attachRel.setTenantId(view.getTenantId());
                attachRel.setAttachId(attachRelQO.getBillId());
                attachRel.setFileName(attachRelQO.getFileName());
                attachRel.setEcsBillType(attachRelQO.getBillTypeCode());
                attachRel.setRelType(PcxExpAttachRelType.ECS.getCode());
                attachRel.setRelId(ecsBillId);
                result.add(attachRel);
            }
        }
        return result;
    }

    private List<PcxBillExpStandResult> calculateNewDetailStandResult(List<PcxBillExpDetailBase> newDetailList) {
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        if (newDetailList.stream().anyMatch(EcsExpOptService::isHotelDetail)){
            List<PcxBillExpStandResult> standResult = standMatchProcessor.matchByEcs(newDetailList);
            if (CollectionUtils.isNotEmpty(standResult)){
                standResultList.addAll(standResult);
            }
        }else{
            for (PcxBillExpDetailBase detailBase : newDetailList) {
                PcxBillExpStandResult standResult = standMatchProcessor.matchByDetail(detailBase);
                if (Objects.nonNull(standResult)){
                    standResultList.add(standResult);
                }
            }
        }
        return standResultList;
    }

    public static boolean isHotelDetail(PcxBillExpDetailBase detailBase){
        return PcxConstant.TRAVEL_DETAIL_3021102.equals(detailBase.getExpDetailCode());
    }

    private UpdateEcsBillDTO analysisUpdateEcsBillDTOEcsItem(List<PcxBillExpDetailBase> newDetailList,
                                                      List<PcxEcsSettl> ecsSettlList,
                                                      UpdateEcsQO updateQO) {
        UpdateEcsBillDTO dto = new UpdateEcsBillDTO();
        dto.setBillNo(updateQO.getBillNo());
        dto.setBillAmt(updateQO.getBillAmt());
        dto.setBillId(updateQO.getEcsBillId());
        dto.setFiscal(updateQO.getFiscal());
        dto.setAgencyCode(updateQO.getAgyCode());
        dto.setMofDivCode(updateQO.getMofDivCode());
        dto.setTenantId(updateQO.getTenantId());
        dto.setBillTypeCode(updateQO.getEcsBillType());
        List<UpdateEcsBillDTO.EcsAttachRelInfo> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getEcsBillClassRelList())){
            for (AttachRelQO attachRelQO : updateQO.getEcsBillClassRelList()) {
                UpdateEcsBillDTO.EcsAttachRelInfo relInfo = UpdateEcsBillDTO.EcsAttachRelInfo.builder()
                        .agencyCode(updateQO.getAgyCode())
                        .fiscal(updateQO.getFiscal())
                        .mofDivCode(updateQO.getMofDivCode())
                        .billId(attachRelQO.getBillId())
                        .fileName(attachRelQO.getFileName())
                        .billTypeCode(attachRelQO.getBillTypeCode())
                        .createUser(updateQO.getUserName())
                        .createUserCode(updateQO.getUserCode())
                        .isDeleted("2")
                        .classType("bus").build();
                attachRelList.add(relInfo);
            }
        }
        dto.setEcsBillClassRelList(attachRelList);
        List<EcsBillSettleDTO> billSettlList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ecsSettlList)){
            for (PcxEcsSettl ecsSettl : ecsSettlList) {
                EcsBillSettleDTO settleDTO = new EcsBillSettleDTO();
                BeanUtils.copyProperties(ecsSettl, settleDTO);
                billSettlList.add(settleDTO);
            }
        }
        dto.setSettlDetails(billSettlList);
        List<ExpDetailDTO> expDetailDTOList = analysisExpDetail(newDetailList, updateQO.getEcsBillType());
        dto.setExpDetails(expDetailDTOList);
        dto.setEcsBillInfo(updateQO.getEcsBillInfo());
        return dto;
    }

    private List<ExpDetailDTO> analysisExpDetail(List<PcxBillExpDetailBase> newDetailList, String ecsBillType) {
        List<ExpDetailDTO> result = new ArrayList<>();
        PcxBillExpDetailBase detailBase = newDetailList.get(0);
        PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detailBase;
        if (detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)
            || detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021112)){
            List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(newDetailList);
            Map<String, List<PcxBillExpDetailTravel>> map = travelDetailList.stream().collect(Collectors.groupingBy(item ->
            {
                if (StringUtil.isEmpty(item.getHotelGroup())) {
                    return item.getId();
                } else {
                    return item.getHotelGroup() + "-" +item.getHotelSeq();
                }
            }));
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : map.entrySet()) {
                PcxBillExpDetailTravel detailTravel = entry.getValue().get(0);
                List<EmpInfo> empInfos = entry.getValue().stream().map(this::getEmpInfo).collect(Collectors.toList());
                ExpDetailDTO detailDTO = new ExpDetailDTO();
                CityInfo cityInfo = new CityInfo();
                cityInfo.setCode(detailTravel.getEndCityCode());
                cityInfo.setName(detailTravel.getEndCity());
                detailDTO.setCityName(JSON.toJSONString(Collections.singletonList(cityInfo)));
                detailDTO.setPassengerName(JSON.toJSONString(empInfos));
                detailDTO.setExpenseTypeCode(detailTravel.getExpDetailCode());
                detailDTO.setCheckInTime(detailTravel.getStartTime());
                detailDTO.setCheckOutTime(detailTravel.getFinishTime());
                detailDTO.setExpenseTypeName(detailTravel.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)
                        ? PcxConstant.TRAVEL_DETAIL_3021102_NAME : PcxConstant.TRAVEL_DETAIL_3021112_NAME);
                result.add(detailDTO);
            }
        } else if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021101, detailBase.getExpDetailCode())
                && Objects.equals(travel.getTrafficToolCode(), TravelToolEnums.TRAVEL_AIRCRAFT)
                && Objects.equals(ecsBillType, EcsEnum.BillType.EINV.getCode())) {
            List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(newDetailList);
            for (PcxBillExpDetailTravel detailTravel : travelDetailList) {
                List<EmpInfo> empInfos = detailTravel.getEmpInfo();
                EmpInfo empInfo = new EmpInfo();
                if (CollectionUtils.isNotEmpty(empInfos)){
                    empInfo = empInfos.get(0);
                }
                empInfo.setBizType("售");
                empInfo.setCabinClass(detailTravel.getInputSeatLevel());
                empInfo.setTrainNo(detailTravel.getTrainNo());
                ExpDetailDTO detailDTO = new ExpDetailDTO();
                CityInfo startCity = new CityInfo();
                startCity.setCode(detailTravel.getStartCityCode());
                startCity.setName(detailTravel.getStartCity());
                CityInfo endCity = new CityInfo();
                endCity.setCode(detailTravel.getEndCityCode());
                endCity.setName(detailTravel.getEndCity());
                detailDTO.setCityName(JSON.toJSONString(Arrays.asList(startCity, endCity)));
                detailDTO.setPassengerName(JSON.toJSONString(empInfos));
                detailDTO.setExpenseTypeCode(detailTravel.getExpDetailCode());
                detailDTO.setCheckInTime(detailTravel.getStartTime());
                detailDTO.setCheckOutTime(detailTravel.getFinishTime());
                detailDTO.setExpenseTypeName(PcxConstant.TRAVEL_DETAIL_3021101_NAME);
                result.add(detailDTO);
            }
        }
        return result;
    }

    private EmpInfo getEmpInfo(PcxBillExpDetailTravel item) {
        EmpInfo empInfo = new EmpInfo();
        empInfo.setEmpCode(item.getEmpCode());
        empInfo.setBudLevel(item.getBudLevel());
        empInfo.setEmpName(item.getEmpName());
        empInfo.setEmpType(item.getEmpType());
        return empInfo;
    }


    private boolean isNoEcsDetail(PcxBillExpDetailBase detail) {
        if (detail instanceof PcxBillExpDetailTravel){
            PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detail;
            return BillExpDetailSourceEnum.REPLENISH.getCode().equals(travel.getSource());
        }
        return false;
    }

    private List<PcxExpDetailEcsRel> analysisNewEcsRel(PcxExpDetailEcsRel rel, List<PcxBillExpDetailBase> detailList) {
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        for (PcxBillExpDetailBase detail : detailList) {
            detail.setEcsAmt(rel.getEcsAmt());
            PcxExpDetailEcsRel newRel = JSON.parseObject(JSON.toJSONString(rel), PcxExpDetailEcsRel.class);
            newRel.setId(IDGenerator.id());
            newRel.setDetailId(detail.getId());
            newRel.setExpenseTypeCode(detail.getExpDetailCode());
            newRel.setEmpCode(detail.getEmpCode());
            newRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            newRel.setInputAmt(detail.getInputAmt());
            newRel.setCheckAmt(detail.getCheckAmt());
            result.add(newRel);
        }
        return result;
    }

    private List<PcxExpDetailEcsRel> analysisNewEcsRelEcsItem(List<PcxExpDetailEcsRel> relList,
                                                              Map<String, List<PcxBillExpDetailBase>> detailMap,
                                                              String expenseTypeCode) {
        //根据票项目遍历，每个项目的费用明细进行生成ecsRel和明细的关联关系
        //如果项目没有费用明细，则把他自己在保存一下
        List<PcxExpDetailEcsRel> result = new ArrayList<>();
        Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = relList.stream()
                .collect(Collectors.groupingBy(rel ->
                        Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                ));
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsDetailMap.entrySet()) {
            PcxExpDetailEcsRel rel = entry.getValue().get(0);
            List<PcxBillExpDetailBase> detailBases = detailMap.get(rel.getEcsDetailId());
            if (CollectionUtils.isNotEmpty(detailBases)){
                List<PcxExpDetailEcsRel> ecsRelList = analysisNewEcsRel(rel, detailBases);
                result.addAll(ecsRelList);
            }else{
                //旧的删掉，重新插入一个
                rel.setId(IDGenerator.id());
                rel.setDetailId("");
                rel.setInputAmt(BigDecimal.ZERO);
                rel.setCheckAmt(BigDecimal.ZERO);
                rel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
                rel.setExpenseTypeCode(expenseTypeCode);
                result.add(rel);
            }
        }
        return result;
    }

    private List<PcxBillExpDetailBase> queryBillExpDetailList(PcxBill view) {
        String[] split = StringUtil.getStringValue(view.getExpenseCodes()).split(",");
        List<PcxBillExpDetailBase> result = new ArrayList<>();
        for (String expTypeCode : split) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(expTypeCode);
            result.addAll(detailBean.listByExpenseCode(expTypeCode, view));
        }
        return result;
    }

    private Triple<List<PcxBillExpDetailBase>, List<PcxBillOuterEmp>, List<PcxBillOuterEmp>> analysisDetailList(JSONArray expenseList, PcxBill view, String expDetailCode, String source) {
        LinkedHashMap<String, Object> detailLinkedHashMap = (LinkedHashMap<String, Object>) expenseList.get(0);
        JSONObject detailJson = new JSONObject(detailLinkedHashMap);
        PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
        qo.setExpenseCode(expDetailCode);
        qo.setAgyCode(view.getAgyCode());
        qo.setMofDivCode(view.getMofDivCode());
        qo.setFiscal(view.getFiscal());
        qo.setTenantId(view.getTenantId());
        PcxBasExpType baseExpByQO = basExpTypeDao.getBaseExpByQO(qo);
        List<PcxBillExpDetailBase> result = new ArrayList<>();
        String uuid = UUID.randomUUID().toString();
        int seq = 1;
        for (Object o : expenseList) {
            detailJson = new JSONObject((LinkedHashMap<String, Object> )o);
            //处理人员费控级别
            disposeEmpLevel(view, detailJson);
            disposeEmpInfo(view, detailJson, expDetailCode);
            PcxBillExpDetailBase detailBase = ExpenseBeanUtil.getEntityDetailBean(baseExpByQO.getParentCode());
            detailBase = JSON.parseObject(detailJson.toJSONString(), detailBase.getClass());
            detailBase.setId(IDGenerator.id());
            detailBase.setExpenseId("");
            detailBase.setExpDetailCode(expDetailCode);
            detailBase.setInputAmt(Objects.isNull(detailBase.getInputAmt()) ? BigDecimal.ZERO : detailBase.getInputAmt());
            detailBase.setEcsAmt(detailBase.getInputAmt());
            detailBase.setCheckAmt(detailBase.getInputAmt());
            detailBase.setSource(source);
            //住宿费需要处理一下多个住宿人
            if (detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)){
                result.addAll(analysisHotelDetail(view, detailBase, uuid, seq ++));
            }else{
                result.add(detailBase);
            }
        }
        Pair<List<PcxBillOuterEmp>, List<PcxBillOuterEmp>> outerEmp = pcxBillOuterEmpService.initBillOuterEmp(result, view, true);
        if (CollectionUtils.isNotEmpty(result)){
            for (PcxBillExpDetailBase detailBase : result) {
                detailBase.setFiscal(view.getFiscal());
                detailBase.setMofDivCode(view.getMofDivCode());
                detailBase.setAgyCode(view.getAgyCode());
            }
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(baseExpByQO.getParentCode());
            CheckMsg<Void> validate = detailBean.validate(result, FormSettingEnums.BillFuncCodeEnum.EXPENSES_BILL.getCode());
            if (!validate.isSuccess()){
                throw new RuntimeException(validate.getMsgInfo());
            }
        }
        validDetail(result);
        return Triple.of(result, outerEmp.getLeft(), outerEmp.getRight());
    }

    private void validDetail(List<PcxBillExpDetailBase> result) {
        if (CollectionUtils.isNotEmpty(result)){
            for (PcxBillExpDetailBase detailBase : result) {
                switch (detailBase.getExpDetailCode()){
                    case PcxConstant.TRAVEL_DETAIL_3021101:
                    case PcxConstant.TRAVEL_DETAIL_3021102:
                        PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detailBase;
                        if (StringUtil.isNotEmpty(travel.getStartTime()) && StringUtil.isNotEmpty(travel.getFinishTime())){
                            if (travel.getFinishTime().compareTo(travel.getStartTime())<0){
                                throw new RuntimeException("结束时间不能早于开始时间");
                            }
                            break;
                        }
                }
            }
        }
    }

    private Map<String, List<PcxBillExpDetailBase>> analysisDetailListItemMap(List<EcsItemExpense> itemExpenseList, PcxBill view,
                                                                              String expDetailCode,
                                                                              String source) {
        Map<String, List<PcxBillExpDetailBase>> result = new HashMap<>();
        BigDecimal detailInputAmt = BigDecimal.ZERO;
        BigDecimal allEcsAmt = BigDecimal.ZERO;
        for (EcsItemExpense itemExpense : itemExpenseList) {
            JSONArray expenseList = itemExpense.getExpenseList();
            BigDecimal ecsAmt = itemExpense.getEcsAmt();
            allEcsAmt = allEcsAmt.add(ecsAmt);
            if (Objects.isNull(expenseList) || expenseList.isEmpty()){
                result.put(itemExpense.getEcsDetailId(), new ArrayList<>());
                continue;
            }
            LinkedHashMap<String, Object> detailLinkedHashMap = (LinkedHashMap<String, Object>) expenseList.get(0);
            JSONObject detailJson = new JSONObject(detailLinkedHashMap);
            PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
            qo.setExpenseCode(expDetailCode);
            qo.setAgyCode(view.getAgyCode());
            qo.setMofDivCode(view.getMofDivCode());
            qo.setFiscal(view.getFiscal());
            qo.setTenantId(view.getTenantId());
            PcxBasExpType baseExpByQO = basExpTypeDao.getBaseExpByQO(qo);
            List<PcxBillExpDetailBase> detailBaseList = new ArrayList<>();
            String uuid = UUID.randomUUID().toString();
            int seq = 1;
            for (Object o : expenseList) {
                detailJson = new JSONObject((LinkedHashMap<String, Object> )o);
                //处理人员费控级别
                disposeEmpLevel(view, detailJson);
                disposeEmpInfo(view, detailJson, expDetailCode);
                PcxBillExpDetailBase detailBase = ExpenseBeanUtil.getEntityDetailBean(baseExpByQO.getParentCode());
                detailBase = JSON.parseObject(detailJson.toJSONString(), detailBase.getClass());
                detailBase.setId(IDGenerator.id());
                detailBase.setExpenseId("");
                detailBase.setExpDetailCode(expDetailCode);
                detailBase.setInputAmt(Objects.isNull(detailBase.getInputAmt()) ? BigDecimal.ZERO : detailBase.getInputAmt());
                detailBase.setEcsAmt(Objects.isNull(detailBase.getEcsAmt()) ? ecsAmt : detailBase.getEcsAmt());
                detailInputAmt = detailInputAmt.add(detailBase.getInputAmt());
                detailBase.setCheckAmt(detailBase.getInputAmt());
                detailBase.setSource(source);
                //住宿费需要处理一下多个住宿人
                if (detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)){
                    detailBaseList.addAll(analysisHotelDetail(view, detailBase, uuid, seq ++));
                }else{
                    detailBaseList.add(detailBase);
                }
            }
            if (CollectionUtils.isNotEmpty(detailBaseList)){
                for (PcxBillExpDetailBase detailBase : detailBaseList) {
                    detailBase.setFiscal(view.getFiscal());
                    detailBase.setMofDivCode(view.getMofDivCode());
                    detailBase.setAgyCode(view.getAgyCode());
                    detailBase.setTenantId(view.getTenantId());
                }
                BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(baseExpByQO.getParentCode());
                CheckMsg<Void> validate = detailBean.validate(detailBaseList, FormSettingEnums.BillFuncCodeEnum.EXPENSES_BILL.getCode());
                if (!validate.isSuccess()){
                    throw new RuntimeException(validate.getMsgInfo());
                }
            }
            result.put(itemExpense.getEcsDetailId(), detailBaseList);
        }
        if (detailInputAmt.compareTo(allEcsAmt) >0){
            throw new RuntimeException("报销金额不能大于发票金额");
        }
        return result;
    }

    private void disposeEmpLevel(PcxBill view, JSONObject detailJson) {
        String empCode = detailJson.getString("empCode");
        if(StringUtil.isNotEmpty(empCode) &&
                (!detailJson.containsKey("budLevel") ||
                StringUtil.isEmpty(detailJson.getString("budLevel")))){
            detailJson.put("empType","inner");
            detailJson.put("budLevel", getEmpLevels(view, empCode));
        }
    }

    private void disposeEmpInfo(PcxBill view, JSONObject detailJson, String expDetailCode) {

        JSONArray empInfo = detailJson.getJSONArray("empInfo");
        if(Objects.nonNull(empInfo) && !empInfo.isEmpty()){
            List<EmpInfo> empInfos = JSON.parseArray(JSON.toJSONString(empInfo), EmpInfo.class);
            EmpInfo info = empInfos.get(0);
            detailJson.put("empCode",info.getEmpCode());
            detailJson.put("empType",info.getEmpType());
            detailJson.put("empName",info.getEmpName());
            if (Objects.nonNull(info.getIsSubsidy())){
                detailJson.put("isSubsidy",info.getIsSubsidy());
            }
            if (Objects.equals(info.getEmpType(), PcxConstant.EMP_TYPE_INNER)){
                detailJson.put("isSubsidy",1);
            }
            if (StringUtil.isNotEmpty(info.getBudLevel())){
                detailJson.put("budLevel", info.getBudLevel());
            } else if ("inner".equals(info.getEmpType())) {
                detailJson.put("budLevel", getEmpLevels(view, info.getEmpCode()));
            }
        }else{
            //人员情况需要把detailJson中的本来的人员信息清空
            detailJson.put("empCode","");
            detailJson.put("empType","");
            detailJson.put("empName","");
            detailJson.put("budLevel","");
            detailJson.put("isSubsidy","");
            detailJson.remove("empInfo");
        }
    }

    private String getEmpLevels(PcxBill bill, String empCode){
        PcxEmployeeWithCostLevelQO pcxBaseDTO = new PcxEmployeeWithCostLevelQO();
        pcxBaseDTO.setAgyCode(bill.getAgyCode());
        pcxBaseDTO.setFiscal(Integer.valueOf(bill.getFiscal()));
        pcxBaseDTO.setMofDivCode(bill.getMofDivCode());
        pcxBaseDTO.setMadCodes(Collections.singletonList(empCode));
        List<PcxEmployeeWithCostLevelVO> result = pcxCostControlLevelService.getEmpsWithCostLevelByCodes(pcxBaseDTO);
        log.info("查询人员费控等级信息{}=>{}", JSON.toJSONString(pcxBaseDTO), JSON.toJSONString(result));
        if (CollectionUtils.isNotEmpty(result)){
            if (CollectionUtils.isNotEmpty(result.get(0).getPcxCostControlLevels()))
                return result.get(0).getPcxCostControlLevels().stream().map(PcxCostControlLevel::getCostControlCode).collect(Collectors.joining(","));
        }
        return "";
    }

    /**
     * 解析住宿费，前端保存的住宿费，多个人会放在empInfoList里面，这里解析成一个人一个住宿费明细
     * @param detailBase
     * @return
     */
    private List<PcxBillExpDetailTravel> analysisHotelDetail(PcxBill bill, PcxBillExpDetailBase detailBase,
                                                             String hotelGroup, int seq) {
        List<PcxBillExpDetailTravel> result = new ArrayList<>();
        PcxBillExpDetailTravel hotel = (PcxBillExpDetailTravel) detailBase;
        hotel.setHotelSeq(seq);
        long daysDifference = calculateHotelDays(hotel.getStartTime(), hotel.getFinishTime());
        hotel.setTravelDays((double)daysDifference);
        int index = 0;
        if (CollectionUtils.isNotEmpty(hotel.getEmpInfoList())){
            for (EmpInfo empInfo : hotel.getEmpInfoList()) {
                PcxBillExpDetailTravel item = new PcxBillExpDetailTravel();
                BeanUtils.copyProperties(hotel, item);
                item.setId(IDGenerator.id());
                item.setEmpType(empInfo.getEmpType());
                item.setEmpCode(empInfo.getEmpCode());
                item.setEmpName(empInfo.getEmpName());
                item.setBudLevel(empInfo.getBudLevel());
                if ("inner".equals(empInfo.getEmpType())){
                    item.setIsSubsidy(1);
                } else{
                    if (Objects.isNull(empInfo.getIsSubsidy())){
                        item.setIsSubsidy(0);
                    }else{
                        item.setIsSubsidy(empInfo.getIsSubsidy());
                    }
                }
                if (StringUtil.isEmpty(item.getBudLevel())){
                    item.setBudLevel(getEmpLevels(bill, item.getEmpCode()));
                }
                item.setHotelGroup(hotelGroup);
                //金额都给第一条明细上
                if (index != 0){
                    item.setInputAmt(BigDecimal.ZERO);
                    item.setCheckAmt(BigDecimal.ZERO);
                }
                index ++;
                result.add(item);
            }
        }else{
            result.add(hotel);
        }

        return result;
    }

    private long calculateHotelDays(String startTime, String finishTime) {
        return TravelTripProcessor.getDiffDays(startTime, finishTime);
    }

    private List<EcsSettlementVO> convertSettlement(List<PcxEcsSettl> settlList) {
        List<EcsSettlementVO> settlementVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settlList)){
            for (PcxEcsSettl ecsSettl : settlList) {
                EcsSettlementVO vo = new EcsSettlementVO();
                BeanUtils.copyProperties(ecsSettl, vo);
                settlementVOS.add(vo);
            }
        }
        return settlementVOS;
    }

    public EcsExpViewVO ecsExpView(UpdateEcsDetailAndPaymentQO updateQO, PcxBill view) {
        EcsExpViewVO vo = new EcsExpViewVO();

        //查询票的支付信息
        List<PcxEcsSettl> settlList = pcxEcsSettlDao.selectList(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getEcsBillId, updateQO.getEcsBillId())
                .eq(PcxEcsSettl::getBillId, updateQO.getBillId()));
        List<EcsSettlementVO> settlementVOS = convertSettlement(settlList);
        vo.setSettlList(settlementVOS);

        //如果是匹配的票，则查询费用明细，付款信息返回
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), updateQO.getEcsBillId());

        //没有匹配到费用类型的票直接返回空数据
        if (ecsRelList.stream().anyMatch(item->!StringUtil.isNotBlank(item.getExpenseTypeCode()))){
            return vo;
        }
        String expenseTypeCode = ecsRelList.get(0).getExpenseTypeCode();
        vo.setExpenseTypeCode(expenseTypeCode);
        List<PcxBasExpTypeVO> expTypeByCodes = getExpTypeByCodes(updateQO, Collections.singletonList(expenseTypeCode));
        vo.setExpenseTypeName(expTypeByCodes.get(0).getExpenseName());
        Map<String, String> expTypeParentMap = expTypeByCodes.stream().collect(Collectors.toMap(PcxBasExpTypeVO::getExpenseCode, PcxBasExpTypeVO::getParentCode));

        //如果是未匹配的票，
        if (ecsRelList.size() == 1 && ecsRelList.get(0).getIsConfirm().equals(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode())){
            unMatchEcsView(ecsRelList.get(0), vo, expTypeParentMap, view);
            return vo;
        }
        //匹配的票，查询出票关联的费用或者费用明细数据，返回给前端
        //住宿费需要特殊处理，把多个人在一起的组成empInfoList给前端
        if (ecsRelList.stream().allMatch(item->StringUtil.isNotEmpty(item.getExpenseId()))){
            //todo 处理费用
        }else{
            List<String> detailIds = ecsRelList.stream().map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(expTypeParentMap.get(expenseTypeCode));
            List<PcxBillExpDetailBase> detailBases = detailBean.listByExpenseCode(expTypeParentMap.get(expenseTypeCode), view);
            detailBases = detailBases.stream().filter(item->detailIds.contains(item.getId())).collect(Collectors.toList());
            if (expenseTypeCode.equals(PcxConstant.TRAVEL_DETAIL_3021102)){
                //住宿费的特殊处理，把同一个数据的住宿人合并成empInfo
                vo.setExpenseList(disposeHotelEcsView(detailBases));
            }else{
                vo.setExpenseList(JSONArray.parseArray(JSON.toJSONString(detailBases)));
            }
        }

        return vo;
    }


    public EcsExpViewVO ecsItemExpView(UpdateEcsQO updateQO, PcxBill view) {
        EcsExpViewVO vo = new EcsExpViewVO();

        //查询票的支付信息
        List<PcxEcsSettl> settlList = pcxEcsSettlDao.selectList(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getEcsBillId, updateQO.getEcsBillId())
                .eq(PcxEcsSettl::getBillId, updateQO.getBillId()));
        List<EcsSettlementVO> settlementVOS = convertSettlement(settlList);
        vo.setSettlList(settlementVOS);

        //如果是匹配的票，则查询费用明细，付款信息返回
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), updateQO.getEcsBillId());

        List<EcsItemExpense> itemExpenseList = new ArrayList<>();
        vo.setItemExpenseList(itemExpenseList);
        String expenseTypeCode = ecsRelList.get(0).getExpenseTypeCode();
        vo.setExpenseTypeCode(expenseTypeCode);
        vo.setExpenseTypeName(ecsRelList.get(0).getExpenseTypeName());
        //没有匹配到费用类型的票直接返回空数据
        if (ecsRelList.stream().anyMatch(item->!EcsExpOptService.isTransExpense(item))){
            List<String> sortEcsDetailId = ecsRelList.stream().map(PcxExpDetailEcsRel::getEcsDetailId).collect(Collectors.toList());
            Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = ecsRelList
                    .stream()
                    .collect(Collectors.groupingBy(rel ->
                            Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                    ));

            int index = 0;
            for (String ecsDetailId : sortEcsDetailId) {
                List<PcxExpDetailEcsRel> ecsRelListValue = ecsDetailMap.get(ecsDetailId);
                if (CollectionUtils.isNotEmpty(ecsRelListValue)){
                    PcxExpDetailEcsRel rel = ecsRelListValue.get(0);
                    EcsItemExpense item = new EcsItemExpense();
                    item.setEcsDetailId(rel.getEcsDetailId());
                    item.setItemName(rel.getItemName());
                    item.setEcsAmt(rel.getEcsAmt());
                    item.setExpenseList(disposeNoMatchEcs(rel.getEcsContent(), expenseTypeCode, index++, view, rel.getEcsAmt()));
                    itemExpenseList.add(item);
                }
            }
            return vo;
        }

        List<PcxBasExpTypeVO> expTypeByCodes = getExpTypeByCodes(updateQO, Collections.singletonList(expenseTypeCode));

        Map<String, String> expTypeParentMap = expTypeByCodes.stream().collect(Collectors.toMap(PcxBasExpTypeVO::getExpenseCode, PcxBasExpTypeVO::getParentCode));

        List<String> detailIds = ecsRelList.stream().map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
        BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(expTypeParentMap.get(expenseTypeCode));
        List<PcxBillExpDetailBase> detailBases = detailBean.listByExpenseCode(expTypeParentMap.get(expenseTypeCode), view);
        detailBases = detailBases.stream().filter(item->detailIds.contains(item.getId())).collect(Collectors.toList());

        Map<String, List<PcxExpDetailEcsRel>> itemMap = ecsRelList
                .stream()
                .collect(Collectors.groupingBy(rel ->
                        Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                ));
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : itemMap.entrySet()) {
            PcxExpDetailEcsRel rel = entry.getValue().get(0);
            EcsItemExpense item = new EcsItemExpense();
            item.setItemName(rel.getItemName());
            item.setEcsDetailId(rel.getEcsDetailId());
            item.setEcsAmt(rel.getEcsAmt());
            List<String> itemExpDetailIds = entry.getValue().stream()
                    .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
            List<PcxBillExpDetailBase> itemDetails = detailBases.stream()
                    .filter(detail->itemExpDetailIds.contains(detail.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemDetails)){
                //匹配的票，查询出票关联的费用或者费用明细数据，返回给前端
                //住宿费需要特殊处理，把多个人在一起的组成empInfoList给前端
                if (expenseTypeCode.equals(PcxConstant.TRAVEL_DETAIL_3021102)){
                    //住宿费的特殊处理，把同一个数据的住宿人合并成empInfo
                    item.setExpenseList(disposeHotelEcsView(itemDetails));
                }else{
                    item.setExpenseList(JSONArray.parseArray(JSON.toJSONString(itemDetails)));
                }
            }else{
                item.setExpenseList(new JSONArray());
            }
            itemExpenseList.add(item);
        }

        return vo;
    }

    private List<PcxExpDetailEcsRel> queryEcsRelList(String billId, String ecsBillId){
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, billId)
                .eq(StringUtil.isNotBlank(ecsBillId), PcxExpDetailEcsRel::getEcsBillId, ecsBillId));
        ecsRelList.forEach(item->{
            if (Objects.isNull(item.getEcsDetailId())){
                item.setEcsDetailId("");
                if (Objects.isNull(item.getEmpCode())){
                    item.setEmpCode("");
                }
            }
        });
        return ecsRelList;
    }

    private JSONArray disposeNoMatchEcs(String ecsContent, String expenseTypeCode, int index, PcxBill bill, BigDecimal ecsAmt) {
        if (StringUtil.isNotEmpty(ecsContent)){
            List<PcxBillExpDetailTravel> detailList = JSONArray.parseArray(ecsContent, PcxBillExpDetailTravel.class);
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021102, expenseTypeCode)){
                Map<String, List<PcxBillExpDetailTravel>> map = detailList.stream()
                        .collect(Collectors.groupingBy(this::getHotelGroupSeq));
                List<PcxBillExpDetailTravel> hotelList = new ArrayList<>();
                for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : map.entrySet()) {
                    List<EmpInfo> infoList = new ArrayList<>();
                    for (PcxBillExpDetailTravel item : entry.getValue()) {
                        infoList.addAll(item.getEmpInfo());
                    }
                    PcxBillExpDetailTravel detailTravel = entry.getValue().get(0);
                    detailTravel.setEmpInfoList(infoList);
                    hotelList.add(detailTravel);
                }
                return JSONArray.parseArray(JSON.toJSONString(hotelList));
            }else{
                for (PcxBillExpDetailTravel detailTravel : detailList) {
                    detailTravel.setEmpInfoList(detailTravel.getEmpInfo());
                }
                return JSONArray.parseArray(JSON.toJSONString(detailList));
            }


        }else{
            //如果没有解析出费用明细，返回给前端一个空的
            if (StringUtil.isNotEmpty(expenseTypeCode) && index == 0){
                PcxBillExpDetailTravel empty = new PcxBillExpDetailTravel();
                empty.setStartTime("");
                empty.setEmpCode("");
                empty.setInputAmt(ecsAmt);
                return JSONArray.parseArray((JSON.toJSONString(Collections.singletonList(empty))));
            }else{
                return new JSONArray();
            }
        }
    }

    /**
     * 把住宿费进行分组，根据hotelGroup分组，一个hotelGroup代表一堆人员在一条住宿费明细上
     * @param detailBases
     * @return
     */
    private JSONArray disposeHotelEcsView(List<PcxBillExpDetailBase> detailBases) {
        List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(detailBases);
        Map<String, List<PcxBillExpDetailTravel>> hotelMap = travelDetailList.stream()
                .collect(Collectors.groupingBy(item->item.getHotelGroup() + "-" + item.getHotelSeq()));
        List<PcxBillExpDetailTravel> hotelList = new ArrayList<>();
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : hotelMap.entrySet()) {
            List<PcxBillExpDetailTravel> value = entry.getValue();
            PcxBillExpDetailTravel baseHotel = value.get(0);
            List<EmpInfo> empInfoList = value.stream().map(item -> {
                EmpInfo info = new EmpInfo();
                info.setEmpCode(item.getEmpCode());
                info.setEmpName(item.getEmpName());
                info.setEmpType(item.getEmpType());
                info.setBudLevel(item.getBudLevel());
                return info;
            }).collect(Collectors.toList());
            baseHotel.setEmpInfoList(empInfoList);
            hotelList.add(baseHotel);
        }
        return JSONArray.parseArray(JSON.toJSONString(hotelList));
    }

    private void unMatchEcsView(PcxExpDetailEcsRel ecsRel, EcsExpViewVO vo,
                                Map<String, String> expTypeParentMap, PcxBill view) {
        // 如果匹配的费用明细，只是明细信息不全，则查询未匹配的票信息，从票关联表中的ecsContent中取
        // 如果没有匹配的费用明细，则让前端去选费用明细
        String ecsContent = ecsRel.getEcsContent();
        if (StringUtil.isEmpty(ecsContent)){
            vo.setExpenseList(new JSONArray());
            return;
        }
        PcxBillExpDetailBase entityDetailBean = ExpenseBeanUtil.getEntityDetailBean(expTypeParentMap.get(ecsRel.getExpenseTypeCode()));
        List<PcxBillExpDetailBase> pcxBillExpDetailBases = (List<PcxBillExpDetailBase>) JSON.parseArray(ecsContent, entityDetailBean.getClass());
        if (ecsRel.getExpenseTypeCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)){
            vo.setExpenseList(disposeHotelEcsView(pcxBillExpDetailBases));
        }else{
            vo.setExpenseList(JSONArray.parseArray(JSON.toJSONString(pcxBillExpDetailBases)));
        }
    }

    /**
     * 移动端添加或者更新无票的费用明细
     *   解析出费用明细列表
     *   如果是住宿费，则特殊解析
     *   解析出附件关联信息
     *   如果是住宿费，附件关联信息中存储的是hotelGroup
     *   查询出票所有的费用明细，如果是更新，则把历史的需要删除的明细找出来
     *   如果添加的或者更新的费用明细时城市间交通费或者住宿费，则重新规划行程，重新计算补助
     *   把费用金额进行更新，把报销单金额进行更新
     *   事务中保存所有数据，删除所有需要删除的数据
     * @param updateQO
     * @param view
     */
    public void updateNoEcsDetail(UpdateNoEcsDetailQO updateQO, PcxBill view) {
        if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021103, updateQO.getExpenseTypeCode())
                || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021104, updateQO.getExpenseTypeCode())){
            throw new RuntimeException("系统不支持手动添加补助");
        }
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(updateQO.getExpenseList());

        //解析出费用明细列表
        //如果是住宿费，则特殊解析
        Triple<List<PcxBillExpDetailBase>, List<PcxBillOuterEmp>, List<PcxBillOuterEmp>>  triple = analysisDetailList(jsonArray, view,
                updateQO.getExpenseTypeCode(), BillExpDetailSourceEnum.MANUAL.getCode());
        List<PcxBillExpDetailBase> newDetailList = triple.getLeft();
        if (updateQO.isOnlySubsidy()){
            List<String> collect = newDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).distinct().collect(Collectors.toList());
            if (collect.size() > 1 || !Objects.equals(PcxConstant.TRAVEL_DETAIL_3021102, collect.get(0))){
                throw new RuntimeException("仅报补助只能添加住宿费明细");
            }
        }
        if (updateQO.isOnlySubsidy() && !view.getOnlySubsidy()){
            view.setOnlySubsidy(updateQO.isOnlySubsidy());
        }
        List<PcxBillOuterEmp> addOuterEmp = triple.getMiddle();
        List<PcxBillOuterEmp> updateOuterEmp = triple.getRight();
        Map<String, PcxBillOuterEmp> outerUpdateSubsidy = updateOuterEmp.stream().collect(Collectors.toMap(PcxBillOuterEmp::getEmpCode, Function.identity(), (key1, key2)->key1));
        //解析出附件关联信息
        List<PcxBillExpAttachRel> attachRelList = analysisDetailRelList(newDetailList, updateQO.getAttachList());

        //查询出报销单的费用数据
        List<PcxBillExpBase> billExpList = queryBillExpBaseList(view);
        List<PcxBillExpDetailBase> oldDetailList = queryBillExpDetailList(view);
        List<PcxBillExpDetailBase> delOldDetailList = new ArrayList<>();
        List<PcxBillTripCityDay> tripCityDays = new ArrayList<>();
        List<PcxBillTripSegment> tripSegments = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        boolean updateOuterSubsidy = !outerUpdateSubsidy.isEmpty();
        //如果编辑票修改了外部人员的是否有补助，则把外部人员所有的明细是否有补助字段都进行更新
        if (updateOuterSubsidy){
            disposeOuterUpdateSubsidy(outerUpdateSubsidy, oldDetailList);
        }

        boolean isAdd = true;
        String relId = "";
        String standStatusFlag = SaveEcsExpenseDTO.STAND_RESULT_DETAIL;
        boolean isQuota = updateQO.isQuota();
        if (StringUtil.isNotEmpty(updateQO.getDetailId())){
            relId = updateQO.getDetailId();
            isAdd = false;
            PcxBillExpDetailBase detailBase = oldDetailList.stream()
                    .filter(item -> item.getId().equals(updateQO.getDetailId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(detailBase)){
                throw new RuntimeException("根据费用明细id未查询到历史数据");
            }
            if (detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)){
                List<PcxBillExpDetailBase> hotelDetailList = oldDetailList.stream()
                        .filter(item -> item.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102))
                        .collect(Collectors.toList());
                List<PcxBillExpDetailTravel> travelDetailList = getTravelDetailList(hotelDetailList);
                PcxBillExpDetailTravel baseHotel = (PcxBillExpDetailTravel) detailBase;
                //如果是住宿费，附件关联信息中存储的是hotelGroup
                relId = baseHotel.getHotelGroup();
                standStatusFlag = SaveEcsExpenseDTO.STAND_RESULT_HOTEL;
                List<PcxBillExpDetailTravel> sameHotelList = travelDetailList.stream().filter(item -> item.getHotelGroup().equals(baseHotel.getHotelGroup())).collect(Collectors.toList());
                delOldDetailList.addAll(sameHotelList);
            }else{
                delOldDetailList.add(detailBase);
            }
            isQuota = oldIsQuota(detailBase);
        }
        if (isQuota){
            for (PcxBillExpDetailBase detailBase : newDetailList) {
                detailBase.setField06("isQuota");
            }
            for (PcxBillExpAttachRel attachRel : attachRelList) {
                attachRel.setEcsBillType(EcsEnum.BillType.QUOTA.getCode());
            }
        }

        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息
        boolean isDoTrip = false;
        MatchTripResult matchTripResult = null;
        List<PcxBillExpDetailBase> reTripDelDetail = new ArrayList<>();
        //如果是城市间交通费或者住宿费，则重新生成整个报销单的行程信息

        TripAndEcsIntercityTraffic judgeResult = judgeTripAndEcsIntercityTraffic(newDetailList);
        if (judgeResult.tripDetail){
            //没有票生成的城市间交通费
            //当前报销单的行程是住宿费行程，并且添加的票是住宿费，则按照住宿费理行程，系统补充的大交通不需要删除，系统补充的住宿可以删除
            //添加的是城市间交通费，就直接理票
            //是修改的系统补充的城市间交通费，则老的会被删除掉，剩余的直接理票
            boolean isHotelTrip = isHotelTrip(view);
            DoTripResultDTO tripResult = doMatchTrip(newDetailList, delOldDetailList, oldDetailList, view.getClaimantCode(), isHotelTrip);
            isDoTrip = tripResult.isDoTrip();
            reTripDelDetail = tripResult.getReTripDelDetail();
            matchTripResult = tripResult.getMatchTripResult();
        }
        //把历史费用明细的金额从费用上和报销单上去掉
        delOldDetailList.addAll(reTripDelDetail);

        CollectTripDto tripDto = collectTrip(updateQO, matchTripResult);
        List<String> oldNeedDelDetailIds  = delOldDetailList.stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList = oldDetailList.stream().filter(item->!oldNeedDelDetailIds.contains(item.getId())).collect(Collectors.toList());
        needSaveOrUpdateDetailList.addAll(newDetailList);
        //行程补充的无票费用明细
        if (CollectionUtils.isNotEmpty(tripDto.getNoEcsDetailBaseList())){
            needSaveOrUpdateDetailList.addAll(tripDto.getNoEcsDetailBaseList());
        }
        List<PcxBillExpStandResult> standResultList = new ArrayList<>();
        CalculateSubsidyResult calculateSubsidyResult = new CalculateSubsidyResult();
        if (isDoTrip){
            //如果有行程信息，则根据行程对应的明细中的住宿费去计算补助
            calculateSubsidyResult = calculateSubsidyByHotel(view, tripDto, needSaveOrUpdateDetailList, standResultList, view.getClaimantCode(), tripCityDays);
            if (CollectionUtils.isNotEmpty(calculateSubsidyResult.getSubsidyList())){
                needSaveOrUpdateDetailList.addAll(calculateSubsidyResult.getSubsidyList());
            }
        }else if (updateOuterSubsidy){
            //如果没有重新理行程，并且外部人员的是否有补助还发生了变化，则把外部人员的补助都删掉，如果有必要的话，重新生成补助
            List<PcxBillExpDetailTravel> travels = getTravelDetailList(oldDetailList);
            List<String> delSubsidyIds = new ArrayList<>();
            List<PcxBillExpDetailTravel> delSubsidy = travels.stream().filter(item->isSubsidyDetail(item)
                            && Objects.equals(item.getEmpType(), PcxConstant.EMP_TYPE_OUTER)
                            && outerUpdateSubsidy.containsKey(item.getEmpCode()))
                    .peek(item->{delSubsidyIds.add(item.getId());})
                    .collect(Collectors.toList());
            delOldDetailList.addAll(delSubsidy);
            needSaveOrUpdateDetailList = needSaveOrUpdateDetailList.stream()
                    .filter(item->!delSubsidyIds.contains(item.getId()))
                    .collect(Collectors.toList());
            List<PcxBillOuterEmp> subsidyOuter = new ArrayList<>();
            Set<String> subsidyOuterEmpCode = new HashSet<>();
            for (Map.Entry<String, PcxBillOuterEmp> entry : outerUpdateSubsidy.entrySet()) {
                if (entry.getValue().getIsSubsidy() == 1){
                    subsidyOuter.add(entry.getValue());
                    subsidyOuterEmpCode.add(entry.getKey());
                }
            }
            if (CollectionUtils.isNotEmpty(subsidyOuter)){
                List<PcxBillExpDetailBase> outerTravelDetail = needSaveOrUpdateDetailList.stream().filter(item -> subsidyOuterEmpCode.contains(item.getEmpCode())
                                && Objects.equals(item.getExpDetailCode(), PcxConstant.TRAVEL_DETAIL_3021101))
                        .collect(Collectors.toList());
                List<PcxBillExpDetailTravel> newSubsidy = generateSubsidy(subsidyOuter, view, outerTravelDetail, standResultList);
                needSaveOrUpdateDetailList.addAll(newSubsidy);
            }
        }
        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), null);
        //查询出明细对于的费用代码
        List<String> otherExpTypeCode = delOldDetailList.stream().map(PcxBillExpDetailBase::getExpDetailCode).collect(Collectors.toList());
        BigDecimal unMatchEcsAmt = getUnMatchEcsAmt(ecsRelList);
        Map<String, String> newParentMap = calculateBillAndExpAmtAndCollectExpTypeMap(updateQO, view, billExpList, needSaveOrUpdateDetailList, otherExpTypeCode, unMatchEcsAmt);
        //删除旧的行程，删除旧的补充明细
        //保存新的行程，保存新的补充明细
        //把付款信息保存
        //把付款信息和费用明细同步给ecs
        //删除历史的补助信息
        List<PcxBillTravelFellows> fellows = disposeTravelMessage(needSaveOrUpdateDetailList, billExpList);
        List<PcxBillExpStandResult> delStandResultList = getDelDetailStandResult(delOldDetailList);
        standResultList.addAll(calculateNewDetailStandResult(newDetailList));

        collectTripSegment(view, tripSegments, amtApportions, amtApportionDepartments, getBillParamsGet(billExpList, needSaveOrUpdateDetailList, tripDto, ecsRelList, view.getId(), isDoTrip));

        List<PcxBillExpAttachRel> allAttachRelList = getAttachRelList(view, getDelExtraAttachRelPredicate(relId));
        allAttachRelList.addAll(attachRelList);
        List<PcxBillExtraAttach> allExtraAttachList = queryExtraAttachList(view.getId());
        SaveEcsExpenseDTO dto = SaveEcsExpenseDTO.builder()
                .bill(view)
                .baseExpList(billExpList)
                .tripDto(tripDto)
                .insertOrUpdateDetailList(needSaveOrUpdateDetailList)
                .isDoTrip(isDoTrip)
                .parentMap(newParentMap)
                .addAttachRelList(attachRelList)
                .allAttachRelList(allAttachRelList)
                .allEcsList(ecsRelList)
                .updateQO(updateQO)
                .isAdd(isAdd)
                .relId(relId)
                .delOldDelDetail(delOldDetailList)
                .fellowsList(fellows)
                .standList(standResultList)
                .delStandResultList(delStandResultList)
                .standResultFlag(standStatusFlag)
                .tripCityDays(tripCityDays)
                .addOuterEmp(addOuterEmp)
                .updateOuterEmp(updateOuterEmp)
                .tripSegments(tripSegments)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .delExtraList(calculateSubsidyResult.getDelExtraList())
                .delExtraAttachList(calculateSubsidyResult.getDelExtraAttachList())
                .allExtraAttachList(allExtraAttachList)
                .build();
        //保存新生成的补助信息
        ecsExpTransService.updateNoEcsDetail(dto);
    }

    public static boolean oldIsQuota(PcxBillExpDetailBase detailBase) {
        return Objects.equals("isQuota", detailBase.getField06());
    }

    private List<PcxBillExtraAttach> queryExtraAttachList(String billId) {
        return pcxBillExtraAttachDao.selectList(Wrappers.lambdaQuery(PcxBillExtraAttach.class)
                .eq(PcxBillExtraAttach::getBillId, billId));
    }

    private Predicate<PcxBillExpAttachRel> getDelExtraAttachRelPredicate(String relId){
        return (a)-> Objects.equals(a.getRelType(), 1) || !Objects.equals(a.getRelId(), relId);
    }

    private TripAndEcsIntercityTraffic judgeTripAndEcsIntercityTraffic(List<PcxBillExpDetailBase> newDetailList) {
        //是否有城市间交通费，或者改签费
        boolean tripDetail = false;
        //是否有票生成的或手动添加的城市间交通费或改签费,这个会影响到住宿费理行程
        boolean ecsIntercityTraffic = false;
        if (CollectionUtils.isEmpty(newDetailList)){
            return new TripAndEcsIntercityTraffic(tripDetail, ecsIntercityTraffic);
        }
        for (PcxBillExpDetailBase detailBase : newDetailList) {
            if (tripDetail && ecsIntercityTraffic){
                break;
            }
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021101, detailBase.getExpDetailCode())
                    || Objects.equals(PcxConstant.TRAVEL_DETAIL_3021110, detailBase.getExpDetailCode())){
                tripDetail = true;
                if (Objects.equals(BillExpDetailSourceEnum.ECS.getCode(), detailBase.getSource())){
                    ecsIntercityTraffic = true;
                }
            }
            if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021102, detailBase.getExpDetailCode())){
                tripDetail = true;
            }
        }
        return new TripAndEcsIntercityTraffic(tripDetail, ecsIntercityTraffic);
    }

    @Data
    @AllArgsConstructor
    private static class TripAndEcsIntercityTraffic{
        private Boolean tripDetail;
        private Boolean ecsIntercityTraffic;
    }

    //通过明细统计出费用金额，和报销单金额
    private void calculateBillAndExpAmt(PcxBill view, List<PcxBillExpBase> billExpList,
                                        List<PcxBillExpDetailBase> needSaveOrUpdateDetailList,
                                        Map<String, String> newParentMap, BigDecimal unMatchEcsAmt) {

        Map<String, BigDecimal> expBaseTypeForAmt = needSaveOrUpdateDetailList.stream()
                .collect(Collectors.groupingBy(item -> newParentMap.get(item.getExpDetailCode()),
                        Collectors.mapping(PcxBillExpDetailBase::getInputAmt,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        BigDecimal total = BigDecimal.ZERO;
        for (PcxBillExpBase expBase : billExpList) {
            BigDecimal expBaseAmt = expBaseTypeForAmt.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO);
            expBase.setInputAmt(expBaseAmt);
            expBase.setCheckAmt(expBaseAmt);
            total = total.add(expBaseAmt);
        }
        view.setInputAmt(total.add(unMatchEcsAmt));
        view.setCheckAmt(total.add(unMatchEcsAmt));
    }

    private List<PcxBillExpAttachRel> analysisDetailRelList(List<PcxBillExpDetailBase> newDetailList, List<DetailAttachRelQO> attachList) {
        List<PcxBillExpAttachRel> relList = new ArrayList<>();
        if (CollectionUtils.isEmpty(attachList)){
            return relList;
        }
        PcxBillExpDetailBase detailBase = newDetailList.get(0);
        String relId = getDetailAttachRelKey(detailBase);
        for (DetailAttachRelQO attachRelQO : attachList) {
            PcxBillExpAttachRel rel = new PcxBillExpAttachRel();
            rel.setId(IDGenerator.id());
            rel.setRelType(PcxExpAttachRelType.EXP_DETAIL.getCode());
            rel.setBillId(detailBase.getBillId());
            rel.setRelId(relId);
            rel.setAttachId(attachRelQO.getFileId());
            rel.setFileName(attachRelQO.getFileName());
            rel.setFiscal(detailBase.getFiscal());
            rel.setAgyCode(detailBase.getAgyCode());
            rel.setMofDivCode(detailBase.getMofDivCode());
            relList.add(rel);
        }
        return relList;
    }

    private String getDetailAttachRelKey(PcxBillExpDetailBase detailBase){
        String relId = detailBase.getId();
        if (detailBase.getExpDetailCode().equals(PcxConstant.TRAVEL_DETAIL_3021102)){
            PcxBillExpDetailTravel hotelDetail = (PcxBillExpDetailTravel) detailBase;
            relId = hotelDetail.getHotelGroup();
        }
        return relId;
    }


    /**
     * 更新票关联的附件
     * 如果是未匹配的票，则重新拉取电子凭证票数据，生成费用明细给前端
     * @param updateEcsAttachQO
     * @param view
     * @return
     */
    public UpdateAttachViewVO updateEcsAttach(UpdateEcsAttachQO updateEcsAttachQO, PcxBill view) {
        List<PcxExpDetailEcsRel> allEcsRelList = queryEcsRelList(view.getId(), null);
        List<PcxBillExpAttachRel> allAttachRelList = getAttachRelList(view, (a) -> true);
        List<PcxBillExpAttachRel> addAttachList = analysisAttachRelList(updateEcsAttachQO.getEcsBillClassRelList(), view, updateEcsAttachQO.getEcsBillId());
        List<PcxBillExpAttachRel> ecsAttach = allAttachRelList.stream().filter(item -> updateEcsAttachQO.getEcsBillId().equals(item.getRelId())).collect(Collectors.toList());
        allAttachRelList.removeAll(ecsAttach);
        ecsExpTransService.updateEcsAttach(view, updateEcsAttachQO.getEcsBillId(), addAttachList, allAttachRelList, allEcsRelList);

        List<PcxExpDetailEcsRel> ecsRelList = queryEcsRelList(view.getId(), updateEcsAttachQO.getEcsBillId());
        UpdateAttachViewVO result = new UpdateAttachViewVO();
        if (ecsRelList.stream().anyMatch(EcsExpOptService::isTransExpense)){
            return result;
        }
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        ExpInvoiceQO.EscBillQO bill = new ExpInvoiceQO.EscBillQO();
        bill.setBillId(updateEcsAttachQO.getEcsBillId());
        bill.setBillAttachType(0);
        invoiceQO.setBillList(Collections.singletonList(bill));
        invoiceQO.setFiscal(view.getFiscal());
        invoiceQO.setAgyCode(view.getAgyCode());
        invoiceQO.setMofDivCode(view.getMofDivCode());
        List<InvoiceDtoWrapper> invoiceDtoWrappers = ecsProcessService.analysisEcs(invoiceQO, view.getItemCode());
        InvoiceDtoWrapper wrapper = invoiceDtoWrappers.get(0);
        List<PcxExpDetailEcsRel> ecsRel = collectEcsRel(wrapper, addAttachList, invoiceQO, Lists.newArrayList());
        String detailContent = "[]";
        if (wrapper.getFlag().equals(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode())){
            detailContent = JSON.toJSONString(wrapper.getExpDetailList());
            ecsRel.get(0).setEcsContent(detailContent);
        }
        for (PcxExpDetailEcsRel rel : ecsRel) {
            rel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode());
        }
        ecsExpTransService.updateEcsRel(view.getId(),  ecsRel);
        result.setIsChange(true);
        result.setExpenseTypeCode(ecsRel.get(0).getExpenseTypeCode());
        result.setExpenseTypeName(ecsRel.get(0).getExpenseTypeName());
        List<EcsItemExpense> itemExpenseList = new ArrayList<>();
        result.setItemExpenseList(itemExpenseList);
        int index = 0;
        for (PcxExpDetailEcsRel rel : ecsRelList) {
            EcsItemExpense item = new EcsItemExpense();
            item.setEcsDetailId(rel.getEcsDetailId());
            item.setItemName(rel.getItemName());
            item.setEcsAmt(rel.getEcsAmt());
            item.setExpenseList(disposeNoMatchEcs(rel.getEcsContent(), rel.getExpenseTypeCode(), index++, view, rel.getEcsAmt()));
            itemExpenseList.add(item);
        }
        return result;
    }
}
