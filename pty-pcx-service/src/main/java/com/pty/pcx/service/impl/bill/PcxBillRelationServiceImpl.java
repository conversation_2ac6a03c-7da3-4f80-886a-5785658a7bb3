package com.pty.pcx.service.impl.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.api.bill.PcxBillRelationService;
import com.pty.pcx.common.enu.ApplyControlEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillRelationDao;
import com.pty.pcx.entity.bill.PcxBillRelation;
import com.pty.pcx.qo.bill.PcxBillRelationQO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 单据关联表(PcxBillRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 16:21:00
 */
@Service("pcxBillRelationService")
@Indexed
public class PcxBillRelationServiceImpl implements PcxBillRelationService {

    @Autowired
    private PcxBillRelationDao pcxBillRelationDao;

    @Override
    public List<PcxBillRelation> selectByBillId(String billId) {
        return pcxBillRelationDao.selectByBillId(billId);
    }

    @Override
    public List<PcxBillRelation> selectByBillIds(List<String> billIds, String relBillFuncCode) {
        return pcxBillRelationDao.selectByBillIds(billIds, relBillFuncCode);
    }

    @Override
    public List<PcxBillRelation> selectByRelBillIds(List<String> billIds) {
        return pcxBillRelationDao.selectByRelBillIds(billIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg save(PcxBillRelationQO qo) {
        CheckMsg checkMsg = validParam(qo);
        if(!checkMsg.isSuccess()){
           return CheckMsg.fail().setMsgInfo(checkMsg.getMsgInfo());
        }
        // 根据年度、单位、区划、租户、billId 和 billFuncCode 删除；
        LambdaQueryWrapper<PcxBillRelation> deletedWrapper = Wrappers.lambdaQuery(PcxBillRelation.class)
                .eq(PcxBillRelation::getFiscal, qo.getFiscal())
                .eq(PcxBillRelation::getAgyCode, qo.getAgyCode())
                .eq(PcxBillRelation::getMofDivCode, qo.getMofDivCode())
                .eq(PcxBillRelation::getBillId, qo.getBillId())
                .eq(PcxBillRelation::getRelBillFuncCode, qo.getRelBillFuncCode())
                .eq(PcxBillRelation::getTenantId, StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId())
                .eq(PcxBillRelation::getBillFuncCode, qo.getBillFuncCode());
        pcxBillRelationDao.delete(deletedWrapper);
        PcxBillRelation pcxBillRelation = new PcxBillRelation();
        if(!ObjectUtils.isEmpty(qo.getIsCancel()) && PubConstant.LOGIC_TRUE == qo.getIsCancel()){
            return CheckMsg.success().setMsgInfo("保存成功");
        }
        BeanUtils.copyProperties(qo,pcxBillRelation);
        pcxBillRelation.setIsVirtual(assembleIsVirtual(qo.getApplyCtrlCode()));
        pcxBillRelation.setId(StringUtil.getUUID());
        pcxBillRelation.setCreator(qo.getUserCode());
        pcxBillRelation.setCreatorName(qo.getUserName());
        pcxBillRelation.setCreatedTime(DateUtil.getCurDate());
        pcxBillRelationDao.insert(pcxBillRelation);
        return CheckMsg.success().setMsgInfo("保存成功").setData(pcxBillRelation);
    }

    @Override
    public PcxBillRelation selectApplyRelByQO(PcxBillRelationQO qo) {
        LambdaQueryWrapper<PcxBillRelation> queryWrapper = Wrappers.lambdaQuery(PcxBillRelation.class);
        queryWrapper.eq(PcxBillRelation::getBillId, qo.getBillId());
        queryWrapper.eq(PcxBillRelation::getBillFuncCode, qo.getBillFuncCode());
        queryWrapper.eq(PcxBillRelation::getRelBillFuncCode, qo.getRelBillFuncCode());
        queryWrapper.eq(PcxBillRelation::getFiscal, qo.getFiscal());
        queryWrapper.eq(PcxBillRelation::getAgyCode, qo.getAgyCode());
        queryWrapper.eq(PcxBillRelation::getMofDivCode, qo.getMofDivCode());
        if (ObjectUtils.isNotEmpty(qo.getTenantId())) {
            queryWrapper.eq(PcxBillRelation::getTenantId, qo.getTenantId());
        } else {
            queryWrapper.eq(PcxBillRelation::getTenantId, PtyContext.getTenantId());
        }
        return pcxBillRelationDao.selectOne(queryWrapper);
    }

    private Integer assembleIsVirtual(String applyCtrlCode) {
        if (StringUtil.isNotEmpty(applyCtrlCode) && ApplyControlEnum.APPLY_BEFORE.getCode().equals(applyCtrlCode)) {
            return PubConstant.LOGIC_FALSE;
        }
        return PubConstant.LOGIC_TRUE;
    }

    private CheckMsg validParam(PcxBillRelationQO qo) {
        if (StringUtil.isEmpty(qo.getMofDivCode())) {
            return CheckMsg.fail("区划编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getAgyCode())) {
            return CheckMsg.fail("单位编码不能为空");
        }
        if (StringUtil.isEmpty(qo.getFiscal())) {
            return CheckMsg.fail("年度不能为空");
        }
        if (StringUtil.isEmpty(qo.getBillId())){
            return CheckMsg.fail("主单据不能为空");
        }
        if (StringUtil.isEmpty(qo.getRelBillId())){
            return CheckMsg.fail("关联单据不能为空");
        }
        return CheckMsg.success();
    }
}
