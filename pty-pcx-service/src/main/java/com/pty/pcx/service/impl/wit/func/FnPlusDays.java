package com.pty.pcx.service.impl.wit.func;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
public class FnPlusDays extends BFunction {
    public static final String FUNC_NAME = "plusDays";

    @Override
    public String getName() {
        return FUNC_NAME;
    }

    @Override
    public String desc() {
        return "日期偏移量计算,获取前几天或者后几天,支持多个日期一起偏移，plusDays(日期, 偏移天数)";
    }

    @Override
    public String methodName() {
        return "日期偏移";
    }
    public String method() {
        return "plusDays(date, days)";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        Object date = arg1.getValue(env);
        Object days = arg2.getValue(env);
        if (date instanceof String){
            if (date == null) {
                throw new IllegalArgumentException("参数date不能为空！");
            }
            if (days == null) {
                throw new IllegalArgumentException("参数days不能为空！");
            }
            DateTime transDate = DateUtil.parse(date.toString());
            int anInt = Integer.parseInt(days.toString());
            DateTime plusData = DateUtil.offsetDay(transDate, anInt);
            String format = DateUtil.formatDate(plusData);
            return new AviatorString(format);
        }

        if (date instanceof Collection){
            List<String> dates = Lists.newArrayList();
            for (Object o : (Collection) date) {
                if (o instanceof String){
                    DateTime transDate = DateUtil.parse(o.toString());
                    int anInt = Integer.parseInt(days.toString());
                    DateTime plusData = DateUtil.offsetDay(transDate, anInt);
                    String format = DateUtil.formatDate(plusData);
                    dates.add( format);
                }
            }
            return AviatorRuntimeJavaType.valueOf(dates);
        }
        return AviatorRuntimeJavaType.valueOf(date);
    }
}
