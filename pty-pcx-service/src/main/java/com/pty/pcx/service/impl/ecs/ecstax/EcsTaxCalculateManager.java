package com.pty.pcx.service.impl.ecs.ecstax;

import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EcsTaxCalculateManager {

    @Autowired
    private List<EcsTaxCalculate> ecsTaxCalculates;

    private Map<String, EcsTaxCalculate> disposeMap = new HashMap<>();

    @PostConstruct
    public void init() {
        ecsTaxCalculates.forEach(ecsTaxCalculate -> {
            for (String ecsBillType : ecsTaxCalculate.ecsBillType()) {
                disposeMap.put(ecsBillType, ecsTaxCalculate);
            }
        });
    }

    private EcsTaxCalculate defaultCalculate = new EcsTaxCalculate() {
        @Override
        public List<String> ecsBillType() {
            return Collections.emptyList();
        }

        @Override
        public void childTaxCalculate(PcxExpDetailEcsRel ecsRel) {

        }
    };

    public void taxCalculate(List<PcxExpDetailEcsRel> ecsRelList) {
        for (PcxExpDetailEcsRel rel : ecsRelList) {
            EcsTaxCalculate ecsTaxCalculate = disposeMap.getOrDefault(rel.getEcsBillType(), defaultCalculate);
            ecsTaxCalculate.calculateTax(rel);
        }
    }
}
