package com.pty.pcx.service.impl.ecs;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pct.entity.vo.PctBillInfoVO;
import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillAmtApportionService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.dao.bas.PcxBasExpTypeDao;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dao.bill.labour.PcxBillExpDetailLabourDao;
import com.pty.pcx.dao.contract.PcxBillContractRelDao;
import com.pty.pcx.dao.ecs.PcxEcsComparedResultDao;
import com.pty.pcx.dao.labour.PcxLabourInfoDao;
import com.pty.pcx.dto.PcxBaseDTO;
import com.pty.pcx.dto.ecs.CommonCheckExpenseTypeAmtDTO;
import com.pty.pcx.dto.ecs.EcsMsgDTO;
import com.pty.pcx.dto.ecs.UpdateEcsBillDTO;
import com.pty.pcx.dto.ecs.file.PcxFileContractDto;
import com.pty.pcx.dto.ecs.settlement.EcsBillSettleDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.ecs.impl.EcsBillExternalServiceImpl;
import com.pty.pcx.ecs.impl.EcsDtoTransHelper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.labour.PcxBillExpDetailLabour;
import com.pty.pcx.entity.bill.labour.PcxLabourInfo;
import com.pty.pcx.entity.contract.PcxBillContractRel;
import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import com.pty.pcx.mad.IMadEmployeeExternalService;
import com.pty.pcx.pct.IPctExternalService;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.qo.bill.apportion.CommonApportionDeptQO;
import com.pty.pcx.qo.bill.apportion.CommonApportionQO;
import com.pty.pcx.qo.bill.apportion.CommonUpdateApportionQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.*;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.service.impl.bill.PcxBillExpTrainingServiceImpl;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.service.impl.ecs.dto.SaveEcsCommonDTO;
import com.pty.pcx.service.impl.ecs.ecstax.EcsTaxCalculateManager;
import com.pty.pcx.service.impl.ecs.trip.EcsItemNameHelper;
import com.pty.pcx.vo.ecs.*;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pcx.vo.training.LabourExpDetailVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.pty.pcx.service.impl.bill.handler.impl.BillExpenseDetailService4Travel.disposeCheckStatus;
import static com.pty.pcx.service.impl.ecs.EcsExpOptService.oldIsQuota;

//通用费用报销
@Indexed
@Service
@Slf4j
public class EcsExpCommonService {
    @Resource
    private PcxBillExpTrainingServiceImpl pcxBillExpTrainingService;
    @Resource
    private PcxLabourInfoDao pcxLabourInfoDao;
    @Resource
    private PcxBillExpDetailLabourDao pcxBillExpDetailLabourDao;
    @Resource
    private IPcxBasItemExpService basItemExpService;
    @Resource
    private EcsExpTransService ecsExpTransService;
    @Resource
    private PcxExpDetailEcsRelDao expDetailEcsRelDao;
    @Resource
    private IMadEmployeeExternalService madEmployeeExternalService;
    @Resource
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;
    @Resource
    private PcxBasExpTypeDao pcxBasExpTypeDao;
    @Resource
    private PcxEcsSettlDao pcxEcsSettlDao;
    @Resource
    private IEcsBillExternalService ecsBillExternalService;
    @Resource
    private EcsTaxCalculateManager ecsTaxCalculateManager;
    @Resource
    private IPctExternalService pctExternalService;
    @Resource
    private PcxBillContractRelDao pcxBillContractRelDao;

    @Resource
    private PcxBillExpDetailCommonDao pcxBillExpDetailCommonDao;

    @Resource
    private PcxBillAmtApportionService pcxBillAmtApportionService;

    @Resource
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Resource
    private PcxBillExpCommonDao pcxBillExpCommonDao;

    @Resource
    private IBusinessRuleOptionService businessRuleOptionService;

    private boolean isPaperBill(PcxBill pcxBill) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxBill.getMofDivCode());
        paOptionQO.setAgyCode(pcxBill.getAgyCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxBill.getFiscal()));
        String value = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        return Objects.equals(PcxConstant.PrintBillType.PASTE_BILL, value);
    }

    private static final PcxBasExpType commonType = new PcxBasExpType();

    static {
        commonType.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        commonType.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
    }


    /**
     * 通用报销发起报销
     *
     * @param expenseQO
     * @param ecsMap
     * @return
     */
    public String saveBillAndExpense(StartExpenseQO expenseQO, Map<String, List> ecsMap) {
        //生成报销单
        //遍历所有wrapper整理出所有费用

        //获取事项关联的费用类型
        List<PcxBasItemExp> baseTypeList = getItemExpenseType(expenseQO);
        List<String> itemExpTypeCodeList = baseTypeList.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.toList());
        itemExpTypeCodeList.remove(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        //所有票关联费用类型
        List<PcxExpDetailEcsRel> allEcsRel = new ArrayList<>();
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        List<PcxEcsSettl> settlList = new ArrayList<>();
        List<PcxBillExpDetailCommon> detailList = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        //收集所有票生成的费用，并建立票与费用和明细的关联关系
        collectEcsRel(expenseQO, allEcsRel, ecsMap, attachRelList, settlList, itemExpTypeCodeList, detailList);
        //如果选了合同则建立合同关联关系
        PcxBillContractRel contractRel = extractContractRel(expenseQO, ecsMap.get(EcsBillExternalServiceImpl.ECS_FLAG_CONTRACT));

        //收集报销单的费用类型
        List<PcxBasExpType> expTypeList = collectEcsRelExpType(allEcsRel);

        //生成报销单的费用
        collectItemExpType(expTypeList, baseTypeList);
        //补充通用费用，没有关联费用类型的金额都挂到通用费用上面
        expTypeList.add(commonType);
        //创建报销单对象
        PcxBill bill = buildBill(expenseQO, expTypeList);

        disposeDetailDepartmentCode(bill, detailList);

        //根据费用类型生成对于的费用实体类对象
        List<PcxBillExpCommon> baseExpList = buildBaseExpByExpType(expTypeList, detailList, expenseQO);

        calculateTax(allEcsRel, detailList);

        //汇总费用金额，报销单金额
        collectBillAmt(bill, baseExpList, allEcsRel);

        collectApportion(bill, amtApportions, amtApportionDepartments, baseExpList);

        PcxBillRelation pcxBillRelation = null;
        if (Objects.nonNull(contractRel)) {
            //建立报销单和合同关联信息
            pcxBillRelation = buildBillRelation(expenseQO, bill, contractRel);
        }

        return ecsExpTransService.createCommonExpenseBill(bill, baseExpList, allEcsRel, attachRelList,
                settlList, contractRel, pcxBillRelation, detailList, amtApportions, amtApportionDepartments);
    }

    private void collectApportion(PcxBill bill, List<PcxBillAmtApportion> amtApportions,
                                  List<PcxBillAmtApportionDepartment> amtApportionDepartments,
                                  List<PcxBillExpCommon> baseExpList) {
        CommonUpdateApportionQO qo = new CommonUpdateApportionQO();
        qo.setFiscal(bill.getFiscal());
        qo.setAgyCode(bill.getAgyCode());
        qo.setMofDivCode(bill.getMofDivCode());

        CommonApportionQO amtApportion = new CommonApportionQO();
        qo.setApportionList(Collections.singletonList(amtApportion));
        amtApportion.setApportionAmt(bill.getInputAmt());
        amtApportion.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        amtApportion.setExpenseTypeName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
        CommonApportionDeptQO amtApportionDept = new CommonApportionDeptQO();
        amtApportionDept.setDepartmentRate(new BigDecimal(100));
        amtApportionDept.setDepartmentAmt(bill.getInputAmt());
        amtApportionDept.setDepartmentCode(bill.getDepartmentCode());
        amtApportionDept.setDepartmentName(bill.getDepartmentName());
        amtApportion.setDeptList(Collections.singletonList(amtApportionDept));
        for (PcxBillExpCommon pcxBillExpCommon : baseExpList) {
            pcxBillExpCommon.setDepartmentCode(bill.getDepartmentCode());
            pcxBillExpCommon.setDepartmentName(bill.getDepartmentName());
        }
        pcxBillAmtApportionService.commonBillApportion(bill, qo, amtApportionDepartments, amtApportions, baseExpList);
        bill.setApportionType(ApportionTypeEnum.BILL.getCode());
    }

    private void disposeDetailDepartmentCode(PcxBill bill, List<PcxBillExpDetailCommon> detailList) {
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (PcxBillExpDetailCommon common : detailList) {
                common.setDepartmentCode(bill.getDepartmentCode());
                common.setDepartmentName(bill.getDepartmentName());
            }
        }
    }

    private void collectItemExpType(List<PcxBasExpType> expTypeList, List<PcxBasItemExp> baseTypeList) {
        List<String> collect = expTypeList.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.toList());
        for (PcxBasItemExp pcxBasItemExp : baseTypeList) {
            if (!collect.contains(pcxBasItemExp.getExpenseCode())) {
                PcxBasExpType pcxBasExpType = new PcxBasExpType();
                pcxBasExpType.setExpenseCode(pcxBasItemExp.getExpenseCode());
                pcxBasExpType.setExpenseName(pcxBasItemExp.getExpenseName());
                expTypeList.add(pcxBasExpType);
            }
        }
    }

    private PcxBillContractRel extractContractRel(ExpInvoiceQO expenseQO, List list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.size() > 1) {
            throw new RuntimeException("只能选择一个合同");
        }
        PcxFileContractDto contractDto = (PcxFileContractDto) list.get(0);
        PctBillInfoVO pctBillInfoVO = pctExternalService.selectBillInfo(contractDto.getFileId());
        if (Objects.isNull(pctBillInfoVO)) {
            throw new RuntimeException("合同信息不存在");
        }
        PcxBillContractRel contractRel = new PcxBillContractRel();
        contractRel.setFiscal(expenseQO.getFiscal());
        contractRel.setAgyCode(expenseQO.getAgyCode());
        contractRel.setMofDivCode(expenseQO.getMofDivCode());
        contractRel.setTenantId(expenseQO.getTenantId());
        contractRel.setContractId(pctBillInfoVO.getBillId());
        contractRel.setContractNo(pctBillInfoVO.getBillNo());
        contractRel.setContractAmt(pctBillInfoVO.getAmount());
        contractRel.setContractName(pctBillInfoVO.getContractName());
        contractRel.setPartyAName(pctBillInfoVO.getPartyAName());
        contractRel.setPartyBName(pctBillInfoVO.getPartyBName());
        contractRel.setSignDate(pctBillInfoVO.getSignDate());
        return contractRel;
    }

    private void calculateTax(List<PcxExpDetailEcsRel> allEcsRel, List<PcxBillExpDetailCommon> detailCommons) {
        ecsTaxCalculateManager.taxCalculate(allEcsRel);
        Map<String, PcxBillExpDetailCommon> collect = detailCommons.stream().collect(Collectors.toMap(PcxBillExpDetailCommon::getId, Function.identity(), (key1, key2) -> key1));
        for (PcxExpDetailEcsRel rel : allEcsRel) {
            PcxBillExpDetailCommon detailCommon = collect.get(rel.getDetailId());
            if (Objects.nonNull(detailCommon)) {
                detailCommon.setTaxRate(rel.getTaxRate());
                detailCommon.setTaxAmt(rel.getTaxAmt());
            }
        }
    }

    private List<PcxBasExpType> collectEcsRelExpType(List<PcxExpDetailEcsRel> allEcsRel) {
        List<PcxBasExpType> result = new ArrayList<>();
        Set<String> expenseTypeCodeSet = new HashSet<>();
        allEcsRel.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                .forEach(item -> {
                    if (!expenseTypeCodeSet.contains(item.getExpenseTypeCode())) {
                        expenseTypeCodeSet.add(item.getExpenseTypeCode());
                        PcxBasExpType expType = new PcxBasExpType();
                        expType.setExpenseCode(item.getExpenseTypeCode());
                        expType.setExpenseName(item.getExpenseTypeName());
                        result.add(expType);
                    }
                });
        return result;
    }

    private void collectBillAmt(PcxBill bill, List<PcxBillExpCommon> baseExpList, List<PcxExpDetailEcsRel> allEcsRel) {
        Map<String, BigDecimal> inputAmtMap = new HashMap<>();
        Map<String, BigDecimal> checkAmtMap = new HashMap<>();
        for (PcxExpDetailEcsRel rel : allEcsRel) {
            String expTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();
            BigDecimal inputAmt = inputAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            inputAmtMap.put(expTypeCode, inputAmt.add(rel.getInputAmt()));
            BigDecimal checkAmt = checkAmtMap.getOrDefault(expTypeCode, BigDecimal.ZERO);
            checkAmtMap.put(expTypeCode, checkAmt.add(rel.getCheckAmt()));
        }
        BigDecimal totalInputAmt = BigDecimal.ZERO;
        BigDecimal totalCheckAmt = BigDecimal.ZERO;
        for (PcxBillExpCommon expBase : baseExpList) {
            expBase.setInputAmt(inputAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            expBase.setCheckAmt(checkAmtMap.getOrDefault(expBase.getExpenseCode(), BigDecimal.ZERO));
            totalInputAmt = totalInputAmt.add(expBase.getInputAmt());
            totalCheckAmt = totalCheckAmt.add(expBase.getCheckAmt());
        }
        bill.setInputAmt(totalInputAmt);
        bill.setCheckAmt(totalCheckAmt);
    }

    private void collectEcsRel(ExpInvoiceQO invoiceQO,
                               List<PcxExpDetailEcsRel> allEcsRel,
                               Map<String, List> ecsMap,
                               List<PcxBillExpAttachRel> attachRelList,
                               List<PcxEcsSettl> settlList,
                               List<String> itemExpTypeCodeList,
                               List<PcxBillExpDetailCommon> detailList) {
        if (Objects.isNull(ecsMap) || ecsMap.isEmpty()) {
            return;
        }
        // 通用报销按要素配置处理费用明细里默认的扩展要素值，获取费用明细的扩展要素
        // 注意：对于一个费用类型含有多费用明细的，需要产品单独设计和开发，通用里仅支持费用类型下一个费用明细的情况
        Map<String, List<PcxBasFormSetting>> expFormSettingMap = queryExpFormSettingMap(invoiceQO, itemExpTypeCodeList);

        for (Map.Entry<String, List> entry : ecsMap.entrySet()) {
            EcsDtoTransHelper.EcsBillDispose ecsBillDispose = EcsBillExternalServiceImpl.getEcsBillDisposeMap().get(entry.getKey());
            if (Objects.isNull(ecsBillDispose)) {
                continue;
            }
            for (Object o : entry.getValue()) {
                List<PcxExpDetailEcsRel> ecsRelList = ecsBillDispose.initEcsRelList(o, attachRelList, settlList, itemExpTypeCodeList, expFormSettingMap);
                //所选票关联的费用不在所选事项中
                List<String> collect = ecsRelList.stream().filter(item->!item.isExpenseTypeMatch()).map(PcxExpDetailEcsRel::getEcsBillId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)){
                    invoiceQO.getNoIncludeEcs().addAll(collect);
                }
                ecsRelList = ecsRelList.stream().filter(PcxExpDetailEcsRel::isExpenseTypeMatch).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ecsRelList)){
                    for (PcxExpDetailEcsRel rel : ecsRelList) {
                        rel.setExpenseTypeCode(rel.getBaseExpTypeCode());
                        rel.setExpenseTypeName(rel.getBaseExpTypeName());
                    }
                    allEcsRel.addAll(ecsRelList);
                }
            }
        }
        fillExpenseTypeName(allEcsRel, invoiceQO);
        log.info("allEcsRel:{}", JSONObject.toJSONString(allEcsRel));

        for (PcxExpDetailEcsRel rel : allEcsRel) {
            rel.setFiscal(invoiceQO.getFiscal());
            rel.setAgyCode(invoiceQO.getAgyCode());
            rel.setMofDivCode(invoiceQO.getMofDivCode());
            rel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            detailList.add(initRelDetail(rel, invoiceQO));
        }
        for (PcxBillExpAttachRel attachRel : attachRelList) {
            attachRel.setFiscal(invoiceQO.getFiscal());
            attachRel.setAgyCode(invoiceQO.getAgyCode());
            attachRel.setMofDivCode(invoiceQO.getMofDivCode());
        }
        for (PcxEcsSettl ecsSettl : settlList) {
            ecsSettl.setFiscal(invoiceQO.getFiscal());
            ecsSettl.setAgyCode(invoiceQO.getAgyCode());
            ecsSettl.setMofDivCode(invoiceQO.getMofDivCode());
        }
    }

    private Map<String, List<PcxBasFormSetting>> queryExpFormSettingMap(ExpInvoiceQO invoiceQO, List<String> itemExpTypeCodeList) {
        Map<String, List<PcxBasFormSetting>> expFormSettingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemExpTypeCodeList)) {
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setFiscal(invoiceQO.getFiscal());
            expTypeQO.setAgyCode(invoiceQO.getAgyCode());
            expTypeQO.setMofDivCode(invoiceQO.getMofDivCode());
            expTypeQO.setParentCodes(itemExpTypeCodeList);
            Map<String, List<PcxBasExpType>> expDetailMap = pcxBasExpTypeDao.selectSimpleList(expTypeQO).stream()
                    .filter((item) -> item.getIsRefine() == 1 && item.getIsEnabled() == 1)
                    .collect(Collectors.groupingBy(PcxBasExpType::getExpenseCode));
            List<String> detailCodes = new ArrayList<>();
            Map<String, String> detailExpMap = new HashMap<>();
            expDetailMap.forEach((k, v) -> {
                if (v.size() == 1) {
                    PcxBasExpType expType = v.get(0);
                    detailCodes.add(expType.getExpenseCode());
                    detailExpMap.put(expType.getExpenseCode(), expType.getParentCode());
                }
            });
            if (CollectionUtils.isNotEmpty(detailCodes)) {
                PcxBasFormSettingQueryQO formSettingQueryQO = new PcxBasFormSettingQueryQO();
                formSettingQueryQO.setFiscal(invoiceQO.getFiscal());
                formSettingQueryQO.setAgyCode(invoiceQO.getAgyCode());
                formSettingQueryQO.setMofDivCode(invoiceQO.getMofDivCode());
                formSettingQueryQO.setFormClassify(FormSettingEnums.FormClassifyEnum.EXPENSE.getCode());
                formSettingQueryQO.setFormType(FormSettingEnums.FormTypeEnum.EXPENSE_DETAIL.getCode());
                formSettingQueryQO.setFormCodes(detailCodes);
                Map<String, List<PcxBasFormSetting>> formSettingMap = pcxBasFormSettingService.selectByQO(formSettingQueryQO).stream()
                        .filter((item) -> {
                            String rule = item.getDefValRule();
                            if (StringUtil.isEmpty(rule)) {
                                return false;
                            }
                            try {
                                JSONObject json = JSON.parseObject(rule);
                                return PcxConstant.FormSettingDefValRule.FROM_ECS.equalsIgnoreCase(
                                        json.getString(PcxConstant.FormSettingDefValRule.KEY_FROM)); // 默认值来源与ecs的才返回
                            } catch (Exception e) {
                                log.error(e.getLocalizedMessage(), e);
                            }
                            return false;
                        }).collect(Collectors.groupingBy(PcxBasFormSetting::getFormCode));
                // 转换为费用明细的map
                formSettingMap.forEach((key, value) -> {
                    List<PcxBasFormSetting> v = expFormSettingMap.computeIfAbsent(detailExpMap.get(key), k -> new ArrayList<>());
                    v.addAll(value);
                });
                log.info("expFormSettingMap:{}", JSONObject.toJSONString(expFormSettingMap));
            }
        }
        return expFormSettingMap;
    }

    private PcxBillExpDetailCommon initRelDetail(PcxExpDetailEcsRel rel, ExpInvoiceQO invoiceQO) {
        PcxBillExpDetailCommon detail = new PcxBillExpDetailCommon();
        detail.setId(IDGenerator.id());
        detail.setEcsAmt(rel.getEcsAmt());
        detail.setInputAmt(rel.getInputAmt());
        detail.setCheckAmt(rel.getCheckAmt());
        detail.setExpenseTypeCode(rel.getExpenseTypeCode());
        detail.setSource(BillExpDetailSourceEnum.ECS.getCode());
        detail.setFiscal(invoiceQO.getFiscal());
        detail.setAgyCode(invoiceQO.getAgyCode());
        detail.setMofDivCode(invoiceQO.getMofDivCode());
        detail.setTaxAmt(rel.getTaxAmt());
        detail.setTaxRate(rel.getTaxRate());
        detail.setRemark(rel.getRemark());
        if (StringUtil.isEmpty(rel.getExpenseTypeCode())){
            detail.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        } else {
            detail.setExpenseTypeCode(rel.getExpenseTypeCode());
        }
        rel.setDetailId(detail.getId());

        // 按rel中的扩展要素默认值给detail扩展要素赋默认值
        if (rel.getExtItemDefValMap() != null) {
            rel.getExtItemDefValMap().forEach((k, v) -> {
                try {
                    ReflectUtil.setFieldValue(detail, k, v);
                } catch (Exception e) {
                    log.error(e.getLocalizedMessage(), e);
                }
            });
        }
        return detail;

    }

    private void fillExpenseTypeName(List<PcxExpDetailEcsRel> allEcsRel, ExpInvoiceQO invoiceQO) {
        List<String> expenseTypeCode = allEcsRel.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode())
                        && StringUtil.isEmpty(item.getExpenseTypeName()))
                .map(PcxExpDetailEcsRel::getExpenseTypeCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expenseTypeCode)) {
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setFiscal(invoiceQO.getFiscal());
            expTypeQO.setAgyCode(invoiceQO.getAgyCode());
            expTypeQO.setMofDivCode(invoiceQO.getMofDivCode());
            expTypeQO.setExpTypeCodes(expenseTypeCode);
            List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(expTypeQO);
            Map<String, String> expTypeNameMap = expTypeList.stream()
                    .collect(Collectors.toMap(PcxBasExpType::getExpenseCode,
                            PcxBasExpType::getExpenseName, (key1, key2) -> key1));
            for (PcxExpDetailEcsRel rel : allEcsRel) {
                if (StringUtil.isNotEmpty(rel.getExpenseTypeCode())
                        && StringUtil.isEmpty(rel.getExpenseTypeName())) {
                    rel.setExpenseTypeName(expTypeNameMap.get(rel.getExpenseTypeCode()));
                }
            }
        }
    }

    private List<PcxBillExpCommon> buildBaseExpByExpType(List<PcxBasExpType> baseTypeList, List<PcxBillExpDetailCommon> detailList, ExpInvoiceQO qo) {
        List<PcxBillExpCommon> expList = new ArrayList<>();
        Map<String, List<PcxBillExpDetailCommon>> map = detailList.stream().collect(Collectors.groupingBy(PcxBillExpDetailCommon::getExpenseTypeCode));
        for (PcxBasExpType basItemExp : baseTypeList) {
            PcxBillExpCommon entityBean = new PcxBillExpCommon();
            BeanUtils.copyProperties(basItemExp, entityBean);
            entityBean.setId(IDGenerator.id());
            entityBean.setInputAmt(BigDecimal.ZERO);
            entityBean.setFiscal(qo.getFiscal());
            entityBean.setAgyCode(qo.getAgyCode());
            entityBean.setMofDivCode(qo.getMofDivCode());
            expList.add(entityBean);
            List<PcxBillExpDetailCommon> detailCommons = map.get(basItemExp.getExpenseCode());
            if (CollectionUtils.isNotEmpty(detailCommons)) {
                for (PcxBillExpDetailCommon common : detailCommons) {
                    common.setExpenseId(entityBean.getId());
                }
            }
        }
        return expList;
    }

    private List<PcxBasItemExp> getItemExpenseType(StartExpenseQO expenseQO) {
        PcxBasItemExpQO expQO = new PcxBasItemExpQO();
        expQO.setItemCode(expenseQO.getItemCode());
        expQO.setAgyCode(expenseQO.getAgyCode());
        expQO.setFiscal(expenseQO.getFiscal());
        expQO.setMofDivCode(expenseQO.getMofDivCode());
        return basItemExpService.selectByItemCode(expQO);
    }

    private MadEmployeeDTO queryMadEmpDTO(StartExpenseQO expenseQO) {
        PcxBaseDTO pcxBaseDTO = new PcxBaseDTO();
        pcxBaseDTO.setFiscal(expenseQO.getFiscal());
        pcxBaseDTO.setAgyCode(expenseQO.getAgyCode());
        pcxBaseDTO.setMofDivCode(expenseQO.getMofDivCode());
        return madEmployeeExternalService.selectEmployeeByUserCode(pcxBaseDTO, expenseQO.getUserCode());
    }

    private PcxBill buildBill(StartExpenseQO expenseQO, List<PcxBasExpType> baseTypeList) {
        MadEmployeeDTO madEmployeeDTO = queryMadEmpDTO(expenseQO);
        if (Objects.isNull(expenseQO.getPcxBill())) {
            return PcxBill.builder()
                    .agyCode(expenseQO.getAgyCode())
                    .fiscal(expenseQO.getFiscal())
                    .mofDivCode(expenseQO.getMofDivCode())
                    .itemCode(expenseQO.getItemCode())
                    .itemName(expenseQO.getItemName())
                    .claimantCode(madEmployeeDTO.getEmployeeCode())
                    .claimantName(madEmployeeDTO.getEmployeeName())
                    .departmentCode(madEmployeeDTO.getDepartmentCode())
                    .departmentName(madEmployeeDTO.getDepartmentName())
                    .billFuncCode(BillFuncCodeEnum.EXPENSE.getCode())
                    .billFuncName(BillFuncCodeEnum.EXPENSE.getName())
                    .transDate(expenseQO.getTransDate())
                    .createdTime(DateUtil.nowTime())
                    .creator(expenseQO.getUserCode())
                    .creatorName(expenseQO.getUserName())
                    .modifiedTime(DateUtil.nowTime())
                    .modifier(expenseQO.getUserCode())
                    .modifierName(expenseQO.getUserName())
                    .reason("")
                    .bizType(ItemBizTypeEnum.COMMON.getCode())
                    .bizTypeName(ItemBizTypeEnum.COMMON.getName())
                    .expenseCodes(baseTypeList.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.joining(",")))
                    .expenseNames(baseTypeList.stream().map(PcxBasExpType::getExpenseName).collect(Collectors.joining(",")))
                    .build();
        }else {
            PcxBill pcxBill = expenseQO.getPcxBill();
            pcxBill.setItemCode(expenseQO.getItemCode());
            pcxBill.setItemName(expenseQO.getItemName());
            pcxBill.setExpenseCodes(baseTypeList.stream().map(PcxBasExpType::getExpenseCode).collect(Collectors.joining(",")));
            pcxBill.setExpenseNames(baseTypeList.stream().map(PcxBasExpType::getExpenseName).collect(Collectors.joining(",")));
            return pcxBill;
        }
    }


    private List<PcxBillExpAttachRel> getAttachRelList(String billId, Predicate<PcxBillExpAttachRel> filter) {
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId));
        return attachRelList.stream().filter(filter).collect(Collectors.toList());
    }


    private List<PcxExpDetailEcsRel> getEcsRelList(String billId) {
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectByBillId(billId);
        ecsRelList.forEach(item->{
            if (Objects.isNull(item.getEcsDetailId())){
                item.setEcsDetailId("");
            }
            if (Objects.isNull(item.getEmpCode())){
                item.setEmpCode("");
            }
        });
        return ecsRelList;
    }


    /**
     * 添加票
     * 取出添加票转换的费用，或者费用明细
     * 如果时城市间交通费或住宿费，在外面会出发重新规划行程
     * 重新规划行程会删除原来系统补充的无票费用明细
     * 统一把所有的明细金额统计，更新到报销单和费用上
     *
     * @param invoiceQO
     * @param ecsMap
     * @param bill
     * @return
     */
    public String addEcsBills(AddInvoicesQO invoiceQO, Map<String, List> ecsMap, PcxBill bill) {

        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();

        List<PcxBillExpCommon> baseExpList = queryBillExpBaseList(bill);
        invoiceQO.setItemCode(bill.getItemCode());
        StartExpenseQO qo = new StartExpenseQO();
        BeanUtils.copyProperties(invoiceQO, qo);
        List<PcxBasItemExp> baseTypeList = getItemExpenseType(qo);
        List<String> expTypeCodeList = baseTypeList.stream().map(PcxBasItemExp::getExpenseCode).collect(Collectors.toList());
        expTypeCodeList.remove(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        //收集票的附件，包括票自己的附件和他关联的酒店流水单等附件
        List<PcxBillExpAttachRel> addAttachRelList = new ArrayList<>();
        List<PcxEcsSettl> ecsSettlList = new ArrayList<>();
        List<PcxBillExpDetailCommon> detailCommons = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        //收集所有票生成的费用
        collectEcsRel(invoiceQO, addEcsRel, ecsMap, addAttachRelList, ecsSettlList, expTypeCodeList, detailCommons);

        List<PcxExpDetailEcsRel> allEcsRelList = getEcsRelList(bill.getId());
        allEcsRelList.addAll(addEcsRel);

        List<PcxBasExpType> expTypeList = collectEcsRelExpType(allEcsRelList);

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(bill.getId(), (a) -> true);
        allAttachRel.addAll(addAttachRelList);

        fillNoExistsExpType(baseExpList, expTypeList, bill);

        disposeDetailDepartmentCode(bill, detailCommons);

        fillDetailExpenseId(baseExpList, detailCommons);
        calculateTax(addEcsRel, detailCommons);

        collectBillAmt(bill, baseExpList, allEcsRelList);

        collectApportion(bill, amtApportions, amtApportionDepartments, baseExpList);
        PcxBillContractRel contractRel = queryOneContractRel(bill.getId());

        return ecsExpTransService.addEcsCommonBill(bill, baseExpList, addEcsRel, addAttachRelList, allEcsRelList, allAttachRel, ecsSettlList,
                contractRel, detailCommons, amtApportions, amtApportionDepartments);
    }

    private void fillDetailExpenseId(List<PcxBillExpCommon> baseExpList, List<PcxBillExpDetailCommon> detailCommons) {
        if (CollectionUtils.isNotEmpty(detailCommons)) {
            Map<String, List<PcxBillExpDetailCommon>> map = detailCommons.stream().collect(Collectors.groupingBy(PcxBillExpDetailCommon::getExpenseTypeCode));
            for (PcxBillExpCommon expBase : baseExpList) {
                List<PcxBillExpDetailCommon> detailList = map.get(expBase.getExpenseCode());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    for (PcxBillExpDetailCommon common : detailList) {
                        common.setExpenseId(expBase.getId());
                    }
                }
            }
        }
    }

    private PcxBillContractRel queryOneContractRel(String billId) {
        List<PcxBillContractRel> relList = pcxBillContractRelDao.selectList(Wrappers.lambdaQuery(PcxBillContractRel.class)
                .eq(PcxBillContractRel::getBillId, billId));
        if (CollectionUtils.isNotEmpty(relList)) {
            return relList.get(0);
        }
        return null;
    }

    /**
     * 修改票费用的时候可能会出现新的费用
     *
     * @param baseExpList
     * @param expTypeList
     * @param bill
     */
    private void fillNoExistsExpType(List<PcxBillExpCommon> baseExpList, List<PcxBasExpType> expTypeList, PcxBill bill) {
        List<String> collect = baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.toList());
        expTypeList.stream().filter(item -> !collect.contains(item.getExpenseCode())).forEach(item -> {
            PcxBillExpCommon base = new PcxBillExpCommon();
            base.setId(IDGenerator.id());
            base.setBillId(bill.getId());
            base.setExpenseCode(item.getExpenseCode());
            base.setExpenseName(item.getExpenseName());
            base.setAgyCode(bill.getAgyCode());
            base.setFiscal(bill.getFiscal());
            base.setMofDivCode(bill.getMofDivCode());
            baseExpList.add(base);
        });
        bill.setExpenseCodes(baseExpList.stream().map(PcxBillExpBase::getExpenseCode).collect(Collectors.joining(",")));
        bill.setExpenseNames(baseExpList.stream().map(PcxBillExpBase::getExpenseName).collect(Collectors.joining(",")));
    }

    /**
     * 获取报销单关联的费用数据
     *
     * @param view
     * @return
     */
    public List<PcxBillExpCommon> queryBillExpBaseList(PcxBill view) {
        return pcxBillExpCommonDao.selectList(Wrappers.lambdaQuery(PcxBillExpCommon.class)
                .eq(PcxBillExpCommon::getBillId, view.getId()));
    }

    /**
     * 删除票操作
     *
     * @param invoiceQO
     * @return
     */
    public String delEcsBillExpense(DelEcsCommonQO invoiceQO, PcxBill bill) {

        String delAttachRelId = "";
        // 1、删除费用或费用明细
        //找到删除票的关联信息，可能有多条，飞机票或者增值税发票多个item，会生成多个费用明细或费用，所以一个票会有多个关联关系
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, invoiceQO.getBillId()));
        List<PcxExpDetailEcsRel> otherRelList = ecsRelList;
        List<PcxExpDetailEcsRel> delEcsRelList = new ArrayList<>();
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        boolean isDelEcsRel = false;
        PcxExpDetailEcsRel dbEcsRel = ecsRelList.stream()
                .filter(item -> Objects.equals(item.getId(), invoiceQO.getEcsRelId()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(dbEcsRel)) {
            throw new RuntimeException("未查询到票关联信息");
        }
        List<String> delRelIDS = new ArrayList<>();
        if (StringUtil.isNotEmpty(dbEcsRel.getEcsBillId())) {
            isDelEcsRel = true;
            delAttachRelId = dbEcsRel.getEcsBillId();
            //需要删除的票关联关系
            delEcsRelList = ecsRelList.stream()
                    .filter(item -> item.getEcsBillId().equals(dbEcsRel.getEcsBillId())).collect(Collectors.toList());
            //需要删除的票关联关系id
            delRelIDS.addAll(delEcsRelList.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
        } else {
            if (StringUtil.isNotEmpty(dbEcsRel.getManualKey())) {
                delAttachRelId = dbEcsRel.getManualKey();
                delEcsRelList = ecsRelList.stream().filter(item -> Objects.equals(item.getManualKey(), dbEcsRel.getManualKey())).collect(Collectors.toList());
                //需要删除的票关联关系id
                delRelIDS.addAll(delEcsRelList.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
            } else {
                delRelIDS.add(dbEcsRel.getId());
                delAttachRelId = dbEcsRel.getId();
                delEcsRelList.add(dbEcsRel);
            }
        }
        List<String> delDetailIds = delEcsRelList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getDetailId()))
                .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
        //除了当前删除的其他的匹配的票，调ecs解除票报销状态，需要传报销单剩余关联的票
        otherRelList = ecsRelList.stream().filter(
                item -> !delRelIDS.contains(item.getId())).collect(Collectors.toList());

        final String fixDelAttachRelId = delAttachRelId;

        List<PcxBillExpCommon> expBasesList = queryBillExpBaseList(bill);

        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId()));
        attachRelList = attachRelList.stream().filter(item -> !item.getRelId().equals(fixDelAttachRelId)).collect(Collectors.toList());

        collectBillAmt(bill, expBasesList, otherRelList);

        collectApportion(bill, amtApportions, amtApportionDepartments, expBasesList);
        PcxBillContractRel contractRel = queryOneContractRel(bill.getId());

        return ecsExpTransService.delEcsCommonBill(bill, expBasesList, delEcsRelList, otherRelList, attachRelList,
                delAttachRelId, isDelEcsRel, contractRel, delDetailIds, amtApportions, amtApportionDepartments);
    }


    /**
     * 移动端查看票详情时
     * 编辑费用明细和支付信息
     *
     * @param updateQO
     * @param bill
     */
    public void updateEcsCommon(UpdateEcsCommonQO updateQO, PcxBill bill) {
        //转换出付款信息列表
        List<PcxEcsSettl> ecsSettlList = analysisEcsSettl(updateQO);
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRelList(updateQO.getEcsBillClassRelList(), bill, updateQO.getEcsBillId());

        analysisUpdateEcsBillDTO(ecsSettlList, updateQO);

        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        //查询出报销单的费用数据
        List<PcxBillExpCommon> billExpList = queryBillExpBaseList(bill);
        //查询出报销单的票关联关系
        List<PcxExpDetailEcsRel> ecsRelList = expDetailEcsRelDao.selectList(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, updateQO.getBillId()));
        //找出当前操作的票
        List<PcxExpDetailEcsRel> optEcsRelList = ecsRelList.stream()
                .filter(item -> item.getEcsBillId().equals(updateQO.getEcsBillId()))
                .collect(Collectors.toList());
        ecsRelList.removeAll(optEcsRelList);
        Map<String, EcsDetailAmtVO> ecsDetailAmtVOMap = updateQO.getEcsAmtList().stream()
                .collect(Collectors.toMap(EcsDetailAmtVO::getEcsRelId, Function.identity(), (key1, key2) -> key1));
        List<PcxBillExpDetailCommon> delDetailList = queryEcsRelDetailCommon(optEcsRelList);

        Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailCommon>> pair = analysisEcsRelAndDetailCommon(optEcsRelList, delDetailList, ecsDetailAmtVOMap, updateQO);
        ecsRelList.addAll(pair.getLeft());

        List<PcxBasExpType> expTypeList = collectEcsRelExpType(ecsRelList);

        fillNoExistsExpType(billExpList, expTypeList, bill);

        fillDetailExpenseId(billExpList, pair.getRight());
        //从新计算进项税
        calculateTax(pair.getLeft(), pair.getRight());

        collectBillAmt(bill, billExpList, ecsRelList);
        collectApportion(bill, amtApportions, amtApportionDepartments, billExpList);
        SaveEcsCommonDTO dto = SaveEcsCommonDTO.builder()
                .bill(bill)
                .baseExpList(billExpList)
                .delEcsRel(optEcsRelList)
                .addEcsRel(pair.getLeft())
                .attachRelList(attachRelList)
                .ecsSettls(ecsSettlList)
                .delDetailList(delDetailList)
                .insertOrUpdateDetailList(pair.getRight())
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .attachRelId(updateQO.getEcsBillId())
                .build();

        ecsExpTransService.updateEcsCommon(dto);
    }

    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailCommon>> analysisEcsRelAndDetailCommon(List<PcxExpDetailEcsRel> optEcsRelList,
                                                                                                       List<PcxBillExpDetailCommon> detailCommonList,
                                                                                                       Map<String, EcsDetailAmtVO> ecsDetailAmtVOMap,
                                                                                                       ExpInvoiceQO invoiceQO) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailCommon> addDetailList = new ArrayList<>();
        Map<String, PcxExpDetailEcsRel> ecsRelMap = optEcsRelList
                .stream().collect(Collectors.toMap(PcxExpDetailEcsRel::getId, Function.identity(), (key1, key2) -> key1));
        for (Map.Entry<String, EcsDetailAmtVO> entry : ecsDetailAmtVOMap.entrySet()) {
            String ecsRelId = entry.getKey();
            EcsDetailAmtVO amtVO = entry.getValue();
            PcxExpDetailEcsRel oldRel = ecsRelMap.get(ecsRelId);
            if (Objects.nonNull(oldRel)) {
                PcxExpDetailEcsRel newRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(oldRel, newRel);
                newRel.setId(IDGenerator.id());
                newRel.setInputAmt(amtVO.getInputAmt());
                newRel.setCheckAmt(amtVO.getInputAmt());
                newRel.setRemark(amtVO.getRemark());
                newRel.setExpenseTypeCode(amtVO.getExpenseTypeCode());
                newRel.setExpenseTypeName(amtVO.getExpenseTypeName());
                if (CollectionUtils.isNotEmpty(amtVO.getDetailList())) {
                    int index = 0;
                    for (EcsCommonDetailVO detailVO : amtVO.getDetailList()) {
                        PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                        BeanUtils.copyProperties(newRel, copyRel);
                        copyRel.setId(IDGenerator.id());
                        PcxBillExpDetailCommon common = initRelDetail(copyRel, invoiceQO);
                        BeanUtils.copyProperties(detailVO, common);
                        if (index > 0) {
                            copyRel.setInputAmt(BigDecimal.ZERO);
                            copyRel.setCheckAmt(BigDecimal.ZERO);
                            common.setInputAmt(BigDecimal.ZERO);
                            common.setCheckAmt(BigDecimal.ZERO);
                        }
                        common.setEcsNum(amtVO.getEcsNum());
                        addEcsRelList.add(copyRel);
                        addDetailList.add(common);
                        index++;
                    }
                } else {
                    PcxBillExpDetailCommon common = initRelDetail(newRel, invoiceQO);
                    common.setEcsNum(amtVO.getEcsNum());
                    addDetailList.add(common);
                    addEcsRelList.add(newRel);
                }
            }

        }
        return Pair.of(addEcsRelList, addDetailList);
    }

    private Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailCommon>> analysisNoEcsRelAndDetailCommon(List<PcxExpDetailEcsRel> optEcsRelList,
                                                                                                         UpdateNoEcsCommonQO noEcsCommonQO,
                                                                                                         PcxBill bill) {
        List<PcxExpDetailEcsRel> addEcsRelList = new ArrayList<>();
        List<PcxBillExpDetailCommon> addDetailList = new ArrayList<>();
        PcxExpDetailEcsRel rel = optEcsRelList.get(0);
        PcxExpDetailEcsRel tempRel = new PcxExpDetailEcsRel();
        BeanUtils.copyProperties(rel, tempRel);
        tempRel.setId(IDGenerator.id());
        tempRel.setInputAmt(noEcsCommonQO.getInputAmt());
        tempRel.setCheckAmt(noEcsCommonQO.getInputAmt());
        tempRel.setRemark(noEcsCommonQO.getRemark());
        tempRel.setExpenseTypeCode(noEcsCommonQO.getExpenseTypeCode());
        tempRel.setExpenseTypeName(noEcsCommonQO.getExpenseTypeName());
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        invoiceQO.setFiscal(bill.getFiscal());
        invoiceQO.setMofDivCode(bill.getMofDivCode());
        invoiceQO.setAgyCode(bill.getAgyCode());
        if (CollectionUtils.isNotEmpty(noEcsCommonQO.getDetailList())) {
            int index = 0;
            for (EcsCommonDetailVO detailVO : noEcsCommonQO.getDetailList()) {
                PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                BeanUtils.copyProperties(tempRel, copyRel);
                copyRel.setId(IDGenerator.id());
                PcxBillExpDetailCommon common = initRelDetail(copyRel, invoiceQO);
                common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                BeanUtils.copyProperties(detailVO, common);
                if (index > 0) {
                    copyRel.setEcsAmt(BigDecimal.ZERO);
                    copyRel.setInputAmt(BigDecimal.ZERO);
                    copyRel.setCheckAmt(BigDecimal.ZERO);
                    common.setInputAmt(BigDecimal.ZERO);
                    common.setCheckAmt(BigDecimal.ZERO);
                }
                index++;
                addDetailList.add(common);
                addEcsRelList.add(copyRel);
            }
        } else {
            PcxBillExpDetailCommon common = initRelDetail(tempRel, invoiceQO);
            common.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
            addDetailList.add(common);
            addEcsRelList.add(tempRel);
        }
        return Pair.of(addEcsRelList, addDetailList);
    }

    private void disposeDetailAmt(PcxExpDetailEcsRel rel,
                                  EcsDetailAmtVO amtVO,
                                  Map<String, PcxBillExpDetailCommon> detailMap,
                                  PcxBill bill) {
        if (StringUtil.isEmpty(rel.getDetailId())) {
            return;
        }
        PcxBillExpDetailCommon common = detailMap.get(rel.getDetailId());
        if (Objects.isNull(common)) {
            return;
        }
        common.setEcsAmt(rel.getEcsAmt());
        common.setInputAmt(rel.getInputAmt());
        common.setCheckAmt(rel.getCheckAmt());
        common.setEcsNum(amtVO.getEcsNum());

        Map<String, Field> fieldMap = ReflectUtil.getFieldMap(PcxBillExpDetailCommon.class);
        BeanUtils.copyProperties(amtVO, common, fieldMap.keySet().stream().filter(item -> {
            return !item.startsWith("field") && !item.startsWith("acitem");
        }).toArray(String[]::new));

        if (StringUtil.isNotEmpty(rel.getExpenseTypeCode())) {
            common.setExpenseTypeCode(rel.getExpenseTypeCode());
        } else {
            common.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
        }
    }

    private List<PcxBillExpDetailCommon> queryEcsRelDetailCommon(List<PcxExpDetailEcsRel> optEcsRelList) {
        List<String> detailIds = optEcsRelList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getDetailId()))
                .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)) {
            return pcxBillExpDetailCommonDao.selectBatchIds(detailIds);
        } else {
            return Lists.newArrayList();
        }
    }

    private List<PcxEcsSettl> analysisEcsSettl(UpdateEcsCommonQO updateQO) {
        List<PcxEcsSettl> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getSettlList())) {
            for (EcsSettlQO ecsSettlQO : updateQO.getSettlList()) {
                PcxEcsSettl ecsSettl = new PcxEcsSettl();
                BeanUtils.copyProperties(ecsSettlQO, ecsSettl);
                ecsSettl.setBillId(updateQO.getBillId());
                ecsSettl.setEcsBillId(updateQO.getEcsBillId());
                ecsSettl.setEcsTypeCode(updateQO.getEcsBillType());
                ecsSettl.setFiscal(updateQO.getFiscal());
                ecsSettl.setAgyCode(updateQO.getAgyCode());
                ecsSettl.setMofDivCode(updateQO.getMofDivCode());
                result.add(ecsSettl);
            }
        }
        return result;
    }


    private List<PcxBillExpAttachRel> analysisAttachRelList(List<AttachRelQO> attachRelList, PcxBill view,
                                                            String ecsBillId) {

        List<PcxBillExpAttachRel> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachRelList)) {
            for (AttachRelQO attachRelQO : attachRelList) {
                PcxBillExpAttachRel attachRel = new PcxBillExpAttachRel();
                attachRel.setBillId(view.getId());
                attachRel.setFiscal(view.getFiscal());
                attachRel.setAgyCode(view.getAgyCode());
                attachRel.setMofDivCode(view.getMofDivCode());
                attachRel.setTenantId(view.getTenantId());
                attachRel.setAttachId(attachRelQO.getBillId());
                attachRel.setFileName(attachRelQO.getFileName());
                attachRel.setRelType(PcxExpAttachRelType.ECS.getCode());
                attachRel.setRelId(ecsBillId);
                result.add(attachRel);
            }
        }
        return result;
    }

    private UpdateEcsBillDTO analysisUpdateEcsBillDTO(List<PcxEcsSettl> ecsSettlList,
                                                      UpdateEcsCommonQO updateQO) {
        UpdateEcsBillDTO dto = new UpdateEcsBillDTO();
        dto.setBillNo(updateQO.getBillNo());
        dto.setBillAmt(updateQO.getBillAmt());
        dto.setBillId(updateQO.getEcsBillId());
        dto.setFiscal(updateQO.getFiscal());
        dto.setAgencyCode(updateQO.getAgyCode());
        dto.setMofDivCode(updateQO.getMofDivCode());
        dto.setTenantId(updateQO.getTenantId());
        dto.setBillTypeCode(updateQO.getEcsBillType());
        List<UpdateEcsBillDTO.EcsAttachRelInfo> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateQO.getEcsBillClassRelList())) {
            for (AttachRelQO attachRelQO : updateQO.getEcsBillClassRelList()) {
                UpdateEcsBillDTO.EcsAttachRelInfo relInfo = UpdateEcsBillDTO.EcsAttachRelInfo.builder()
                        .agencyCode(updateQO.getAgyCode())
                        .fiscal(updateQO.getFiscal())
                        .mofDivCode(updateQO.getMofDivCode())
                        .billId(attachRelQO.getBillId())
                        .fileName(attachRelQO.getFileName())
                        .billTypeCode(attachRelQO.getBillTypeCode())
                        .createUser(updateQO.getUserName())
                        .createUserCode(updateQO.getUserCode())
                        .isDeleted("2")
                        .classType("bus").build();
                attachRelList.add(relInfo);
            }
        }
        dto.setEcsBillClassRelList(attachRelList);
        List<EcsBillSettleDTO> billSettlList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ecsSettlList)) {
            for (PcxEcsSettl ecsSettl : ecsSettlList) {
                EcsBillSettleDTO settleDTO = new EcsBillSettleDTO();
                BeanUtils.copyProperties(ecsSettl, settleDTO);
                billSettlList.add(settleDTO);
            }
        }
        dto.setSettlDetails(billSettlList);
        dto.setEcsBillInfo(updateQO.getEcsBillInfo());
        return dto;
    }

    public void updateNoEcsCommon(UpdateNoEcsCommonQO commonQO, PcxBill bill) {

        List<PcxBillExpCommon> expBaseList = queryBillExpBaseList(bill);
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();
        List<PcxExpDetailEcsRel> delEcsRel = new ArrayList<>();
        List<PcxExpDetailEcsRel> addEcsRel = new ArrayList<>();
        List<PcxBillExpDetailCommon> addDetail = new ArrayList<>();
        List<PcxBillExpDetailCommon> delDetailList = new ArrayList<>();
        boolean isQuota = commonQO.isQuota();
        if (StringUtil.isNotEmpty(commonQO.getEcsRelId())){
            PcxExpDetailEcsRel ecsRel = ecsRelList.stream()
                    .filter(item -> item.getId().equals(commonQO.getEcsRelId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(ecsRel)) {
                throw new RuntimeException("未查询到历史数据");
            }
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            if (StringUtil.isNotEmpty(ecsRel.getManualKey())) {
                delEcsRel = ecsRelList.stream()
                        .filter(item -> item.getManualKey().equals(ecsRel.getManualKey()))
                        .collect(Collectors.toList());
            } else {
                delEcsRel.add(ecsRel);
            }
            List<PcxBillExpDetailCommon> detailCommons = pcxBillExpDetailCommonDao.selectBatchIds(delEcsRel.stream().map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList()));
            delDetailList.addAll(detailCommons);
            Pair<List<PcxExpDetailEcsRel>, List<PcxBillExpDetailCommon>> pair = analysisNoEcsRelAndDetailCommon(delEcsRel, commonQO, bill);
            addEcsRel.addAll(pair.getLeft());
            addDetail.addAll(pair.getRight());
            if (CollectionUtils.isNotEmpty(detailCommons)){
                isQuota = oldIsQuota(detailCommons.get(0));
            }
        }else{
            ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
            invoiceQO.setFiscal(bill.getFiscal());
            invoiceQO.setMofDivCode(bill.getMofDivCode());
            invoiceQO.setAgyCode(bill.getAgyCode());
            PcxExpDetailEcsRel ecsRel = new PcxExpDetailEcsRel();
            BeanUtils.copyProperties(commonQO, ecsRel);
            ecsRel.setId(IDGenerator.id());
            ecsRel.setEcsAmt(commonQO.getInputAmt());
            ecsRel.setInputAmt(commonQO.getInputAmt());
            ecsRel.setCheckAmt(commonQO.getInputAmt());
            ecsRel.setEcsContent("");
            ecsRel.setBillId(bill.getId());
            ecsRel.setIsConfirm(InvoiceDtoWrapper.InvoiceFlag.CONFIRM.getCode());
            ecsRel.setFiscal(bill.getFiscal());
            ecsRel.setAgyCode(bill.getAgyCode());
            ecsRel.setMofDivCode(bill.getMofDivCode());
            ecsRel.setManualKey(UUID.randomUUID().toString());
            if (CollectionUtils.isNotEmpty(commonQO.getDetailList())) {
                int index = 0;
                for (EcsCommonDetailVO detailVO : commonQO.getDetailList()) {
                    PcxExpDetailEcsRel copyRel = new PcxExpDetailEcsRel();
                    BeanUtils.copyProperties(ecsRel, copyRel);
                    copyRel.setId(IDGenerator.id());
                    PcxBillExpDetailCommon detail = initRelDetail(copyRel, invoiceQO);
                    BeanUtils.copyProperties(detailVO, detail);
                    detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                    if (index > 0){
                        copyRel.setEcsAmt(BigDecimal.ZERO);
                        copyRel.setInputAmt(BigDecimal.ZERO);
                        copyRel.setCheckAmt(BigDecimal.ZERO);
                        detail.setInputAmt(BigDecimal.ZERO);
                        detail.setCheckAmt(BigDecimal.ZERO);
                    }
                    addEcsRel.add(copyRel);
                    addDetail.add(detail);
                    index++;
                }
            } else {
                PcxBillExpDetailCommon detail = initRelDetail(ecsRel, invoiceQO);
                detail.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                addDetail.add(detail);
                addEcsRel.add(ecsRel);
            }
        }
        ecsRelList.removeAll(delEcsRel);
        ecsRelList.addAll(addEcsRel);
        List<PcxBasExpType> expTypeList = collectEcsRelExpType(ecsRelList);
        fillNoExistsExpType(expBaseList, expTypeList, bill);

        if (CollectionUtils.isNotEmpty(addDetail)) {
            for (PcxBillExpDetailCommon detailCommon : addDetail) {
                fillDetailExpenseId(expBaseList, Collections.singletonList(detailCommon));
            }
        }
        List<PcxBillExpAttachRel> attachRelList = analysisAttachRel(commonQO.getAttachList(), addEcsRel.get(0).getManualKey(), bill);
        if (isQuota){
            for (PcxBillExpDetailBase detailBase : addDetail) {
                detailBase.setField06("isQuota");
            }
            for (PcxBillExpAttachRel attachRel : attachRelList) {
                attachRel.setEcsBillType(EcsEnum.BillType.QUOTA.getCode());
            }
        }

        collectBillAmt(bill, expBaseList, ecsRelList);
        collectApportion(bill, amtApportions, amtApportionDepartments, expBaseList);

        List<PcxBillExpAttachRel> allAttachRelList = getAttachRelList(bill.getId(), getDelExtraAttachRelPredicate(addEcsRel.get(0).getManualKey()));
        allAttachRelList.addAll(attachRelList);
        SaveEcsCommonDTO dto = SaveEcsCommonDTO.builder()
                .bill(bill)
                .baseExpList(expBaseList)
                .delEcsRel(delEcsRel)
                .addEcsRel(addEcsRel)
                .allAttachRelList(allAttachRelList)
                .delDetailList(delDetailList)
                .allEcsRel(ecsRelList)
                .insertOrUpdateDetailList(addDetail)
                .amtApportions(amtApportions)
                .amtApportionDepartments(amtApportionDepartments)
                .attachRelList(attachRelList)
                .attachRelId(addEcsRel.get(0).getManualKey())
                .build();
        //保存新生成的补助信息
        ecsExpTransService.updateNoEcsCommon(dto);
    }

    private Predicate<PcxBillExpAttachRel> getDelExtraAttachRelPredicate(String relId){
        return (a)-> Objects.equals(a.getRelType(), 1) || !Objects.equals(a.getRelId(), relId);
    }

    private List<PcxBillExpAttachRel> analysisAttachRel(List<DetailAttachRelQO> attachList, String relKey, PcxBill bill) {
        List<PcxBillExpAttachRel> attachRelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachList)) {
            for (DetailAttachRelQO attachRelQO : attachList) {
                PcxBillExpAttachRel rel = new PcxBillExpAttachRel();
                rel.setId(IDGenerator.id());
                rel.setRelType(PcxExpAttachRelType.EXP_DETAIL.getCode());
                rel.setBillId(bill.getId());
                rel.setRelId(relKey);
                rel.setAttachId(attachRelQO.getFileId());
                rel.setFileName(attachRelQO.getFileName());
                rel.setFiscal(bill.getFiscal());
                rel.setAgyCode(bill.getAgyCode());
                rel.setMofDivCode(bill.getMofDivCode());
                rel.setTenantId(bill.getTenantId());
                attachRelList.add(rel);
            }
        }
        return attachRelList;
    }

    /**
     * 财务审核岗编辑费用类型和核定金额
     *
     * @param ecsRelSumQO
     * @param bill
     */
    public void updateEcsExpType(EcsRelSumQO ecsRelSumQO, PcxBill bill) {
        List<PcxBillExpCommon> expBaseList = queryBillExpBaseList(bill);
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());
        Map<String, EcsRelItemQO> ecsRelItemVOMap = ecsRelSumQO.getItemList().stream().collect(Collectors.toMap(EcsRelItemQO::getEcsRelId, Function.identity(), (key1, key2) -> key1));

        List<PcxExpDetailEcsRel> optEcsRelList = new ArrayList<>();
        Map<String, List<PcxExpDetailEcsRel>> ecsRelMap =ecsRelList.stream().collect(Collectors.groupingBy(this::getEcsRelKey));
        Set<String> originExpenseType = new HashSet<>();
        Set<String> targetExpenseType = new HashSet<>();
        Map<String, String> changeExpenseTypeMap = new HashMap<>();
        boolean dataChange = false;
        for (PcxExpDetailEcsRel rel : ecsRelList) {
            EcsRelItemQO itemVO = ecsRelItemVOMap.get(rel.getId());
            if (Objects.nonNull(itemVO)) {
                if (!Objects.equals(rel.getCheckAmt(), itemVO.getCheckAmt())
                        || !Objects.equals(rel.getExpenseTypeCode(), itemVO.getExpenseTypeCode())){
                    dataChange = true;
                    String sourceTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();
                    originExpenseType.add(sourceTypeCode);
                    targetExpenseType.add(itemVO.getExpenseTypeCode());
                    changeExpenseTypeMap.put(itemVO.getExpenseTypeCode(), sourceTypeCode);
                }
                rel.setCheckAmt(itemVO.getCheckAmt());
                if (Objects.nonNull(itemVO.getTaxAmt())){
                    rel.setTaxAmt(itemVO.getTaxAmt());
                }
                if (Objects.nonNull(itemVO.getTaxRate())){
                    rel.setTaxRate(itemVO.getTaxRate());
                }
                rel.setCheckReason(ecsRelSumQO.getCheckReason());
                rel.setEcsCheckStatus(ecsRelSumQO.getEcsCheckStatus());
                rel.setExpenseTypeCode(itemVO.getExpenseTypeCode());
                rel.setExpenseTypeName(itemVO.getExpenseTypeName());
                optEcsRelList.add(rel);
                //一个票明细项关联多个费用明细，把其他明细对于的rel也进行更新
                String ecsRelKey = getEcsRelKey(rel);
                List<PcxExpDetailEcsRel> brotherRel = ecsRelMap.get(ecsRelKey);
                if (CollectionUtils.isNotEmpty(brotherRel)) {
                    for (PcxExpDetailEcsRel pcxExpDetailEcsRel : brotherRel) {
                        if (Objects.equals(pcxExpDetailEcsRel.getId(), rel.getId())) {
                            pcxExpDetailEcsRel.setExpenseTypeCode(itemVO.getExpenseTypeCode());
                            pcxExpDetailEcsRel.setExpenseTypeName(itemVO.getExpenseTypeName());
                            optEcsRelList.add(pcxExpDetailEcsRel);
                        }
                    }
                }
            }
        }
        ecsRelSumQO.setDataChange(dataChange);
        List<PcxBillAmtApportion> amtApportions = new ArrayList<>();
        List<PcxBillAmtApportionDepartment> amtApportionDepartments = new ArrayList<>();

        List<PcxBillExpDetailCommon> detailCommonList = queryEcsRelDetailCommon(optEcsRelList);
        Map<String, PcxBillExpDetailCommon> detailMap = detailCommonList.stream()
                .collect(Collectors.toMap(PcxBillExpDetailCommon::getId, Function.identity(), (key1, key2) -> key1));

        for (PcxExpDetailEcsRel rel : optEcsRelList) {
            PcxBillExpDetailCommon common = detailMap.get(rel.getDetailId());
            if (Objects.nonNull(common)) {
                common.setCheckAmt(rel.getCheckAmt());
                common.setExpenseTypeCode(rel.getExpenseTypeCode());
                common.setTaxAmt(rel.getTaxAmt());
                common.setTaxRate(rel.getTaxRate());
            }
        }
        List<PcxBasExpType> expTypeList = collectEcsRelExpType(ecsRelList);

        reCollectApportionForUpdateEcsExpTypeNew(bill, ecsRelList, originExpenseType, targetExpenseType, changeExpenseTypeMap, amtApportions, amtApportionDepartments);

        //根据费用类型生成对于的费用实体类对象
        fillNoExistsExpType(expBaseList, expTypeList, bill);

        fillDetailExpenseId(expBaseList, detailCommonList);

        //calculateTax(optEcsRelList, detailCommonList);

        collectBillAmt(bill, expBaseList, ecsRelList);
        ecsExpTransService.updateEcsExpType(bill, expBaseList, ecsRelList, detailCommonList, amtApportions, amtApportionDepartments);
    }

    private void reCollectApportionForUpdateEcsExpTypeNew(PcxBill bill,
                                                          List<PcxExpDetailEcsRel> ecsRelList,
                                                          Set<String> originExpenseType,
                                                          Set<String> targetExpenseType,
                                                          Map<String, String> changeExpenseTypeMap,
                                                          List<PcxBillAmtApportion> amtApportions,
                                                          List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
        if (CollectionUtils.isNotEmpty(ecsRelList)){
            Map<String, CommonCheckExpenseTypeAmtDTO> map = new HashMap<>();
            for (PcxExpDetailEcsRel rel : ecsRelList) {
                String expenseTypeCode = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_CODE : rel.getExpenseTypeCode();
                String expenseTypeName = StringUtil.isEmpty(rel.getExpenseTypeCode()) ? PcxConstant.UNIVERSAL_EXPENSE_NAME : rel.getExpenseTypeName();
                CommonCheckExpenseTypeAmtDTO dto =  map.computeIfAbsent(expenseTypeCode, k->new CommonCheckExpenseTypeAmtDTO());
                dto.setExpenseTypeCode(expenseTypeCode);
                dto.setExpenseTypeName(expenseTypeName);
                dto.setAmt(dto.getAmt().add(rel.getCheckAmt()));
                dto.setLastAmt(dto.getLastAmt().add(rel.getCheckAmt()));
                dto.setInputAmt(dto.getInputAmt().add(rel.getInputAmt()));
                dto.setLastInputAmt(dto.getLastInputAmt().add(rel.getInputAmt()));
            }
            pcxBillAmtApportionService.reCollectApportionForUpdateEcsExpTypeNew(bill, amtApportions, amtApportionDepartments, originExpenseType, targetExpenseType, changeExpenseTypeMap, map);
        }
    }

    private String getEcsRelKey(PcxExpDetailEcsRel item) {
        if (StringUtil.isNotEmpty(item.getEcsDetailId())) {
            return item.getEcsDetailId();
        } else if (StringUtil.isNotEmpty(item.getManualKey())) {
            return item.getManualKey();
        } else {
            return item.getId();
        }
    }

    public EcsExpMatchVO ecsView(PcxBill bill) {
        EcsExpMatchVO vo = new EcsExpMatchVO();
        vo.setBillId(bill.getId());
        List<String> expenseTypeCodeList = Arrays.asList(StringUtil.getStringValue(bill.getExpenseCodes()).split(","));
        vo.setExpenseTypeCodeList(expenseTypeCodeList);
        EcsRelMsgTemp ecsRelMsgTemp = ecsRelList(bill, a -> true);
        vo.setEcsRelList(ecsRelMsgTemp.getEcsRelVOS());
        vo.setExpenseTypeAmtList(ecsRelMsgTemp.getEcsExpenseTypeAmtVOS());
        vo.setEcsbills(ecsRelMsgTemp.getEcsJsonList());
        return vo;
    }

    /**
     * 培训费/会议费的劳务人员信息处理
     * @param bill
     * @param vo
     */
    public void buildLabourDetailVos(PcxBill bill, EcsExpMatchVO vo) {
        // 培训费、会议费额外添加劳务人员信息
        if (Objects.equals(bill.getBizType(), ItemBizTypeEnum.MEETING.getCode()) || Objects.equals(bill.getBizType(), ItemBizTypeEnum.TRAINING.getCode())) {
            // ========== 关联劳务人员信息 ==========
            // 查询明细表的关联的劳务人员明细信息
            List<PcxBillExpDetailLabour> detailList = pcxBillExpDetailLabourDao.selectList(new LambdaQueryWrapper<PcxBillExpDetailLabour>()
                    .eq(PcxBillExpDetailLabour::getBillId, bill.getId()));
            // 去重并获取关联劳务人员信息ids
            List<String> labourInfoIds = detailList.stream()
                    .map(PcxBillExpDetailLabour::getLabourInfoId)
                    .distinct()
                    .collect(Collectors.toList());

            // 获取关联的劳务人员主表信息
            List<PcxLabourInfo> labourInfos = Collections.emptyList();
            if (CollectionUtil.isNotEmpty(labourInfoIds)) {
                labourInfos = pcxLabourInfoDao.selectBatchIds(labourInfoIds);
            }

            // 构建 Map：id → 劳务人员信息
            Map<String, PcxLabourInfo> labourInfoMap = labourInfos.stream()
                    .collect(Collectors.toMap(PcxLabourInfo::getId, Function.identity()));

            // 构建 Map：labourInfoId → 明细列表
            Map<String, List<PcxBillExpDetailLabour>> detailGroupMap = detailList.stream()
                    .collect(Collectors.groupingBy(PcxBillExpDetailLabour::getLabourInfoId));

            // 附件相关
            List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectList(new LambdaQueryWrapper<PcxBillExpAttachRel>()
                    .eq(PcxBillExpAttachRel::getBillId, bill.getId()));
            // 构建最终的 DetailLabourInfo 结构
            Map<String, List<LabourExpDetailVO>> detailLabourInfoMap = new HashMap<>();

            // 遍历每个劳务人员的分组明细
            for (Map.Entry<String, List<PcxBillExpDetailLabour>> entry : detailGroupMap.entrySet()) {
                String labourInfoId = entry.getKey();
                List<PcxBillExpDetailLabour> details = entry.getValue();

                // 获取当前劳务人员的主表信息
                PcxLabourInfo labourInfo = labourInfoMap.get(labourInfoId);
                if (labourInfo == null) {
                    continue;
                }

                // 初始化当前劳务人员的明细列表
                List<LabourExpDetailVO> labourDetails = new ArrayList<>();

                // 处理每个明细项
                for (PcxBillExpDetailLabour detail : details) {
                    // 构建明细VO
                    LabourExpDetailVO detailVO = LabourExpDetailVO.builder()
                            .labourExpInfo(pcxBillExpTrainingService.buildLabourExpInfo(labourInfo, detail))
                            .labourExpDetail(pcxBillExpTrainingService.buildLabourExpDetail(detail))
                            .labourAttachInfoList(pcxBillExpTrainingService.buildAttachInfoVO(attachRelList))
                            .labourInfoId(labourInfo.getId())
                            .expenseTypeCodes(detail.getExpDetailCode())
                            .expenseTypeNames(detail.getExpDetailName())
                            .build();

                    labourDetails.add(detailVO);
                }

                // 将当前劳务人员的所有明细放入结果map
                detailLabourInfoMap.put(labourInfoId, labourDetails);
            }

            // 设置到 expenseTypeCode == "3021602"（师资费） 的明细中
            List<LabourExpDetailVO> allDetailLabourInfos = new ArrayList<>();
            for (String labourInfoId : labourInfoIds) {
                List<LabourExpDetailVO> list = detailLabourInfoMap.get(labourInfoId);
                allDetailLabourInfos.addAll(list);
            }
            allDetailLabourInfos = allDetailLabourInfos.stream().distinct().collect(Collectors.toList());
            vo.setDetailLabourInfos(allDetailLabourInfos);
        }
    }

    public NoEcsDetailVO queryNoEcsDetail(QueryNoEcsDetailQO qo, PcxBill view) {
        NoEcsDetailVO noEcsDetailVO = new NoEcsDetailVO();
        List<PcxExpDetailEcsRel> allEcsRelList = getEcsRelList(qo.getBillId());
        PcxExpDetailEcsRel rel = allEcsRelList.stream().filter(item -> Objects.equals(item.getDetailId(), qo.getDetailId())).findFirst().orElse(null);
        if (Objects.isNull(rel)){
            throw new RuntimeException("未查询到费用明细数据");
        }
        List<PcxExpDetailEcsRel> collect = allEcsRelList.stream().filter(item -> Objects.equals(item.getManualKey(), rel.getManualKey())).collect(Collectors.toList());
        PcxExpDetailEcsRel rel1 = collect.get(0);
        noEcsDetailVO.setDetail(JSON.parseObject(JSON.toJSONString(rel1)));
        List<PcxBillExpAttachRel> attachRelList = getAttachRelList(view.getId(),
                attachRel -> Objects.equals(attachRel.getRelType(), PcxExpAttachRelType.EXP_DETAIL.getCode())
                        && Objects.equals(rel.getManualKey(), attachRel.getRelId()));
        if (CollectionUtils.isNotEmpty(attachRelList)){
            for (PcxBillExpAttachRel attachRel : attachRelList) {
                DetailAttachVO relQO = new DetailAttachVO();
                relQO.setAttachId(attachRel.getAttachId());
                relQO.setFileName(attachRel.getFileName());
                noEcsDetailVO.getFileList().add(relQO);
            }
        }
        return noEcsDetailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void erasureOldData(ChangeItemContext changeItemContext) {
        PcxBill pcxBill = changeItemContext.getPcxBill();
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(pcxBill.getId());
        changeItemContext.setEcsRelList(ecsRelList);
        ecsExpTransService.erasureCommonOldData(pcxBill.getId());
    }

    @AllArgsConstructor
    @Data
    public static class EcsRelMsgTemp {
        List<EcsRelVO> ecsRelVOS;
        List<EcsExpenseTypeAmtVO> ecsExpenseTypeAmtVOS;
        List<JSONObject> ecsJsonList;

        public static EcsRelMsgTemp of(List<EcsRelVO> ecsRelVOS, List<EcsExpenseTypeAmtVO> ecsExpenseTypeAmtVOS, List<JSONObject> ecsJsonList) {
            return new EcsRelMsgTemp(ecsRelVOS, ecsExpenseTypeAmtVOS, ecsJsonList);
        }
    }

    public EcsRelMsgTemp ecsRelList(PcxBill bill, Predicate<PcxExpDetailEcsRel> filter) {
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());

        ecsRelList = ecsRelList.stream().filter(filter).collect(Collectors.toList());

        List<EcsRelVO> result = new ArrayList<>();
        Map<String, EcsExpenseTypeAmtVO> expenseTypeAmtMap = new HashMap<>();
        List<JSONObject> ecsJsonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ecsRelList)) {
            List<PcxBillExpAttachRel> attachRelList = getAttachRelList(bill.getId(), (a) -> true);
            List<String> relIds = attachRelList.stream().map(PcxBillExpAttachRel::getRelId).collect(Collectors.toList());
            Set<String> ecsBillIds = new HashSet<>();
            Map<String, List<PcxExpDetailEcsRel>> ecsRelMap = ecsRelList.stream().collect(Collectors.groupingBy(
                    item -> {
                        if (StringUtil.isNotEmpty(item.getEcsBillId())) {
                            ecsBillIds.add(item.getEcsBillId());
                            return item.getEcsBillId();
                        } else if (StringUtil.isEmpty(item.getManualKey())) {
                            return item.getId();
                        } else {
                            return item.getManualKey();
                        }
                    }
            ));

            Map<String, PcxEcsComparedResult> comparedMap = new HashMap<>();
            //获取票信息，是否验真，票类信息，附件数量
            Map<String, EcsMsgDTO> ecsMsgMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(ecsBillIds)) {
                List<PcxEcsComparedResult> billEcsComparedResult = getBillEcsComparedResult(bill.getId());
                comparedMap = billEcsComparedResult
                        .stream()
                        .collect(Collectors.toMap(PcxEcsComparedResult::getSourceAttachId,
                                Function.identity(), (v1, v2) -> v1));

                Pair<Map<String, EcsMsgDTO>, List<JSONObject>> ecsBillMsgMap = getEcsMsgMap(new ArrayList<>(ecsBillIds), bill);
                ecsMsgMap = ecsBillMsgMap.getLeft();
                ecsJsonList = ecsBillMsgMap.getRight();
            }

            boolean paperBill = isPaperBill(bill);
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsRelMap.entrySet()) {
                List<PcxExpDetailEcsRel> value = entry.getValue();
                String ecsItems = value.stream().filter(item->StringUtil.isNotEmpty(item.getItemName())).map(PcxExpDetailEcsRel::getItemName).distinct().collect(Collectors.joining(","));
                BigDecimal ecsAmt = BigDecimal.ZERO;
                BigDecimal checkAmt = BigDecimal.ZERO;
                BigDecimal inputAmt = BigDecimal.ZERO;
                BigDecimal taxAmt = BigDecimal.ZERO;
                for (PcxExpDetailEcsRel rel : value) {
                    ecsAmt = ecsAmt.add(rel.getEcsAmt());
                    checkAmt = checkAmt.add(rel.getCheckAmt());
                    inputAmt = inputAmt.add(rel.getInputAmt());
                    taxAmt = taxAmt.add(rel.getTaxAmt());
                }
                EcsRelVO ecsRelVO = EcsRelVO.builder()
                        .ecsRelId(value.get(0).getId())
                        .ecsBillId(value.get(0).getEcsBillId())
                        .ecsBillNo(value.get(0).getEcsBillNo())
                        .ecsBillDate(value.get(0).getEcsBillDate())
                        .ecsItems(ecsItems)
                        .ecsBillType(value.get(0).getEcsBillType())
                        .ecsBillTypeName(getEcsBillTypeName(value.get(0).getEcsBillType()))
                        .ecsBillKind(value.get(0).getEcsBillKind())
                        .ecsBillDesc(value.get(0).getEcsBillDesc())
                        .totalAmt(ecsAmt)
                        .ecsAmt(ecsAmt)
                        .checkAmt(checkAmt)
                        .inputAmt(inputAmt)
                        .ecsCheckStatus(disposeCheckStatus(value.get(0).getEcsCheckStatus(), value.get(0).getEcsBillKind(), paperBill))
                        .taxRate(value.get(0).getTaxRate())
                        .taxAmt(taxAmt)
                        .billId(value.get(0).getBillId())
                        .isRelAttach(relIds.contains(entry.getKey()) ? 1 : 0)
                        .isDisplayCheck(1)
                        .remark(value.get(0).getRemark())
                        .detailId(entry.getValue().get(0).getDetailId())
                        .build();

                if (StringUtil.isNotEmpty(ecsRelVO.getEcsBillId())) {
                    disposeEcsMsg(ecsMsgMap, ecsRelVO);
                    PcxEcsComparedResult comparedResult = comparedMap.get(ecsRelVO.getEcsBillId());
                    fillComparedResult(ecsRelVO, comparedResult);
                    ecsRelVO.setSource(BillExpDetailSourceEnum.ECS.getCode());
                } else {
                    ecsRelVO.setSource(BillExpDetailSourceEnum.MANUAL.getCode());
                }
                String expenseCodes = value.stream().filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode())).map(PcxExpDetailEcsRel::getExpenseTypeCode).distinct().collect(Collectors.joining("、"));
                String expenseNames = value.stream().filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode())).map(PcxExpDetailEcsRel::getExpenseTypeName).distinct().collect(Collectors.joining("、"));
                ecsRelVO.setExpenseTypeCodes(expenseCodes);
                ecsRelVO.setExpenseTypeNames(expenseNames);

                value.stream()
                        .filter(item -> StringUtil.isNotEmpty(item.getExpenseTypeCode()))
                        .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getExpenseTypeCode))
                        .entrySet().forEach(kv -> {
                            String expenseTypeCode = kv.getKey();
                            EcsExpenseTypeAmtVO ecsExpenseTypeAmtVO = expenseTypeAmtMap.computeIfAbsent(expenseTypeCode, key -> new EcsExpenseTypeAmtVO());
                            BigDecimal typeInputAmt = BigDecimal.ZERO;
                            BigDecimal typeCheckAmt = BigDecimal.ZERO;
                            for (PcxExpDetailEcsRel rel : kv.getValue()) {
                                typeInputAmt = typeInputAmt.add(rel.getInputAmt());
                                typeCheckAmt = typeCheckAmt.add(rel.getCheckAmt());
                            }
                            ecsExpenseTypeAmtVO.setInputAmt(ecsExpenseTypeAmtVO.getInputAmt().add(typeInputAmt));
                            ecsExpenseTypeAmtVO.setCheckAmt(ecsExpenseTypeAmtVO.getCheckAmt().add(typeCheckAmt));
                            ecsExpenseTypeAmtVO.setExpenseTypeCode(expenseTypeCode);
                            ecsExpenseTypeAmtVO.setExpenseTypeName(kv.getValue().get(0).getExpenseTypeName());
                        });

                result.add(ecsRelVO);
            }
        }
        return EcsRelMsgTemp.of(result, new ArrayList<>(expenseTypeAmtMap.values()), ecsJsonList);
    }

    @Resource
    private PcxEcsComparedResultDao pcxEcsComparedResultDao;

    private List<PcxEcsComparedResult> getBillEcsComparedResult(String billId) {
        LambdaQueryWrapper<PcxEcsComparedResult> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(PcxEcsComparedResult::getEcsId, PcxEcsComparedResult::getEcsBillNo, PcxEcsComparedResult::getSourceAttachId);
        lambdaQueryWrapper.eq(PcxEcsComparedResult::getPcxBillId, billId);
        return pcxEcsComparedResultDao.selectList(lambdaQueryWrapper);
    }


    private void fillComparedResult(EcsRelVO relVO, PcxEcsComparedResult comparedResult) {
        if (Objects.nonNull(comparedResult)) {
            relVO.setCompareResultId(comparedResult.getId());
            relVO.setCompareEcsBillType(comparedResult.getEcsBillType());
        }
    }

    private void disposeEcsMsg(Map<String, EcsMsgDTO> ecsMsgMap, EcsRelVO ecsRelVO) {
        EcsMsgDTO ecsMsgDTO = ecsMsgMap.get(ecsRelVO.getEcsBillId());
        if (Objects.nonNull(ecsMsgDTO)) {
            ecsRelVO.setIsValid(ecsMsgDTO.getIsValid());
            ecsRelVO.setIsRelFileTaxi(ecsMsgDTO.isRelFileTaxi());
            ecsRelVO.setIsRelFileHotel(ecsMsgDTO.isRelFileHotel());
            ecsRelVO.setRelFileCnt(ecsMsgDTO.relFileCnt());
            ecsRelVO.setRelFileHotelCnt(ecsMsgDTO.relFileHotelCnt());
            ecsRelVO.setRelFileTaxiCnt(ecsMsgDTO.relFileTaxiCnt());
            ecsRelVO.setRelOtherFileCnt(ecsMsgDTO.relOtherFileCnt());
            ecsRelVO.setDisplayBizTypeName(EcsItemNameHelper.getDisplayBizTypeName(ecsMsgDTO.getBizTypeName()));
        }
    }

    private Pair<Map<String, EcsMsgDTO>, List<JSONObject>> getEcsMsgMap(List<String> ecsBillIds, PcxBill view) {
        return ecsBillExternalService.getEcsMsgMapByEcsIds(ecsBillIds, view.getFiscal(), view.getAgyCode(), view.getMofDivCode());
    }

    public static String getEcsBillTypeName(String ecsBillType) {
        if (StringUtil.isEmpty(ecsBillType)){
            return "";
        }
        EcsEnum.BillType byCode = EcsEnum.BillType.getByCode(ecsBillType);
        if (Objects.nonNull(byCode)) {
            return byCode.getName();
        }
        return "";
    }

    /**
     * 查询ecsRel
     * 查询关联的附件
     * 组装itemList
     *
     * @param qo
     * @param bill
     * @return
     */
    public EcsRelSumVO ecsRelItemList(QueryEcsRelItemQO qo, PcxBill bill) {
        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(bill.getId());
        PcxExpDetailEcsRel rel = ecsRelList.stream().filter(item -> item.getId().equals(qo.getEcsRelId())).findFirst().orElse(null);
        if (Objects.isNull(rel)) {
            throw new RuntimeException("未查询到历史数据");
        }
        List<PcxExpDetailEcsRel> list = Collections.singletonList(rel);
        String settlRelId = rel.getId();
        if (StringUtil.isNotEmpty(rel.getEcsBillId())) {
            list = ecsRelList.stream().filter(item -> Objects.equals(item.getEcsBillId(), rel.getEcsBillId())).collect(Collectors.toList());
            settlRelId = rel.getEcsBillId();
        }
        String attachRelId = settlRelId;
        List<PcxBillExpAttachRel> attachRelList = getAttachRelList(bill.getId(), (a) -> Objects.equals(a.getRelId(), attachRelId));
        EcsRelSumVO result = new EcsRelSumVO();
        List<PcxEcsSettl> settlList = pcxEcsSettlDao.selectList(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getEcsBillId, settlRelId)
                .eq(PcxEcsSettl::getBillId, bill.getId()));
        result.setSettlList(settlList);
        List<PcxAttachRelVO> attachRelVOS = new ArrayList<>();
        for (PcxBillExpAttachRel attachRel : attachRelList) {
            PcxAttachRelVO relVO = new PcxAttachRelVO();
            relVO.setFileId(attachRel.getAttachId());
            relVO.setFileName(attachRel.getFileName());
            attachRelVOS.add(relVO);
        }
        result.setAttachList(attachRelVOS);
        result.setFileId(rel.getFileId());
        result.setFileName(rel.getFileName());
        result.setCheckReason(rel.getCheckReason());
        result.setEcsCheckStatus(rel.getEcsCheckStatus());

        result.setItemList(collectEcsRelItem(list, bill));

        return result;
    }

    /**
     * 组装票的项目列表
     *
     * @param list
     * @param bill
     * @return
     */
    private List<EcsRelItemVO> collectEcsRelItem(List<PcxExpDetailEcsRel> list, PcxBill bill) {
        MatchEcsRelExpenseTypeDTO ecsRelExpenseType = getEcsRelExpenseType(list, bill);

        List<EcsRelItemVO.ExpenseTypeVO> expTypeVoList = ecsRelExpenseType.getExpenseTypeList();
        Map<String, List<EcsRelItemVO.ExpenseTypeVO>> ecsBillExpTypeMap = ecsRelExpenseType.getEcsBillExpTypeMap();
        List<EcsRelItemVO> result = new ArrayList<>();
        Map<String, List<PcxExpDetailEcsRel>> collect = list.stream().collect(Collectors.groupingBy(this::getEcsRelKey));
        for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : collect.entrySet()) {
            PcxExpDetailEcsRel rel = entry.getValue().get(0);
            EcsRelItemVO itemVO = new EcsRelItemVO();
            BeanUtils.copyProperties(rel, itemVO);
            itemVO.setEcsRelId(rel.getId());
            itemVO.setTotalAmt(rel.getEcsAmt().compareTo(BigDecimal.ZERO) != 0 ? rel.getEcsAmt() : rel.getInputAmt());
            if (StringUtil.isEmpty(itemVO.getEcsBillId())) {
                itemVO.setExpenseTypeList(expTypeVoList);
            } else if (StringUtil.isNotEmpty(itemVO.getEcsDetailId())) {
                itemVO.setExpenseTypeList(ecsBillExpTypeMap.getOrDefault(itemVO.getEcsDetailId(), expTypeVoList));
            } else {
                itemVO.setExpenseTypeList(ecsBillExpTypeMap.getOrDefault(itemVO.getEcsBillId(), expTypeVoList));
            }
            result.add(itemVO);
        }
        return result;
    }

    /**
     * 查询票可以选择的费用
     * 如果是通用事项，则取全部费用
     * 如果是ecs票，则去查票类关联的费用，如果事项关联了费用，则取两者的交集，如果票类没有关联费用，则全都用事项的费用
     * 如果事项没有关联费用，票类关联了费用，则用票类的费用，票类没有关联费用，则取全部没有关联事项的费用
     *
     * @param list
     * @param bill
     * @return
     */
    public MatchEcsRelExpenseTypeDTO getEcsRelExpenseType(List<PcxExpDetailEcsRel> list, PcxBill bill) {
        //事项关联的费用
        List<EcsRelItemVO.ExpenseTypeVO> expTypeVoList = new ArrayList<>();
        if (Objects.equals(bill.getItemCode(), PcxConstant.UNIVERSAL_ITEM_CODE)){
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setFiscal(bill.getFiscal());
            expTypeQO.setMofDivCode(bill.getMofDivCode());
            expTypeQO.setAgyCode(bill.getAgyCode());
            expTypeQO.setIsLeaf(1);
            expTypeQO.setIsEnabled(1);
            expTypeQO.setIsRefine(0);
            List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(expTypeQO);
            expTypeVoList.addAll(convertExpType(expTypeList, Maps.newHashMap(), bill));
            Set<String> expenseTypeSet = new HashSet<>();
            expTypeVoList.forEach(item->{expenseTypeSet.add(item.getExpenseTypeCode());});
            ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
            invoiceQO.setFiscal(bill.getFiscal());
            invoiceQO.setAgyCode(bill.getAgyCode());
            invoiceQO.setMofDivCode(bill.getMofDivCode());
            Map<String, List<PcxBasFormSetting>> expFormSettingMap = queryExpFormSettingMap(invoiceQO, new ArrayList<>(expenseTypeSet));
            Map<String, List<BlockPropertyVO>> propertyMap = collectPropertyMap(expFormSettingMap);
            expTypeVoList.forEach(item->{item.setPropertyList(propertyMap.getOrDefault(item.getExpenseTypeCode(), new ArrayList<>()));});
            return MatchEcsRelExpenseTypeDTO.of(Maps.newHashMap(), expTypeVoList);
        }

        //获取当前单据的费用类型
        List<String> expenseCodes = Arrays.asList(StringUtil.getStringValue(bill.getExpenseCodes()).split(","));
        if( Objects.equals(expenseCodes.size(), 1) && (PcxConstant.MEETING_EXPENSE_30215.equals(expenseCodes.get(0)) ||
                PcxConstant.TRAINING_EXPENSE_30216.equals(expenseCodes.get(0)) ||
                PcxConstant.TREAT_EXPENSE_30217.equals(expenseCodes.get(0))) ){
            //获取费用类型下的叶子节点类型
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setAgyCode(bill.getAgyCode());
            expTypeQO.setFiscal(bill.getFiscal());
            expTypeQO.setMofDivCode(bill.getMofDivCode());
            expTypeQO.setLastCodes(expenseCodes);
            expTypeQO.setIsLeaf(PubConstant.LOGIC_TRUE);
            expTypeQO.setIsEnabled(PubConstant.LOGIC_TRUE);
            expTypeQO.setIsRefine(PubConstant.LOGIC_TRUE);
            List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectSimpleList(expTypeQO);
            expTypeVoList.addAll(convertExpType(expTypeList));

            return MatchEcsRelExpenseTypeDTO.of(Maps.newHashMap(), expTypeVoList);
        }

        //是电子凭证的票
        List<String> ecsBillIdList = list.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getEcsBillId()))
                .map(PcxExpDetailEcsRel::getEcsBillId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, List<EcsRelItemVO.ExpenseTypeVO>> ecsBillExpTypeMap = new HashMap<>();
        //获取事项关联的费用
        StartExpenseQO startExpenseQO = new StartExpenseQO();
        startExpenseQO.setFiscal(bill.getFiscal());
        startExpenseQO.setAgyCode(bill.getAgyCode());
        startExpenseQO.setMofDivCode(bill.getMofDivCode());
        startExpenseQO.setItemCode(bill.getItemCode());
        List<PcxBasItemExp> baseTypeList = getItemExpenseType(startExpenseQO);
        Map<String,String> itemExpTypeMap = new HashMap<>();

        Set<String> expenseTypeSet = new HashSet<>();
        baseTypeList.stream()
                .filter(item -> !PcxConstant.UNIVERSAL_EXPENSE_CODE.equals(item.getExpenseCode()))
                .forEach(item -> {
                    itemExpTypeMap.put(item.getExpenseCode(), item.getExpenseName());
                    expTypeVoList.add(new EcsRelItemVO.ExpenseTypeVO(item.getExpenseCode(), item.getExpenseName(), Lists.newArrayList()));
                    expenseTypeSet.add(item.getExpenseCode());

                });
        itemExpTypeMap.remove("*");

        //查询票类关联的费用
        if (CollectionUtils.isNotEmpty(ecsBillIdList)) {
            ExpInvoiceQO qo = new ExpInvoiceQO();
            List<ExpInvoiceQO.EscBillQO> billList = ecsBillIdList.stream().map(item -> {
                ExpInvoiceQO.EscBillQO escBillQO = new ExpInvoiceQO.EscBillQO();
                escBillQO.setBillId(item);
                escBillQO.setBillAttachType(0);
                return escBillQO;
            }).collect(Collectors.toList());
            qo.setBillList(billList);
            qo.setFiscal(bill.getFiscal());
            qo.setAgyCode(bill.getAgyCode());
            qo.setMofDivCode(bill.getMofDivCode());
            Map<String, List<PcxBasExpType>> billExpTypeMap = ecsBillExternalService.queryBillExpTypeList(qo);
            for (Map.Entry<String, List<PcxBasExpType>> entry : billExpTypeMap.entrySet()) {
                List<EcsRelItemVO.ExpenseTypeVO> expenseTypeVOS = convertExpType(entry.getValue(), itemExpTypeMap, bill);
                if (CollectionUtils.isNotEmpty(expenseTypeVOS)) {
                    ecsBillExpTypeMap.put(entry.getKey(), expenseTypeVOS);
                    for (EcsRelItemVO.ExpenseTypeVO expenseTypeVO : expenseTypeVOS) {
                        expenseTypeSet.add(expenseTypeVO.getExpenseTypeCode());
                    }
                }
            }
        }
        //如果事项没有关联费用，则查询没有关联事项的所有费用
        if (CollectionUtils.isEmpty(expTypeVoList)) {
            PcxBasExpTypeQO expTypeQO = new PcxBasExpTypeQO();
            expTypeQO.setFiscal(bill.getFiscal());
            expTypeQO.setMofDivCode(bill.getMofDivCode());
            expTypeQO.setAgyCode(bill.getAgyCode());
            List<PcxBasExpType> expTypeList = pcxBasExpTypeDao.selectNoBindItemExpTypeList(expTypeQO);
            expTypeVoList.addAll(convertExpType(expTypeList, Maps.newHashMap(), bill));
            for (EcsRelItemVO.ExpenseTypeVO expenseTypeVO : expTypeVoList) {
                expenseTypeSet.add(expenseTypeVO.getExpenseTypeCode());
            }
        }
        ExpInvoiceQO invoiceQO = new ExpInvoiceQO();
        invoiceQO.setFiscal(bill.getFiscal());
        invoiceQO.setAgyCode(bill.getAgyCode());
        invoiceQO.setMofDivCode(bill.getMofDivCode());
        Map<String, List<PcxBasFormSetting>> expFormSettingMap = queryExpFormSettingMap(invoiceQO, new ArrayList<>(expenseTypeSet));
        Map<String, List<BlockPropertyVO>> propertyMap = collectPropertyMap(expFormSettingMap);
        ecsBillExpTypeMap.values().stream().flatMap(Collection::stream).forEach(item -> {
            item.setPropertyList(propertyMap.getOrDefault(item.getExpenseTypeCode(), new ArrayList<>()));
        });
        expTypeVoList.forEach(item -> {
            item.setPropertyList(propertyMap.getOrDefault(item.getExpenseTypeCode(), new ArrayList<>()));
        });
        return MatchEcsRelExpenseTypeDTO.of(ecsBillExpTypeMap, expTypeVoList);
    }

    private Map<String, List<BlockPropertyVO>> collectPropertyMap(Map<String, List<PcxBasFormSetting>> expFormSettingMap) {
        Map<String, List<BlockPropertyVO>> propertyMap = new HashMap<>();
        expFormSettingMap.forEach((key, value) -> {
            List<BlockPropertyVO> propertyVOS = new ArrayList<>();
            value.forEach(item -> {
                BlockPropertyVO blockPropertyVO = new BlockPropertyVO();
                BeanUtils.copyProperties(item, blockPropertyVO);
                blockPropertyVO.setIsRequired(item.getIsNull());
                propertyVOS.add(blockPropertyVO);
            });
            propertyMap.put(key, propertyVOS);
        });
        return propertyMap;
    }

    @Data
    @AllArgsConstructor
    public static class MatchEcsRelExpenseTypeDTO {
        private List<EcsRelItemVO.ExpenseTypeVO> expenseTypeList;
        private Map<String, List<EcsRelItemVO.ExpenseTypeVO>> ecsBillExpTypeMap;

        public static MatchEcsRelExpenseTypeDTO of(Map<String, List<EcsRelItemVO.ExpenseTypeVO>> ecsBillExpTypeMap, List<EcsRelItemVO.ExpenseTypeVO> expenseTypeList) {
            return new MatchEcsRelExpenseTypeDTO(expenseTypeList, ecsBillExpTypeMap);
        }
    }

    private List<EcsRelItemVO.ExpenseTypeVO> convertExpType(List<PcxBasExpType> expTypeList) {
        List<EcsRelItemVO.ExpenseTypeVO> result = new ArrayList<>();
        expTypeList.stream().forEach(expType -> {
                EcsRelItemVO.ExpenseTypeVO expenseTypeVO = new EcsRelItemVO.ExpenseTypeVO();
                expenseTypeVO.setExpenseTypeCode(expType.getExpenseCode());
                expenseTypeVO.setExpenseTypeName(expType.getExpenseName());
                result.add(expenseTypeVO);
            });
        return result;
    }

    private List<EcsRelItemVO.ExpenseTypeVO> convertExpType(List<PcxBasExpType> expTypeList,
                                                            Map<String, String> itemExpTypeMap,
                                                            PcxBill bill) {

        List<EcsRelItemVO.ExpenseTypeVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(expTypeList)) {
            boolean haveItemExpType = Objects.nonNull(itemExpTypeMap) && !itemExpTypeMap.isEmpty();
            Map<String, String> expTypeMap = new HashMap<>();
            if (!haveItemExpType) {
                List<String> expTypeCode = expTypeList.stream().filter(item -> item.getIsRefine() == 1).map(PcxBasExpType::getParentCode).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(expTypeCode)) {
                    PcxBasExpTypeQO qo = new PcxBasExpTypeQO();
                    qo.setFiscal(bill.getFiscal());
                    qo.setMofDivCode(bill.getMofDivCode());
                    qo.setAgyCode(bill.getAgyCode());
                    qo.setExpTypeCodes(new ArrayList<>(expTypeCode));
                    List<PcxBasExpType> itemExps = pcxBasExpTypeDao.selectSimpleList(qo);
                    expTypeMap.putAll(itemExps.stream().collect(Collectors.toMap(PcxBasExpType::getExpenseCode, PcxBasExpType::getExpenseName)));
                }
            } else {
                expTypeMap.putAll(itemExpTypeMap);
            }
            Set<String> keySet = new HashSet<>();
            expTypeList.stream().filter(item -> {
                if (haveItemExpType) {
                    if (item.getIsRefine() == 1) {
                        return itemExpTypeMap.containsKey(item.getParentCode());
                    }
                    return itemExpTypeMap.containsKey(item.getExpenseCode());
                }
                return true;
            }).forEach(expType -> {
                EcsRelItemVO.ExpenseTypeVO expenseTypeVO = new EcsRelItemVO.ExpenseTypeVO();
                if (expType.getIsRefine() == 1) {
                    expenseTypeVO.setExpenseTypeCode(expType.getParentCode());
                    expenseTypeVO.setExpenseTypeName(expTypeMap.get(expType.getParentCode()));
                } else {
                    expenseTypeVO.setExpenseTypeCode(expType.getExpenseCode());
                    expenseTypeVO.setExpenseTypeName(expType.getExpenseName());
                }
                if (!keySet.contains(expenseTypeVO.getExpenseTypeCode())) {
                    keySet.add(expenseTypeVO.getExpenseTypeCode());
                    result.add(expenseTypeVO);
                }
            });
        }
        List<EcsRelItemVO.ExpenseTypeVO> finalResult = result.stream()
                .filter(item->!Objects.equals(PcxConstant.TRAVEL_EXPENSE_30211, item.getExpenseTypeCode())).collect(Collectors.toList());
        return finalResult;
    }

    public EcsExpCommonViewVO ecsExpCommonView(UpdateEcsCommonQO updateQO, PcxBill bill) {
        EcsExpCommonViewVO vo = new EcsExpCommonViewVO();

        List<PcxExpDetailEcsRel> ecsRelList = getEcsRelList(updateQO.getBillId());
        PcxExpDetailEcsRel ecsRel = ecsRelList.stream()
                .filter(item -> Objects.equals(item.getId(), updateQO.getEcsRelId())).findFirst().orElse(null);
        if (Objects.isNull(ecsRel)) {
            throw new RuntimeException("未查询到票信息");
        }
        String attachRelId = null;
        if (StringUtil.isEmpty(ecsRel.getEcsBillId())) {
            if (StringUtil.isNotEmpty(ecsRel.getManualKey())) {
                ecsRelList = ecsRelList.stream().filter(item -> Objects.equals(item.getManualKey(), ecsRel.getManualKey())).collect(Collectors.toList());
            } else {
                ecsRelList = Collections.singletonList(ecsRel);
            }
            attachRelId = ecsRel.getManualKey();
        } else {
            ecsRelList = ecsRelList.stream()
                    .filter(item -> Objects.equals(item.getEcsBillId(), ecsRel.getEcsBillId())).collect(Collectors.toList());
            attachRelId = ecsRel.getEcsBillId();
        }
        Map<String, PcxBillExpDetailCommon> detailMap = getDetailMap(ecsRelList);
        String relAttachRelId = attachRelId;
        List<PcxBillExpAttachRel> attachRelList = getAttachRelList(updateQO.getBillId(), (a) -> Objects.equals(a.getRelId(), relAttachRelId));
        List<DetailAttachRelQO> attachRelInfos = new ArrayList<>();
        attachRelList.forEach(item -> {
            DetailAttachRelQO detailAttachRelQO = new DetailAttachRelQO();
            detailAttachRelQO.setFileId(item.getAttachId());
            detailAttachRelQO.setFileName(item.getFileName());
            attachRelInfos.add(detailAttachRelQO);
        });
        vo.setAttachList(attachRelInfos);
        //查询票的支付信息
        List<PcxEcsSettl> settlList = pcxEcsSettlDao.selectList(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getEcsBillId, updateQO.getEcsBillId())
                .eq(PcxEcsSettl::getBillId, updateQO.getBillId()));
        List<EcsSettlementVO> settlementVOS = convertSettlement(settlList);
        vo.setSettlList(settlementVOS);
        List<EcsDetailAmtVO> ecsAmtList = new ArrayList<>();
        vo.setEcsAmtList(ecsAmtList);
        MatchEcsRelExpenseTypeDTO ecsRelExpenseType = getEcsRelExpenseType(ecsRelList, bill);
        Map<String, List<EcsRelItemVO.ExpenseTypeVO>> ecsBillExpTypeMap = ecsRelExpenseType.getEcsBillExpTypeMap();
        List<EcsRelItemVO.ExpenseTypeVO> expenseTypeList = ecsRelExpenseType.getExpenseTypeList();
        if (StringUtil.isNotEmpty(ecsRel.getEcsBillId())) {
            Map<String, List<PcxExpDetailEcsRel>> ecsDetailMap = ecsRelList
                    .stream()
                    .collect(Collectors.groupingBy(rel ->
                            Optional.ofNullable(rel.getEcsDetailId()).orElse("")
                    ));
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> entry : ecsDetailMap.entrySet()) {
                List<EcsCommonDetailVO> detailList = new ArrayList<>();
                PcxExpDetailEcsRel rel = entry.getValue().get(0);
                EcsDetailAmtVO detailAmtVO = new EcsDetailAmtVO();
                detailAmtVO.setEcsBillId(rel.getEcsBillId());
                detailAmtVO.setEcsDetailId(rel.getEcsDetailId());
                detailAmtVO.setItemName(StringUtil.isEmpty(rel.getItemName()) ? rel.getEcsBillDesc() : rel.getItemName());
                detailAmtVO.setEcsRelId(rel.getId());
                detailAmtVO.setEcsAmt(rel.getEcsAmt());
                List<String> detailIds = new ArrayList<>();
                BigDecimal inputAmt = BigDecimal.ZERO;
                for (int i = 0; i < entry.getValue().size(); i++) {
                    detailIds.add(entry.getValue().get(i).getDetailId());
                    inputAmt = inputAmt.add(entry.getValue().get(i).getInputAmt());
                }
                detailAmtVO.setInputAmt(inputAmt);
                detailAmtVO.setRemark(rel.getRemark());
                detailAmtVO.setExpenseTypeCode(rel.getExpenseTypeCode());
                detailAmtVO.setExpenseTypeName(rel.getExpenseTypeName());
                for (String detailId : detailIds) {
                    PcxBillExpDetailCommon common = detailMap.get(detailId);
                    if (Objects.nonNull(common)) {
                        detailAmtVO.setEcsNum(common.getEcsNum());
                        EcsCommonDetailVO commonDetailVO = new EcsCommonDetailVO();
                        BeanUtils.copyProperties(common, commonDetailVO);
                        detailList.add(commonDetailVO);
                        detailAmtVO.setEcsNum(common.getEcsNum());
                    }
                }
                detailAmtVO.setDetailList(detailList);

                if (StringUtil.isNotEmpty(rel.getEcsDetailId())) {
                    detailAmtVO.setExpenseTypeList(ecsBillExpTypeMap.getOrDefault(rel.getEcsDetailId(), expenseTypeList));
                } else {
                    detailAmtVO.setExpenseTypeList(ecsBillExpTypeMap.getOrDefault(rel.getEcsBillId(), expenseTypeList));
                }
                ecsAmtList.add(detailAmtVO);
            }
        } else {
            PcxExpDetailEcsRel rel = ecsRelList.get(0);
            List<String> detailIds = new ArrayList<>();
            BigDecimal inputAmt = BigDecimal.ZERO;
            for (int i = 0; i < ecsRelList.size(); i++) {
                detailIds.add(ecsRelList.get(i).getDetailId());
                inputAmt = inputAmt.add(ecsRelList.get(i).getInputAmt());
            }
            List<EcsCommonDetailVO> detailList = new ArrayList<>();
            for (String detailId : detailIds) {
                PcxBillExpDetailCommon common = detailMap.get(detailId);
                if (Objects.nonNull(common)) {
                    EcsCommonDetailVO commonDetailVO = new EcsCommonDetailVO();
                    BeanUtils.copyProperties(common, commonDetailVO);
                    detailList.add(commonDetailVO);
                    vo.setEcsNum(common.getEcsNum());
                }
            }
            vo.setDetailList(detailList);
            vo.setInputAmt(inputAmt);
            vo.setEcsBillDate(rel.getEcsBillDate());
            vo.setRemark(rel.getRemark());
            vo.setExpenseTypeCode(ecsRel.getExpenseTypeCode());
            vo.setExpenseTypeName(ecsRel.getExpenseTypeName());
            vo.setExpenseTypeList(expenseTypeList);
        }
        return vo;
    }

//    private void disposeExpenseTypeAddition(EcsExpCommonViewVO vo, PcxBill bill) {
//        Set<String> expenseTypeCode = new HashSet<>();
//        if (StringUtil.isNotEmpty(vo.getExpenseTypeCode())){
//            expenseTypeCode.add(vo.getExpenseTypeCode());
//        }
//        if (CollectionUtils.isNotEmpty(vo.getEcsAmtList())){
//            for (EcsDetailAmtVO ecsDetailAmtVO : vo.getEcsAmtList()) {
//                if (StringUtil.isNotEmpty(ecsDetailAmtVO.getExpenseTypeCode())){
//                    expenseTypeCode.add(ecsDetailAmtVO.getExpenseTypeCode());
//                }
//            }
//        }
//        Map<String, List<BlockPropertyVO>> expensePropertiesMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(expenseTypeCode)){
//            for (String expenseType : expenseTypeCode) {
//                QueryExpenseTypeAdditionQO qo = new QueryExpenseTypeAdditionQO();
//                qo.setFiscal(bill.getFiscal());
//                qo.setMofDivCode(bill.getMofDivCode());
//                qo.setAgyCode(bill.getAgyCode());
//                qo.setExpenseTypeCode(expenseType);
//                List<BlockPropertyVO> blockPropertyVOS = pcxBasFormSettingService.collectExpenseTypeAdditionField(qo);
//                if (CollectionUtils.isNotEmpty(blockPropertyVOS)){
//                    expensePropertiesMap.put(expenseType, blockPropertyVOS);
//                    MadExpendVo costItemData = ecsExpOptService.getCostItemData(bill.getFiscal(), bill.getAgyCode(), bill.getMofDivCode(), expenseType);
//                    if (Objects.nonNull(costItemData)){
//                        for (BlockPropertyVO blockPropertyVO : blockPropertyVOS) {
//                            if (Objects.equals(blockPropertyVO.getFieldValue(), "budGetItemCode")){
//                                blockPropertyVO.setDefVal(costItemData.getBudgetItemCode());
//                            } else if (Objects.equals(blockPropertyVO.getFieldValue(),"accountingExpenseItemCode")) {
//                                blockPropertyVO.setDefVal(costItemData.getAccountingItemCode());
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        vo.setPropertyMap(expensePropertiesMap);
//    }


    private Map<String, PcxBillExpDetailCommon> getDetailMap(List<PcxExpDetailEcsRel> ecsRelList) {
        List<String> detailIds = ecsRelList.stream().filter(item -> StringUtil.isNotEmpty(item.getDetailId()))
                .map(PcxExpDetailEcsRel::getDetailId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)) {
            List<PcxBillExpDetailCommon> detailCommons = pcxBillExpDetailCommonDao.selectBatchIds(detailIds);
            return detailCommons.stream().collect(Collectors.toMap(PcxBillExpDetailCommon::getId, item -> item, (key1, key2) -> key1));
        }
        return new HashMap<>();
    }

    private List<EcsSettlementVO> convertSettlement(List<PcxEcsSettl> settlList) {
        List<EcsSettlementVO> settlementVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settlList)) {
            for (PcxEcsSettl ecsSettl : settlList) {
                EcsSettlementVO vo = new EcsSettlementVO();
                BeanUtils.copyProperties(ecsSettl, vo);
                settlementVOS.add(vo);
            }
        }
        return settlementVOS;
    }

    public BigDecimal calculateTaxAudit(EcsCalculateTaxQO qo) {
        if (qo.getCheckAmt().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        PcxExpDetailEcsRel rel = expDetailEcsRelDao.selectById(qo.getEcsRelId());
        rel.setCheckAmt(qo.getCheckAmt());
        calculateTax(Collections.singletonList(rel), Lists.newArrayList());
        return rel.getTaxAmt();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEcsCheckStatus(EcsExpMatchQO ecsExpMatch, PcxBill pcxBill) {
        if (Objects.nonNull(ecsExpMatch) && CollectionUtils.isNotEmpty(ecsExpMatch.getEcsRelList())) {
            PcxExpDetailEcsRel ecsRel = new PcxExpDetailEcsRel();
            for (EcsRelQO ecsRelQO : ecsExpMatch.getEcsRelList()) {
                if (Objects.isNull(ecsRelQO.getEcsCheckStatus()) || Objects.equals(ecsRelQO.getEcsCheckStatus(), BillExpenseCommonService.CheckStatus.NO_NEED_CHECK.getCode())){
                    continue;
                }
                ecsRel.setEcsCheckStatus(ecsRelQO.getEcsCheckStatus());
                ecsRel.setCheckReason(ecsRelQO.getCheckReason());
                if (StringUtil.isEmpty(ecsRelQO.getEcsBillId())) {
                    expDetailEcsRelDao.update(ecsRel, Wrappers.lambdaUpdate(PcxExpDetailEcsRel.class)
                            .eq(PcxExpDetailEcsRel::getId, ecsRelQO.getEcsRelId()));
                } else {
                    expDetailEcsRelDao.update(ecsRel, Wrappers.lambdaUpdate(PcxExpDetailEcsRel.class)
                            .eq(PcxExpDetailEcsRel::getEcsBillId, ecsRelQO.getEcsBillId())
                            .eq(PcxExpDetailEcsRel::getBillId, pcxBill.getId()));
                }
            }
        }
    }

    public void bindContract(List list, PcxBill bill, BindContractQO bindContractQO) {
        PcxBillContractRel contractRel = extractContractRel(bindContractQO, list);
        if (Objects.isNull(contractRel)) {
            throw new RuntimeException("未查询到合同信息");
        }
        contractRel.setBillId(bill.getId());

        List<PcxExpDetailEcsRel> allEcsRelList = getEcsRelList(bill.getId());

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(bill.getId(), (a) -> true);

        PcxBillRelation pcxBillRelation = buildBillRelation(bindContractQO, bill, contractRel);

        ecsExpTransService.bindContract(bill, contractRel, allEcsRelList, allAttachRel, pcxBillRelation);

    }

    private PcxBillRelation buildBillRelation(ExpInvoiceQO expInvoiceQO, PcxBill bill, PcxBillContractRel contractRel) {
        PcxBillRelation pcxBillRelation = new PcxBillRelation();
        pcxBillRelation.setId(IDGenerator.id());
        pcxBillRelation.setFiscal(expInvoiceQO.getFiscal());
        pcxBillRelation.setAgyCode(expInvoiceQO.getAgyCode());
        pcxBillRelation.setMofDivCode(expInvoiceQO.getMofDivCode());
        pcxBillRelation.setBillId(bill.getId());
        pcxBillRelation.setBillFuncCode(bill.getBillFuncCode());
        pcxBillRelation.setRelBillFuncCode(BillFuncCodeEnum.CONTRACT.getCode());
        pcxBillRelation.setRelBillId(contractRel.getContractId());
        pcxBillRelation.setRelBillNo(contractRel.getContractNo());
        pcxBillRelation.setRelBillName(contractRel.getContractName());
        pcxBillRelation.setCreator(expInvoiceQO.getUserCode());
        pcxBillRelation.setCreatorName(expInvoiceQO.getUserName());
        return pcxBillRelation;
    }

    public void unbindContract(PcxBill bill) {
        List<PcxBillContractRel> relList = pcxBillContractRelDao.selectList(Wrappers.lambdaQuery(PcxBillContractRel.class)
                .eq(PcxBillContractRel::getBillId, bill.getId()));
        if (CollectionUtils.isEmpty(relList)) {
            throw new RuntimeException("报销单未关联合同");
        }
        List<PcxExpDetailEcsRel> allEcsRelList = getEcsRelList(bill.getId());

        List<PcxBillExpAttachRel> allAttachRel = getAttachRelList(bill.getId(), (a) -> true);
        ecsExpTransService.unbindContract(bill, relList, allEcsRelList, allAttachRel);

    }
}
