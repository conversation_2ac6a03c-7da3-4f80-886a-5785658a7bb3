package com.pty.pcx.service.impl.wit;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.api.wit.WitLoadDataService;
import com.pty.pcx.common.constant.WitConstants;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.enu.wit.AuditRuleTypeEnum;
import com.pty.pcx.common.exception.ForbidTipsException;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.wit.PcxAuditRuleDao;
import com.pty.pcx.dao.wit.WitRuleResultDao;
import com.pty.pcx.dao.wit.WitRuleResultDetailDao;
import com.pty.pcx.dao.wit.WitRuleResultDetailHisDao;
import com.pty.pcx.dto.wit.AuditDTO;
import com.pty.pcx.dto.wit.PcxBillDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.entity.wit.PcxAuditRule;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.entity.wit.WitRuleResultDetail;
import com.pty.pcx.entity.wit.WitRuleResultDetailHis;
import com.pty.pcx.qo.wit.WitAttachQO;
import com.pty.pcx.qo.wit.WitCreateRuleQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pub.common.util.*;
import com.pty.rule.RuleAct;
import com.pty.rule.api.IPtyRuleService;
import com.pty.rule.aviator.AviatorRule;
import com.pty.rule.entity.PtyRule;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jeasy.rules.api.Rule;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Indexed
public class WitAuditRuleServiceImpl implements IWitAuditRuleService {

  @Autowired(required = false)
  private IPtyRuleService ptyRuleService;

  @Autowired
  private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;

  @Autowired
  private BillMainService billMainService;

  @Autowired
  private WitRuleResultDao witRuleResultDao;
  @Autowired
  private FireRuleHandler fireRuleHandler;

  @Autowired
  private WitRuleResultDetailDao witRuleResultDetailDao;

  @Autowired
  private WitRuleResultDetailHisDao witRuleResultDetailHisDao;

  @Autowired
  private BatchServiceUtil batchService;

  @Autowired
  private WitLoadDataService witLoadDataService;

  @Autowired
  private PcxAuditRuleDao auditRuleDao;

  /**
   * 查询稽核规则，先查单位级规则，查不到再查系统级
   */
  @Override
  public List<AviatorRule> selectRules(PcxAuditRule auditRule) {
    Assert.hasText(auditRule.getFiscal(), "参数：fiscal不能为空");
    Assert.hasText(auditRule.getAgyCode(), "参数：agyCode不能为空");
    Assert.hasText(auditRule.getMofDivCode(), "参数：mofDivCode不能为空");
    auditRule.setRuleGroup(WitConstants.SUBMIT);
    auditRule.setRuleModule(WitConstants.PCX);
    auditRule.setEnabled(1);
    auditRule.setRuleCorrect(1);
    List<AviatorRule> rules = auditRuleDao.select(auditRule).stream().map(PcxAuditRule::toAviatorRule).collect(Collectors.toList());
    if (CollectionUtil.isEmpty(rules)) {//先查单位级规则，查不到再查系统级
      PtyRule cond = new PtyRule();
      cond.setRuleModule(WitConstants.PCX);
      cond.setRuleGroup(WitConstants.SUBMIT);
      cond.setRuleReference(auditRule.getRuleReference());
      cond.setRuleCorrect(1);
      cond.setEnabled(1);
      rules = ptyRuleService.selectRule(cond).stream().map(PtyRule::toAviatorRule).collect(Collectors.toList());
    }
    return rules;
  }

  /**
   * 批量稽核
   */
  @Override
  public CheckMsg<WitRuleResult> auditRule(AuditDTO auditDTO) {
    AuditDTO.ReqMeta meta = auditDTO.getReqMeta();
    if (meta == null) {
      return CheckMsg.fail("请求参数reqMeta不能为空");
    }
    if (StringUtil.isEmpty(meta.getBillId())) {
      return CheckMsg.fail("参数billId不能为空");
    }
    if (StringUtil.isEmpty(meta.getFiscal())) {
      return CheckMsg.fail("参数fiscal不能为空");
    }
    if (StringUtil.isEmpty(meta.getCreator()) || StringUtil.isEmpty(meta.getCreatorName())) {
      return CheckMsg.fail("参数稽核人：creator、creatorName不能为空");
    }
    PcxBill pcxBill = billMainService.view(auditDTO.getReqMeta().getBillId());
    if (pcxBill == null) {
      return CheckMsg.fail("参数billId=" + auditDTO.getReqMeta().getBillId() + "对应的单据不存在");
    }
    StopWatch watch = new StopWatch();
    watch.start("封装稽核规则数据");
    PcxBillDTO billDTO = buildData(auditDTO);
    PcxRuleDomainObject ruleDomainObject = new PcxRuleDomainObject(billDTO);
    ruleDomainObject.setGroup(WitConstants.SUBMIT);
    watch.stop();

    watch.start("加载稽核规则");
    List<AviatorRule> rules = Lists.newArrayList();
    PcxAuditRule auditRule = new PcxAuditRule();
    auditRule.setMofDivCode(pcxBill.getMofDivCode());
    auditRule.setAgyCode(pcxBill.getAgyCode());
    auditRule.setFiscal(pcxBill.getFiscal());
    if (PositionBlockEnum.EXPENSE_DETAIL.getCode().equals(auditDTO.getReqMeta().getRefBlock())
            || PositionBlockEnum.ECSEXPMATCH.getCode().equals(auditDTO.getReqMeta().getRefBlock())){
      auditRule.setRuleReference(PositionBlockEnum.EXPENSE_DETAIL.getCode());
      List<AviatorRule> detailRules = this.selectRules(auditRule);
      if(CollectionUtils.isNotEmpty(detailRules)){
        rules.addAll(detailRules);
      }
      auditRule.setRuleReference(PositionBlockEnum.ECSEXPMATCH.getCode());
      List<AviatorRule> ecsRules = this.selectRules(auditRule);
      if(CollectionUtils.isNotEmpty(ecsRules)){
        rules.addAll(ecsRules);
      }
      auditRule.setRuleReference("info");
      List<AviatorRule> infoRules = this.selectRules(auditRule);
      if(CollectionUtils.isNotEmpty(infoRules)){
        rules.addAll(infoRules);
      }
    }else {
      auditRule.setRuleReference(auditDTO.getReqMeta().getRefBlock());
      rules = this.selectRules(auditRule);
    }

    if (CollectionUtils.isEmpty(rules)){
      //移除当前稽核块的稽核规则
      witRuleResultDao.delByBillIdAndBlock(auditDTO.getReqMeta().getBillId(), auditDTO.getReqMeta().getRefBlock());
      witRuleResultDetailDao.delByBillIdAndBlock(auditDTO.getReqMeta().getBillId(), auditDTO.getReqMeta().getRefBlock());
      return CheckMsg.success();
    }
    watch.stop();
    watch.start("处理稽核规则");

    List<FireRuleHandler.FireResult> results = fireRuleHandler.handle(rules, ruleDomainObject,auditDTO.getReqMeta().getRefBlock());
    watch.stop();

    watch.start("保存稽核结果");

    WitRuleResult ruleResult = createRuleResult(auditDTO, results);
    IWitAuditRuleService ruleService = SpringUtil.getBean(WitAuditRuleServiceImpl.class);
    ruleResult.setTotalCount(rules.size());
    List<WitRuleResultDetail> oldDetailList = witRuleResultDetailDao.selectByResultId(ruleResult.getId());//数据库已有数据
    ruleService.saveRuleResult(ruleResult, oldDetailList);
    watch.stop();
    log.info(watch.prettyPrint());
    return CheckMsg.success(ruleResult);
  }

  @Override
  public PcxBillDTO buildData(AuditDTO auditDTO) {
    PcxBillDTO billDTO = new PcxBillDTO();
    billDTO.setMainBill(auditDTO.getPcxBill());
    billDTO.setSettles(auditDTO.getSettles());
    billDTO.setAttachs(auditDTO.getAttachs());
    billDTO.setInvoices(auditDTO.getInvoices());
    billDTO.setExpenses(auditDTO.getExpenses());
    billDTO.setDetails(auditDTO.getDetails());
    billDTO.setRelations(auditDTO.getRelations());
    billDTO.setBalances(auditDTO.getBalances());
    //加载关联数据
    witLoadDataService.execute(billDTO);
    if (CollectionUtil.isNotEmpty(auditDTO.getExpenses())){
      billDTO.getContext().put(WitConstants.EXPENSES, auditDTO.getExpenses());
    }
    if (CollectionUtil.isNotEmpty(auditDTO.getExpenses())){
      billDTO.getContext().put(WitConstants.DETAILS, auditDTO.getDetails());
    }
    if (CollectionUtil.isNotEmpty(auditDTO.getRelations())){
      billDTO.getContext().put(WitConstants.RELATIONS, auditDTO.getRelations());
    }
    if (CollectionUtil.isNotEmpty(auditDTO.getBalances())){
      billDTO.getContext().put(WitConstants.BALANCES, auditDTO.getBalances());
    }

    if (auditDTO.getMadEmployeeDTO() != null) {
      billDTO.getContext().put(WitConstants.EMP,auditDTO.getMadEmployeeDTO());
    }
    if (auditDTO.getPaOrg() != null) {
      billDTO.getContext().put(WitConstants.ORG,auditDTO.getPaOrg());
    }
    billDTO.getContext().put(WitConstants.SUB,auditDTO.getYearSubAmt());
    if (auditDTO.getBeforeLoan() != null) {
      billDTO.getContext().put(WitConstants.BEFORE_LOAN,auditDTO.getBeforeLoan());
    }
    if (CollectionUtil.isNotEmpty(auditDTO.getLeaderDepartments())) {
      billDTO.getContext().put(WitConstants.LEADERDEPT,auditDTO.getLeaderDepartments());
    }
    if (CollectionUtil.isNotEmpty(auditDTO.getApportionDepartments())) {
      billDTO.getContext().put(WitConstants.APPORTION_DEPARTMENTS,auditDTO.getApportionDepartments());
    }

    return billDTO;
  }

  @Override
  public CheckMsg<List<WitRuleResult>> queryRuleResult(WitRuleResult result) {
    if (StringUtil.isEmpty(result.getBillId())) {
      return CheckMsg.fail("参数billId不能为空");
    }
    if (StringUtil.isEmpty(result.getFiscal())) {
      return CheckMsg.fail("参数fiscal不能为空");
    }
    if (StringUtil.isEmpty(result.getAuditType())) {
      //默认查询系统稽核
      result.setAuditType(AuditRuleTypeEnum.SUBMIT.getCode());
    }
    List<WitRuleResult> ruleResults = witRuleResultDao.select(result);
    if (CollectionUtil.isNotEmpty(ruleResults)) {
      //对ruleResults中的refBlock  PositionBlockEnum 的索引排序
      ruleResults.sort(Comparator.comparing(ruleResult -> PositionBlockEnum.getIndex(ruleResult.getRefBlock())));
      ruleResults.forEach(ruleResult -> {
        List<WitRuleResultDetail> witRuleResultDetails = witRuleResultDetailDao.selectByResultId(ruleResult.getId());
        if (CollectionUtil.isNotEmpty(witRuleResultDetails)){
          witRuleResultDetails.stream().filter(witRuleResultDetail -> StringUtil.isNotEmpty(witRuleResultDetail.getErrMsg()));
          if (CollectionUtil.isNotEmpty(witRuleResultDetails)){
            ruleResult.setDetailList(witRuleResultDetails);
          }
        }
      });
      return CheckMsg.success(ruleResults);
    }
    return CheckMsg.fail("数据不存在！");
  }

  @Override
  public List<String> queryRuleAttach(WitRuleResult result) {
    try {
      CheckMsg<List<WitRuleResult>> listCheckMsg = queryRuleResult(result);
      //解析出稽核规则的附件信息
      if (CollectionUtils.isNotEmpty(listCheckMsg.getData())){
        List<PcxBillAttachRelationVO> attachList =Lists.newArrayList();
        List<WitRuleResultDetail> resultDetails = Lists.newArrayList();
        listCheckMsg.getData().forEach(ruleResult -> {
          resultDetails.addAll(ruleResult.getDetailList());
        });
        for (WitRuleResultDetail resultDetail : resultDetails) {
          if (StringUtil.isNotEmpty(resultDetail.getField2())){
            String jsonStr = resultDetail.getField2();
            JSONArray jsonArray = JSON.parseArray(jsonStr);
            for (Object o : jsonArray) {
              JSONObject jsonObject = (JSONObject) o;
              JSONArray fileList = jsonObject.getJSONArray("fileList");
              if (CollectionUtil.isNotEmpty(fileList)){
                for (Object fileObject : fileList) {
                  if (Objects.nonNull(fileObject) && fileObject instanceof JSONObject){
                    JSONObject file = (JSONObject) fileObject;
                    PcxBillAttachRelationVO attach = new PcxBillAttachRelationVO();
                    attach.setAttachId(file.getString("attachId"));
                    attachList.add(attach);
                  }
                }
              }

            }
          }
        }
        return attachList.stream().map(PcxBillAttachRelationVO::getAttachId).collect(Collectors.toList());
      }
      return Collections.emptyList();
    }catch (Exception e){
      log.error("获取稽核规则附件失败，bilId {}",result.getBillId(), e);
      return Collections.emptyList();
    }
  }

  private WitRuleResult createRuleResult(AuditDTO auditDTO,List<FireRuleHandler.FireResult> results) {

    int okCount = results.stream().mapToInt(FireRuleHandler.FireResult::getOkCount).sum();
    WitRuleResult result = witRuleResultDao.selectById(auditDTO.getReqMeta().key());
    String nowTime = DateUtil.nowTime();
    if (result == null) {
      result = new WitRuleResult();
      BeanUtils.copyProperties(auditDTO.getPcxBill(), result);
      result.setId(auditDTO.getReqMeta().key());
      result.setRefBlock(auditDTO.getReqMeta().getRefBlock());
      result.setBillId(auditDTO.getPcxBill().getId());
      result.setBillAmt(auditDTO.getPcxBill().getCheckAmt());
      result.setCreatedTime(nowTime);
      result.setCreator(auditDTO.getReqMeta().getCreator());
      result.setCreatorName(auditDTO.getReqMeta().getCreatorName());
      result.setAuditType(WitConstants.SUBMIT);
      result.setBillStatus("1");
      result.setWarnCount(0);
      result.setErrCount(0);
      //特殊处理
      if (result.getRefBlock().equals(PositionBlockEnum.ECSEXPMATCH.getCode())){
        result.setRefBlock(PositionBlockEnum.EXPENSE_DETAIL.getCode());
      }
      witRuleResultDao.insert(result);
    }
    result.setCreatedTime(nowTime);
    List<WitRuleResultDetail> details = new ArrayList<>();
    results.forEach(fire -> {
      Map<Rule, String> maps = new HashMap();
      maps.putAll(fire.getFire().getWarns());
      maps.putAll(fire.getFire().getErrors());
      for (Map.Entry<Rule, String> entry : maps.entrySet()) {
        WitRuleResultDetail detail = new WitRuleResultDetail();
        detail.setResultId(auditDTO.getReqMeta().key());
        detail.setBillId(auditDTO.getReqMeta().getBillId());
        detail.setFiscal(auditDTO.getReqMeta().getFiscal());
        detail.setRuleId(((AviatorRule) entry.getKey()).id());
        detail.setRuleName((entry.getKey()).getDescription());
        detail.setRuleType(((AviatorRule) entry.getKey()).act().toString());
        detail.setRuleReference(auditDTO.getReqMeta().getRefBlock());
        detail.setRefBlock(auditDTO.getReqMeta().getRefBlock());
        detail.setRuleIdentify(((AviatorRule) entry.getKey()).ruleIdentify());
        detail.setAuditType(WitConstants.SUBMIT);
        detail.setCreator(auditDTO.getReqMeta().getCreator());
        detail.setCreatedTime(nowTime);
        if (detail.getRuleType().equals("WARN")){
          detail.setPassState(1);
        }
        detail.setStatus("2");
        if (Objects.nonNull(entry.getValue())){
          detail.setErrMsg(entry.getValue());
        }else{
          detail.setErrMsg(((AviatorRule) entry.getKey()).errorTemplate());
        }
        detail.setRuleField(((AviatorRule) entry.getKey()).ruleField());
        PagePosition build = PagePosition.builder()
                .rowId(fire.getRowId())
                .blockCode(fire.getBlockCode())
                .classifyCode(auditDTO.getReqMeta().getRefBlock())
                .fieldValue(((AviatorRule) entry.getKey()).ruleField()).build();
        //特殊处理
        if (build.getClassifyCode().equals(PositionBlockEnum.ECSEXPMATCH.getCode())){
          build.setClassifyCode(PositionBlockEnum.EXPENSE_DETAIL.getCode());
        }
        detail.setDataInfo(JSONUtil.toJsonStr(build));
        detail.setCurData(fire.getRowId());
        detail.setField1(((AviatorRule) entry.getKey()).field1());
        detail.setField2(((AviatorRule) entry.getKey()).field2());
        if (StringUtil.isNotEmpty(fire.getItemId())){
          detail.setId(MD5.create().digestHex(detail.key()+fire.getItemId()));
        }else {
          detail.setId(detail.key());
        }

        //特殊处理
        if (detail.getRuleReference().equals(PositionBlockEnum.ECSEXPMATCH.getCode())){
          detail.setRuleReference(PositionBlockEnum.EXPENSE_DETAIL.getCode());
        }
        if (detail.getRefBlock().equals(PositionBlockEnum.ECSEXPMATCH.getCode())){
          detail.setRefBlock(PositionBlockEnum.EXPENSE_DETAIL.getCode());
        }
        //如果返回的结果中包含计算错误等字段则不进行保存
        if (!detail.getErrMsg().contains("计算错误")){
          details.add(detail);
        }
      }
    });

    result.setWarnCount(details.stream().filter(r -> RuleAct.WARN.name().equals(r.getRuleType())).collect(Collectors.toList()).size());
    result.setErrCount(details.stream().filter(r -> RuleAct.REJECT.name().equals(r.getRuleType())).collect(Collectors.toList()).size());
    result.setOkCount(okCount);
    result.setBillStatus((result.getWarnCount() + result.getErrCount()) == 0 ? "1" : "2");
    result.setDetailList(details);
    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void saveRuleResult(WitRuleResult result, List<WitRuleResultDetail> oldDetailList) {
    witRuleResultDao.updateById(result);
    if (CollectionUtil.isNotEmpty(result.getDetailList())) {
      List<WitRuleResultDetail> details = result.getDetailList();
      List<String> newIdList = details.stream().map(WitRuleResultDetail::getId).collect(Collectors.toList());
      List<String> oldIdList = oldDetailList.stream().map(WitRuleResultDetail::getId).collect(Collectors.toList());
      Map<String, List<WitRuleResultDetail>> map = oldDetailList.stream().collect(Collectors.groupingBy(WitRuleResultDetail::getRuleId));// 已存在的结果集
      details.forEach(detail -> {
        List<WitRuleResultDetail> oldDetails = map.get(detail.getRuleId());
        if (CollectionUtil.isNotEmpty(oldDetails)){
          //存在单行数据的明细
          if (StringUtil.isNotEmpty(detail.getCurData())){
            Optional<WitRuleResultDetail> first = oldDetails.stream().filter(o -> detail.getCurData().equals(o.getCurData())).findFirst();
            if (first.isPresent()){
              WitRuleResultDetail oldDetail = first.get();
              detail.setPassState(oldDetail.getPassState());
              detail.setPassCause(oldDetail.getPassCause());
              detail.setStatus(oldDetail.getStatus());
              detail.setRemarks(oldDetail.getRemarks());
              detail.setAttachId(oldDetail.getAttachId());
              detail.setField2(oldDetail.getField2());
            }
          }else {
            //不存在明细
            WitRuleResultDetail oldDetail = oldDetails.get(0);
            detail.setPassState(oldDetail.getPassState());
            detail.setPassCause(oldDetail.getPassCause());
            detail.setStatus(oldDetail.getStatus());
            detail.setRemarks(oldDetail.getRemarks());
            detail.setAttachId(oldDetail.getAttachId());
            detail.setField2(oldDetail.getField2());
          }
        }
      });
      List<String> delList = oldIdList.stream().filter(id -> !newIdList.contains(id)).collect(Collectors.toList());//删除的
      List<String> addList = newIdList.stream().filter(id -> !oldIdList.contains(id)).collect(Collectors.toList());//新增的
      List<String> updateList = oldIdList.stream().filter(newIdList::contains).collect(Collectors.toList());//更新的
      if (!CollectionUtils.isEmpty(delList)) {
        List<WitRuleResultDetailHis> delteData = oldDetailList.stream().filter(d -> delList.contains(d.getId())).map(WitRuleResultDetail::tolHis).collect(Collectors.toList());
        batchService.batchProcess(delList, WitRuleResultDetailDao.class, WitRuleResultDetailDao::deleteById);
        if (!CollectionUtils.isEmpty(delteData)){
          batchService.batchProcess(delteData, WitRuleResultDetailHisDao.class, WitRuleResultDetailHisDao::insert);
        }
      }
      List<WitRuleResultDetail> insertData = details.stream().filter(d -> addList.contains(d.getId())).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(insertData)) {
        batchService.batchProcess(insertData, WitRuleResultDetailDao.class, WitRuleResultDetailDao::insert);
      }
      List<WitRuleResultDetail> updateData = details.stream().filter(d -> updateList.contains(d.getId())).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(updateData)) {
        batchService.batchProcess(updateData, WitRuleResultDetailDao.class, WitRuleResultDetailDao::update);
      }
    }else {
      //直接移除老的数据
      List<String> delList = oldDetailList.stream().map(WitRuleResultDetail::getId).collect(Collectors.toList());
      batchService.batchProcess(delList, WitRuleResultDetailDao.class, WitRuleResultDetailDao::deleteById);
      List<WitRuleResultDetailHis> delteData = oldDetailList.stream().map(WitRuleResultDetail::tolHis).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(delteData)){
        batchService.batchProcess(delteData, WitRuleResultDetailHisDao.class, WitRuleResultDetailHisDao::insert);
      }
    }
  }

  @Override
  public CheckMsg auditHandle(WitRuleResultDetail detail) {
    WitRuleResultDetail witRuleResultDetail = witRuleResultDetailDao.selectById(detail.getId());
    if (witRuleResultDetail == null){
      return CheckMsg.fail("数据不存在");
    }
    witRuleResultDetail.setPassState(detail.getPassState());
    witRuleResultDetail.setPassCause(detail.getPassCause());
    witRuleResultDetail.setAttachId(detail.getAttachId());
    witRuleResultDetail.setRemarks(detail.getRemarks());
    witRuleResultDetail.setStatus(detail.getStatus());
    witRuleResultDetail.setField2(detail.getField2());
    witRuleResultDetailDao.update(witRuleResultDetail);
    return CheckMsg.success();
  }

  @Override
  public void validateRule(PcxBill pcxBill,  String positionCode) {
    WitRuleResult query = new WitRuleResult();
    query.setBillId(pcxBill.getId());
    query.setFiscal(pcxBill.getFiscal());
    CheckMsg<List<WitRuleResult>> listCheckMsg = this.queryRuleResult(query);
    if (listCheckMsg.getData() != null){
      String result = getResult(listCheckMsg.getData(), positionCode);
      if (RuleAct.REJECT.toString().equals(result)){
        throw new ForbidTipsException("当前单据存在未处理的稽核结果，请处理");
      }
    }
  }

  private static final String STATUS_PENDING = "2";

  @Override
  public String getResult(List<WitRuleResult> results, String positionCode) {
    if (CollectionUtil.isEmpty(results) || StringUtil.isEmpty(positionCode)) {
      return RuleAct.ACCESS.toString();
    }

    List<WitRuleResultDetail> details = extractDetails(results);

    if (PositionEnum.MAKE_BILL.getCode().equals(positionCode)) {
      return checkForStatus(details, RuleAct.REJECT, STATUS_PENDING)
              ? RuleAct.REJECT.toString()
              : checkForStatus(details, RuleAct.WARN, STATUS_PENDING)
              ? RuleAct.WARN.toString()
              : RuleAct.ACCESS.toString();
    }

    if (PositionEnum.isFinance(positionCode)){
      return checkForPassState(details, RuleAct.REJECT, 2)
              ? RuleAct.REJECT.toString()
              : checkForPassState(details, RuleAct.WARN, 2)
              ? RuleAct.REJECT.toString()
              : RuleAct.ACCESS.toString();
    }
    return RuleAct.ACCESS.toString();
  }

  @Override
  public void delAttachRel(WitAttachQO witAttachQO) {

    PcxExpDetailEcsRel pcxExpDetailEcsRel = getPcxExpDetailEcsRel(witAttachQO);
    if (pcxExpDetailEcsRel == null) return;
    //调用ecs
    pcxExpDetailEcsRelService.deleteById(pcxExpDetailEcsRel.getId());
  }


  @Override
  public void addAttachRel(WitAttachQO witAttachQO) {
    getPcxExpDetailEcsRel(witAttachQO);
  }

  /**
   * 创建人工稽核规则
   * @param createRuleQO
   */
  @Override
  public WitRuleResultDetail createRule(WitCreateRuleQO createRuleQO) {
    PcxBill pcxBill = billMainService.view(createRuleQO.getBillId());
    //查询块信息 如果人工的块存在
    WitRuleResult query = new WitRuleResult();
    query.setBillId(createRuleQO.getBillId());
    query.setRefBlock(createRuleQO.getClassifyCode());
    query.setAuditType(AuditRuleTypeEnum.MANUAL.getCode());
    List<WitRuleResult> select = witRuleResultDao.select(query);
    if (CollectionUtil.isNotEmpty(select)){
      WitRuleResult witRuleResult = select.get(0);
      //创建稽核结果
      if (StringUtil.isEmpty(createRuleQO.getRuleDetailId())){
        WitRuleResultDetail detail = createRuleDetail(createRuleQO, witRuleResult.getId(), pcxBill);
        witRuleResultDetailDao.insert(detail);
        return detail;
      }else {
        //更新
        WitRuleResultDetail witRuleResultDetail = witRuleResultDetailDao.selectById(createRuleQO.getRuleDetailId());
        witRuleResultDetail.setErrMsg(createRuleQO.getErrMsg());
        witRuleResultDetailDao.update(witRuleResultDetail);
        return witRuleResultDetail;
      }
    }else {
      WitRuleResult result = new WitRuleResult();
      AuditDTO.ReqMeta auditDTO = new AuditDTO.ReqMeta();
      auditDTO.setBillId(createRuleQO.getBillId());
      auditDTO.setFiscal(pcxBill.getFiscal());
      auditDTO.setAuditType(AuditRuleTypeEnum.MANUAL.getCode());
      auditDTO.setRefBlock(createRuleQO.getClassifyCode());
      result.setId(auditDTO.key());
      result.setRefBlock(createRuleQO.getClassifyCode());
      result.setBillId(createRuleQO.getBillId());
      result.setBillNo(pcxBill.getBillNo());
      result.setAgyCode(pcxBill.getAgyCode());
      result.setAgyName(pcxBill.getAgyName());
      result.setFiscal(pcxBill.getFiscal());
      result.setMofDivCode(pcxBill.getMofDivCode());
      result.setBillFunc(pcxBill.getBillFunc());
      result.setBillAmt(pcxBill.getCheckAmt());
      result.setCreatedTime(DateUtil.nowTime());
      result.setCreator(PtyContext.getUsername());
      result.setCreatorName(PtyContext.getUsernameCn());
      result.setAuditType(AuditRuleTypeEnum.MANUAL.getCode());
      result.setBillStatus("1");
      result.setWarnCount(0);
      result.setErrCount(0);
      witRuleResultDao.insert(result);

      WitRuleResultDetail detail = createRuleDetail(createRuleQO, auditDTO.key(), pcxBill);
      witRuleResultDetailDao.insert(detail);
      return detail;
    }
  }

  private static @NotNull WitRuleResultDetail createRuleDetail(WitCreateRuleQO createRuleQO, String ruleResultId, PcxBill pcxBill) {
    //生成稽核明细
    WitRuleResultDetail detail = new WitRuleResultDetail();
    detail.setResultId(ruleResultId);
    detail.setBillId(pcxBill.getId());
    detail.setFiscal(pcxBill.getFiscal());
    detail.setRuleId(ruleResultId);
    detail.setRuleName(createRuleQO.getErrMsg());


    detail.setRuleType(RuleAct.WARN.name());
    detail.setRuleReference(createRuleQO.getClassifyCode());
    detail.setRefBlock(createRuleQO.getClassifyCode());
    detail.setAuditType(AuditRuleTypeEnum.MANUAL.getCode());
    detail.setCreator(PtyContext.getUsername());
    detail.setCreatedTime(DateUtil.nowTime());
    detail.setPassState(1);
    detail.setStatus("1");
    detail.setErrMsg(createRuleQO.getErrMsg());
    detail.setRuleField(createRuleQO.getFieldValue());
    PagePosition build = PagePosition.builder()
            .rowId(createRuleQO.getRowId())
            .blockCode(createRuleQO.getBlockCode())
            .blockName(createRuleQO.getBlockName())
            .blockTitle(createRuleQO.getBlockTitle())
            .classifyName(createRuleQO.getClassifyName())
            .classifyCode(createRuleQO.getClassifyCode())
            .fieldValue(createRuleQO.getFieldValue())
            .fieldName(createRuleQO.getFieldName())
            .fieldTitle(createRuleQO.getFieldTitle())
            .build();
    detail.setDataInfo(JSONUtil.toJsonStr(build));
    detail.setCurData(createRuleQO.getRowId());
    if (StringUtil.isNotEmpty(createRuleQO.getRowId())){
      detail.setId(MD5.create().digestHex(detail.key()+ createRuleQO.getRowId()));
    }else {
      detail.setId(detail.key());
    }
    return detail;
  }


  private List<WitRuleResultDetail> extractDetails(List<WitRuleResult> results) {
        if (CollectionUtil.isEmpty(results)) {
            return Collections.emptyList();
        }
        return results.stream()
                .map(WitRuleResult::getDetailList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

  private boolean checkForStatus(List<WitRuleResultDetail> details, RuleAct ruleAct, String status) {
      return details.stream()
              .anyMatch(detail -> ruleAct.toString().equals(detail.getRuleType()) && status.equals(detail.getStatus()));
  }

  private boolean checkForPassState(List<WitRuleResultDetail> details, RuleAct ruleAct, int passState) {
      return details.stream()
              .anyMatch(detail -> ruleAct.toString().equals(detail.getRuleType()) && (Objects.isNull(detail.getPassState()) || Integer.valueOf(passState).equals(detail.getPassState())));
  }


  private @Nullable PcxExpDetailEcsRel getPcxExpDetailEcsRel(WitAttachQO witAttachQO) {
    WitRuleResultDetail witRuleResultDetail = witRuleResultDetailDao.selectById(witAttachQO.getRuleDetailId());
    if (witRuleResultDetail == null){
      log.warn("当前稽核信息不存在 detailId {}", witAttachQO.getRuleDetailId());
      return null;
    }
    //稽核的信息在票据上才处理稽核信息，否则不处理
    if (!witRuleResultDetail.getRefBlock().equals(PositionBlockEnum.ECSEXPMATCH.getCode())){
      return null;
    }
    //查询票信息
    PcxExpDetailEcsRel pcxExpDetailEcsRel = pcxExpDetailEcsRelService.selectById(witRuleResultDetail.getCurData());
    if (pcxExpDetailEcsRel == null){
      pcxExpDetailEcsRel = pcxExpDetailEcsRelService.selectByDetailId(witRuleResultDetail.getCurData());
      if (Objects.isNull(pcxExpDetailEcsRel)){
        log.warn("当前稽核信息不存在 detailId {}", witAttachQO.getRuleDetailId());
        return null;
      }
    }
    return pcxExpDetailEcsRel;
  }
  @Builder
  @Data
  public static class PagePosition {

    /**
     * 稽核锚点
     */
    private String classifyCode;
    private String classifyName;
    /**
     * 行id
     */
    private String rowId;
    /**
     * 字段值
     */
    private String fieldValue;
    private String fieldName;
    private String fieldTitle;
    /**
     * block code
     */
    private String blockCode;
    private String blockName;
    private String blockTitle;
  }
}
