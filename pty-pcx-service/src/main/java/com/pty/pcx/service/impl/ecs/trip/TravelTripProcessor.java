package com.pty.pcx.service.impl.ecs.trip;

import com.beust.jcommander.internal.Lists;
import com.pty.mad.entity.MadArea;
import com.pty.mad.entity.MadWorkLocations;
import com.pty.mad.qo.MadWorkLocationsByEmpQO;
import com.pty.mad.vo.MadEmployeeWithWorkLocationVO;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.constant.TripTypeEnum;
import com.pty.pcx.common.enu.BillExpDetailSourceEnum;
import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.mad.IMadWorklocationsExternalService;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.service.impl.ecs.EcsExpOptService;
import com.pty.pcx.service.impl.ecs.dto.CityDateVO;
import com.pty.pcx.service.impl.ecs.dto.DateVO;
import com.pty.pcx.service.impl.ecs.dto.EcsExpTripDTO;
import com.pty.pcx.service.impl.ecs.handler.GeneralExpenseHandler;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public class TravelTripProcessor {

    @Autowired
    private IMadWorklocationsExternalService madWorklocationsExternalService;

    public MatchTripResult matchTripByDetail(List<PcxBillExpDetailBase> details, String claimantCode, Boolean isHotelTrip){
        MatchTripResult result = new MatchTripResult();
        List<PcxBillExpDetailBase> detailList = details.stream().filter(item->EcsExpOptService.isTripDetail(item) && tripNeedField(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailList)){
            return result;
        }
        InvoiceDtoWrapper<Object, PcxBillExpBase, PcxBillExpDetailBase> noEcsWrapper = InvoiceDtoWrapper.builder().type("noEcs").build();
        result.setNoEcsDetail(noEcsWrapper);
        List<PcxBillExpDetailTravel> travelDetailList = EcsExpOptService.getTravelDetailList(detailList);

        //进行规划行程
        doMatchTrip(result, noEcsWrapper, travelDetailList, claimantCode, isHotelTrip);

        return result;
    }

    //理行程只需要满足条件的明细
    private boolean tripNeedField(PcxBillExpDetailBase detailBase){
        PcxBillExpDetailTravel travel = (PcxBillExpDetailTravel) detailBase;
        if (Objects.equals(PcxConstant.TRAVEL_DETAIL_3021102, travel.getExpDetailCode())){
            return StringUtil.isNotEmpty(travel.getEmpCode(), travel.getStartTime(), travel.getFinishTime(),  travel.getEndCityCode());
        }else{
            return StringUtil.isNotEmpty(travel.getEmpCode(), travel.getStartTime(), travel.getFinishTime(), travel.getStartCityCode(),travel.getEndCityCode());
        }

    }
    private void doMatchTrip(MatchTripResult result,
                             InvoiceDtoWrapper noEcsWrapper,
                             List<PcxBillExpDetailTravel> travelDetailList,
                             String claimantCode,
                             Boolean isHotelTrip) {

        //擦除历史的被改签明细关联的改签明细id,整理改签关联的原行程
        disposeEcsChangeDetail(travelDetailList);

        //匹配行程，行程不完整的标记上需要补充的信息
        //把每个人的票分组，使用城市间交通费作为依据
        //城市间交通费按时间排序，
        //只要是闭环就不用补
        // 如果不是从工作地出发，则提示补充开始行程
        // 如果不是从工作地结束，则提示补充结束行程
        // 如果中间存在不衔接行程，则提示补充从哪到哪的行程
        //行程匹配完后，匹配住宿费是否可以填充到城市间交通费上，如果不能匹配则提示补充住宿费
        Map<String, List<PcxBillExpDetailTravel>> userCityTrafficMap = new HashMap<>();
        Map<String, List<PcxBillExpDetailTravel>> userHotelMap = new HashMap<>();
        //每个人最早的城市间交通费，如果没有工作地，就以出发地作为工作地
        Map<String, PcxBillExpDetailTravel> userEarlyTripMap = new HashMap<>();
        boolean haveCityTraffic = false;
        boolean haveEcsCityTraffic = false;
        Set<String> employerCodes = new HashSet<>();
        employerCodes.add(claimantCode);
        for (PcxBillExpDetailTravel travel : travelDetailList) {
            //城市间交通费或者改签费
            if (EcsExpOptService.isIntercityTraffic(travel)){
                haveCityTraffic = true;
                if (!haveEcsCityTraffic && Objects.equals("ecs", travel.getSource())){
                    haveEcsCityTraffic = true;
                }
                List<PcxBillExpDetailTravel> cityTrafficList = userCityTrafficMap.computeIfAbsent(travel.getEmpCode(), key -> new ArrayList<>());
                cityTrafficList.add(travel);
                PcxBillExpDetailTravel earlyTraffic = userEarlyTripMap.get(travel.getEmpCode());
                if (Objects.isNull(earlyTraffic) || travel.getStartTime().compareTo(earlyTraffic.getStartTime()) < 0){
                    userEarlyTripMap.put(travel.getEmpCode(), travel);
                }
            }
            employerCodes.add(travel.getEmpCode());
            if (EcsExpOptService.isHotelDetail(travel)){
                List<PcxBillExpDetailTravel> hotelList = userHotelMap.computeIfAbsent(travel.getEmpCode(), key -> new ArrayList<>());
                hotelList.add(travel);
            }

        }
        //如果没有城市间交通费，也没有住宿费，则不处理
        if (!haveCityTraffic && userHotelMap.isEmpty()){
            return;
        }
        //如果还不是住宿费理票,还没有城市间交通费，但是有住宿费，则改为住宿费理票
        if (!isHotelTrip && !haveCityTraffic){
            isHotelTrip = true;
        }
        PcxBillExpDetailTravel travel = travelDetailList.get(0);
        Map<String, MadArea> empWorkCityMap = queryEmpWorkCityMap(travel.getFiscal(), travel.getAgyCode(), travel.getMofDivCode(),
                userEarlyTripMap, employerCodes, claimantCode);

        //有真正的电子凭证的票生成的城市间交通费，则用城市间交通费为依据进行理票
        //否则用住宿费为依据进行理票
        //住宿费理票会以住宿费作为排序，给它生成中间的城市间交通费，但系统补充的城市间交通费，填报人可以改成手动添加，所有生成中间的城市间交通费时会用手动添加的城市间交通费进行先匹配填充
        //住宿费理票不能有真正的电子凭证城市间交通费的票
        if (isHotelTrip && !haveEcsCityTraffic){
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : userHotelMap.entrySet()) {
                List<PcxBillExpDetailTravel> hotelList = entry.getValue();
                List<PcxBillExpDetailTravel> travelList = userCityTrafficMap.getOrDefault(entry.getKey(), Lists.newArrayList());
                MadArea madArea = empWorkCityMap.get(entry.getKey());
                doUserHotelMatchTrip(result, noEcsWrapper, hotelList, madArea, travelList);
            }
        }else{
            for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : userCityTrafficMap.entrySet()) {

                List<PcxBillExpDetailTravel> hotelList = userHotelMap.getOrDefault(entry.getKey(), Lists.newArrayList());

                MadArea madArea = empWorkCityMap.get(entry.getKey());
                doUserMatchTrip(result, noEcsWrapper, entry.getValue(), hotelList, madArea, TripTypeEnum.TRAFFIC.getName());
            }
        }

        margeTripByCityDate(result);
    }

    private void doUserHotelMatchTrip(MatchTripResult result,
                                      InvoiceDtoWrapper noEcsWrapper,
                                      List<PcxBillExpDetailTravel> hotelList,
                                      MadArea madArea,
                                      List<PcxBillExpDetailTravel> travelList) {
        //手动添加的城市间交通费，按照时间城市分组，给住宿费添加城市间交通费，使用手动添加的先匹配一下，如果有，则不用系统补充
        //同一天到达多个城市的A-B-C，算一个中间行程，出发地市A，到达地是C
        Map<String, List<PcxBillExpDetailTravel>> dateMap = travelList.stream().collect(Collectors.groupingBy(item -> transDate(item.getFinishTime())));

        Map<String, List<PcxBillExpDetailTravel>> finalDateMap = new HashMap<>();
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : dateMap.entrySet()) {
            List<PcxBillExpDetailTravel> value = entry.getValue();
            value.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
            String key = String.format("%s#%s#%s", entry.getKey(), value.get(0).getStartCityCode(), value.get(value.size()-1).getEndCityCode());
            finalDateMap.put(key, value);
        }

        hotelList.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
        PcxBillExpDetailTravel first = hotelList.get(0);
        PcxBillExpDetailTravel last = hotelList.get(hotelList.size()-1);

        if(Objects.isNull(madArea)){
            madArea = new MadArea();
            madArea.setAreaCode(first.getEndCityCode());
            madArea.setAreaName(first.getEndCity());
        }

        List<PcxBillExpDetailTravel> replenishList = new ArrayList<>();
        String key = String.format("%s#%s#%s", first.getStartTime(), madArea.getAreaCode(), first.getEndCityCode());
        if (!finalDateMap.containsKey(key)){
            PcxBillExpDetailTravel replenishFirst = hotelReplenishTraffic(madArea.getAreaCode(), madArea.getAreaName(), first.getEndCityCode(), first.getEndCity(),
                    first.getEmpCode(), first.getEmpName(), first.getEmpType(), first.getBudLevel(),
                    first.getStartTime(), PcxConstant.NO_ARRIVE_ECS, first.getFiscal(), first.getMofDivCode(), first.getAgyCode(), PcxConstant.NO_ARRIVE_ECS_CODE);
            replenishList.add(replenishFirst);
            noEcsWrapper.addExpDetail(replenishFirst);
        }
        for (int i = 0; i < hotelList.size()-1; i++) {
            PcxBillExpDetailTravel one = hotelList.get(i);
            PcxBillExpDetailTravel two = hotelList.get(i+1);
            if (one.getFinishTime().compareTo(two.getStartTime())<0){
                PcxBillExpDetailTravel replenishHotel = hotelReplenishHotel(one.getEndCityCode(), one.getEndCity(),
                        first.getEmpCode(), first.getEmpName(), first.getEmpType(), first.getBudLevel(),
                        two.getStartTime(), one.getFinishTime(), "无住宿票", first.getFiscal(), first.getMofDivCode(), first.getAgyCode());
                hotelList.add(i+1, replenishHotel);
                noEcsWrapper.addExpDetail(replenishHotel);
            } else if (!Objects.equals(one.getEndCityCode(), two.getEndCityCode())) {
                String insertKey = String.format("%s#%s#%s", one.getFinishTime(), one.getEndCityCode(), two.getEndCityCode());
                if (!finalDateMap.containsKey(insertKey)){
                    PcxBillExpDetailTravel replenishTraffic = hotelReplenishTraffic(one.getEndCityCode(), one.getEndCity(), two.getEndCityCode(), two.getEndCity(),
                            first.getEmpCode(), first.getEmpName(), first.getEmpType(), first.getBudLevel(),
                            one.getFinishTime(),  PcxConstant.NO_ARRIVE_ECS, first.getFiscal(), first.getMofDivCode(), first.getAgyCode(), PcxConstant.NO_ARRIVE_ECS_CODE);
                    noEcsWrapper.addExpDetail(replenishTraffic);
                    replenishList.add(replenishTraffic);
                }

            }
        }
        String lastKey = String.format("%s#%s#%s", last.getFinishTime(), last.getEndCityCode(), madArea.getAreaCode());
        if (!finalDateMap.containsKey(lastKey)){
            PcxBillExpDetailTravel replenishLast = hotelReplenishTraffic(last.getEndCityCode(), last.getEndCity(), madArea.getAreaCode(), madArea.getAreaName(),
                    first.getEmpCode(), first.getEmpName(), first.getEmpType(), first.getBudLevel(),
                    last.getFinishTime(), PcxConstant.NO_BACK_ECS, first.getFiscal(), first.getMofDivCode(), first.getAgyCode(), PcxConstant.NO_BACK_ECS_CODE);
            noEcsWrapper.addExpDetail(replenishLast);
            replenishList.add(replenishLast);
        }
        replenishList.addAll(travelList);

        doUserMatchTrip(result, noEcsWrapper, replenishList, hotelList, madArea, TripTypeEnum.HOTEL.getName());
    }

    private PcxBillExpDetailTravel hotelReplenishHotel(String endCityCode, String endCity, String empCode, String empName,
                                                       String empType, String budLevel, String finishTime, String startTime,
                                                       String remark, String fiscal, String mofDivCode, String agyCode) {
        PcxBillExpDetailTravel hotel = new PcxBillExpDetailTravel();
        hotel.setId(IDGenerator.id());
        hotel.setEcsAmt(BigDecimal.ZERO);
        hotel.setInputAmt(BigDecimal.ZERO);
        hotel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
        hotel.setEmpCode(empCode);
        hotel.setEmpName(empName);
        hotel.setEmpType(empType);
        hotel.setBudLevel(budLevel);
        hotel.setStartTime(startTime);
        hotel.setFinishTime(finishTime);
        hotel.setEndCityCode(endCityCode);
        hotel.setEndCity(endCity);
        hotel.setRemark(remark);
        hotel.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        hotel.setFiscal(fiscal);
        hotel.setMofDivCode(mofDivCode);
        hotel.setAgyCode(agyCode);
        return hotel;
    }

    private PcxBillExpDetailTravel hotelReplenishTraffic(String areaCode, String areaName, String endCityCode, String endCity,
                                       String empCode, String empName, String empType, String budLevel,
                                       String startTime, String remark, String fiscal, String mofDivCode, String agyCode, int tripFlag) {
        PcxBillExpDetailTravel travel = new PcxBillExpDetailTravel();
        travel.setId(IDGenerator.id());
        travel.setEcsAmt(BigDecimal.ZERO);
        travel.setInputAmt(BigDecimal.ZERO);
        travel.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021101);
        travel.setEmpCode(empCode);
        travel.setEmpName(empName);
        travel.setEmpType(empType);
        travel.setBudLevel(budLevel);
        travel.setStartCityCode(areaCode);
        travel.setStartCity(areaName);
        if (Objects.equals("无返程票", remark)){
            travel.setStartTime(String.format("%s %s", startTime, "14:00"));
            travel.setFinishTime(String.format("%s %s", startTime, "15:00"));
        }else{
            travel.setStartTime(String.format("%s %s", startTime, GeneralExpenseHandler.defaultStartTime));
            travel.setFinishTime(String.format("%s %s", startTime, GeneralExpenseHandler.defaultFinishTime));
        }

        travel.setEndCityCode(endCityCode);
        travel.setEndCity(endCity);
        travel.setRemark(remark);
        travel.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        travel.setFiscal(fiscal);
        travel.setMofDivCode(mofDivCode);
        travel.setAgyCode(agyCode);
        travel.setTripFlag(tripFlag);
        return travel;
    }

    /**
     * 擦除历史被改签明细关联的改签明细id,整理改签关联的原行程
     * 如果有改签费，找出改签费对应的原城市间交通费，并把原城市间交通费记录改签费id。原城市间交通费不进入行程计算
     * @param travelDetailList
     */
    private void disposeEcsChangeDetail(List<PcxBillExpDetailTravel> travelDetailList) {
        Map<String, List<PcxBillExpDetailTravel>> empEcsChangeMap = new HashMap<>();
        Map<String, List<PcxBillExpDetailTravel>> empIntercityTrafficMap = new HashMap<>();
        //把行程相关信息擦除，重新理票后会写上新的行程信息
        travelDetailList
                .forEach(item -> {
                    item.setTripId("");
                    item.setTripSeq(0);
                    item.setOriginId("");
                    //职员的改签和城市间交通费
                    if (PcxConstant.TRAVEL_DETAIL_3021110.equals(item.getExpDetailCode())){
                        List<PcxBillExpDetailTravel> travels = empEcsChangeMap.computeIfAbsent(item.getEmpCode(), key -> new ArrayList<>());
                        travels.add(item);
                    } else if (PcxConstant.TRAVEL_DETAIL_3021101.equals(item.getExpDetailCode())) {
                        List<PcxBillExpDetailTravel> travels = empIntercityTrafficMap.computeIfAbsent(item.getEmpCode(), key -> new ArrayList<>());
                        travels.add(item);
                    }
                });
        //处理改签找到改签票对应的城市间交通费，把城市间交通费记录上改签费明细id，这些城市间交通费不用理行程
        for (Map.Entry<String, List<PcxBillExpDetailTravel>> entry : empEcsChangeMap.entrySet()) {

            List<PcxBillExpDetailTravel> travels = empIntercityTrafficMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(travels)){
                continue;
            }
            List<PcxBillExpDetailTravel> value = entry.getValue();
            value.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
            travels.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));
            for (int i = 0; i < value.size() && !travels.isEmpty(); i++) {
                long matchClose = monthMinutes;
                PcxBillExpDetailTravel matchTravel = null;
                for (PcxBillExpDetailTravel travel : travels) {
                    //找到改签票对应的城市间交通费，时间最接近的获胜
                    Pair<Boolean, Long> pair = matchEcsChangeDetail(value.get(i), travel);
                    if (pair.getLeft() && pair.getRight() < matchClose) {
                        matchClose = pair.getRight();
                        matchTravel = travel;
                    }
                }
                if (Objects.nonNull(matchTravel)){
                    travels.remove(matchTravel);
                    matchTravel.setOriginId(value.get(i).getId());
                }
            }
        }
    }

    private final Pair<Boolean, Long> matchFail = Pair.of(Boolean.FALSE, 0L);
    private Pair<Boolean, Long> matchEcsChangeDetail(PcxBillExpDetailTravel ecsChange, PcxBillExpDetailTravel travel) {
        //交通工具相同，出发地相同，时间在30天内
        if (!Objects.equals(ecsChange.getTrafficToolCode(), travel.getTrafficToolCode())){
            return matchFail;
        }
        if (!Objects.equals(ecsChange.getStartCityCode(), travel.getStartCityCode())){
            return matchFail;
        }
        long betweenMinute = PcxDateUtil.betweenMinuteDateTime(ecsChange.getStartTime(), travel.getStartTime());
        if (betweenMinute > monthMinutes){
            return matchFail;
        }
        return Pair.of(Boolean.TRUE, betweenMinute);
    }

    private static long monthMinutes = 30 * 24 * 60;

    /**
     * 通过行程人是否在一个城市驻留了有交集的时间段，来合并为同一行程
     */
    private void margeTripByCityDate(MatchTripResult result) {
        if (CollectionUtils.isEmpty(result.getTripDTOList())){
            return;
        }
        List<EcsExpTripDTO> tripList = result.getTripDTOList();
        int index = 0;
        while (index < tripList.size()-1){
            EcsExpTripDTO baseTrip = tripList.get(index);
            List<EcsExpTripDTO> lastTripList = tripList.subList(index+1, tripList.size());
            Iterator<EcsExpTripDTO> iterator = lastTripList.iterator();
            while (iterator.hasNext()){
                EcsExpTripDTO next = iterator.next();
                if (judgeSameTrip(baseTrip.getCityDateVOList(), next.getCityDateVOList())) {
                    //合并同行程
                    baseTrip.eatSameTrip(next);
                    iterator.remove();
                }
            }
            index ++;
        }
    }

    /**
     * 判断两个时间区间列表是否在同一个城市有重叠的时间区间
     * 用例1：baseCityDateVOList:[{"cityCode":"1101","startDate":"2024-12-11","endDate":"2024-12-13"}],nextCityDateVOList:[{"cityCode":"1101","startDate":"2024-12-12","endDate":"2024-12-15"}],则返回true
     * 用例1：baseCityDateVOList:[{"cityCode":"1101","startDate":"2024-12-11","endDate":"2024-12-13"}],nextCityDateVOList:[{"cityCode":"1201","startDate":"2024-12-11","endDate":"2024-12-13"},{"cityCode":"1101","startDate":"2024-12-13","endDate":"2024-12-15"}],则返回false
     * @param baseCityDateVOList
     * @param nextCityDateVOList
     * @return
     */
    private boolean judgeSameTrip(List<CityDateVO> baseCityDateVOList, List<CityDateVO> nextCityDateVOList) {
        if (CollectionUtils.isEmpty(baseCityDateVOList) || CollectionUtils.isEmpty(nextCityDateVOList)){
            return false;
        }
        //通过行程中在一个城市的时间段是否有交集来判断是否同一行程
        for (CityDateVO baseCityDateVO : baseCityDateVOList) {
            for (CityDateVO nextCityDateVO : nextCityDateVOList) {
                if (baseCityDateVO.getCityCode().equals(nextCityDateVO.getCityCode())) {
                    LocalDate baseStartDate = LocalDate.parse(baseCityDateVO.getStartDate());
                    LocalDate baseEndDate = LocalDate.parse(baseCityDateVO.getEndDate());
                    LocalDate nextStartDate = LocalDate.parse(nextCityDateVO.getStartDate());
                    LocalDate nextEndDate = LocalDate.parse(nextCityDateVO.getEndDate());

                    // 检查时间段是否有重叠
                    if (!(baseEndDate.isBefore(nextStartDate) || baseStartDate.isAfter(nextEndDate))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private void doUserMatchTrip(MatchTripResult result,
                                 InvoiceDtoWrapper noEcsWrapper,
                                 List<PcxBillExpDetailTravel> fixTrafficList,
                                 List<PcxBillExpDetailTravel> hotelList,
                                 MadArea area,
                                 String tripType) {

        //拷贝一份列表
        List<PcxBillExpDetailTravel> tempTrafficList = new ArrayList<>(fixTrafficList);

        //按照出发时间排序
        tempTrafficList.sort(Comparator.comparing(PcxBillExpDetailTravel::getStartTime));

        //如果出发地不是工作地，则补充一个出发地行程
        if (!tempTrafficList.get(0).getStartCityCode().equals(area.getAreaCode())){
            PcxBillExpDetailTravel detail = replenishHeadTraffic(area, tempTrafficList.get(0));
            noEcsWrapper.addExpDetail(detail);
            tempTrafficList.add(0, detail);
        }
        //最后的目的地不是最开始的出发地，需要补充返回出发地的行程
        if (!tempTrafficList.get(tempTrafficList.size() - 1).getEndCityCode().equals(tempTrafficList.get(0).getStartCityCode())) {
            PcxBillExpDetailTravel detail = replenishEndTraffic(tempTrafficList.get(tempTrafficList.size() - 1), tempTrafficList.get(0),"无到达票");
            noEcsWrapper.addExpDetail(detail);
            tempTrafficList.add(detail);
        }

        //中间有未衔接的行程，需要补充
        for (int i = 0; i < tempTrafficList.size() - 1; i++) {
            PcxBillExpDetailTravel startTraffic = tempTrafficList.get(i);
            PcxBillExpDetailTravel endTraffic = tempTrafficList.get(i + 1);
            if (!startTraffic.getEndCityCode().equals(endTraffic.getStartCityCode())) {
                PcxBillExpDetailTravel detail = replenishTraffic(startTraffic, endTraffic,"无到达票", area.getAreaCode());
                noEcsWrapper.addExpDetail(detail);
                tempTrafficList.add(i+1, detail);
            }
        }

        //真正的理行程
        List<EcsExpTripDTO> tripList = obtainTripList(tempTrafficList, hotelList, noEcsWrapper, area.getAreaCode());
        for (EcsExpTripDTO tripDTO : tripList) {
            tripDTO.setTripType(tripType);
        }

        result.getTripDTOList().addAll(tripList);

    }

    private List<EcsExpTripDTO> obtainTripList(List<PcxBillExpDetailTravel> tempTrafficList,
                                               List<PcxBillExpDetailTravel> hotelList,
                                               InvoiceDtoWrapper noEcsWrapper,
                                               String workCityCode) {
        //到达城市就是住宿城市
        List<String> hotelCityCodes = new ArrayList<>();
        //多个行程会有多个住宿城市list
        List<List<String>> multiHotelCityCodes = new ArrayList<>();
        //多个行程会有多个list
        List<List<PcxBillExpDetailTravel>> multiTrip = new ArrayList<>();
        int start = 0;
        for (int i = 0; i < tempTrafficList.size() - 1; i++) {
            PcxBillExpDetailTravel startTraffic = tempTrafficList.get(i);
            //从工作地出发，记录一个新的行程
            if (i > 0 && startTraffic.getStartCityCode().equals(workCityCode)){
                //如果中间返回工作地，并且同一天又出发则不切分行程
                PcxBillExpDetailTravel prevTravel = tempTrafficList.get(i - 1);
                if (!( Objects.equals(prevTravel.getEndCityCode(), workCityCode)
                        && Objects.equals(transDate(prevTravel.getFinishTime()), transDate(startTraffic.getStartTime())) )){
                    if (BillExpDetailSourceEnum.REPLENISH.getCode().equals(startTraffic.getSource())){
                        startTraffic.setRemark("无出发票");
                    }
                    multiTrip.add(new ArrayList<>(tempTrafficList.subList(start, i)));
                    start = i;
                    hotelCityCodes.remove(hotelCityCodes.size()-1);
                    multiHotelCityCodes.add(hotelCityCodes);
                    hotelCityCodes = new ArrayList<>();
                }
            }
            PcxBillExpDetailTravel endTraffic = tempTrafficList.get(i + 1);
            hotelCityCodes.add(endTraffic.getStartCityCode());
            //i==size-2说明到最后了 把剩下的归类到一个行程中
            if (i == tempTrafficList.size() - 2){
                multiTrip.add(new ArrayList<>(tempTrafficList.subList(start, tempTrafficList.size())));
                multiHotelCityCodes.add(hotelCityCodes);
            }
        }
        List<EcsExpTripDTO> tripDTOList = new ArrayList<>();
        //遍历处理每个行程，校验行程每个城市的驻留时间，是否有住宿费，如果没有，则系统补充一个住宿费
        for (int i = 0; i < multiTrip.size(); i++) {
            //行程的住宿舍城市
            List<String> itemHotelCity = multiHotelCityCodes.get(i);
            //行程的城市间交通费
            List<PcxBillExpDetailTravel> travels = multiTrip.get(i);
            //系统补充的住宿费
            List<PcxBillExpDetailTravel> replishHotelList = new ArrayList<>();

            EcsExpTripDTO dto = new EcsExpTripDTO();
            tripDTOList.add(dto);
            dto.setId(IDGenerator.id());
            dto.setStartTime(travels.get(0).getStartTime());
            dto.setEndTime(travels.get(travels.size() - 1).getFinishTime());
            dto.setEmpName(travels.get(0).getEmpName());
            dto.setEmpCode(travels.get(0).getEmpCode());

            //从住宿费中过滤出符合行程时间和城市的住宿费
            List<PcxBillExpDetailTravel> itemTripHotelList = hotelList.stream().filter(item -> item.getStartTime().compareTo(transDate(dto.getStartTime()))>=0
                    && item.getStartTime().compareTo(transDate(dto.getEndTime()))<=0
                    && itemHotelCity.contains(item.getEndCityCode())).collect(Collectors.toList());
            PcxBillExpDetailTravel lastHotel =  itemTripHotelList.stream().max(Comparator.comparing(PcxBillExpDetailTravel::getFinishTime)).orElse(null);

            //如果返程是系统补充的，则根据最后面的住宿费明细来补充行程
            disposeReplenishLastTraffic(lastHotel, travels.get(travels.size()-1));

            //在一个城市住宿的时间段
            Map<String, List<DateVO>> hotelDateListMap = itemTripHotelList.stream()
                    .collect(Collectors.groupingBy(PcxBillExpDetailTravel::getEndCityCode,
                            Collectors.mapping(this::transHotelDateVo, Collectors.toList())));
            dto.setEmpCityHotelDateMap(hotelDateListMap);
            boolean alwaysSameDay = true;
            for (int j = 1; j < travels.size(); j++) {
                PcxBillExpDetailTravel startTraffic = travels.get(j - 1);
                PcxBillExpDetailTravel endTraffic = travels.get(j);
                String hotelCityCode = endTraffic.getStartCityCode();
                String hotelCity = endTraffic.getStartCity();
                //行程人在hotelCityCode驻留的时间段
                //在城市滞留的时间不带时分
                //最后的返程不算城市滞留时间
                //驻留时间改成取两个大交通的出发时间，因为出发时间和到达时间有可能跨天，以出发时间作为在下一个城市的驻留时间
                boolean sameDay = transDate(startTraffic.getStartTime()).equals(transDate(endTraffic.getStartTime()));
                if (!sameDay){
                    alwaysSameDay = false;
                }
                //如果就一个往返A-B-A，则记录B城市驻留时间
                //如果多个段，则最后返程不需要记录城市驻留时间
                //如果中间的段是当天到达并出发，也不需要记录，以后面记录的城市记驻留时间
                //如果一天内从A-B-C-A,则最后要记录一下C的驻留时间
                boolean canAdd = travels.size()==2 || (!sameDay || (alwaysSameDay &&j == travels.size()-1));
                if (canAdd){
                    dto.addCityDate(new CityDateVO(hotelCityCode, transDate(startTraffic.getStartTime()), transDate(endTraffic.getStartTime()), transDate(endTraffic.getFinishTime())));
                }
                //同一天的两个行程，不需要住宿
                boolean sameHotelDay = transDate(startTraffic.getFinishTime()).equals(transDate(endTraffic.getStartTime()));
                if (sameHotelDay){
                    continue;
                }
                //在城市滞留的时间
                //城市驻留时间还以
                DateVO cityDate = new DateVO(transDate(startTraffic.getFinishTime()), transDate(endTraffic.getStartTime()));
                List<DateVO> dateVOS = hotelDateListMap.get(hotelCityCode);
                //在城市的滞留时间，没有住宿费明细的时间
                List<DateVO> noCoverDateList = getNoCoverDateList(cityDate, dateVOS);
                if (CollectionUtils.isNotEmpty(noCoverDateList)){
                    for (DateVO dateVO : noCoverDateList) {
                        PcxBillExpDetailTravel hotel = replenishHotel(dateVO.getStartDate(), dateVO.getEndDate(), hotelCityCode, hotelCity, startTraffic);
                        replishHotelList.add(hotel);
                        noEcsWrapper.addExpDetail(hotel);
                    }
                }
            }
            //处理一下返程的结束时间，返程出发时间可结束时间跨0点的情况，以返程的结束时间计算补助
            disposeLastCityDate(dto.getCityDateVOList(), travels.get(travels.size()-1));
            int seq = 1;
            //记录一下行程序号
            for (PcxBillExpDetailTravel travel : travels) {
                travel.setTripSeq(seq ++);
                dto.addDetailId(travel.getId());
            }
            //更新行程的排序，有可能两个城市是同一天到达并出发
            dto.setTrafficList(travels);

            if (CollectionUtils.isNotEmpty(replishHotelList)){
                for (PcxBillExpDetailTravel noEcsDetail : replishHotelList) {
                    dto.addDetailId(noEcsDetail.getId());
                }
            }

            for (PcxBillExpDetailTravel hotel : itemTripHotelList) {
                dto.addDetailId(hotel.getId());
            }
        }
        return tripDTOList;
    }

    private void disposeLastCityDate(List<CityDateVO> cityDateVOList, PcxBillExpDetailTravel lastTraffic) {
        if (CollectionUtils.isEmpty(cityDateVOList)){
            return;
        }
        String startDate = transDate(lastTraffic.getStartTime());
        String endDate = transDate(lastTraffic.getFinishTime());
        if (!Objects.equals(startDate, endDate)){
            CityDateVO cityDateVO = cityDateVOList.get(cityDateVOList.size() - 1);
            if (Objects.equals(cityDateVO.getEndDate(), startDate)){
                cityDateVO.setEndFinishDate(endDate);
            }
        }
    }

    private void disposeReplenishLastTraffic(PcxBillExpDetailTravel lastHotel, PcxBillExpDetailTravel traffic) {
        if (Objects.isNull(lastHotel) || !BillExpDetailSourceEnum.REPLENISH.getCode().equals(traffic.getSource())){
            return;
        }
        if (lastHotel.getEndCityCode().equals(traffic.getStartCityCode())
        && lastHotel.getStartTime().compareTo(transDate(traffic.getStartTime())) >= 0){
            traffic.setStartTime(lastHotel.getFinishTime() + " " + traffic.getStartTime().substring(11));
            traffic.setFinishTime(lastHotel.getFinishTime() + " " + traffic.getFinishTime().substring(11));
        }
    }

    public static String transDate(String finishTime) {
        if (StringUtils.isEmpty(finishTime)){
            return "";
        }
        return finishTime.substring(0, 10);
    }


    private DateVO transHotelDateVo(PcxBillExpDetailTravel travel){
        return new DateVO(travel.getStartTime(), travel.getFinishTime(), travel.getNoEcsReason(), travel.getSource());
    }

    public static long getDiffDays(String startTime, String endTime){
        if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)){
            return 0;
        }
        LocalDate startDate = LocalDate.parse(transDate(startTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(transDate(endTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        long between = ChronoUnit.DAYS.between(startDate, endDate);
        if (between == 0){
            return 1;
        }
        return between;
    }

    private PcxBillExpDetailTravel replenishHeadTraffic(MadArea area, PcxBillExpDetailTravel travel) {
        PcxBillExpDetailTravel expenseDetail = new PcxBillExpDetailTravel();
        expenseDetail.setId(IDGenerator.id());
        expenseDetail.setEcsAmt(BigDecimal.ZERO);
        expenseDetail.setInputAmt(BigDecimal.ZERO);
        expenseDetail.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021101);
        expenseDetail.setStartTime(getSubHour(travel.getStartTime(),-1));
        expenseDetail.setFinishTime(getSubHour(travel.getStartTime(),-1));
        expenseDetail.setStartCity(area.getAreaName());
        expenseDetail.setStartCityCode(area.getAreaCode());
        expenseDetail.setStartPlace("");
        expenseDetail.setEndCity(travel.getStartCity());
        expenseDetail.setEndCityCode(travel.getStartCityCode());
        expenseDetail.setEndPlace("");
        expenseDetail.setEmpName(travel.getEmpName());
        expenseDetail.setEmpCode(travel.getEmpCode());
        expenseDetail.setEmpType(travel.getEmpType());
        expenseDetail.setBudLevel(travel.getBudLevel());
        expenseDetail.setIsSubsidy(travel.getIsSubsidy());
        expenseDetail.setRemark("无出发票");
        expenseDetail.setFiscal(travel.getFiscal());
        expenseDetail.setMofDivCode(travel.getMofDivCode());
        expenseDetail.setAgyCode(travel.getAgyCode());
        expenseDetail.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        return expenseDetail;
    }

    private String getSubHour(String startTime, int hour) {
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        return startDateTime.plusHours(hour).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

    public Map<String, MadArea> queryEmpWorkCityMap(String fiscal, String agyCode, String mofDivCode,
                                                     Map<String, PcxBillExpDetailTravel> userEarlyTripMap,
                                                     Set<String> empCodes,
                                                     String claimantCode) {
        MadWorkLocationsByEmpQO empQO = new MadWorkLocationsByEmpQO();
        empQO.setFiscal(Integer.valueOf(fiscal));
        empQO.setAgyCode(agyCode);
        empQO.setMofDivCode(mofDivCode);
        empCodes.add(claimantCode);
        empQO.setEmpCodes(new ArrayList<>(empCodes));
        Map<String, MadArea> resultMap = new HashMap<>();
        List<MadEmployeeWithWorkLocationVO> locationVOS = madWorklocationsExternalService.selectListByEmpCodes(empQO);
        Map<String, MadWorkLocations> empWorkLocationMap = locationVOS.stream()
                .filter(item->Objects.nonNull(item.getMadWorkLocations()))
                .collect(Collectors.toMap(MadEmployeeWithWorkLocationVO::getMadCode, MadEmployeeWithWorkLocationVO::getMadWorkLocations));
        MadWorkLocations claimantWorkCity = empWorkLocationMap.get(claimantCode);
        for (String empCode : empCodes) {
            MadArea madArea = new MadArea();

            MadWorkLocations workCity = null;
            if (empCode.startsWith("OUT")){
                workCity = claimantWorkCity;
            }else{
                workCity = empWorkLocationMap.get(empCode);
            }
            if (Objects.nonNull(workCity)){
                madArea.setAreaCode(workCity.getCityCode());
                madArea.setAreaName(workCity.getCity());
                madArea.setParentCode(workCity.getProvinceCode());
                resultMap.put(empCode, madArea);
            }else{
                PcxBillExpDetailTravel travel = userEarlyTripMap.get(empCode);
                if (Objects.nonNull(travel)){
                    madArea.setAreaCode(travel.getStartCityCode());
                    madArea.setParentCode(travel.getStartCityCode().substring(0, travel.getStartCityCode().length()-2));
                    madArea.setAreaName(travel.getStartCity());
                    resultMap.put(empCode, madArea);
                }else if (Objects.nonNull(claimantWorkCity)){
                    madArea.setAreaCode(claimantWorkCity.getCityCode());
                    madArea.setParentCode(claimantWorkCity.getCityCode().substring(0, claimantWorkCity.getCity().length()-2));
                    madArea.setAreaName(claimantWorkCity.getCity());
                    resultMap.put(empCode, madArea);
                }
            }
        }
        return resultMap;
    }

    private PcxBillExpDetailTravel replenishHotel (String hotelStartTime,
                                                   String hotelEndTime,
                                                   String hotelCityCode,
                                                   String hotelCityName,
                                                   PcxBillExpDetailTravel travel){
        PcxBillExpDetailTravel expenseDetail = new PcxBillExpDetailTravel();
        expenseDetail.setId(IDGenerator.id());
        expenseDetail.setEcsAmt(BigDecimal.ZERO);
        expenseDetail.setInputAmt(BigDecimal.ZERO);
        expenseDetail.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021102);
        expenseDetail.setStartTime(hotelStartTime);
        expenseDetail.setFinishTime(hotelEndTime);
        long daysDifference = getDiffDays(hotelStartTime, hotelEndTime);
        expenseDetail.setTravelDays((double)daysDifference);
        expenseDetail.setEndCityCode(hotelCityCode);
        expenseDetail.setEmpName(travel.getEmpName());
        expenseDetail.setEmpCode(travel.getEmpCode());
        expenseDetail.setEmpType(travel.getEmpType());
        expenseDetail.setBudLevel(travel.getBudLevel());
        expenseDetail.setIsSubsidy(travel.getIsSubsidy());
        expenseDetail.setEndCity(hotelCityName);
        expenseDetail.setRemark("缺少住宿");
        expenseDetail.setFiscal(travel.getFiscal());
        expenseDetail.setMofDivCode(travel.getMofDivCode());
        expenseDetail.setAgyCode(travel.getAgyCode());
        expenseDetail.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        expenseDetail.setHotelGroup(UUID.randomUUID().toString());
        return expenseDetail;
    }

    private PcxBillExpDetailTravel replenishEndTraffic (PcxBillExpDetailTravel startTraffic,
                                                     PcxBillExpDetailTravel endTraffic, String remark){
        PcxBillExpDetailTravel expenseDetail = new PcxBillExpDetailTravel();
        expenseDetail.setId(IDGenerator.id());
        expenseDetail.setEcsAmt(BigDecimal.ZERO);
        expenseDetail.setInputAmt(BigDecimal.ZERO);
        expenseDetail.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021101);
        expenseDetail.setStartTime(getSubHour(startTraffic.getFinishTime(),1));
        expenseDetail.setFinishTime(getSubHour(startTraffic.getFinishTime(),1));
        expenseDetail.setStartCity(startTraffic.getEndCity());
        expenseDetail.setStartCityCode(startTraffic.getEndCityCode());
        expenseDetail.setStartPlace(startTraffic.getEndPlace());
        expenseDetail.setEndCity(endTraffic.getStartCity());
        expenseDetail.setEndCityCode(endTraffic.getStartCityCode());
        expenseDetail.setEndPlace(endTraffic.getStartPlace());
        expenseDetail.setEmpName(endTraffic.getEmpName());
        expenseDetail.setEmpCode(endTraffic.getEmpCode());
        expenseDetail.setEmpType(endTraffic.getEmpType());
        expenseDetail.setBudLevel(endTraffic.getBudLevel());
        expenseDetail.setIsSubsidy(endTraffic.getIsSubsidy());
        expenseDetail.setRemark(remark);
        expenseDetail.setTenantId(startTraffic.getTenantId());
        expenseDetail.setFiscal(startTraffic.getFiscal());
        expenseDetail.setMofDivCode(startTraffic.getMofDivCode());
        expenseDetail.setAgyCode(startTraffic.getAgyCode());
        expenseDetail.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        return expenseDetail;
    }

    private PcxBillExpDetailTravel replenishTraffic (PcxBillExpDetailTravel startTraffic,
                                                     PcxBillExpDetailTravel endTraffic,
                                                     String remark,
                                                     String workCityCode){
        PcxBillExpDetailTravel expenseDetail = new PcxBillExpDetailTravel();
        expenseDetail.setId(IDGenerator.id());
        expenseDetail.setEcsAmt(BigDecimal.ZERO);
        expenseDetail.setInputAmt(BigDecimal.ZERO);
        expenseDetail.setExpDetailCode(PcxConstant.TRAVEL_DETAIL_3021101);
        //如果衔接的下一个是从工作地出发，那么时间用当前行程的时间，下一个城市间交通费会单独开一段行程
        if (endTraffic.getStartCityCode().equals(workCityCode)){
            expenseDetail.setStartTime(getSubHour(startTraffic.getFinishTime(), 1));
            expenseDetail.setFinishTime(getSubHour(startTraffic.getFinishTime(), 1));
        }else{
            expenseDetail.setStartTime(getSubHour(endTraffic.getStartTime(), -1));
            expenseDetail.setFinishTime(getSubHour(endTraffic.getStartTime(), -1));
        }

        expenseDetail.setStartCity(startTraffic.getEndCity());
        expenseDetail.setStartCityCode(startTraffic.getEndCityCode());
        expenseDetail.setStartPlace(startTraffic.getEndPlace());
        expenseDetail.setEndCity(endTraffic.getStartCity());
        expenseDetail.setEndCityCode(endTraffic.getStartCityCode());
        expenseDetail.setEndPlace(endTraffic.getStartPlace());
        expenseDetail.setEmpName(endTraffic.getEmpName());
        expenseDetail.setEmpCode(endTraffic.getEmpCode());
        expenseDetail.setEmpType(endTraffic.getEmpType());
        expenseDetail.setBudLevel(endTraffic.getBudLevel());
        expenseDetail.setIsSubsidy(endTraffic.getIsSubsidy());
        expenseDetail.setRemark(remark);
        expenseDetail.setTenantId(startTraffic.getTenantId());
        expenseDetail.setFiscal(startTraffic.getFiscal());
        expenseDetail.setMofDivCode(startTraffic.getMofDivCode());
        expenseDetail.setAgyCode(startTraffic.getAgyCode());
        expenseDetail.setSource(BillExpDetailSourceEnum.REPLENISH.getCode());
        return expenseDetail;
    }

//    public static void main(String[] args) {
//        TravelTripProcessor travelTripProcessor = new TravelTripProcessor();
//        DateVO baseDate = new DateVO("2019-12-02", "2019-12-20", "");
//        DateVO one = new DateVO("2019-12-02", "2019-12-20", "");
//        DateVO two = new DateVO("2019-12-01", "2019-12-02", "");
//        DateVO three = new DateVO("2019-12-01", "2019-12-20", "");
//        DateVO four = new DateVO("2019-12-02", "2019-12-07", "");
//        DateVO five = new DateVO("2019-12-07", "2019-12-20", "");
//        DateVO six = new DateVO("2019-12-03", "2019-12-07", "");
//        DateVO seven = new DateVO("2019-12-10", "2019-12-20", "");
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(one)));
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(two)));
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(three)));
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(four)));
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(four,five)));
//        System.out.println(travelTripProcessor.getNoCoverDateList(baseDate, Arrays.asList(six, seven)));
//    }


    /**
     *  @param baseDate
     * @param dateList
     * @return
     */
    public List<DateVO> getNoCoverDateList(DateVO baseDate, List<DateVO> dateList){
        /**
         * * 以baseDate的开始日期和结束日期为两端
         * 通过把dateList中的时间区间进行匹配，找出没有覆盖baseDate时间区间的时间集合
         * 用例1: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-02","endDate":"2019-12-20"}],返回[]
         * 用例2: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-01","endDate":"2019-12-02"}],返回[{"startDate":"2019-12-02","endDate":"2019-12-20"}]
         * 用例3: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-01","endDate":"2019-12-20"}],返回[]
         * 用例4: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-02","endDate":"2019-12-07"}],返回[{"startDate":"2019-12-07","endDate":"2019-12-20"}]
         * 用例5: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-02","endDate":"2019-12-07"},{"startDate":"2019-12-07","endDate":"2019-12-20"}],返回[]
         * 用例6: baseDate = {"startDate":"2019-12-02","endDate":"2019-12-20"} dateList = [{"startDate":"2019-12-03","endDate":"2019-12-07"},{"startDate":"2019-12-10","endDate":"2019-12-20"}],返回[{"startDate":"2019-12-02","endDate":"2019-12-03"},{"startDate":"2019-12-07","endDate":"2019-12-10"}]
         */

        List<DateVO> noCoverDateList = new ArrayList<>();

        if (dateList == null || dateList.isEmpty()) {
            noCoverDateList.add(baseDate);
            return noCoverDateList;
        }

        // 对 dateList 按 startDate 进行排序
        dateList.sort(Comparator.comparing(DateVO::getStartDate));

        String currentStartTime = baseDate.getStartDate();
        String currentEndTime = baseDate.getEndDate();

        for (DateVO dateVO : dateList) {
            // 如果当前时间段已经完全被覆盖，跳出循环
            if (transDate(currentStartTime).compareTo(transDate(dateVO.getStartDate())) >= 0
                    && transDate(currentEndTime).compareTo(transDate(dateVO.getEndDate())) <= 0) {
                currentStartTime = "";
                currentEndTime = "";
                break;
            }

            // 如果当前时间段与 dateVO 没有交集，继续下一个 dateVO
            if (transDate(currentEndTime).compareTo(transDate(dateVO.getStartDate())) <= 0
                    || transDate(currentStartTime).compareTo(transDate(dateVO.getEndDate())) >= 0) {
                continue;
            }

            // 更新未覆盖的时间段
            if (beforeOrEq(currentStartTime, dateVO.getStartDate())) {
                if (beforeOrEq(dateVO.getEndDate(), currentEndTime)) {
                    // dateVO 完全在 currentStartTime 和 currentEndTime 之间
                    if (!currentStartTime.equals(dateVO.getStartDate())) {
                        noCoverDateList.add(new DateVO(currentStartTime, dateVO.getStartDate()));
                    }
                    currentStartTime = dateVO.getEndDate();
                } else {
                    // dateVO 结束时间在 currentEndTime 之后
                    if (!currentStartTime.equals(dateVO.getStartDate())) {
                        noCoverDateList.add(new DateVO(currentStartTime, dateVO.getStartDate()));
                    }
                    currentStartTime = "";
                    currentEndTime = "";
                    break;
                }
            } else {
                if (beforeOrEq(dateVO.getEndDate(), currentEndTime)) {
                    // dateVO 结束时间在 currentEndTime 之前
                    currentStartTime = dateVO.getEndDate();
                } else {
                    // dateVO 结束时间在 currentEndTime 之后
                    noCoverDateList.add(new DateVO(dateVO.getEndDate(), currentEndTime));
                    currentStartTime = "";
                    currentEndTime = "";
                    break;
                }
            }
        }

        // 如果还有未覆盖的时间段，添加到结果列表
        if (StringUtils.isNotEmpty(currentStartTime) && StringUtils.isNotEmpty(currentEndTime)
                && transDate(currentStartTime).compareTo(transDate(currentEndTime)) < 0) {
            noCoverDateList.add(new DateVO(currentStartTime, currentEndTime));
        }

        return noCoverDateList;
    }


    public static List<DateVO> getCoverDateList(DateVO baseDate, List<DateVO> dateList){
        /**
         * * 以baseDate的开始日期和结束日期为两端
         * 通过把dateList中的时间区间进行匹配，能够覆盖baseDate时间区间的时间集合
         *  用例1: baseDate = {"startDate":"2019-01-01","endDate":"2019-01-20"} dateList = [{"startDate":"2019-01-01","endDate":"2019-01-19"}],返回[{"startDate":"2019-01-01","endDate":"2019-01-19"}]
         *  用例2: baseDate = {"startDate":"2019-01-01","endDate":"2019-01-20"} dateList = [{"startDate":"2019-01-01","endDate":"2019-01-10"},{"startDate":"2019-01-12","endDate":"2019-01-13"}],返回[{"startDate":"2019-01-01","endDate":"2019-01-10"},{"startDate":"2019-01-12","endDate":"2019-01-13"}]
         *  用例3: baseDate = {"startDate":"2019-01-01","endDate":"2019-01-20"} dateList = [{"startDate":"2019-01-02","endDate":"2019-01-10"},{"startDate":"2019-01-01","endDate":"2019-01-05"}],返回[{"startDate":"2019-01-02","endDate":"2019-01-05"}]
         *
         */
        List<DateVO> coverDateList = new ArrayList<>();
        for (DateVO dateVO : dateList) {
            //baseDate的日期范围包含dateVo的日期范围
            if (beforeOrEq(baseDate.getStartDate(), dateVO.getStartDate())&&
                    beforeOrEq(dateVO.getEndDate(), baseDate.getEndDate())) {
                coverDateList.add(new DateVO(dateVO.getStartDate(), dateVO.getEndDate()));
                //dateVO的日期范围包含baseDate的日期范围
            } else if (beforeOrEq(dateVO.getStartDate(), baseDate.getStartDate())&&
                    beforeOrEq(baseDate.getEndDate(), dateVO.getEndDate())) {
                coverDateList.add(new DateVO(baseDate.getStartDate(), baseDate.getEndDate()));
                //如果baseDate的开始日期在dateVo中间
            } else if (beforeOrEq(dateVO.getStartDate(), baseDate.getStartDate())&&
                    beforeOrEq(dateVO.getEndDate(), baseDate.getEndDate())&&
                    beforeOrEq(baseDate.getStartDate(), dateVO.getEndDate()))  {
                coverDateList.add(new DateVO(baseDate.getStartDate(), dateVO.getEndDate()));
                //如果baseDate的结束日期在dateVo中间
            }else if (beforeOrEq(baseDate.getStartDate(), dateVO.getStartDate()) &&
                    beforeOrEq(baseDate.getEndDate(), dateVO.getEndDate()) &&
                    beforeOrEq(dateVO.getStartDate(), baseDate.getStartDate()) ) {
                coverDateList.add(new DateVO(dateVO.getStartDate(), baseDate.getEndDate()));
            }
        }
        return mergeDateRanges(coverDateList);
    }

    private static List<DateVO> mergeDateRanges(List<DateVO> dateList) {
        if (dateList == null || dateList.isEmpty()) {
            return new ArrayList<>();
        }

        // 按 startDate 排序
        List<DateVO> sortedList = new ArrayList<>(dateList);
        sortedList.sort(Comparator.comparing(DateVO::getStartDate));

        List<DateVO> mergedList = new ArrayList<>();
        DateVO current = sortedList.get(0);

        for (int i = 1; i < sortedList.size(); i++) {
            DateVO next = sortedList.get(i);
            if (Objects.equals(current.getStartDate(), next.getStartDate())
                    && Objects.equals(current.getEndDate(), next.getEndDate())){
                continue;
            }
            // 判断是否重叠或连续
            if (beforeOrEq(current.getStartDate(), next.getEndDate()) &&
                    beforeOrEq(next.getStartDate(), current.getEndDate())) {
                // 合并两个时间段
                String newStartDate = current.getStartDate();
                String newEndDate = !beforeOrEq(current.getEndDate(), next.getEndDate()) ? current.getEndDate() : next.getEndDate();
                current = new DateVO(newStartDate, newEndDate);
            } else {
                mergedList.add(current);
                current = next;
            }
        }
        mergedList.add(current); // 添加最后一个时间段

        return mergedList;
    }


    private static boolean beforeOrEq(String startTime, String endTime){
        return transDate(startTime).compareTo(transDate(endTime)) <=0;
    }

}
