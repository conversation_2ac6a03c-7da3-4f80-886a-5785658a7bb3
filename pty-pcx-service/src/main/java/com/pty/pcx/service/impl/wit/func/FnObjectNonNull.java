package com.pty.pcx.service.impl.wit.func;

import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class FnObjectNonNull extends BFunction {

    public static final String FUNC_NAME = "nonNull";

    @Override
    public String getName() {
        return FUNC_NAME;
    }

    public String method() {
        return "nonNull(obj)";
    }

    @Override
    public String methodName() {
        return "判断对象是否不为null或者字符串是否不为空";
    }

    @Override
    public String desc() {
        return "判断对象是否不为null或者字符串是否不为空";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Object value = arg1.getValue(env);
        return AviatorBoolean.valueOf(Objects.nonNull(value) && StringUtil.isNotEmpty(value.toString()));
    }
}
