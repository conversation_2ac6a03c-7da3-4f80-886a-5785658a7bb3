package com.pty.pcx.service.impl.bas;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.pty.mad.vo.BaseDataVo;
import com.pty.pa.license.common.util.DateUtil;
import com.pty.pcx.api.bas.PcxBasCityClassifyService;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasCityClassifyDao;
import com.pty.pcx.dao.bas.PcxBasCityClassifyMainDao;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bas.PcxBasCityClassifyMain;
import com.pty.pcx.qo.bas.*;
import com.pty.pcx.vo.bas.CityPeakVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市分类表(PcxBasCityClassify)表服务实现类
 * <AUTHOR>
 * @since 2024-11-08 10:06:12
 */
@Slf4j
@Indexed
@Service
public class PcxBasCityClassifyServiceImpl implements PcxBasCityClassifyService {

	@Autowired
	private PcxBasCityClassifyDao pcxBasCityClassifyDao;
	@Autowired
	private PcxBasCityClassifyMainDao pcxBasCityClassifyMainDao;
	@Autowired
	private TransOptService transOptService;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBasCityClassify selectById(String id) {
		return pcxBasCityClassifyDao.selectById(id);
	}

	/**
	 * 查询多条数据
	 * @param pcxBasCityClassify 实例对象
	 * @return 对象列表
	 */
	@Override
	public List<PcxBasCityClassify> selectList(PcxBasCityClassify pcxBasCityClassify) {
		return pcxBasCityClassifyDao.selectList(pcxBasCityClassify);
	}

	/**
	 * 新增数据
	 * @param pcxBasCityClassify 实例对象
	 * @return 实例对象
	 */
	@Override
	public int insertSelective(PcxBasCityClassify pcxBasCityClassify) {
		return pcxBasCityClassifyDao.insertSelective(pcxBasCityClassify);
	}

	/**
	 * 修改数据
	 * @param pcxBasCityClassify 实例对象
	 * @return 实例对象
	 */
	@Override
	public int update(PcxBasCityClassify pcxBasCityClassify) {
		return pcxBasCityClassifyDao.update(pcxBasCityClassify);
	}

	/**
	 * 通过主键id删除数据
	 * @param id 主键
	 */
	@Override
	public int deleteById(String id) {
		return pcxBasCityClassifyDao.deleteById(id);
	}

	@Override
	public Response updateCityClassify(BatchUpdateCityClassifyQO qo) {
		CheckMsg checkMsg = validInsertOrUpdateCityClassify(qo);
		if (!checkMsg.isSuccess()){
			return Response.fail().setMsg(checkMsg.getMsgInfo());
		}
		String updateTime = DateUtil.nowTime();

		List<PcxBasCityClassifyMain> mainList = new ArrayList<>();
		List<PcxBasCityClassify> list = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(qo.getClassifyList())){
			for (PcxBasCityClassifyQO classifyQO : qo.getClassifyList()) {
				if (CollectionUtils.isNotEmpty(classifyQO.getCityList())){
					for (DataQO cityQO : classifyQO.getCityList()) {
						PcxBasCityClassify pcxBasCityClassify = new PcxBasCityClassify();
						BeanUtils.copyProperties(qo, pcxBasCityClassify);
//						pcxBasCityClassify.setIsSys(qo.getIsSys());
						pcxBasCityClassify.setId(IDGenerator.id());
						pcxBasCityClassify.setClassifyName(classifyQO.getClassifyName());
						pcxBasCityClassify.setClassifyCode(classifyQO.getClassifyCode());
						pcxBasCityClassify.setDataName(cityQO.getDataName());
						pcxBasCityClassify.setDataCode(cityQO.getDataCode());
						pcxBasCityClassify.setCreater(qo.getUserCode());
						pcxBasCityClassify.setCreateTime(updateTime);
						pcxBasCityClassify.setTenantId(PtyContext.getTenantId());
						if(CollectionUtils.isNotEmpty(classifyQO.getPeakDateJson())){
							pcxBasCityClassify.setPeakDateJson(JSON.toJSONString(classifyQO.getPeakDateJson()));
						}
						list.add(pcxBasCityClassify);
					}
				}
				PcxBasCityClassifyMain pcxBasCityClassifyMain = new PcxBasCityClassifyMain();
				BeanUtils.copyProperties(qo, pcxBasCityClassifyMain);
//				pcxBasCityClassifyMain.setIsSys(qo.getIsSys());
				pcxBasCityClassifyMain.setId(IDGenerator.id());
				pcxBasCityClassifyMain.setClassifyName(classifyQO.getClassifyName());
				pcxBasCityClassifyMain.setClassifyCode(classifyQO.getClassifyCode());
				pcxBasCityClassifyMain.setCreater(qo.getUserCode());
				pcxBasCityClassifyMain.setCreateTime(updateTime);
				pcxBasCityClassifyMain.setTenantId(PtyContext.getTenantId());
				if(CollectionUtils.isNotEmpty(classifyQO.getPeakDateJson())){
					pcxBasCityClassifyMain.setPeakDateJson(JSON.toJSONString(classifyQO.getPeakDateJson()));
				}
				mainList.add(pcxBasCityClassifyMain);
			}
		}
		transOptService.delAndInsertCityClassifyMain(qo.getClassifyType(),qo.getExpenseTypeCode(), 0, qo.getAgyCode(), qo.getFiscal(), qo.getMofDivCode(), list, mainList);
		return Response.success();
	}

	@Override
	public Response bizSelectAllClassify(BizQueryAllCityClassifyQO queryAllCityClassifyQO) {
		//校验参数
		CheckMsg checkMsg = validBizSelectAllClassify(queryAllCityClassifyQO);
		if (!checkMsg.isSuccess()){
			return Response.fail().setMsg(checkMsg.getMsgInfo());
		}
		//查询所有城市信息，只查询城市
		List<DataQO> list = pcxBasCityClassifyDao.selectAllCity();
		//查询城市分类表数据
		PcxBasCityClassify query = new PcxBasCityClassify();
		BeanUtils.copyProperties(queryAllCityClassifyQO, query);
		List<PcxBasCityClassify> classifyList = selectList(query);

		PcxBasCityClassifyMain mainQuery = new PcxBasCityClassifyMain();
		BeanUtils.copyProperties(queryAllCityClassifyQO, mainQuery);
		List<PcxBasCityClassifyMain> classifyMainList = pcxBasCityClassifyMainDao.selectList(mainQuery);
		//组装已有城市分类数据，其他城市全部放入其他分类中
		return Response.success().setData(assemblyClassify(list, classifyList, classifyMainList));
	}

	@Override
	public List<BaseDataVo> selectCityClassify(BizQueryAllCityClassifyQO qo) {
		List<PcxBasCityClassify> pcxBasCityClassifies = pcxBasCityClassifyMainDao.selectCityClassify(qo);
		if (CollectionUtils.isEmpty(pcxBasCityClassifies)){
			return new ArrayList<>();
		}
		List<BaseDataVo> list = pcxBasCityClassifies
				.stream()
				.map(pcxCostControlLevelVO -> {
					BaseDataVo baseDataVo = new BaseDataVo();
					baseDataVo.setId(pcxCostControlLevelVO.getId());
					baseDataVo.setCode(pcxCostControlLevelVO.getClassifyCode());
					baseDataVo.setName(pcxCostControlLevelVO.getClassifyName());
					return baseDataVo;
				})
				.collect(Collectors.toList());
		if (list.stream().anyMatch(f->!f.getCode().equals("other"))){
			BaseDataVo baseDataVo = new BaseDataVo();
			baseDataVo.setCode("other");
			baseDataVo.setName("其他地区");
			list.add(baseDataVo);
		}
		return list;
	}

	@Override
	public List<CityPeakVO> isCityPeak(QueryCityPeakClassifyQO queryCityPeakClassifyQO) {

		List<PcxBasCityClassify> cityPeakList = pcxBasCityClassifyDao.selectCityPeakList(queryCityPeakClassifyQO);
		List<CityPeakVO> cityPeakVOList = new ArrayList<>();

		for (String dataCode : queryCityPeakClassifyQO.getDataCodes()) {
			CityPeakVO cityPeakVO = new CityPeakVO();
			cityPeakVO.setDataCode(dataCode);
			if(CollectionUtils.isNotEmpty(cityPeakList)) {
				PcxBasCityClassify pcxBasCityClassify = cityPeakList.stream().filter(p -> p.getDataCode().equals(dataCode)).findFirst().orElse(null);
				if (null == pcxBasCityClassify) {
					cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NOT_CONFIG);
				} else {
					List<PeakDateQO> peakDateQOList = JSON.parseArray(pcxBasCityClassify.getPeakDateJson(), PeakDateQO.class);
					Set<Integer> peakFlagSet = new HashSet<>();
					Set<LocalDate> inRanges = new HashSet<>();

					for (PeakDateQO peakDateQO : peakDateQOList) {
						DateRangeResult dateRangeResult = validDateBetweenPeak(peakDateQO, queryCityPeakClassifyQO.getPeakDateQO());
						peakFlagSet.add(dateRangeResult.getPeakFlag());
						inRanges.addAll(dateRangeResult.getInRange());
					}
					if (peakFlagSet.contains(PcxCityClassifyConstant.PEAK_FLAG_YES)) {
						cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_YES);
					} else if (peakFlagSet.contains(PcxCityClassifyConstant.PEAK_FLAG_PART)) {
						cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_PART);
					} else {
						cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NO);
					}
					cityPeakVO.setPeakDateQOList(peakDateQOList);
					cityPeakVO.setInRange(inRanges);

				}
			}else{
				cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NOT_CONFIG);
			}

			cityPeakVOList.add(cityPeakVO);
		}
		return cityPeakVOList;

	}

	@Override
	public List<CityPeakVO> getCityPeakDate(QueryCityPeakClassifyQO queryCityPeakClassifyQO) {

		List<PcxBasCityClassify> cityPeakList = pcxBasCityClassifyDao.selectCityPeakList(queryCityPeakClassifyQO);
		List<CityPeakVO> cityPeakVOList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(cityPeakList)){
			cityPeakList.forEach(item -> {
				CityPeakVO cityPeakVO = new CityPeakVO();
				cityPeakVO.setDataCode(item.getDataCode());
				cityPeakVO.setClassifyCode(item.getClassifyCode());
				if(CollectionUtils.isNotEmpty(cityPeakList)) {
					PcxBasCityClassify pcxBasCityClassify = cityPeakList.stream().filter(p -> p.getDataCode().equals(item.getDataCode())).findFirst().orElse(null);
					if (null == pcxBasCityClassify) {
						cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NOT_CONFIG);
					} else {
						List<PeakDateQO> peakDateQOList = JSON.parseArray(pcxBasCityClassify.getPeakDateJson(), PeakDateQO.class);
						Set<Integer> peakFlagSet = new HashSet<>();
						Set<LocalDate> inRanges = Sets.newHashSet();

						for (PeakDateQO peakDateQO : peakDateQOList) {
							DateRangeResult dateRangeResult = validDateBetweenPeak(peakDateQO, queryCityPeakClassifyQO.getPeakDateQO());
							peakFlagSet.add(dateRangeResult.getPeakFlag());
							inRanges.addAll(dateRangeResult.getInRange());

						}
						if (peakFlagSet.contains(PcxCityClassifyConstant.PEAK_FLAG_YES)) {
							cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_YES);
						} else if (peakFlagSet.contains(PcxCityClassifyConstant.PEAK_FLAG_PART)) {
							cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_PART);
						} else {
							cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NO);
						}
						cityPeakVO.setPeakDateQOList(peakDateQOList);
						cityPeakVO.setInRange(inRanges);


					}
				}else{
					cityPeakVO.setPeakFlag(PcxCityClassifyConstant.PEAK_FLAG_NOT_CONFIG);
				}
				cityPeakVO.setPeakDateQOList(JSON.parseArray(item.getPeakDateJson(), PeakDateQO.class));
				cityPeakVOList.add(cityPeakVO);
			});
			return cityPeakVOList;
		}
		return Collections.emptyList();
	}

	@Override
	public CityPeakVO getCityPeakDateByClassifyCode(String classifyCode) {
		PcxBasCityClassify pcxBasCityClassify = pcxBasCityClassifyDao.selectCityPeak(classifyCode);
		if(null != pcxBasCityClassify) {
			CityPeakVO cityPeakVO = new CityPeakVO();
			cityPeakVO.setDataCode(pcxBasCityClassify.getDataCode());
			cityPeakVO.setDataName(pcxBasCityClassify.getDataName());
			cityPeakVO.setPeakDateQOList(JSON.parseArray(pcxBasCityClassify.getPeakDateJson(), PeakDateQO.class));
			return cityPeakVO;
		}
		return null;
	}

	private DateRangeResult validDateBetweenPeak(PeakDateQO peakDateQO,PeakDateQO validPeakDateQO){
		int year = DateUtil.getCurYear();
		String[] startArr = peakDateQO.getStartDay().split("-");
		String[] endArr = peakDateQO.getEndDay().split("-");

		String[] validStartArr = validPeakDateQO.getStartDay().split("-");
		String[] validEndArr = validPeakDateQO.getEndDay().split("-");

		LocalDate start1 = LocalDate.of(year,Integer.valueOf(validStartArr[1]), Integer.valueOf(validStartArr[2]));
		LocalDate end1 = LocalDate.of(year, Integer.valueOf(validEndArr[1]), Integer.valueOf(validEndArr[2]));

		LocalDate start2 = LocalDate.of(year, Integer.valueOf(startArr[0]), Integer.valueOf(startArr[1]));
		LocalDate end2 = LocalDate.of(year, Integer.valueOf(endArr[0]), Integer.valueOf(endArr[1]));
		return calculateDateRanges(start1, end1, start2, end2);
	}

	private BizAllClassifyVO assemblyClassify(List<DataQO> allCityList, List<PcxBasCityClassify> classifyList,
											  List<PcxBasCityClassifyMain> classifyMainList) {
		BizAllClassifyVO vo = new BizAllClassifyVO();
		//组装已有分类，按照分类编码分组，一个分类下挂着该分类下的全部城市
		Map<String, List<PcxBasCityClassify>> classifyMap = classifyList.stream()
				.collect(Collectors.groupingBy(PcxBasCityClassify::getClassifyCode));
		List<CityClassifyVO> classifyVOList = new ArrayList<>();
		vo.setClassifyList(classifyVOList);
		List<String> existsCityCodes = new ArrayList<>();
		for (PcxBasCityClassifyMain classifyMain : classifyMainList) {
			CityClassifyVO classifyVO = new CityClassifyVO();
			List<PcxBasCityClassify> classifies = classifyMap.get(classifyMain.getClassifyCode());
			classifyVO.setClassifyCode(classifyMain.getClassifyCode());
			classifyVO.setClassifyName(classifyMain.getClassifyName());
			classifyVO.setIsSys(classifyMain.getIsSys());
			classifies.forEach(item->{
				existsCityCodes.add(item.getDataCode());
				classifyVO.getCityList().add(new DataQO(item.getDataCode(), item.getDataName(),item.getDataName(),null,"0"));
			});
			vo.getClassifyList().add(classifyVO);
		}
		//全部城市不在分类中的，归入其他分类中
		allCityList = allCityList.stream()
				.filter(item->!existsCityCodes.contains(item.getDataCode()))
				.collect(Collectors.toList());
		CityClassifyVO other = new CityClassifyVO();
		other.setClassifyName("其他");
		other.setClassifyCode("other");
		other.setIsSys(1);
		if (CollectionUtils.isNotEmpty(allCityList)){
			for (DataQO qo : allCityList) {
				other.getCityList().add(new DataQO(qo.getDataCode(), qo.getFullName(),qo.getFullName(),null,qo.getGrandParentCode()));
			}
		}
		vo.setOtherClassify(other);
		return vo;
	}

	private CheckMsg validInsertOrUpdateCityClassify(BatchUpdateCityClassifyQO qo) {
		if (Objects.isNull(qo)){
			return CheckMsg.fail("参数为空");
		}
		if (StringUtil.isEmpty(qo.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(qo.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(qo.getMofDivCode())){
			return CheckMsg.fail("区划不能为空");
		}
		return CheckMsg.success();
	}

	private CheckMsg validBizSelectAllClassify(BizQueryAllCityClassifyQO queryAllCityClassifyQO){
		if (Objects.isNull(queryAllCityClassifyQO)){
			return CheckMsg.fail("参数为空");
		}
		if (Objects.isNull(queryAllCityClassifyQO.getClassifyType())){
			return CheckMsg.fail("分类类型不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getExpenseTypeCode())){
			return CheckMsg.fail("费用明细编码不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getMofDivCode())){
			return CheckMsg.fail("区划不能为空");
		}
		return CheckMsg.success();
	}


	/**
	 * 获取淡旺季的时间范围是否在区间内
	 * @param start1
	 * @param end1
	 * @param start2
	 * @param end2
	 * @return
	 */
	private  DateRangeResult calculateDateRanges(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
		Set<LocalDate> inRange = new HashSet<>();
		Set<LocalDate> outOfRange = new HashSet<>();

		long daysBetween = ChronoUnit.DAYS.between(start1, end1) + 1;
		for (int i = 0; i < daysBetween; i++) {
			LocalDate date = start1.plusDays(i);
			if (!date.isBefore(start2) && !date.isAfter(end2)) {
				inRange.add(date);
			} else {
				outOfRange.add(date);
			}
		}

		return new DateRangeResult(inRange, outOfRange);
	}

	@Data
	@AllArgsConstructor
	public static class DateRangeResult {
		private Set<LocalDate> inRange;
		private Set<LocalDate> outOfRange;

		public int getPeakFlag() {
			if (inRange.isEmpty()) {
				return PcxCityClassifyConstant.PEAK_FLAG_NO;
			} else if (outOfRange.isEmpty()) {
				return PcxCityClassifyConstant.PEAK_FLAG_YES;
			} else {
				return PcxCityClassifyConstant.PEAK_FLAG_PART;
			}
		}
	}
}


