package com.pty.pcx.service.impl.ecs.trip;

import com.pty.ecs.common.enu.EcsEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillExpDetailSourceEnum;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pub.common.util.StringUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class EcsItemNameHelper {
    public interface DisposeItemName{
        String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap);
        default String getBizType(String bizTypeName){
            if (StringUtil.isEmpty(bizTypeName)){
                return "";
            }
            if (bizTypeName.contains("改签")){
                return "改签";
            }else if (bizTypeName.contains("退票")){
                return "退票";
            } else if (bizTypeName.contains("飞机")) {
                return "飞机";
            } else if (bizTypeName.contains("火车") ||
                    bizTypeName.contains("动车")||
                    bizTypeName.contains("高铁")) {
                return "火车";
            } else if (bizTypeName.contains("住宿费")) {
                return "住宿";
            } else if (bizTypeName.contains("网约车")) {
                return "网约车";
            } else if (bizTypeName.contains("出租车")) {
                return "出租车";
            } else if (bizTypeName.contains("公交地铁渡轮")) {
                return "公交地铁渡轮";
            } else if (bizTypeName.contains("机场大巴")) {
                return "机场大巴";
            }else {
                return "";
            }
        }
    }
    static Map<String, DisposeItemName> map = new HashMap<>();
    static DisposeItemName defaultDispose = new DisposeItemNameDefault();
    static {
        map.put(PcxConstant.TRAVEL_DETAIL_3021101, new DisposeItemName3021101());
        map.put(PcxConstant.TRAVEL_DETAIL_3021110, new DisposeItemName3021101());
        map.put(PcxConstant.TRAVEL_DETAIL_3021102, new DisposeItemName3021102());
        map.put(PcxConstant.TRAVEL_DETAIL_3021106, new DisposeItemName3021106());
        map.put(PcxConstant.TRAVEL_DETAIL_3021112, new DisposeItemName3021112());
        map.put("default", defaultDispose);
    }
    public static class DisposeItemName3021101 implements DisposeItemName{
        @Override
        public String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType,Map<String, String> expenseTypeMap) {
            if (Objects.equals(BillExpDetailSourceEnum.ECS.getCode(), detail.getSource())
                    && StringUtil.isNotEmpty(detail.getStartPlace(), detail.getEndPlace())){
                return String.format("%s→%s", detail.getStartPlace(), detail.getEndPlace());
            }else{
                return String.format("%s→%s", detail.getStartCity(), detail.getEndCity());
            }
        }
    }
    public static class DisposeItemName3021102 implements DisposeItemName{
        @Override
        public String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap) {
            return String.format("住宿费·%s", detail.getEndCity());
        }
    }
    public static class DisposeItemName3021106 implements DisposeItemName{
        @Override
        public String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap) {
            if (StringUtil.isNotEmpty(detail.getStartPlace(), detail.getEndPlace())){
                return String.format("%s→%s", detail.getStartPlace(), detail.getEndPlace());
            }
            return String.format("%s→%s", detail.getStartCity(), detail.getEndCity());
        }
    }
    public static class DisposeItemName3021112 implements DisposeItemName{
        @Override
        public String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap) {
            String bizType = getBizType(bizTypeName);
            if (StringUtil.isEmpty(bizType)){
                bizType = expenseTypeMap.get(detail.getExpDetailCode());
            }

            if (StringUtil.isNotEmpty(detail.getEndCity())){
                return String.format("%s·%s", bizType, detail.getEndCity());
            }else{
                return bizType;
            }
        }
    }

    public static class DisposeItemNameDefault implements DisposeItemName{
        @Override
        public String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap) {
            if (StringUtil.isNotEmpty(ecsBillType)){
                EcsEnum.BillType billType = EcsEnum.BillType.getByCode(ecsBillType);
                if (StringUtil.isNotEmpty(detail.getEndCity())){
                    return String.format("%s·%s", billType.getName(), detail.getEndCity());
                }else{
                    return billType.getName();
                }
            }
            return expenseTypeMap.getOrDefault(detail.getExpDetailCode(),"");
        }
    }

    public static String disposeItemName(String bizTypeName, PcxBillExpDetailTravel detail, String ecsBillType, Map<String, String> expenseTypeMap){
        DisposeItemName disposeItemName = map.getOrDefault(detail.getExpDetailCode(), map.get("default"));
        return disposeItemName.disposeItemName(bizTypeName, detail, ecsBillType, expenseTypeMap);
    }

    public static String getDisplayBizTypeName(String bizTypeName){
        return defaultDispose.getBizType(bizTypeName);
    }
}
