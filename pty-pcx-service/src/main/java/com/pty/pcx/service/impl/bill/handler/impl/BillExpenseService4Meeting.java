package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillExpDetailMeetingService;
import com.pty.pcx.api.bill.PcxBillExpMeetingService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.dao.bill.PcxBillExpMeetingDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.meeting.PcxBillExpMeeting;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.vo.bill.PcxBillListVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.UNIVERSAL_ITEM_CODE;


/**
 * 会议费用处理接口接口
 * <AUTHOR>
 * @since 2025/05/15
 */
@Service("billExpenseService4Meeting")
@Slf4j
@Indexed
public class BillExpenseService4Meeting extends BillExpenseCommonService implements BillExpenseService<PcxBillExpMeeting>{

    @Autowired
    private PcxBillExpMeetingDao pcxBillExpMeetingDao;

    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Autowired
    private PcxBillExpDetailMeetingService pcxBillExpDetailMeetingService;

    @Autowired
    private PcxBillExpMeetingService pcxBillExpMeetingService;

    /**
     * 校验逻辑（校验专属属性和明细项）
     * @param expBase 校验的块信息
     * @return
     */
    @Override
    public CheckMsg<Void> validate(PcxBillExpMeeting expBase, String billFuncCode) {
        if (Objects.isNull(expBase)) {
            return CheckMsg.fail("会议费用信息为空");
        }
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)) {
            return CheckMsg.fail("暂不支持单据类型为：[" + billFuncCode + "]的业务操作");
        }
        //查询会议费启用的必填的专属字段
        PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO(
                FormSettingEnums.FormClassifyEnum.EXPENSE.getCode(),
                billFuncCodeEnum.getBit(),
                expBase.getExpenseCode(),
                FormSettingEnums.FormTypeEnum.EXPENSE_EXCLUSIVE.getCode(),
                expBase.getAgyCode(),
                expBase.getFiscal(),
                expBase.getMofDivCode());
        Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //启用且必填
            List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == PubConstant.LOGIC_TRUE
                    & item.getIsNull() == PubConstant.LOGIC_TRUE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(formSettingQOS)) {
                Map<String, Object> stringObjectMap = BeanUtil.beanToMap(expBase);
                List<String> missingFields = new ArrayList<>();

                for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                    //todo 费用承担部门不做校验
                    if (formSettingQO.getFieldValue().equals("departmentCode")){
                        continue;
                    }
                    Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                    if (Objects.isNull(o) || StringUtil.isEmpty(o.toString())) {
                        String fieldName = formSettingQO.getFieldName();
                        missingFields.add(fieldName);
                        log.warn("专属属性:[{}]不能为空", fieldName);
                    }
                }

                if (!missingFields.isEmpty()) {
                    String errorMessage = "专属属性:" + String.join("、", missingFields) + "不能为空";
                    return CheckMsg.fail(errorMessage);
                }
            }
        }
        return CheckMsg.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcxBillExpMeeting saveOrUpdate(PcxBillExpMeeting pcxBillExpMeeting, PcxBill pcxBill) {
        PcxBillExpMeeting existingRecord = pcxBillExpMeetingDao.selectByUnionKey(pcxBill.getId(), pcxBillExpMeeting.getExpenseCode(), pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        if (Objects.isNull(existingRecord)) {
            pcxBillExpMeeting.setId(Objects.isNull(pcxBillExpMeeting.getId()) ? IDGenerator.id() : pcxBillExpMeeting.getId());
            pcxBillExpMeeting.setBillId(pcxBill.getId());
            pcxBillExpMeeting.setAgyCode(pcxBill.getAgyCode());
            pcxBillExpMeeting.setFiscal(pcxBill.getFiscal());
            pcxBillExpMeeting.setMofDivCode(pcxBill.getMofDivCode());
            pcxBillExpMeeting.setTenantId(StringUtil.isNotBlank(pcxBill.getTenantId()) ? pcxBill.getTenantId() : PtyContext.getTenantId());
            pcxBillExpMeeting.setDuration(PcxDateUtil.calculateDays(pcxBillExpMeeting.getStartTime(),  pcxBillExpMeeting.getFinishTime()));
            //TODO 背景：之前存在一个问题，不传ID但是有具体费用，并且前端更新部分不传你ID条件，
            PcxBillExpMeeting billExpMeeting = pcxBillExpMeetingDao.selectById(pcxBillExpMeeting.getId());
            if (Objects.isNull(billExpMeeting)){
                setCreatorInfo(pcxBillExpMeeting);
                pcxBillExpMeetingDao.insert(pcxBillExpMeeting);
            }else {
                setModifierInfo(pcxBillExpMeeting);
                pcxBillExpMeetingDao.updateById(pcxBillExpMeeting);
            }
        } else {
            setModifierInfo(pcxBillExpMeeting);
            pcxBillExpMeeting.setId(existingRecord.getId());
            pcxBillExpMeeting.setDuration(PcxDateUtil.calculateDays(pcxBillExpMeeting.getStartTime(),  pcxBillExpMeeting.getFinishTime()));
            pcxBillExpMeetingDao.updateById(pcxBillExpMeeting);
        }
        // 补充公共信息
        saveCommonData(pcxBillExpMeeting, pcxBill);
        return pcxBillExpMeeting;
    }

    @Override
    public PcxBillExpMeeting view(String expenseCode, PcxBill pcxBill) {
        PcxBillExpMeeting billExpMeeting = pcxBillExpMeetingDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        //特殊业务字段补充实现
        return billExpMeeting;
    }

    @Override
    public void delete(String expenseCode, PcxBill pcxBill) {
        PcxBillExpMeeting billExpMeeting = pcxBillExpMeetingDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        if (Objects.nonNull(billExpMeeting)){
            //删除费用信息
            pcxBillExpMeetingDao.deleteById(billExpMeeting.getId());
        }
    }

    @Override
    public void dealContent(List<PcxBillListVO> bills) {
        if(CollectionUtil.isEmpty(bills)) return;

        List<String> billIdList = bills.stream().map(PcxBillListVO::getBillId).collect(Collectors.toList());
        Map<String[], List<PcxBillListVO>> afm$bills = bills.stream().collect(Collectors.groupingBy(bill -> new String[]{bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode()}));
        afm$bills.forEach((afm, afmBills) -> {
            String _agyCode = afm[0];
            String _fiscal = afm[1];
            String _mofDivCode = afm[2];
            // 查询差旅
            List<PcxBillExpMeeting> meetings = pcxBillExpMeetingService.selectList(billIdList, _agyCode, _fiscal, _mofDivCode);

            // 将差旅信息及其详情按单据ID分组，便于后续处理
            Map<String, List<PcxBillExpMeeting>> meetingMap = meetings.stream().collect(Collectors.groupingBy(PcxBillExpMeeting::getBillId));

            // 遍历每个单据，准备其对应的差旅信息及其详情
            bills.forEach(bill -> {
                List<PcxBillExpMeeting> meetingList = meetingMap.get(bill.getBillId());

                // 通用报销不返回content
                if (!bill.getItemCode().equals(UNIVERSAL_ITEM_CODE))
                    // 准备数据
                    prepareContent(bill,meetingList);
            });
        });
    }

    /**
     * 准备会议费首页回显数据
     * @param bill
     * @param meetingList
     */
    private void prepareContent(PcxBillListVO bill, List<PcxBillExpMeeting> meetingList) {
        if (CollectionUtil.isEmpty(meetingList)) return ;

        Optional<PcxBillExpMeeting> first = meetingList.stream().filter(item -> item.getExpenseCode().equals(PcxBillProcessConstant.ExpenseProcessBeanEnum.MEETING.getCode())).findFirst();
        if (first.isPresent()) {
            PcxBillExpMeeting meeting = first.get();
            PcxBillListVO.BillContentVO content = new PcxBillListVO.BillContentVO();
            content.setStartDate(meeting.getStartTime());
            content.setEndDate(meeting.getFinishTime());
            content.setDays(PcxDateUtil.calculateDays(meeting.getStartTime(),  meeting.getFinishTime()));

            List<Map<String, Object>> eventInfos = new ArrayList<>();

            Map<String, Object> styleMap01 = new HashMap<>();
            styleMap01.put("fontWeight",  "bold");

            Map<String, Object> map01 = new HashMap<>();
            map01.put("style",  null);
            map01.put("label",  "在");
            eventInfos.add(map01);

            if (StringUtil.isNotEmpty(meeting.getRegionName())){
                Map<String, Object> map02 = new HashMap<>();
                map02.put("style",  styleMap01);
                map02.put("label",  meeting.getRegionName());
                eventInfos.add(map02);
            }

            if (StringUtil.isNotEmpty(meeting.getMeetingFormatName())){
                Map<String, Object> map03 = new HashMap<>();
                map03.put("style",  styleMap01);
                map03.put("label",  meeting.getMeetingFormatName());
                eventInfos.add(map03);
            }

            if (meeting.getPeopleNum() != null){
                Map<String, Object> map04 = new HashMap<>();
                map04.put("style",  styleMap01);
                map04.put("label",  meeting.getPeopleNum()  + "人");
                eventInfos.add(map04);
            }

            if (meeting.getWorkerNum() != null){
                Map<String, Object> map05 = new HashMap<>();
                map05.put("style",  null);
                map05.put("label",  "（工作人员" + meeting.getWorkerNum() + "人）");
                eventInfos.add(map05);
            }

            content.setEventInfos(eventInfos);
            bill.setContent(content);
        }
    }

    /**
     * 设置创建者信息
     * @param billExpMeeting 会议费用对象
     */
    private void setCreatorInfo(PcxBillExpMeeting billExpMeeting) {
        billExpMeeting.setCreator(PtyContext.getUsername());
        billExpMeeting.setCreatorName(PtyContext.getUsernameCn());
        billExpMeeting.setCreatedTime(DateUtil.nowTime());
    }

    /**
     * 设置修改者信息
     * @param billExpMeeting 会议费用对象
     */
    private void setModifierInfo(PcxBillExpMeeting billExpMeeting) {
        billExpMeeting.setModifier(PtyContext.getUsername());
        billExpMeeting.setModifierName(PtyContext.getUsernameCn());
        billExpMeeting.setModifiedTime(DateUtil.nowTime());
    }
}
