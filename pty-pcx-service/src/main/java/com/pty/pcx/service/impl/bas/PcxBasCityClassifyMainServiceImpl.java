package com.pty.pcx.service.impl.bas;

import com.alibaba.fastjson.JSON;
import com.pty.pa.license.common.util.DateUtil;
import com.pty.pcx.api.bas.PcxBasCityClassifyMainService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.common.enu.CityClassifyTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasCityClassifyMainDao;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bas.PcxBasCityClassifyMain;
import com.pty.pcx.entity.stand.qo.PcxStandShowButtonQO;
import com.pty.pcx.qo.bas.*;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市分类主表(PcxBasCityClassifyMain)表服务实现类
 * <AUTHOR>
 * @since 2024-11-22 14:12:05
 */
@Slf4j
@Indexed
@Service
public class PcxBasCityClassifyMainServiceImpl implements PcxBasCityClassifyMainService {

	@Autowired
	private PcxBasCityClassifyMainDao pcxBasCityClassifyMainDao;

	@Autowired
	private TransOptService transOptService;

	@Autowired
	private IBusinessRuleOptionService businessRuleOptionService;

	/**
	 * 通过ID查询单条数据
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public PcxBasCityClassifyMain selectById(String id) {
		return pcxBasCityClassifyMainDao.selectById(id);
	}

	/**
	 * 查询多条数据
	 * @param pcxBasCityClassifyMain 实例对象
	 * @return 对象列表
	 */
	@Override
	public List<PcxBasCityClassifyMain> selectList(PcxBasCityClassifyMain pcxBasCityClassifyMain) {
		return pcxBasCityClassifyMainDao.selectList(pcxBasCityClassifyMain);
	}

	/**
	 * 新增数据
	 * @param pcxBasCityClassifyMain 实例对象
	 * @return 实例对象
	 */
	@Override
	public int insertSelective(PcxBasCityClassifyMain pcxBasCityClassifyMain) {
		return pcxBasCityClassifyMainDao.insertSelective(pcxBasCityClassifyMain);
	}

	/**
	 * 修改数据
	 * @param pcxBasCityClassifyMain 实例对象
	 * @return 实例对象
	 */
	@Override
	public int update(PcxBasCityClassifyMain pcxBasCityClassifyMain) {
		return pcxBasCityClassifyMainDao.update(pcxBasCityClassifyMain);
	}

	/**
	 * 通过主键id删除数据
	 * @param id 主键
	 */
	@Override
	public int deleteById(String id) {
		return pcxBasCityClassifyMainDao.deleteById(id);
	}

	@Override
	public Response bizSelectAllClassify(BizQueryAllCityClassifyQO queryAllCityClassifyQO) {
		//校验参数
		CheckMsg checkMsg = validBizSelectAllClassify(queryAllCityClassifyQO);
		if (!checkMsg.isSuccess()){
			return Response.fail().setMsg(checkMsg.getMsgInfo());
		}
		List<DataQO> list = new ArrayList<>();
		if(queryAllCityClassifyQO.getClassifyType() == PcxCityClassifyConstant.CLASSIFY_TYPE_CITY
				|| queryAllCityClassifyQO.getClassifyType()==PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK){
			// 查询所有城市信息，查询所有城市和区
			list = pcxBasCityClassifyMainDao.selectAllRegion();
			// 添加祖父节点code
			for (DataQO dataQO : list) {
				if (dataQO.getDataCode().length()==2){
					dataQO.setGrandParentCode("0");
				}
				if (dataQO.getDataCode().length()==4){
					dataQO.setGrandParentCode("0");
				}
				if (dataQO.getDataCode().length()==6){
					dataQO.setGrandParentCode(dataQO.getDataCode().substring(0,2));
				}
			}
		}
		//查询分类表数据
		PcxBasCityClassifyMain mainQuery = new PcxBasCityClassifyMain();
		BeanUtils.copyProperties(queryAllCityClassifyQO, mainQuery);
		List<PcxBasCityClassify> classifyMainList = pcxBasCityClassifyMainDao.selectAllList(mainQuery);
		//组装已有分类数据，其他全部放入其他分类中
		BizAllClassifyVO vo = new BizAllClassifyVO();
		assemblyClassify(list, classifyMainList, vo);
//		if(queryAllCityClassifyQO.getClassifyType()==PcxCityClassifyConstant.CLASSIFY_TYPE_CITY){
//			//预置城市分类数据，保证至少有2级数据
//			if(vo.getClassifyList().isEmpty()){
//				CityClassifyVO classifyVO = new CityClassifyVO();
//				classifyVO.setClassifyCode("first");
//				classifyVO.setClassifyName("一类地区");
//				classifyVO.setIsSys(0);
//				setSortVO(classifyVO);
//				vo.getClassifyList().add(classifyVO);
//
//				CityClassifyVO classifyVO2 = new CityClassifyVO();
//				classifyVO2.setClassifyCode("second");
//				classifyVO2.setClassifyName("二类地区");
//				classifyVO2.setIsSys(0);
//				setSortVO(classifyVO2);
//				vo.getClassifyList().add(classifyVO2);
//			}
//
//			if(vo.getClassifyList().size() == 1){
//				CityClassifyVO classifyVO = new CityClassifyVO();
//				classifyVO.setClassifyCode("second");
//				classifyVO.setClassifyName("二类地区");
//				classifyVO.setIsSys(0);
//				setSortVO(classifyVO);
//				vo.getClassifyList().add(classifyVO);
//			}
//		}
		return Response.success(vo);
	}

	private void assemblyClassify(List<DataQO> allCityList,
								  List<PcxBasCityClassify> classifyList,
								  BizAllClassifyVO vo) {
		//组装已有分类，按照分类编码分组，一个分类下挂着该分类下的全部
		Map<String, List<PcxBasCityClassify>> classifyMap = classifyList.stream()
				.collect(Collectors.groupingBy(PcxBasCityClassify::getClassifyCode));
		List<CityClassifyVO> classifyVOList = new ArrayList<>();
		vo.setClassifyList(classifyVOList);
		List<String> existsCityCodes = new ArrayList<>();
		for (List<PcxBasCityClassify> classifies : classifyMap.values()) {
			CityClassifyVO classifyVO = new CityClassifyVO();
			classifies.forEach(item->{
				existsCityCodes.add(item.getDataCode());
				classifyVO.setClassifyCode(item.getClassifyCode());
				classifyVO.setClassifyName(item.getClassifyName());
				classifyVO.setIsSys(0);
				if(StringUtils.isNotBlank(item.getPeakDateJson())){
					classifyVO.setPeakDateJson(JSON.parseArray(item.getPeakDateJson(), PeakDateQO.class));
				}

				DataQO cityQO = new DataQO();
				cityQO.setDataCode(item.getDataCode());
				cityQO.setDataName(item.getDataName());
				cityQO.setParentCode(item.getParentCode());
				cityQO.setFullName(item.getDataName());
				// 添加祖父节点code
				if (cityQO.getDataCode().length()==2){
					cityQO.setGrandParentCode("0");
				}
				if (cityQO.getDataCode().length()==4){
					cityQO.setGrandParentCode("0");
				}
				if (cityQO.getDataCode().length()==6){
					cityQO.setGrandParentCode(cityQO.getDataCode().substring(0,2));
				}
				classifyVO.getCityList().add(cityQO);
			});
			setSortVO(classifyVO);
			vo.getClassifyList().add(classifyVO);
		}

		//不在分类中的，归入其他分类中
		allCityList = allCityList.stream()
				.filter(item->!existsCityCodes.contains(item.getDataCode()))
				.collect(Collectors.toList());
		CityClassifyVO other = new CityClassifyVO();
		other.setClassifyName("其他");
		other.setClassifyCode("other");
		other.setIsSys(1);
		if (CollectionUtils.isNotEmpty(allCityList)){
			for (DataQO qo : allCityList) {
				other.getCityList().add(new DataQO(qo.getDataCode(), qo.getDataName(),qo.getFullName(),qo.getParentCode(),qo.getGrandParentCode()));
			}
		}
		vo.setOtherClassify(other);
	}

	// 设置排序
	public void setSortVO(CityClassifyVO vo){
//		vo.setSort(vo.getClassifyCode().equals("first") ? 1 : vo.getClassifyCode().equals("second") ? 2 : 3);
		vo.setSort(0);
	}

	public Response updateCityClassify(BatchUpdateCityClassifyQO qo) {
		CheckMsg checkMsg = validInsertOrUpdateCityClassify(qo);
		if (!checkMsg.isSuccess()){
			return Response.fail().setMsg(checkMsg.getMsgInfo());
		}
		String updateTime = DateUtil.nowTime();

		List<PcxBasCityClassifyMain> mainList = new ArrayList<>();
		List<PcxBasCityClassify> list = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(qo.getClassifyList())){
			for (PcxBasCityClassifyQO classifyQO : qo.getClassifyList()) {
				// 城市分类code
				String classifyCode = "CITYCLASSIFY_"+IDGenerator.id();
				if (CollectionUtils.isNotEmpty(classifyQO.getCityList())){
					for (DataQO cityQO : classifyQO.getCityList()) {
						PcxBasCityClassify pcxBasCityClassify = new PcxBasCityClassify();
						BeanUtils.copyProperties(qo, pcxBasCityClassify);
						pcxBasCityClassify.setIsSys(0);
						pcxBasCityClassify.setId(IDGenerator.id());
						pcxBasCityClassify.setClassifyName(classifyQO.getClassifyName());
						pcxBasCityClassify.setClassifyCode(classifyQO.getClassifyCode());
						pcxBasCityClassify.setDataName(cityQO.getDataName());
						pcxBasCityClassify.setDataCode(cityQO.getDataCode());
						pcxBasCityClassify.setParentCode(cityQO.getParentCode());
						pcxBasCityClassify.setCreater(qo.getUserCode());
						pcxBasCityClassify.setCreateTime(updateTime);
						pcxBasCityClassify.setTenantId(PtyContext.getTenantId());
						pcxBasCityClassify.setExpenseTypeCode(qo.getExpenseTypeCode());
						if(CollectionUtils.isNotEmpty(classifyQO.getPeakDateJson())){
							pcxBasCityClassify.setPeakDateJson(JSON.toJSONString(classifyQO.getPeakDateJson()));
						}
						if(qo.getClassifyType()!=3&&StringUtil.isEmpty(classifyQO.getClassifyCode())){
							// 重新设置code
							pcxBasCityClassify.setClassifyCode(classifyCode);
						}
						list.add(pcxBasCityClassify);
					}
				}
				PcxBasCityClassifyMain pcxBasCityClassifyMain = new PcxBasCityClassifyMain();
				BeanUtils.copyProperties(qo, pcxBasCityClassifyMain);
				pcxBasCityClassifyMain.setIsSys(0);
				pcxBasCityClassifyMain.setId(IDGenerator.id());
				pcxBasCityClassifyMain.setClassifyName(classifyQO.getClassifyName());
				pcxBasCityClassifyMain.setClassifyCode(classifyQO.getClassifyCode());
				pcxBasCityClassifyMain.setCreater(qo.getUserCode());
				pcxBasCityClassifyMain.setCreateTime(updateTime);
				pcxBasCityClassifyMain.setTenantId(PtyContext.getTenantId());
				pcxBasCityClassifyMain.setExpenseTypeCode(qo.getExpenseTypeCode());
				if(CollectionUtils.isNotEmpty(classifyQO.getPeakDateJson())){
					pcxBasCityClassifyMain.setPeakDateJson(JSON.toJSONString(classifyQO.getPeakDateJson()));
				}
				if(qo.getClassifyType()!=3&&StringUtil.isEmpty(classifyQO.getClassifyCode())){
					// 重新设置code
					pcxBasCityClassifyMain.setClassifyCode(classifyCode);
				}
				mainList.add(pcxBasCityClassifyMain);
			}
		}
		if(qo.getClassifyType().equals(CityClassifyTypeEnum.CITY_CLASSIFY_TYPE.getType()) || qo.getClassifyType().equals(CityClassifyTypeEnum.POSITION_CLASSIFY_TYPE.getType())) {
			transOptService.delAndInsertCityClassifyMain(qo.getClassifyType(), qo.getExpenseTypeCode(), 0, qo.getAgyCode(), qo.getFiscal(), qo.getMofDivCode(), list, mainList);
		}else {
			transOptService.delAndInsertCityPeakClassifyMain(qo.getClassifyType(), qo.getExpenseTypeCode(), 0, qo.getAgyCode(), qo.getFiscal(), qo.getMofDivCode(), list, mainList);
		}
		return Response.success();
	}

	@Override
	public Response<?> getClassifyExpList(PcxStandShowButtonQO pcxStandShowButtonQO) {
		if(StringUtils.isBlank(pcxStandShowButtonQO.getBtnTypeCode())) {
			pcxStandShowButtonQO.setBtnTypeCode("SHOW_AREA_BUTTION");
		}
		PaOptionQO optionQuery = new PaOptionQO();
		optionQuery.setFiscal(pcxStandShowButtonQO.getFiscal());
		optionQuery.setMofDivCode(pcxStandShowButtonQO.getMofDivCode());
		optionQuery.setAgyCode(pcxStandShowButtonQO.getAgyCode());
		optionQuery.setGroupName("PCX_STAND_BUTTON");
		optionQuery.setOptCode(pcxStandShowButtonQO.getBtnTypeCode());
		return businessRuleOptionService.selectListByQO(optionQuery);
	}

	private CheckMsg validInsertOrUpdateCityClassify(BatchUpdateCityClassifyQO qo) {
		if (Objects.isNull(qo)){
			return CheckMsg.fail("参数为空");
		}
//		if (Objects.isNull(qo.getIsSys())){
//			return CheckMsg.fail("是否预置参数为空");
//		}
		if (Objects.isNull(qo.getClassifyType())){
			return CheckMsg.fail("分类类型不能为空");
		}
//		if (StringUtil.isEmpty(qo.getExpenseTypeCode())){
//			return CheckMsg.fail("费用明细编码不能为空");
//		}
		if (StringUtil.isEmpty(qo.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(qo.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(qo.getMofDivCode())){
			return CheckMsg.fail("区划不能为空");
		}
		return CheckMsg.success();
	}


	private CheckMsg validBizSelectAllClassify(BizQueryAllCityClassifyQO queryAllCityClassifyQO){
		if (Objects.isNull(queryAllCityClassifyQO)){
			return CheckMsg.fail("参数为空");
		}
//		if (Objects.isNull(queryAllCityClassifyQO.getIsSys())){
//			return CheckMsg.fail("是否预置参数为空");
//		}
		if (Objects.isNull(queryAllCityClassifyQO.getClassifyType())){
			return CheckMsg.fail("分类类型不能为空");
		}
//		if (StringUtil.isEmpty(queryAllCityClassifyQO.getExpenseTypeCode())){
//			return CheckMsg.fail("费用明细编码不能为空");
//		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getAgyCode())){
			return CheckMsg.fail("单位编码不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getFiscal())){
			return CheckMsg.fail("年度不能为空");
		}
		if (StringUtil.isEmpty(queryAllCityClassifyQO.getMofDivCode())){
			return CheckMsg.fail("区划不能为空");
		}
		return CheckMsg.success();
	}
}


