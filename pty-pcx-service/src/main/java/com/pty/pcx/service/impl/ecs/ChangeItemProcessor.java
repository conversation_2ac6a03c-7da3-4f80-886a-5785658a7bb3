package com.pty.pcx.service.impl.ecs;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.attachlist.PcxAttachListRelationDao;
import com.pty.pcx.entity.attachlist.PcxAttachListRelation;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.qo.ecs.ChangeItemQO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16 17:23
 * @description 修改事项接口
 */
@Service
public class ChangeItemProcessor {


    @Resource
    private List<ChangeItemCode> changeItemCodeList;
    @Autowired
    private PcxAttachListRelationDao pcxAttachListRelationDao;

    private Map<Integer, ChangeItemCode> changeItemCodeMap;

    @PostConstruct
    private void init() {
        changeItemCodeMap = new HashMap<>();
        for (ChangeItemCode changeItemCode : changeItemCodeList) {
            changeItemCodeMap.put(changeItemCode.itemBizType(), changeItemCode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public CheckMsg changeItem(ChangeItemQO changeItemQO) {
        PcxBill pcxBill = changeItemQO.getPcxBill();
        Integer originBizType = pcxBill.getBizType();
        ChangeItemCode changeItemCode = changeItemCodeMap.get(originBizType);
        if (changeItemCode == null) {
            throw new RuntimeException("原业务类型不支持更改事项");
        }
        ChangeItemCode targetChangeItemCode = changeItemCodeMap.get(changeItemQO.getBizType());
        if (targetChangeItemCode == null) {
            throw new RuntimeException("目标业务类型不支持修改事项");
        }
        PcxAttachListRelation pcxAttachListRelation = new PcxAttachListRelation();
        pcxAttachListRelation.setBillId(pcxBill.getId());
        List<PcxAttachListRelation> attachListRelations = pcxAttachListRelationDao.select(pcxAttachListRelation);
        ChangeItemContext changeItemContext = ChangeItemContext.builder()
                        .pcxBill(pcxBill)
                        .itemCode(changeItemQO.getItemCode())
                        .build();

        changeItemCode.erasureOldData(changeItemContext);

        if (CollectionUtils.isNotEmpty(attachListRelations)){
            for (PcxAttachListRelation attachListRelation : attachListRelations) {
                attachListRelation.setAttachListId("PCX");
                pcxAttachListRelationDao.updateById(attachListRelation);
            }
        }
        return changeItemCode.initNewData(changeItemContext);
    }

}
