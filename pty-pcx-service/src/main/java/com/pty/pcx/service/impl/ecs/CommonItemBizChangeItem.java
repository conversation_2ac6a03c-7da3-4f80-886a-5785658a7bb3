package com.pty.pcx.service.impl.ecs;

import com.pty.pcx.api.ecs.EcsProcessService;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.StartExpenseQO;
import com.pty.pub.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/16 17:27
 * @description 通用报销更新事项
 */
@Service
public class CommonItemBizChangeItem implements ChangeItemCode{

    @Resource
    private EcsExpCommonService ecsExpCommonService;
    @Resource
    private EcsProcessService ecsProcessService;

    @Override
    public Integer itemBizType() {
        return ItemBizTypeEnum.COMMON.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void erasureOldData(ChangeItemContext changeItemContext) {
        ecsExpCommonService.erasureOldData(changeItemContext);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckMsg initNewData(ChangeItemContext changeItemContext) {
        PcxBill pcxBill = changeItemContext.getPcxBill();
        StartExpenseQO startExpenseQO = new StartExpenseQO();
        startExpenseQO.setUserCode(pcxBill.getCreator());
        startExpenseQO.setUserName(pcxBill.getCreatorName());
        startExpenseQO.setItemCode(changeItemContext.getItemCode());
        startExpenseQO.setTransDate(pcxBill.getTransDate());
        startExpenseQO.setChangeItemCode(true);
        startExpenseQO.setFiscal(pcxBill.getFiscal());
        startExpenseQO.setAgyCode(pcxBill.getAgyCode());
        startExpenseQO.setMofDivCode(pcxBill.getMofDivCode());
        startExpenseQO.setPcxBill(pcxBill);
        startExpenseQO.setItemName(changeItemContext.getItemName());
        List<PcxExpDetailEcsRel> ecsRelList = changeItemContext.getEcsRelList().stream().filter(item -> StringUtil.isNotEmpty(item.getEcsBillId())).distinct().collect(Collectors.toList());
        List<ExpInvoiceQO.EscBillQO> ecsList = new ArrayList<>();
        startExpenseQO.setBillList(ecsList);
        startExpenseQO.setTypeArr(new String[]{PositionBlockEnum.ATTACH_LIST.getCode()});
        if (CollectionUtils.isNotEmpty(ecsRelList)){
            for (PcxExpDetailEcsRel rel : ecsRelList) {
                ExpInvoiceQO.EscBillQO escBillQO = new ExpInvoiceQO.EscBillQO();
                escBillQO.setBillId(rel.getEcsBillId());
                escBillQO.setBillAttachType(0);
                ecsList.add(escBillQO);
            }
        }

        return ecsProcessService.startCommonExpense(startExpenseQO);
    }
}
