package com.pty.pcx.service.impl.treasurypay.detail;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.bill.PcxBillBalanceService;
import com.pty.pcx.api.bill.PcxBillSettlementInfoService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BalanceTypeEnum;
import com.pty.pcx.common.enu.BillPayDetailStatusEnum;
import com.pty.pcx.common.enu.BillPayTypeEnum;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.mad.MadAgyAccountDTO;
import com.pty.pcx.dto.mad.MadBankNodeDTO;
import com.pty.pcx.dto.mad.MadCurrentDTO;
import com.pty.pcx.dto.mad.MadEmployeeCardDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.entity.treasurypay.detail.PcxBillPayDetail;
import com.pty.pcx.mad.IMadAgyAccountExternalService;
import com.pty.pcx.mad.IMadBankNodeExternalService;
import com.pty.pcx.mad.IMadCurrentExternalService;
import com.pty.pcx.mad.IMadEmployeeCardExternalService;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.bill.PcxBillBalanceQO;
import com.pty.pcx.qo.mad.MadAgyAccountQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailSaveQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.treasurypay.detail.PayDetailVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class PayDetailCalculator {

    @Autowired
    private PcxBillBalanceService pcxBillBalanceService;
    @Autowired
    private PcxBillSettlementInfoService pcxBillSettlementInfoService;
    @Autowired
    private IMadCurrentExternalService madCurrentExternalService;
    @Autowired
    private IMadBankNodeExternalService madBankNodeExternalService;
    @Autowired
    private IMadEmployeeCardExternalService madEmployeeCardExternalService;
    @Autowired
    private IMadAgyAccountExternalService madAgyAccountExternalService;
    @Autowired
    private BillMainService billMainService;

    @Autowired
    private IPcxBasItemService pcxBasItemService;



    private static final BiFunction<String, String, String> getCardKey = (accountNo, payeeAccCode) -> accountNo + "_" + payeeAccCode;

    public List<PcxBillPayDetail> buildPayDetails(PayDetailSaveQO qo) {
        String billId = qo.getBillId();
        Assert.notBlank(billId, "单据ID不能为空");
        PcxBill bill = billMainService.view(billId);

        List<PcxBillBalance> balances = pcxBillBalanceService.selectByBillId(billId);
        List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(billId));
        Map<String, PcxBillSettlement> settlementMap = settlements.stream().collect(Collectors.toMap(PcxBillSettlement::getSettlementUk, Function.identity(), (a, b) -> a));

        List<PayDetailVO.PayDetailRow> rows = Stream.of(qo.getSettleCash(), qo.getSettleTransfer(), qo.getSettleBusicard(), qo.getSettleCheque(), qo.getSettleBusiTransfer()).flatMap(Collection::stream).collect(Collectors.toList());
        rows.forEach(row -> {
            PcxBillSettlement settlement = settlementMap.get(row.getSettlementUk());
            if (settlement != null)
                settlement.setPayeeAccNo(row.getReceiveAccountNo());
        });
        // 要是页面上有收款账户信息则使用页面上的


        LinkedList<RemainingBean<PcxBillSettlement>> remainSettlements = settlements.stream()
                .map(settlement -> new RemainingBean<>(settlement, settlement.getCheckAmt())).collect(Collectors.toCollection(LinkedList::new));
        // 收款帐户信息追加
        appendBankNodeInfo(remainSettlements, bill);

        Map<String, List<RemainingBean<PcxBillBalance>>> remainBalances = balances.stream()
                .map(balance -> new RemainingBean<>(balance, balance.getUsedAmt()))
                .collect(Collectors.groupingBy(remain ->
                        remain.getData().getPayAccountNo() != null ? remain.getData().getPayAccountNo() : ""));
        PcxBasItemQO itemQO = new PcxBasItemQO();
        itemQO.setItemCode(bill.getItemCode());
        itemQO.setMofDivCode(bill.getMofDivCode());
        itemQO.setFiscal(bill.getFiscal());
        itemQO.setAgyCode(bill.getAgyCode());
        itemQO.setBilltypeCode(bill.getBillFunc());
        boolean isCtrlBudget = pcxBasItemService.isIsCtrlBudget(itemQO);

        // 付款账户信息追加
        corpBank(bill, remainBalances);

        Map<String, RemainingBean<PcxBillSettlement>> uk$settlement$rel = remainSettlements.stream().collect(Collectors
                .toMap(
                        remain -> remain.getData().getSettlementUk(),
                        remain -> remain,
                        (remain1, remain2) -> remain1));

        Map<String, RemainingBean<PcxBillBalance>> uk$balance$rel = remainBalances.values().stream().flatMap(Collection::stream).collect(Collectors
                .toMap(
                        remain -> remain.getData().getBalanceUK(),
                        remain -> remain,
                        (remain1, remain2) -> remain1));

        List<PcxBillPayDetail> result = new ArrayList<>();


        rows.forEach(balance -> {
            RemainingBean<PcxBillBalance> ukBalance = uk$balance$rel.get(balance.getBalanceUk());
                    /*RemainingBean.<PcxBillBalance>builder()
                    .data(PcxBillBalance.builder().usedAmt(BigDecimal.valueOf(Long.MAX_VALUE)).build())
                    .build();*/
            if (isCtrlBudget) {
                Assert.state(StrUtil.isNotBlank(balance.getBalanceUk()) && StrUtil.isNotBlank(balance.getSettlementUk()), "指标/结算方式唯一键不存在");


                Assert.state(ukBalance != null, "指标{}对应记录不存在", balance.getBalanceUk());
                Assert.state(Objects.nonNull(ukBalance.getBankNodeDTO()), "指标{}对应的付款账户信息不存在", balance.getReceiveAccountNo());
            }
            RemainingBean<PcxBillSettlement> ukSettlement = uk$settlement$rel.get(balance.getSettlementUk());
            Assert.state(ukSettlement != null, "结算方式{}对应记录不存在", balance.getSettlementUk());
            Assert.state(Objects.nonNull(ukSettlement.getBankNodeDTO()), "结算方式{}对应的收款账户信息不存在", balance.getReceiveAccountNo());

            result.add(buildPayDetail(bill, ukBalance, ukSettlement, balance));
        });
        return result;
    }




    private PcxBillPayDetail buildPayDetail(PcxBill bill, RemainingBean<PcxBillBalance> ukBalanceRemain, RemainingBean<PcxBillSettlement> ukSettlementRemain, PayDetailVO.PayDetailRow balance) {
        PcxBillBalance ukBalance = ukBalanceRemain.getData();
        PcxBillSettlement ukSettlement = ukSettlementRemain.getData();
        MadBankNodeDTO payDTO = ukSettlementRemain.getBankNodeDTO();
        MadBankNodeDTO payeeDTO = ukBalanceRemain.getBankNodeDTO();

        return PcxBillPayDetail.builder()
                .id(IDGenerator.snowflakeId())
                .billId(bill.getId())
                .fiscal(bill.getFiscal())
                .mofDivCode(bill.getMofDivCode())
                .agyCode(bill.getAgyCode())
                .agyName(bill.getAgyName())
                .checkAmt(balance.getInputAmt())
                .balanceId(ukBalance.getId())
                .balanceNo(ukBalance.getBalanceNo())
                .balanceSource(ukBalance.getBalanceSource())
                .balanceUk(ukBalance.getBalanceUK())
                .expenseCode(ukBalance.getExpenseCode())
                .settlementTypeName(ukSettlement.getSettlementName())
                .settlementType(ukSettlement.getSettlementType())
                .settlementUk(ukSettlement.getSettlementUk())
                .payeeAccountNo(ukSettlement.getPayeeAccNo())
                .payeeAccountName(ukSettlement.getPayeeAccName())
                .payeeBankCode(ukSettlement.getPayeeAccCode())
                .payeeBankName(ukSettlement.getPayeeBankName())
                .payeeAccountTypeCode(payeeDTO.getAccountType())
                .payeeAccountTypeName(payeeDTO.getAccountTypeName())
                .payeeAccountCity(payeeDTO.getCityCode())
                .payeeBankNodeNo(payeeDTO.getBankNodeNo())
                .payeeBankNodeName(payeeDTO.getBankNodeName())
                .payStatus(BillPayDetailStatusEnum.STASH.getCode())
                .payBankName(payDTO.getBankName())
                .payBankCode(payDTO.getBankCode())
                .payAccountTypeCode(payDTO.getAccountType())
                .payAccountTypeName(payDTO.getAccountTypeName())
                .payAccountCity(payDTO.getCityCode())
                .payAccountNo(ukBalance.getPayAccountNo())
                .payAccountName(ukBalance.getPayAccountName())
                .payType(ukSettlement.getPayType())
                .payTypeCode(BillPayTypeEnum.BANK_TRANSFER.getCode())
                .payTypeName(BillPayTypeEnum.BANK_TRANSFER.getName())
                .build();
    }

    public CheckMsg<PayDetailVO> restoreFromPayDetails(String billId, List<PcxBillPayDetail> payDetails) {
        if (CollectionUtils.isEmpty(payDetails))
            return CheckMsg.success();
        PcxBill bill = billMainService.view(billId);
        // 初始化支付详情对象
        PayDetailVO payDetailVO = PayDetailVO.builder()
                .billId(billId)
                .settleCash(new LinkedList<>())
                .settleTransfer(new LinkedList<>())
                .settleBusiTransfer(new LinkedList<>())
                .settleBusicard(new LinkedList<>())
                .settleCheque(new LinkedList<>())
                .build();

        List<PcxBillBalance> balances = pcxBillBalanceService.selectByBillId(billId);
        List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(billId));

        Map<String, RemainingBean<PcxBillBalance>> uk$balance$rel = balances.stream().collect(Collectors.toMap(PcxBillBalance::getBalanceUK, a -> new RemainingBean<>(a, a.getUsedAmt()), (v1, v2) -> v1));
        Map<String, RemainingBean<PcxBillSettlement>> uk$settlement = settlements.stream().collect(Collectors.toMap(PcxBillSettlement::getSettlementUk, a -> new RemainingBean<>(a, a.getCheckAmt()), (v1, v2) -> v1));

        appendBankNodeInfo(new LinkedList<>(uk$settlement.values()), bill);

        payDetails.forEach(payDetail -> {
            RemainingBean<PcxBillBalance> balance = uk$balance$rel.get(payDetail.getBalanceUk());
            RemainingBean<PcxBillSettlement> settlement = uk$settlement.get(payDetail.getSettlementUk());
            generatePayDetailRow(settlement, balance, payDetail.getCheckAmt(),
                    payDetailVO.getSettleCash(),
                    payDetailVO.getSettleTransfer(),
                    payDetailVO.getSettleBusiTransfer(),
                    payDetailVO.getSettleBusicard(),
                    payDetailVO.getSettleCheque());
        });

        setSettlementCheckAmt(payDetailVO);
        return CheckMsg.success(payDetailVO);
    }

    /**
     * 计算支付详情
     *
     * @param qo 支付详情查询对象，包含查询支付详情所需的参数
     * @return 返回计算后的支付详情信息
     */
    public CheckMsg<PayDetailVO> calculatePayDetails(PayDetailQO qo) {

        // 获取账单主信息
        PcxBill bill = billMainService.view(qo.getBillId());
        //补充缺失信息
        qo.getBalanceList().stream().forEach(item->{
            if (StringUtil.isEmpty(item.getExpenseCode())){
                item.setExpenseCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
                item.setExpenseName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            }
            if (StringUtil.isEmpty(item.getBalanceSource())){
                item.setBalanceSource(bill.getBillFuncCode());
            }
        });
        // 获取settlementUK对应的记录
        List<PcxBillSettlement> settlements = pcxBillSettlementInfoService.selectByBillIds(Collections.singletonList(qo.getBillId()));

        // 创建结算余额剩余对象列表，用于后续的余额分配
        LinkedList<RemainingBean<PcxBillSettlement>> settlementRemains = settlements.stream()
                // 有核定金额使用核定金额，没有使用录入金额，都没有这条记录不做分配指标
                .map(settlement -> new RemainingBean<>(settlement,
                        set ->
                                set.getCheckAmt() != null && set.getCheckAmt().compareTo(BigDecimal.ZERO) > 0 ? set.getCheckAmt() :
                                        set.getInputAmt() != null && set.getInputAmt().compareTo(BigDecimal.ZERO) > 0 ? set.getInputAmt() : BigDecimal.ZERO)).collect(Collectors.toCollection(LinkedList::new));

        // 创建余额剩余对象列表，过滤掉无效的余额记录
        LinkedList<RemainingBean<? extends PcxBillBalance>> balanceRemains = qo.getBalanceList().stream()
                .filter(Objects::nonNull)
                .filter(balance -> StrUtil.isNotBlank(balance.getBalanceUK()))
                .filter(balance -> ObjectUtils.firstNonNull(balance.getUsedAmt(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0)
                .filter(balance -> BalanceTypeEnum.BUD.getCode().equals(balance.getBalanceType()))
                .filter(balance -> StrUtil.isNotBlank(balance.getBalanceUK()))
                .map(balance -> new RemainingBean<>(balance, PcxBillBalance::getUsedAmt)).collect(Collectors.toCollection(LinkedList::new));

        // 初始化支付详情对象
        PayDetailVO payDetailVO = PayDetailVO.builder()
                .billId(qo.getBillId())
                .settleCash(new LinkedList<>())
                .settleTransfer(new LinkedList<>())
                .settleBusiTransfer(new LinkedList<>())
                .settleBusicard(new LinkedList<>())
                .settleCheque(new LinkedList<>())
                .build();

        // 补全收款信息
        appendBankNodeInfo(settlementRemains, bill);

        // 根据分配类型，将余额分配到结算对象中
        allocateBalance2settlement(settlementRemains, balanceRemains, payDetailVO, qo.getAllocateType());

        //合并checkAmt
        setSettlementCheckAmt(payDetailVO);

        return CheckMsg.success(payDetailVO);
    }

    private static void setSettlementCheckAmt(PayDetailVO payDetailVO) {
        Map<String, BigDecimal> settlementTotals =
                Stream.of(
                        payDetailVO.getSettleCash(),
                        payDetailVO.getSettleTransfer(),
                        payDetailVO.getSettleBusiTransfer(),
                        payDetailVO.getSettleBusicard(),
                        payDetailVO.getSettleCheque()
                ).flatMap(Collection::stream)
                .filter(item -> StringUtil.isNotEmpty(item.getSettlementUk()))
                .collect(Collectors.groupingBy(PayDetailVO.PayDetailRow::getSettlementUk,
                        Collectors.mapping(PayDetailVO.PayDetailRow::getInputAmt,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        // 更新每个结算方式的核定金额
        Stream.of(
                        payDetailVO.getSettleCash(),
                        payDetailVO.getSettleTransfer(),
                        payDetailVO.getSettleBusiTransfer(),
                        payDetailVO.getSettleBusicard(),
                        payDetailVO.getSettleCheque()
                ).flatMap(Collection::stream)
                .forEach(row -> {
                    if (settlementTotals.containsKey(row.getSettlementUk())) {
                        row.setCheckAmt(settlementTotals.get(row.getSettlementUk()));
                    }
                });
    }

    /**
     * 分配结算单金额到结算单行
     * @param settlementRemains
     * @param balanceRemains
     * @param payDetailVO
     * @param allocateType
     */
    private static void allocateBalance2settlement(LinkedList<RemainingBean<PcxBillSettlement>> settlementRemains,
                                                   LinkedList<RemainingBean<? extends PcxBillBalance>> balanceRemains,
                                                   PayDetailVO payDetailVO,
                                                   PayDetailQO.AllocateTypeEnum allocateType) {
        List<PayDetailVO.PayDetailRow> cash = payDetailVO.getSettleCash();
        List<PayDetailVO.PayDetailRow> transfer = payDetailVO.getSettleTransfer();
        List<PayDetailVO.PayDetailRow> busiTransfer = payDetailVO.getSettleBusiTransfer();
        List<PayDetailVO.PayDetailRow> busicard = payDetailVO.getSettleBusicard();
        List<PayDetailVO.PayDetailRow> cheque = payDetailVO.getSettleCheque();
        Map<String, List<PayDetailVO.PayDetailRow>> type2rows = new HashMap<>();
        type2rows.put(SettlementTypeEnum.SETTLE_CASH.getCode(), cash);
        type2rows.put(SettlementTypeEnum.SETTLE_TRANSFER.getCode(), transfer);
        type2rows.put(SettlementTypeEnum.SETTLE_BUSICARD.getCode(), busicard);
        type2rows.put(SettlementTypeEnum.SETTLE_CHEQUE.getCode(), cheque);
        type2rows.put(SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode(), busiTransfer);
        int s_ptr = 0, b_ptr = 0;
        while (s_ptr < settlementRemains.size()) {
            if (allocateType.equals(PayDetailQO.AllocateTypeEnum.AMOUNT)) {
                // 将余额相等的, 提到s_ptr, b_ptr位置, 后面优先处理
                int _s_ptr = s_ptr, _b_ptr = b_ptr;
                amount_check:
                while (_s_ptr < settlementRemains.size()) {
                    while (_b_ptr < balanceRemains.size()) {
                        RemainingBean<PcxBillSettlement> settleRemain = settlementRemains.get(_s_ptr);
                        RemainingBean<? extends PcxBillBalance> balanceRemain = balanceRemains.get(_b_ptr);
                        if (balanceRemain.getRemaining().compareTo(settleRemain.getRemaining()) == 0) {
                            // settlementRemains交换s_ptr 和 _s_ptr 位置
                            RemainingBean<PcxBillSettlement> s_settle = settlementRemains.get(s_ptr);
                            settlementRemains.set(s_ptr, settleRemain);
                            settlementRemains.set(_s_ptr, s_settle);
                            // balanceRemains交换b_ptr 和 _b_ptr 位置
                            RemainingBean<? extends PcxBillBalance> b_balance = balanceRemains.get(b_ptr);
                            balanceRemains.set(b_ptr, balanceRemain);
                            balanceRemains.set(_b_ptr, b_balance);
                            break amount_check;
                        }
                        _b_ptr ++;
                    }
                    _s_ptr ++;
                }
            }else if (allocateType.equals(PayDetailQO.AllocateTypeEnum.OTHER)) {
                balanceRemains.clear();
            }

            RemainingBean<PcxBillSettlement> settleRemain = settlementRemains.get(s_ptr);
            // 如果指标行提前分配完, 为了避免循环判断, 提前设置一个最大值, 让settlement继续往后执行, 如果当前结算方式已有记录, 则不补空记录
//            if (CollUtil.isNotEmpty(type2rows.get(settleRemain.getData().getSettlementType()))) {
//                s_ptr++;
//                continue;
//            };
            // 如果只有一条结算方式, 则将所有指标拼接到该结算方式上
            RemainingBean<? extends PcxBillBalance> balanceRemain = null;
            BigDecimal incr = BigDecimal.ZERO;
            // 没有更多指标可匹配
            boolean noMoreBalances = b_ptr >= balanceRemains.size();
            // 至少包含结算方式中存在的一条settlementUK记录
            boolean containSettlement = type2rows.get(settleRemain.getData().getSettlementType()).stream().map(PayDetailVO.PayDetailRow::getSettlementUk).anyMatch(existSettlement -> existSettlement.equals(settleRemain.getData().getSettlementUk()));
            if (settlementRemains.size() == 1) {
                // 没指标匹配且已包含结算记录, 跳出循环
                if (noMoreBalances && containSettlement)
                    break;
                // 还有待匹配指标, 全部累计到当前结算记录
                if (!noMoreBalances) {
                    balanceRemain = balanceRemains.get(b_ptr++);
                    incr = balanceRemain.getRemaining();
                }
            }else {;
                balanceRemain = noMoreBalances ?
                        new RemainingBean<>(new PcxBillBalanceQO(), BigDecimal.ZERO) : balanceRemains.get(b_ptr);
                // 取最小可用量
                incr = NumberUtil.min(settleRemain.getRemaining(), balanceRemain.getRemaining());
                // settle/balance 行都减去最小值, 至少有一个为0
                settleRemain.setRemaining(settleRemain.getRemaining().subtract(incr));
                balanceRemain.setRemaining(balanceRemain.getRemaining().subtract(incr));
                // 剩余量小的(0), 要往后跳一位, 相等(都是0), 都往后跳一位
                if (settleRemain.getRemaining().compareTo(balanceRemain.getRemaining()) > 0 && !noMoreBalances) {
                    b_ptr++;
                } else if (settleRemain.getRemaining().compareTo(balanceRemain.getRemaining()) < 0) {
                    s_ptr++;
                } else {
                    b_ptr++;
                    s_ptr++;
                }
                // 如果已没有更多指标, 并且返回中已有该结算方式, 则不再添加匹配记录到
                if (noMoreBalances && containSettlement) {
                    continue;
                }
            }

            generatePayDetailRow(settleRemain, balanceRemain, incr, cash, transfer, busiTransfer, busicard, cheque);
        }
        transfer.sort(Comparator.comparing(PayDetailVO.PayDetailRow::getReceiveAccountNo));
        busiTransfer.sort(Comparator.comparing(PayDetailVO.PayDetailRow::getReceiveAccountNo));
        busicard.sort(Comparator.comparing(PayDetailVO.PayDetailRow::getReceiveAccountNo));
    }

    private static void generatePayDetailRow(RemainingBean<PcxBillSettlement> settleRemain,
                                             RemainingBean<? extends PcxBillBalance> balanceRemain,
                                             BigDecimal min,
                                             List<PayDetailVO.PayDetailRow> cash,
                                             List<PayDetailVO.PayDetailRow> transfer,
                                             List<PayDetailVO.PayDetailRow> busiTransfer,
                                             List<PayDetailVO.PayDetailRow> busicard,
                                             List<PayDetailVO.PayDetailRow> cheque) {
        if (settleRemain == null || settleRemain.getData() == null) {
            return;
        }

        String settlementTypeCode = settleRemain.getData().getSettlementType();
        if (StrUtil.isBlank(settlementTypeCode)) {
            return;
        }

        SettlementTypeEnum settlementType = SettlementTypeEnum.getByCode(settlementTypeCode);
        if (settlementType == null) {
            log.error("未知的结算类型编码: {}", settlementTypeCode);
            return;
        }

        balanceRemain = ObjectUtils.firstNonNull(balanceRemain, new RemainingBean<>(new PcxBillBalance(), BigDecimal.ZERO));
        PcxBillBalance balance = ObjectUtils.firstNonNull(balanceRemain.getData(), new PcxBillBalance());
        PayDetailVO.PayDetailRow row = PayDetailVO.PayDetailRow.builder()
                .balanceUk(ObjectUtils.firstNonNull(balance, new PcxBillBalance()).getBalanceUK())
                .balanceNo(balance.getBalanceNo())
                .settlementUk(settleRemain.getData().getSettlementUk())
                .isLabour(settleRemain.getData().getIsLabour())
                .taxAmt(settleRemain.getData().getTaxAmt())
                .inputAmt(min)
                .checkAmt(min)//和inputAmt一致
                .receiveAccountNo(settleRemain.getData().getPayeeAccNo())
                .receiveAccountName(settleRemain.getData().getPayeeAccName())
                .receiveBank(settleRemain.getData().getPayeeBankName())
                .receiveBanknodeNo(settleRemain.bankNodeDTO.getBankNodeNo())
                .receiveBanknodeName(settleRemain.bankNodeDTO.getBankNodeName())
                .build();

        switch (Objects.requireNonNull(settlementType)) {
            case SETTLE_CASH:
                cash.add(row);
                break;
            case SETTLE_TRANSFER:
                transfer.add(row);
                break;
            case SETTLE_BUSI_TRANSFER:
                busiTransfer.add(row);
                break;
            case SETTLE_BUSICARD:
                busicard.add(row);
                break;
            case SETTLE_CHEQUE:
                cheque.add(row);
                break;
            default:
                throw new IllegalArgumentException("settlement type is illegal:" + settlementType);
        }
    }

    private void appendBankNodeInfo(LinkedList<RemainingBean<PcxBillSettlement>> settlements, PcxBill bill) {
        // 对公
        Map<String, List<RemainingBean<PcxBillSettlement>>> corporateAccounts = settlements.stream()
                .filter(settlement -> settlement.getData().getSettlementType().equals(SettlementTypeEnum.SETTLE_BUSI_TRANSFER.getCode()))
                .collect(Collectors.groupingBy(settlement -> settlement.getData().getPayeeAccNo()));

        // 对私 个人卡 (涉及到劳务费)
        Map<String, List<RemainingBean<PcxBillSettlement>>> selfAccounts = settlements.stream()
                .filter(settlement -> settlement.getData().getSettlementType().equals(SettlementTypeEnum.SETTLE_TRANSFER.getCode())
                        && !Objects.equals(settlement.getData().getIsLabour(), PubConstant.STR_LOGIC_TRUE) )
                .collect(Collectors.groupingBy(settlement -> settlement.getData().getPayeeAccNo()));

        // 公务卡
        Map<String, List<RemainingBean<PcxBillSettlement>>> busiAccounts = settlements.stream()
                .filter(settlement -> settlement.getData().getSettlementType().equals(SettlementTypeEnum.SETTLE_BUSICARD.getCode()))
                .collect(Collectors.groupingBy(settlement -> StringUtil.getStringValue(settlement.getData().getPayeeAccNo())));

        Map<String, List<RemainingBean<PcxBillSettlement>>> noAccounts = settlements.stream()
                .filter(settlement -> {
                    PcxBillSettlement data = settlement.getData();
                    if (data == null) return false;
                    String settlementType = data.getSettlementType();
                    return SettlementTypeEnum.SETTLE_CHEQUE.getCode().equals(settlementType)
                            || SettlementTypeEnum.SETTLE_CASH.getCode().equals(settlementType);
                })
                .collect(Collectors.groupingBy(
                        settlement -> Optional.ofNullable(settlement.getData().getPayeeAccNo()).orElse("UNKNOWN"),
                        Collectors.toList()
                ));

        // 批量取对公转账
        corporateBank(bill, corporateAccounts);

        // 个人卡
        personalBank(bill, selfAccounts);

        // 公务卡
        personalBank(bill, busiAccounts);

        // 现金公务卡
        noAccounts.values().stream().flatMap(Collection::stream).forEach(remainingBean -> {
            remainingBean.setBankNodeDTO(new MadBankNodeDTO());
        });
    }

    private void corporateBank(PcxBill bill, Map<String, List<RemainingBean<PcxBillSettlement>>> corporateAccounts) {
        if (CollectionUtil.isNotEmpty(corporateAccounts)) {
            List<MadCurrentDTO> currentDTOS = madCurrentExternalService.selectByAccountNoList(CollectionUtil.newArrayList(corporateAccounts.keySet()), bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode());
            // 每个账号对应一个网点
            Map<String, MadCurrentDTO> allAccountNo$current = currentDTOS.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(MadCurrentDTO::getBankAccountNo, Function.identity(), (a, b) -> a));
            // 反写corporateAccounts
            corporateAccounts.forEach((accountNo, dtos) -> {
                MadCurrentDTO currentDTO = allAccountNo$current.get(accountNo);
                if (Objects.nonNull(currentDTO)) {
                    dtos.stream().map(RemainingBean::getData).forEach(data -> {
                        data.setPayeeAccNo(accountNo);
                        data.setPayeeAccName(currentDTO.getMadName());
                        data.setPayeeBankName(currentDTO.getBankName());
                    });
                }
            });
            Map<String, MadCurrentDTO> accountNo$current = currentDTOS.stream()
                    .filter(Objects::nonNull)
                    .filter(current -> StrUtil.isNotBlank(current.getBanknodeNo()))
                    .collect(Collectors.toMap(MadCurrentDTO::getBankAccountNo, Function.identity(), (a, b) -> a));
            if (CollectionUtil.isNotEmpty(accountNo$current.keySet())) {


                List<MadBankNodeDTO> madBankNodeDTOS = madBankNodeExternalService.selectByBankNodeNos(CollectionUtil.newArrayList(accountNo$current.keySet()));
                // 每个网点编码对应一个网点
                Map<String, MadBankNodeDTO> madBankNodeRel = madBankNodeDTOS.stream().collect(Collectors.toMap(MadBankNodeDTO::getBankNodeNo, Function.identity(), (a, b) -> a));
                corporateAccounts.forEach((accountNo, dtos) -> {
                    // 账号对应的settlement信息
                    dtos.forEach(dto -> {
                        MadCurrentDTO currentDTO = accountNo$current.getOrDefault(accountNo, new MadCurrentDTO());
                        if (madBankNodeRel.containsKey(currentDTO.getBanknodeNo())) {
                            dto.setBankNodeDTO(madBankNodeRel.get(accountNo));
                        } else {
                            log.error("corporate account not found bill id:{} bank node no:{}", bill.getId(), accountNo);
                        }
                    });
                });
            }
        }
    }


    private void corpBank(PcxBill bill, Map<String, List<RemainingBean<PcxBillBalance>>> accounts) {
        MadAgyAccountQO query = MadAgyAccountQO.builder()
                .agyCode(bill.getAgyCode())
                .mofDivCode(bill.getMofDivCode())
                .fiscal(bill.getFiscal())
                .accountCodes(CollUtil.isEmpty(accounts) ? null : new ArrayList<>(accounts.keySet()))
                .build();
        List<MadAgyAccountDTO> madAgyAccountDTOS = madAgyAccountExternalService.selectAgyAccountList(query);
        Assert.state(CollectionUtil.isNotEmpty(madAgyAccountDTOS), "单位付款账户不存在billNo:{} 账号:{}", bill.getBillNo(), accounts.keySet());
        Assert.state(CollUtil.isEmpty(accounts) || madAgyAccountDTOS.size() == accounts.size(), "部分单位付款账户不存在billNo:{} 账号:{}", bill.getBillNo(), CollUtil.disjunction(accounts.keySet(), madAgyAccountDTOS.stream().map(MadAgyAccountDTO::getAccountCode).collect(Collectors.toList())));
        Map<String, MadAgyAccountDTO> accountNo$card = madAgyAccountDTOS.stream().collect(Collectors.toMap(MadAgyAccountDTO::getAccountCode, Function.identity(), (a, b) -> a));
        if (CollectionUtil.isNotEmpty(accountNo$card.keySet())) {
            List<MadBankNodeDTO> madBankNodeDTOS = madBankNodeExternalService.selectByBankCodeAndBankNodeNoList(
                    accountNo$card.values().stream().map(MadAgyAccountDTO::getBankCode).collect(Collectors.toList()),
                    accountNo$card.values().stream().map(MadAgyAccountDTO::getBanknodeNo).collect(Collectors.toList()));
            Map<String, MadBankNodeDTO> bankNodeAndCodeRel = madBankNodeDTOS.stream().collect(Collectors.toMap(dto ->
                    getCardKey.apply(dto.getBankCode(), dto.getBankNodeNo()), Function.identity(), (a, b) -> a));
            if (CollUtil.isEmpty(accounts)) {
                Optional<MadAgyAccountDTO> first = madAgyAccountDTOS.stream().filter(dto -> dto.getAccountType().equals("06")).findFirst();
                Assert.isTrue(first.isPresent(), "单位基本户不存在！");
                MadAgyAccountDTO cardDTO = first.get();
                RemainingBean<PcxBillBalance> remainingBean = RemainingBean.<PcxBillBalance>builder()
                        .data(PcxBillBalance.builder()
                                .payAccountNo(cardDTO.getAccountCode())
                                .payAccountName(cardDTO.getAccountName())
                                .build())
                        .build();
                accounts.put(cardDTO.getBankCode(), Collections.singletonList(remainingBean));
                if (bankNodeAndCodeRel.containsKey(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBanknodeNo()))) {
                    remainingBean.setBankNodeDTO(bankNodeAndCodeRel.get(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBanknodeNo())));
                }else {
                    remainingBean.setBankNodeDTO(new MadBankNodeDTO());
                }
            }else {
                accounts.forEach((accountNo, dtos) -> {
                    MadAgyAccountDTO cardDTO = accountNo$card.getOrDefault(accountNo, new MadAgyAccountDTO());
                    dtos.forEach(dto -> {
                        if (bankNodeAndCodeRel.containsKey(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBanknodeNo()))) {
                            dto.setBankNodeDTO(bankNodeAndCodeRel.get(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBanknodeNo())));
                        }
                    });
                });
                Assert.state(accounts.values().stream().flatMap(Collection::stream).noneMatch(dto -> ObjectUtil.isNull(dto.getBankNodeDTO())), "部分付款账户网点不存在billNo:{} 账号:{}",
                        bill.getBillNo(),
                        accounts.values().stream().flatMap(Collection::stream).filter(dto -> ObjectUtil.isNull(dto.getBankNodeDTO())).map(dto -> dto.getData().getPayAccountNo()).collect(Collectors.toList()));
            }
        }
    }

    private void personalBank(PcxBill bill, Map<String, List<RemainingBean<PcxBillSettlement>>> accounts) {
        if (CollectionUtil.isNotEmpty(accounts)) {

            List<MadEmployeeCardDTO> selfCardDTOS = madEmployeeCardExternalService.selectEmployeeCardByNos(bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode(), CollectionUtil.newArrayList(accounts.keySet()));
            Assert.state(CollectionUtil.isNotEmpty(selfCardDTOS), "个人收款帐户不存在billNo:{} 账号:{}", bill.getBillNo(), accounts.keySet());
            Assert.state(selfCardDTOS.size() == accounts.size(), "部分个人收款帐户不存在billNo:{} 账号:{}", bill.getBillNo(), CollUtil.disjunction(accounts.keySet(), selfCardDTOS.stream().map(MadEmployeeCardDTO::getAccountNo).collect(Collectors.toList())));
            // 每个账号对应多个员工卡, 取第一个, 不管是谁的, 只要账号对应的bankCode和bankNodeCode, 这不同员工的应该是一样的
            Map<String, MadEmployeeCardDTO> allAccountNo$card = selfCardDTOS.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(MadEmployeeCardDTO::getAccountNo, Function.identity(), (a, b) -> a));
            accounts.forEach((accountNo, dtos) -> {
                MadEmployeeCardDTO currentDTO = allAccountNo$card.get(accountNo);
                if (Objects.nonNull(currentDTO)) {
                    dtos.stream().map(RemainingBean::getData).forEach(data -> {
                        data.setPayeeAccNo(accountNo);
                        data.setPayeeAccName(currentDTO.getAccountName());
                        data.setPayeeBankName(currentDTO.getBankName());
                    });
                }
            });
            Map<String, MadEmployeeCardDTO> accountNo$card = selfCardDTOS.stream()
                    .filter(Objects::nonNull)
                    .filter(card -> StrUtil.isNotBlank(card.getBankCode()))
                    .filter(card -> StrUtil.isNotBlank(card.getBankNodeCode()))
                    .collect(Collectors.toMap(MadEmployeeCardDTO::getAccountNo, Function.identity(), (a, b) -> a));
            if (CollectionUtil.isNotEmpty(accountNo$card.keySet())) {
                List<MadBankNodeDTO> madBankNodeDTOS = madBankNodeExternalService.selectByBankCodeAndBankNodeNoList(
                        accountNo$card.values().stream().map(MadEmployeeCardDTO::getBankCode).collect(Collectors.toList()),
                        accountNo$card.values().stream().map(MadEmployeeCardDTO::getBankNodeCode).collect(Collectors.toList()));
                Map<String, MadBankNodeDTO> bankNodeAndCodeRel = madBankNodeDTOS.stream().collect(Collectors.toMap(dto ->
                        getCardKey.apply(dto.getBankCode(), dto.getBankNodeNo()), Function.identity(), (a, b) -> a));
                accounts.forEach((accountNo, dtos) -> {
                    MadEmployeeCardDTO cardDTO = accountNo$card.getOrDefault(accountNo, new MadEmployeeCardDTO());
                    dtos.forEach(dto -> {
                        if (bankNodeAndCodeRel.containsKey(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBankNodeCode()))) {
                            dto.setBankNodeDTO(bankNodeAndCodeRel.get(getCardKey.apply(cardDTO.getBankCode(), cardDTO.getBankNodeCode())));
                        }
                    });
                });
                Assert.state(accounts.values().stream().flatMap(Collection::stream).noneMatch(dto -> ObjectUtil.isNull(dto.getBankNodeDTO())), "部分收款账户网点不存在billNo:{} 账号:{}",
                        bill.getBillNo(),
                        accounts.values().stream().flatMap(Collection::stream).filter(dto -> ObjectUtil.isNull(dto.getBankNodeDTO())).map(dto -> StringUtil.getStringValue(dto.getData().getPayeeAccNo())).collect(Collectors.toList()));
            }
        }
    }

    @Data
    @SuperBuilder
    public static final class RemainingBean<T> {
        private T data;
        private BigDecimal remaining;

        @JsonIgnore
        private MadBankNodeDTO bankNodeDTO = new MadBankNodeDTO();


        public RemainingBean(T data, Function<T, BigDecimal> remainingFunc) {
            this.data = data;
            this.remaining = remainingFunc.apply(data);
        }

        public RemainingBean(T data, BigDecimal remaining) {
            this.data = data;
            this.remaining = remaining;
        }
    }

}
