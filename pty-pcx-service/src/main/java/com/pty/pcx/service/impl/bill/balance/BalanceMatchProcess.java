package com.pty.pcx.service.impl.bill.balance;

import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 指标动态匹配处理
 */
@Component
@Slf4j
public class BalanceMatchProcess {

    /**
     * 指标动态匹配
     * @param pcxBillBalance
     */
    public void match(PcxBillBalance pcxBillBalance) {
        if (StringUtil.isNotEmpty(pcxBillBalance.getDepartmentCode()) && StringUtil.isNotEmpty(pcxBillBalance.getExpenseCode())) {
            //根据项目开支范围确定



        }

    }
}
