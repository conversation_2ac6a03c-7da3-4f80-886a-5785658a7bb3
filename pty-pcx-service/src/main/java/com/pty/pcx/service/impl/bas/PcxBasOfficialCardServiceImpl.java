package com.pty.pcx.service.impl.bas;

import cn.hutool.core.io.IoUtil;
import com.pty.pa.security.api.UserService;
import com.pty.pa.security.entity.PtyUser;
import com.pty.pcx.api.bas.IPcxBasOfficialCardService;
import com.pty.pcx.api.bas.IPcxMadBankNodeService;
import com.pty.pcx.api.bas.IPcxMadEmployeeService;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.ExcelUtil;
import com.pty.pcx.common.util.FileUtil;
import com.pty.pcx.dao.bas.PcxBasOfficialCardDao;
import com.pty.pcx.dto.mad.MadBankNodeDTO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bas.PcxBasOfficialCard;
import com.pty.pcx.qo.PcxExportQo;
import com.pty.pcx.qo.bas.PcxBasOfficialCardQO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pcx.vo.bas.PcxBasOfficialCardVO;
import com.pty.pub.common.bean.PageResult;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.rest.RestClientReference;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Indexed
@Service
public class PcxBasOfficialCardServiceImpl implements IPcxBasOfficialCardService {

    @Autowired
    private PcxBasOfficialCardDao pcxBasOfficialCardDao;

    @Autowired
    private IPcxMadEmployeeService pcxMadEmployeeService;

    @Autowired
    private IPcxMadBankNodeService madBankNodeService;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Autowired(required = false)
    @RestClientReference(microServiceNames = {"pa"})
    private UserService userService;

    //公务卡信息模板表头
    private static final String[] BAS_CARD_HEADERS = {"人员编码", "人员名称", "开户银行", "银行卡号"};

    @Override
    public Response<?> selectPageList(PcxBasOfficialCardQO pcxBasOfficialCard) {
        //业务参数校验
        CheckMsg<?> validated = isValided(pcxBasOfficialCard, true, false);
        if (!validated.isSuccess()) {
            //直接返回错误信息
            return Response.fail().setMsg(validated.getMsgInfo());
        }
        //获取人员信息（启用的）
        List<MadEmployeeDTO> madEmployeeList = getMadEmployeeList(pcxBasOfficialCard);
        if (CollectionUtil.isEmpty(madEmployeeList)) {
            return Response.success(Collections.emptyList());
        }
        List<PcxBasOfficialCardVO> result = new ArrayList<>();
        //存在启用或者未启用的数据
        List<PcxBasOfficialCardVO> pcxBasOfficialCards = pcxBasOfficialCardDao.selectList(pcxBasOfficialCard);
        // 创建一个 Map，将 pcxBasOfficialCards 按 userCode 索引
        Map<String, PcxBasOfficialCardVO> cardMap = pcxBasOfficialCards.stream()
                .filter(card -> StringUtil.isNotEmpty(card.getEmployeeCode()))  // 过滤掉 employeeCode 为 null 的项
                .collect(Collectors.toMap(PcxBasOfficialCardVO::getEmployeeCode, card -> card));
        if (ObjectUtils.isEmpty(cardMap)){
            return Response.success(Collections.emptyList());
        }
        for (MadEmployeeDTO card : madEmployeeList) {
            String userCode = card.getEmployeeCode();
            // 从公务卡信息中获取用户
            PcxBasOfficialCardVO officialCard = cardMap.get(userCode);
            if (ObjectUtils.isEmpty(officialCard)) {
                officialCard = new PcxBasOfficialCardVO();
                //默认给ID，用户页面触发修改逻辑处理
                officialCard.setId(StringUtil.isEmpty(card.getId()) ? StringUtil.getUUID() : card.getId());
                officialCard.setEmployeeCode(userCode);
            }
            officialCard.setDepartmentCode(card.getDepartmentCode());
            officialCard.setDepartmentName(card.getDepartmentName());
            officialCard.setEmployeeName(card.getEmployeeName());
            officialCard.setMadId(card.getMadId());
            result.add(officialCard);
        }
        // 处理分页
        int pageIndex = pcxBasOfficialCard.getPageIndex();
        int pageSize = pcxBasOfficialCard.getPageSize();
        int fromIndex = (pageIndex - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, result.size());
        List<PcxBasOfficialCardVO> officialCardList = result.subList(fromIndex, toIndex);
        PageResult<PcxBasOfficialCardVO> pageResult = new PageResult<PcxBasOfficialCardVO>().setResult(officialCardList).setTotal((long) result.size());
        return Response.success(pageResult);
    }


    @Override
    public List<PcxBasOfficialCardVO> select(PcxBasOfficialCardQO pcxBasOfficialCard) {
        //业务参数校验
        CheckMsg<?> validated = isValided(pcxBasOfficialCard, false, false);
        if (!validated.isSuccess()) {
            return Collections.emptyList();
        }
        //获取人员信息（启用的）
        List<MadEmployeeDTO> madEmployeeList = getMadEmployeeList(pcxBasOfficialCard);
        //人员表为空则去掉
        if (CollectionUtil.isEmpty(madEmployeeList)) {
            //返回空数据
            return Collections.emptyList();
        }
        List<PcxBasOfficialCardVO> result = new ArrayList<>();
        //存在启用或者未启用的数据
        List<PcxBasOfficialCardVO> pcxBasOfficialCards = pcxBasOfficialCardDao.selectList(pcxBasOfficialCard);
        // 创建一个 Map，将 pcxBasOfficialCards 按 userCode 索引
        Map<String, PcxBasOfficialCardVO> cardMap = pcxBasOfficialCards.stream()
                .collect(Collectors.toMap(PcxBasOfficialCardVO::getEmployeeCode, card -> card));
        // 遍历 madEmployeeList，根据 userCode 匹配 pcxBasOfficialCards，如果不存在，则创建新的 PcxBasOfficialCard 对象
        for (MadEmployeeDTO card : madEmployeeList) {
            String userCode = card.getEmployeeCode();
            // 从公务卡信息中获取用户
            PcxBasOfficialCardVO officialCard = cardMap.get(userCode);
            if (officialCard == null) {
                // 处理为空的情况, 创建新的 PcxBasOfficialCard 对象
                officialCard = new PcxBasOfficialCardVO();
                //默认给ID，用户页面触发修改逻辑处理
                officialCard.setId(StringUtil.isEmpty(card.getId()) ? StringUtil.getUUID() : card.getId());
                officialCard.setEmployeeCode(userCode);
            }
            officialCard.setDepartmentCode(card.getDepartmentCode());
            officialCard.setDepartmentName(card.getDepartmentName());
            officialCard.setEmployeeName(card.getEmployeeName());
            officialCard.setMadId(card.getMadId());
            result.add(officialCard);
        }
        return result;
    }

    @Override
    public CheckMsg<?> save(PcxBasOfficialCardQO pcxBasOfficialCard) {
        CheckMsg<?> checkMsg = isValided(pcxBasOfficialCard, false, true);
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        pcxBasOfficialCardDao.insertSelective(buildOfficialCardInfo(pcxBasOfficialCard));
        return CheckMsg.success("保存成功");
    }

    @Override
    public CheckMsg<?> updateOfficial(PcxBasOfficialCardQO basOfficialCardQo) {
        try {
            CheckMsg<?> validated = isValided(basOfficialCardQo, false, false);
            if (!validated.isSuccess()) {
                return validated;
            }
            if (StringUtil.isEmpty(basOfficialCardQo.getId())) {
                return CheckMsg.fail().setMsgInfo("参数校验失败,请提供有效的ID,唯一ID不能为空");
            }
            // 根据 ID 查询现有的公务卡信息
            PcxBasOfficialCard officialCard = pcxBasOfficialCardDao.selectById(basOfficialCardQo.getId());
            // 修改、新增校验开户行信息
            if (StringUtil.isNotEmpty(basOfficialCardQo.getBankNodeName())) {
                //获取银行基础信息
                PcxMadBaseQO PcxMadBaseQO = new PcxMadBaseQO();
                PcxMadBaseQO.setAgyCode(basOfficialCardQo.getAgyCode());
                PcxMadBaseQO.setFiscal(basOfficialCardQo.getFiscal());
                PcxMadBaseQO.setMofDivCode(basOfficialCardQo.getMofDivCode());
                PcxMadBaseQO.setBankNodeName(basOfficialCardQo.getBankNodeName());
                List<MadBankNodeDTO> select = madBankNodeService.select(PcxMadBaseQO);
                Map<String, String> bankNodeMap = select.stream()
                        .collect(Collectors.toMap(MadBankNodeDTO::getBankNodeName, MadBankNodeDTO::getBankNodeCode));
                if (!bankNodeMap.containsKey(basOfficialCardQo.getBankNodeName())) {
                    return CheckMsg.fail("开户银行网点在系统中不存在");
                }
            }
            // 如果找到了该公务卡信息，进行修改操作
            if (!ObjectUtils.isEmpty(officialCard)) {
                // 设置修改人相关信息
                officialCard.setModifier(basOfficialCardQo.getUserCode());
                officialCard.setModifierName(basOfficialCardQo.getUserName());
                // 设置修改时间
                officialCard.setModifiedTime(DateUtil.nowTime());
                // 更新银行卡相关信息（账户号、银行网点等）
                officialCard.setAccountNo(basOfficialCardQo.getAccountNo());
                officialCard.setBankNode(basOfficialCardQo.getBankNode());
                officialCard.setBankNodeName(basOfficialCardQo.getBankNodeName());
                // 执行数据库更新操作
                pcxBasOfficialCardDao.updateById(officialCard);
            } else {
                // 如果没有找到对应的公务卡记录，则尝试保存新卡
                CheckMsg<?> save = save(basOfficialCardQo);
                // 如果保存失败，则返回失败的提示信息
                if (!save.isSuccess()) {
                    return save;
                }
            }
            // 如果操作成功，返回成功消息
            return CheckMsg.success();
        } catch (Exception e) {
            // 捕获异常并记录错误日志，返回保存失败的提示
            log.error("公务卡保存失败:{}", e.getMessage(), e);
            return CheckMsg.fail("保存失败");
        }
    }

    @Override
    public CheckMsg<?> batchSave(List<PcxBasOfficialCard> pcxBasOfficialCardList) {
        if (CollectionUtil.isEmpty(pcxBasOfficialCardList)) {
            return CheckMsg.fail("保存的数据为空，无法进行操作，请检查 [pcxBasOfficialCardList] 参数是否正确。");
        }
        batchServiceUtil.batchProcess(pcxBasOfficialCardList, PcxBasOfficialCardDao.class, PcxBasOfficialCardDao::insertSelective);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    public int update(PcxBasOfficialCardQO pcxBasOfficialCardQo) {
        if (null == pcxBasOfficialCardQo || StringUtil.isEmpty(pcxBasOfficialCardQo.getId())) {
            return 0;
        }
        return pcxBasOfficialCardDao.updateById(pcxBasOfficialCardQo);
    }

    @Override
    public void batchUpdate(List<PcxBasOfficialCard> basOfficialCard) {
        //数据判断非空处理
        if (CollectionUtil.isNotEmpty(basOfficialCard)) {
            batchServiceUtil.batchProcess(basOfficialCard, PcxBasOfficialCardDao.class, PcxBasOfficialCardDao::updateById);
        }
    }

    @Override
    public PcxExportQo downloadOfficialCardImportTpl() {
        PcxExportQo pexExportQo = new PcxExportQo();
        String fileName = StringUtil.urlEncode("公务卡信息导入模板.xlsx");
        pexExportQo.setFileName(fileName);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("公务卡信息页");
            CellStyle style = ExcelUtil.getHeadStyle(workbook);
            //设置表头以及样式信息
            Row row = sheet.createRow(0);
            for (int i = 0; i < BAS_CARD_HEADERS.length; i++) {
                Cell headerCell = row.createCell(i);
                headerCell.setCellValue(BAS_CARD_HEADERS[i]);
                headerCell.setCellStyle(style);
                sheet.setColumnWidth(i, 30 * 256);
            }
            workbook.write(bos);
            pexExportQo.setBytes(bos.toByteArray());
        } catch (IOException e) {
            log.error("生成公务卡信息导入模板异常:{}", e.getMessage(), e);
            pexExportQo.setBytes(new byte[0]);
            return pexExportQo;
        } finally {
            IoUtil.close(bos);
        }
        return pexExportQo;
    }

    @Override
    public CheckMsg<?> importOfficialCard(MultipartFile file, PcxBasOfficialCardQO basOfficialCardQo) {
        if (Objects.isNull(file) || file.getSize() <= 0) {
            return CheckMsg.fail("导入文件不能为空");
        }
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if (!Objects.equals(suffix, ".xlsx")) {
            return CheckMsg.fail("导入文件格式不是xlsx,请检查！");
        }
        File peerFile = FileUtil.getFileByMutipartFile(file);
        if (Objects.isNull(peerFile) || !peerFile.exists()) {
            return CheckMsg.fail("导入文件损坏,请检查！");
        }
        CheckMsg<?> validated = isValided(basOfficialCardQo, false, false);
        if (!validated.isSuccess()) {
            //直接返回错误信息
            return validated;
        }
        StringBuffer resultMsg = new StringBuffer();
        List<PcxBasOfficialCard> saveList = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(peerFile);
             Workbook workbook = new XSSFWorkbook(fis)) {
            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 校验标题行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return CheckMsg.fail("导入文件标题行为空,请检查！");
            }
            for (int i = 0; i < BAS_CARD_HEADERS.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !BAS_CARD_HEADERS[i].equals(cell.getStringCellValue())) {
                    return CheckMsg.fail("标题行第" + i + "列标题错误,应为" + BAS_CARD_HEADERS[i] + ",请检查！");
                }
            }
            //获取人员信息（启用的）
            List<MadEmployeeDTO> madEmployeeList = getMadEmployeeList(basOfficialCardQo);
            Map<String, MadEmployeeDTO> madEmployeeDTOMap = madEmployeeList.stream()
                    .collect(Collectors.toMap(MadEmployeeDTO::getEmployeeCode, card -> card));
            int lastRowNum = sheet.getLastRowNum();
            //数据校验状态
            boolean valid = true;
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                //构建数据对象
                PcxBasOfficialCard card = new PcxBasOfficialCard();
                for (int j = 0; j < BAS_CARD_HEADERS.length; j++) {
                    Cell cell = row.getCell(j);//cell相当于每个单元格的值。
                    if (null == cell) {
                        continue;
                    }
                    cell.setCellType(CellType.STRING);
                    String cellValue = cell.getStringCellValue().trim();
                    valid = handleCellData(card, cellValue, BAS_CARD_HEADERS[j], i + 1, resultMsg, madEmployeeDTOMap);
                    if (!valid) {
                        //日志已在handleCellData中处理添加
                        break;
                    }
                }
                if (!valid) {
                    continue;
                }
                // 设置基础信息
                setCardBaseInfo(card, basOfficialCardQo);
                saveList.add(card);
            }
            //保存导入信息
            if (CollectionUtil.isNotEmpty(saveList)) {
                //获取银行基础信息
                //从saveList中获取银行名称、
                List<String> bankNodeNameList = saveList.stream().map(PcxBasOfficialCard::getBankNodeName).distinct().collect(Collectors.toList());
                PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
                pcxMadBaseQO.setAgyCode(basOfficialCardQo.getAgyCode());
                pcxMadBaseQO.setFiscal(basOfficialCardQo.getFiscal());
                pcxMadBaseQO.setMofDivCode(basOfficialCardQo.getMofDivCode());
                pcxMadBaseQO.setBankNodeNameList(bankNodeNameList);
                List<MadBankNodeDTO> select = madBankNodeService.select(pcxMadBaseQO);
                Map<String, List<MadBankNodeDTO>> bankNodeMap = select.stream().filter(e -> StringUtil.isNotEmpty(e.getBankNodeName()))
                        .collect(Collectors.groupingBy(MadBankNodeDTO::getBankNodeName));
                //校验银行信息是否存在.只保留存在的数据，不存在的记录到日志中
                saveList = saveList.stream()
                        .filter(card -> {
                            // 如果银行网点名称不存在于系统中，则记录错误信息
                            if (!bankNodeMap.containsKey(card.getBankNodeName())) {
                                resultMsg.append(String.format("行号 %d, 开户银行网点 %s 不在系统中存在\n", card.getRowIndex(), card.getBankNodeName()));
                                return false; // 过滤掉这个卡片
                            }

                            // 设置银行网点编号
                            List<MadBankNodeDTO> bankNodes = bankNodeMap.get(card.getBankNodeName());
                            if (bankNodes != null && !bankNodes.isEmpty()) {
                                card.setBankNode(bankNodes.get(0).getBankNodeNo()); // 设置第一个网点编号
                            }

                            return true; // 如果银行网点存在，保留该卡片
                        })
                        .collect(Collectors.toList());
                //人员编码是否存在重复
                if (CollectionUtil.isNotEmpty(saveList)) {
                    List<String> employeeCodes = saveList.stream().map(PcxBasOfficialCard::getEmployeeCode).collect(Collectors.toList());
                    PcxBasOfficialCardQO delParams = new PcxBasOfficialCardQO();
                    delParams.setAgyCode(basOfficialCardQo.getAgyCode());
                    delParams.setFiscal(basOfficialCardQo.getFiscal());
                    delParams.setMofDivCode(basOfficialCardQo.getMofDivCode());
                    delParams.setEmployeeCodeList(employeeCodes);
                    removeAndSaveCard(delParams, saveList);
                }
            }
            // 如果有不规范信息，先输出这些信息
            if (resultMsg.length() > 0) {
                if (saveList.isEmpty()) {
                    resultMsg.insert(0, "本次未导入公务卡信息，以下信息填写不规范：\n");
                } else {
                    resultMsg.insert(0, "本次成功导入 " + saveList.size() + " 条公务卡信息，以下信息填写不规范：\n");
                }

            } else {
                if (saveList.isEmpty()) {
                    resultMsg.append("导入模板未填写公务卡信息");
                } else {
                    resultMsg.append("本次成功导入 ").append(saveList.size()).append(" 条公务卡信息");
                }
            }
        } catch (Exception e) {
            log.error("公务卡信息导入模板解析失败:{}", e.getMessage(), e);
            return CheckMsg.fail("公务卡信息导入解析失败");
        }
        return CheckMsg.success().setMsgInfo(resultMsg.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAndSaveCard(PcxBasOfficialCardQO pcxBasOfficialCardQO, List<PcxBasOfficialCard> pcxBasOfficialCards) {
        pcxBasOfficialCardDao.delByEmployeeCodeList(pcxBasOfficialCardQO.getAgyCode(), pcxBasOfficialCardQO.getFiscal(), pcxBasOfficialCardQO.getMofDivCode(), pcxBasOfficialCardQO.getEmployeeCodeList());
        batchSave(pcxBasOfficialCards);
    }

    @Override
    public CheckMsg<?> getCardInfo(PcxBasOfficialCardQO basOfficialCardQo) {
        Map<String, Object> result = new HashMap<>();
        CheckMsg<List<PcxBasOfficialCardVO>> checkMsg = handleBasOfficialCardValidation(basOfficialCardQo);
        if (checkMsg.isSuccess()) {
            // 将银行卡账户和银行名称放入结果 Map 中
            List<PcxBasOfficialCardVO> pcxBasOfficialCardVOS = checkMsg.getData();
            if (CollectionUtil.isEmpty(pcxBasOfficialCardVOS)) {
                return CheckMsg.success(result);
            }
            result.put("bankAccountNo", pcxBasOfficialCardVOS.get(0).getAccountNo());
            result.put("bankName", pcxBasOfficialCardVOS.get(0).getBankNodeName());
            return CheckMsg.success(result);
        }
        return checkMsg;
    }

    @Override
    public CheckMsg<?> addCardInfo(PcxBasOfficialCardQO basOfficialCardQo) {
        // 校验银行名称和网点不能为空
        // StringUtil.isEmpty(basOfficialCardQo.getBankNode() 这个参数待和产品沟通
        if (StringUtil.isEmpty(basOfficialCardQo.getBankNodeName())) {
            return CheckMsg.fail("所属银行不允许为空");
        }
        if (StringUtil.isEmpty(basOfficialCardQo.getAccountNo())) {
            return CheckMsg.fail("卡号不允许为空");
        }
        // 执行银行卡信息验证
        CheckMsg<List<PcxBasOfficialCardVO>> checkMsg = handleBasOfficialCardValidation(basOfficialCardQo);

        // 如果验证失败，直接返回
        if (!checkMsg.isSuccess()) {
            return checkMsg;
        }
        // 获取验证结果数据
        List<PcxBasOfficialCardVO> pcxBasOfficialCardVOS = checkMsg.getData();
        // 如果没有找到已有的公务卡信息，尝试保存新卡
        if (CollectionUtil.isEmpty(pcxBasOfficialCardVOS)) {
            //save 方法内部有参数校验 同 handleBasOfficialCardValidation里面相同，所以不关注返回状态，异常除外
            save(basOfficialCardQo);
            return CheckMsg.success(basOfficialCardQo);
        }

        // 如果找到了已有的公务卡信息，则执行更新操作
        PcxBasOfficialCardVO existingCard = pcxBasOfficialCardVOS.get(0);
        PcxBasOfficialCard officialCard = new PcxBasOfficialCard();
        officialCard.setId(existingCard.getId());
        officialCard.setModifier(basOfficialCardQo.getUserCode());
        officialCard.setModifierName(basOfficialCardQo.getUserName());
        officialCard.setModifiedTime(DateUtil.nowTime());
        officialCard.setAccountNo(basOfficialCardQo.getAccountNo());
        officialCard.setBankNode(basOfficialCardQo.getBankNode());
        officialCard.setBankNodeName(basOfficialCardQo.getBankNodeName());

        try {
            // 执行数据库更新操作
            pcxBasOfficialCardDao.updateById(officialCard);
        } catch (Exception e) {
            return CheckMsg.fail("更新银行卡信息失败：" + e.getMessage());
        }

        return CheckMsg.success(basOfficialCardQo);  // 更新成功返回
    }

    @Override
    public CheckMsg<?> checkIdCard(PcxBasOfficialCardQO basOfficialCardQo) {
        // 调用 isValided 方法对输入参数 basOfficialCardQo 进行验证
        CheckMsg<?> validated = isValided(basOfficialCardQo, false, false);
        // 如果验证失败，返回验证错误信息
        if (!validated.isSuccess()) {
            return validated;
        }
        if (StringUtil.isEmpty(basOfficialCardQo.getCardId())) {
            return CheckMsg.fail("请输入身份证号后六位");
        }
        // 创建 PcxMadBaseQO 对象，封装查询条件
        PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
        // 获取租户 ID，如果 basOfficialCardQo 中没有提供租户 ID，则使用默认值
        String tenantId = StringUtil.isEmpty(basOfficialCardQo.getTenantId()) ? PtyContext.getTenantId() : basOfficialCardQo.getTenantId();
        // 设置查询条件
        /*pcxMadBaseQO.setFiscal(basOfficialCardQo.getFiscal());
        pcxMadBaseQO.setMofDivCode(basOfficialCardQo.getMofDivCode());
        pcxMadBaseQO.setAgyCode(basOfficialCardQo.getAgyCode());
        pcxMadBaseQO.setTenantId(tenantId);  // 设置租户 ID
        pcxMadBaseQO.setUserCode(basOfficialCardQo.getUserCode());  // 设置用户代码
        // 查询与给定条件匹配的员工信息
        List<MadEmployeeDTO> madEmployeeList = pcxMadEmployeeService.select(pcxMadBaseQO);*/
        PtyUser ptyUser = userService.selectByUserCode(basOfficialCardQo.getUserCode());
        // 如果没有找到有效的员工信息，返回失败信息
        if (null == ptyUser) {
            return CheckMsg.fail("用户未启用或不存在");
        }
        if (StringUtils.isBlank(ptyUser.getIdentityCode())) {
            return CheckMsg.fail("用户未维护身份证号信息");
        }
        String lastNum = ptyUser.getIdentityCode().substring(ptyUser.getIdentityCode().length() - 6);
        if (!lastNum.equals(basOfficialCardQo.getCardId())) {
            return CheckMsg.fail().setMsgInfo("身份信息不符");
        }
        return CheckMsg.success().setMsgInfo("校验成功");
    }

    @Override
    public CheckMsg<?> unboundOfficialCard(PcxBasOfficialCardQO basOfficialCardQo) {
        // 校验参数以及获取需要删除的公务卡数据
        CheckMsg<List<PcxBasOfficialCardVO>> checkMsg = handleBasOfficialCardValidation(basOfficialCardQo);
        if (checkMsg.isSuccess()) {
            List<PcxBasOfficialCardVO> pcxBasOfficialCardVOS = checkMsg.getData();
            if (CollectionUtil.isNotEmpty(pcxBasOfficialCardVOS)) {
                pcxBasOfficialCardDao.delById(pcxBasOfficialCardVOS.get(0).getId());
            }
            return CheckMsg.success().setMsgInfo("解绑成功");
        }
        return checkMsg;
    }


    /**
     * 处理基础公务卡验证逻辑并获取公务卡信息
     *
     * @param basOfficialCardQO
     * @return
     */
    private CheckMsg<List<PcxBasOfficialCardVO>> handleBasOfficialCardValidation(PcxBasOfficialCardQO basOfficialCardQO) {
        // 调用 isValided 方法对输入参数 basOfficialCardQo 进行验证
        CheckMsg<?>  validated = isValided(basOfficialCardQO, false, false);
        // 如果验证失败，返回验证错误信息
        if (!validated.isSuccess()) {
            return CheckMsg.fail(validated.getMsgInfo());
        }
        //单独拿出来校验用户编码
        if (StringUtil.isEmpty(basOfficialCardQO.getUserCode())) {
            return CheckMsg.fail("参数校验失败,用户编码不能为空");
        }

        // 创建 PcxMadBaseQO 对象，封装查询条件
        PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
        // 获取租户 ID，如果 basOfficialCardQo 中没有提供租户 ID，则使用默认值
        String tenantId = StringUtil.isEmpty(basOfficialCardQO.getTenantId()) ? PtyContext.getTenantId() : basOfficialCardQO.getTenantId();
        // 设置查询条件
        pcxMadBaseQO.setFiscal(basOfficialCardQO.getFiscal());
        pcxMadBaseQO.setMofDivCode(basOfficialCardQO.getMofDivCode());
        pcxMadBaseQO.setAgyCode(basOfficialCardQO.getAgyCode());
        pcxMadBaseQO.setTenantId(tenantId);  // 设置租户 ID
        pcxMadBaseQO.setUserCode(basOfficialCardQO.getUserCode());  // 设置用户代码
        // 查询与给定条件匹配的员工信息
        List<MadEmployeeDTO> madEmployeeList = pcxMadEmployeeService.select(pcxMadBaseQO);

        // 如果没有找到有效的员工信息，返回失败信息
        if (CollectionUtil.isEmpty(madEmployeeList)) {
            return CheckMsg.fail("人员信息未启用或不存在");
        }
        // 获取第一个员工的员工代码
        basOfficialCardQO.setEmployeeCode(madEmployeeList.get(0).getEmployeeCode());
        basOfficialCardQO.setTenantId(tenantId);
        // 使用员工代码和其他查询条件查询银行卡信息
        List<PcxBasOfficialCardVO> pcxBasOfficialCardVOS = pcxBasOfficialCardDao.selectList(basOfficialCardQO);
        return CheckMsg.success(pcxBasOfficialCardVOS);
    }

    private List<MadEmployeeDTO> getMadEmployeeList(PcxBasOfficialCardQO basOfficialCardQo) {
        PcxMadBaseQO pcxMadBaseQO = new PcxMadBaseQO();
        pcxMadBaseQO.setFiscal(basOfficialCardQo.getFiscal());
        pcxMadBaseQO.setMofDivCode(basOfficialCardQo.getMofDivCode());
        pcxMadBaseQO.setAgyCode(basOfficialCardQo.getAgyCode());
        pcxMadBaseQO.setTenantId(StringUtil.isEmpty(basOfficialCardQo.getTenantId()) ? PtyContext.getTenantId() : basOfficialCardQo.getTenantId());
        if (StringUtil.isNotEmpty(basOfficialCardQo.getEmployeeCode())) {
            pcxMadBaseQO.setEmployeeCode(basOfficialCardQo.getEmployeeCode());
        }
        if (StringUtil.isNotEmpty(basOfficialCardQo.getEmployeeName())) {
            pcxMadBaseQO.setEmployeeName(basOfficialCardQo.getEmployeeName());
        }
        if (StringUtil.isNotEmpty(basOfficialCardQo.getDepartmentCode())) {
            pcxMadBaseQO.setDepartmentCode(basOfficialCardQo.getDepartmentCode());
        }
        //得到启用的该单位、年度、区划人员信息数据
        return pcxMadEmployeeService.select(pcxMadBaseQO);
    }

    // 设置卡片基本信息的方法
    private void setCardBaseInfo(PcxBasOfficialCard card, PcxBasOfficialCardQO basOfficialCardQo) {
        card.setId(StringUtil.getUUID());
        card.setAgyCode(basOfficialCardQo.getAgyCode());
        card.setFiscal(basOfficialCardQo.getFiscal());
        card.setMofDivCode(basOfficialCardQo.getMofDivCode());
        //(来源：内部导入)
        card.setInfoSource(PcxConstant.INTERNAL);
        card.setCreatorCode(basOfficialCardQo.getUserCode());
        card.setCreatorName(basOfficialCardQo.getUserName());
        card.setCreatedTime(DateUtil.nowTime());
        card.setModifier(basOfficialCardQo.getUserCode());
        card.setModifierName(basOfficialCardQo.getUserName());
        card.setModifiedTime(card.getCreatedTime());
        card.setTenantId(StringUtil.isEmpty(basOfficialCardQo.getTenantId()) ? PtyContext.getTenantId() : basOfficialCardQo.getTenantId());
    }

    // 设置公务卡保存基本信息
    private PcxBasOfficialCard buildOfficialCardInfo(PcxBasOfficialCardQO basOfficialCardQo) {
        PcxBasOfficialCard card = new PcxBasOfficialCard();
        card.setId(StringUtil.isEmpty(basOfficialCardQo.getId()) ? StringUtil.getUUID() : basOfficialCardQo.getId());
        card.setAgyCode(basOfficialCardQo.getAgyCode());
        card.setFiscal(basOfficialCardQo.getFiscal());
        card.setMofDivCode(basOfficialCardQo.getMofDivCode());
        card.setBankNode(basOfficialCardQo.getBankNode());
        card.setEmployeeCode(basOfficialCardQo.getEmployeeCode());
        //(来源：内部导入)
        card.setAccountNo(basOfficialCardQo.getAccountNo());
        card.setBankNodeName(basOfficialCardQo.getBankNodeName());
        card.setBankNode(basOfficialCardQo.getBankNode());
        card.setInfoSource(PcxConstant.INTERNAL);
        card.setCreatorCode(basOfficialCardQo.getUserCode());
        card.setCreatorName(basOfficialCardQo.getUserName());
        card.setCreatedTime(DateUtil.nowTime());
        card.setModifier(basOfficialCardQo.getUserCode());
        card.setModifierName(basOfficialCardQo.getUserName());
        card.setModifiedTime(card.getCreatedTime());
        card.setTenantId(StringUtil.isEmpty(basOfficialCardQo.getTenantId()) ? PtyContext.getTenantId() : basOfficialCardQo.getTenantId());
        return card;
    }

    /**
     * 处理每个单元格的数据 (人员名称暂不校验)
     */
    private boolean handleCellData(PcxBasOfficialCard card, String cellValue, String header, int rowIndex, StringBuffer errorMsg, Map<String, MadEmployeeDTO> madEmployeeDTOMap) {
        switch (header) {
            case "人员编码":
                if (StringUtil.isEmpty(cellValue)) {
                    errorMsg.append(String.format("行号 %d, 人员编码为空\n", rowIndex));
                    return false;
                }
                if (!madEmployeeDTOMap.containsKey(cellValue)) {
                    errorMsg.append(String.format("行号 %d, 人员编码 %s 在当前单位中不存在\n", rowIndex, cellValue));
                    return false;
                }
                card.setEmployeeCode(cellValue);
                break;
            case "开户银行":
                if (StringUtil.isEmpty(cellValue)) {
                    errorMsg.append(String.format("行号 %d, 开户银行为空\n", rowIndex));
                    return false;
                }
                card.setBankNodeName(cellValue);
                break;

            case "银行卡号":
                if (StringUtil.isEmpty(cellValue)) {
                    errorMsg.append(String.format("行号 %d, 银行卡号为空\n", rowIndex));
                    return false;
                }
                card.setAccountNo(cellValue);
                break;

            default:
                // 如果是其他字段，暂时不处理
                break;
        }
        card.setRowIndex(rowIndex);
        return true;
    }

    /**
     * 校验查询参数必填项
     *
     * @param qo     查询对象
     * @param isPage 是否校验分页参数
     * @return 校验结果
     */
    private CheckMsg<?> isValided(PcxBasOfficialCardQO qo, boolean isPage, boolean isUserInfo) {
        // 校验必填字段
        Map<String, String> requiredFields = new HashMap<>();
        requiredFields.put("参数校验失败,单位编码", qo.getAgyCode());
        requiredFields.put("参数校验失败,区划编码", qo.getMofDivCode());
        requiredFields.put("参数校验失败,年度", qo.getFiscal());
//      requiredFields.put("参数校验失败,租户ID", qo.getTenantId());
        //租户处理默认值
        qo.setTenantId(StringUtil.isEmpty(qo.getTenantId()) ? PtyContext.getTenantId() : qo.getTenantId());
        // 遍历检查必填字段
        for (Map.Entry<String, String> entry : requiredFields.entrySet()) {
            if (StringUtil.isEmpty(entry.getValue())) {
                return CheckMsg.fail(entry.getKey() + "不能为空");
            }
        }
        // 校验分页参数
        if (isPage) {
            if (StringUtil.isEmpty(qo.getPageIndex()) || StringUtil.isEmpty(qo.getPageSize())) {
                return CheckMsg.fail("参数校验失败,分页参数不能为空");
            }
        }
        // 校验用户信息
        if (isUserInfo) {
            if (StringUtil.isEmpty(qo.getUserCode()) || StringUtil.isEmpty(qo.getUserName())) {
                return CheckMsg.fail("参数校验失败,操作人信息不能为空");
            }
        }
        return CheckMsg.success();
    }

}
