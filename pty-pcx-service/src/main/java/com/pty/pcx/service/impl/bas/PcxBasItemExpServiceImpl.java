package com.pty.pcx.service.impl.bas;

import com.pty.pcx.api.bas.IPcxBasItemExpService;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bas.PcxBasItemExpDao;
import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pcx.util.BatchServiceUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Indexed
@Service
public class PcxBasItemExpServiceImpl implements IPcxBasItemExpService {

    @Autowired
    private PcxBasItemExpDao pcxBasItemExpDao;

    @Autowired
    private BatchServiceUtil batchServiceUtil;

    @Override
    public List<PcxBasItemExp> selectByItemCode(PcxBasItemExpQO pcxBasItemExpQO) {
        CheckMsg<?> checkMsg = isValided(pcxBasItemExpQO);
        if(!checkMsg.isSuccess()){
            return new ArrayList<>();
        }
        if(StringUtil.isEmpty(pcxBasItemExpQO.getItemCode())){
            return new ArrayList<>();
        }
        return pcxBasItemExpDao.selectByQO(pcxBasItemExpQO);
    }

    @Override
    public CheckMsg<?> save(PcxBasItemExpQO pcxBasItemExpQO) {
        CheckMsg<?> checkMsg = checkParams(pcxBasItemExpQO);
        if(!checkMsg.isSuccess()){
            return checkMsg;
        }
        PcxBasItemExp pcxBasItemExp = new PcxBasItemExp();
        BeanUtils.copyProperties(pcxBasItemExpQO, pcxBasItemExp);
        pcxBasItemExpDao.insert(pcxBasItemExp);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    @Override
    public CheckMsg<?> batchSave(List<PcxBasItemExpQO> pcxBasItemExpQOList) {
        if(StringUtil.isEmpty(pcxBasItemExpQOList)){
            return CheckMsg.fail("参数不能为空");
        }
        batchServiceUtil.batchProcess(pcxBasItemExpQOList,PcxBasItemExpDao.class, PcxBasItemExpDao::insert);
        return CheckMsg.success().setMsgInfo("保存成功");
    }

    /****
     * 根据事项类型删除相关联的费用类型关系
     * @param pcxBasItemExpQO
     * @return
     */
    @Override
    public CheckMsg<?> deleteByItemCode(PcxBasItemExpQO pcxBasItemExpQO){
        CheckMsg<?> checkMsg = isValided(pcxBasItemExpQO);
        if(!checkMsg.isSuccess()){
            return checkMsg;
        }
        if(StringUtil.isEmpty(pcxBasItemExpQO.getItemCode())){
            return CheckMsg.fail().setMsgInfo("事项类型代码不能为空");
        }
        pcxBasItemExpDao.deleteByItemCode(pcxBasItemExpQO);
        return CheckMsg.success().setMsgInfo("删除成功");
    }

    @Override
    public List<PcxBasItemExp> selectByQO(PcxBasItemExpQO pcxBasItemExpQO) {
        if(ObjectUtils.isEmpty(pcxBasItemExpQO)){
            return new ArrayList<>();
        }
        return pcxBasItemExpDao.selectByQO(pcxBasItemExpQO);
    }

    @Override
    public int updateExpenseTypeIsLeaf() {
        int num = 0;
        List<String> ids = pcxBasItemExpDao.selectValidIsLeafTypeIds();
        while (CollectionUtils.isNotEmpty(ids)){
            num += ids.size();
            pcxBasItemExpDao.updateExpenseTypeIsLeaf(ids);
            ids = pcxBasItemExpDao.selectValidIsLeafTypeIds();
        }
        return num;
    }

    private CheckMsg<?> isValided(PcxBasItemExpQO qo) {
        if(StringUtil.isEmpty(qo.getAgyCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("单位编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getMofDivCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("区划编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getFiscal())){
            return com.pty.pcx.common.util.CheckMsg.fail("年度不能为空");
        }
        return CheckMsg.success();
    }

    private CheckMsg<?> checkParams(PcxBasItemExpQO qo){
        if(StringUtil.isEmpty(qo.getAgyCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("单位编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getMofDivCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("区划编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getFiscal())){
            return com.pty.pcx.common.util.CheckMsg.fail("年度不能为空");
        }
        if(StringUtil.isEmpty(qo.getItemCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("事项类型编码不能为空");
        }
        if(StringUtil.isEmpty(qo.getExpenseCode())){
            return com.pty.pcx.common.util.CheckMsg.fail("费用类型编码不能为空");
        }
        return CheckMsg.success();
    }

}
