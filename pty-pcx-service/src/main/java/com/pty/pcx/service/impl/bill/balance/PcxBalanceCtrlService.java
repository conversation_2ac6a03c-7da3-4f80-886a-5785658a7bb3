package com.pty.pcx.service.impl.bill.balance;

import com.pty.balance.vo.CtrlDTO;
import com.pty.pcx.entity.bill.PcxBill;

/**
 * 指标余额控制服务
 * <AUTHOR>
 * @since 2024/12/09
 */
public interface PcxBalanceCtrlService {


    CtrlDTO convertToCtrlDTO(PcxBill contentBill, boolean... finalSubmit);
    /**
     * 指标预占接口
     * @param pcxBill
     * @param isValidate
     */
    void saveCtrl(PcxBill pcxBill, Boolean isValidate);

    /**
     * 指标预占接口
     * @param pcxBill
     * @param isValidate
     * @param isFinalSubmit -- 是否办结
     */
    void saveCtrl(PcxBill pcxBill, Boolean isValidate,boolean isFinalSubmit);

    /**
     * 删除指标
     * @param pcxBill
     */
    void deleteCtrl(PcxBill pcxBill);

    /**
     * 终审之后调用
     * @param pcxBill
     */
    void auditCtrl(PcxBill pcxBill);

    /**
     * 终审撤回之后调用
     * @param pcxBill
     */
    void reStartCtrl(PcxBill pcxBill);

    /**
     * 更新预算执行金额
     * @param pcxBill
     * @param flag 是否是终审撤回
     */
    void updateBalanceAmt(PcxBill pcxBill, boolean flag);

    /**
     * 保存并终审
     * @param pcxBill
     * @param isValidate
     * @param isFinalSubmit
     */
    void saveAuditCtrl(PcxBill pcxBill, Boolean isValidate,boolean isFinalSubmit);
}
