package com.pty.pcx.service.impl.ecs;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.ecs.common.EcsServiceMsg;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.common.enu.PcxExpAttachRelType;
import com.pty.pcx.dao.bill.*;
import com.pty.pcx.dao.contract.PcxBillContractRelDao;
import com.pty.pcx.ecs.IEcsBillExternalService;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.contract.PcxBillContractRel;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.qo.bill.extrasubsidy.UpdateExtraSubsidyQO;
import com.pty.pcx.qo.ecs.BuildExpRelQO;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.qo.ecs.UpdateAdditionQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.service.impl.bill.handler.impl.DefaultBillExpenseDetailService;
import com.pty.pcx.service.impl.ecs.dto.SaveEcsCommonDTO;
import com.pty.pcx.service.impl.ecs.dto.SaveEcsExpenseDTO;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pcx.vo.bill.PcxBillRelationVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pty.pcx.service.impl.ecs.EcsExpOptService.isTransExpense;

/**
 * 理票事务操作
 */
@Indexed
@Service
@Slf4j
public class EcsExpTransService {

    @Autowired
    private BillMainService billMainService;
    @Autowired
    private PcxBillTravelTripDao pcxBillTravelTripDao;
    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;
    @Autowired
    private PcxBillExpDetailTravelDao pcxBillExpDetailTravelDao;
    @Autowired
    private PcxBillExpStandResultDao pcxBillExpStandResultDao;
    @Autowired
    private IEcsBillExternalService ecsBillExternalService;
    @Autowired
    private PcxEcsSettlDao pcxEcsSettlDao;
    @Resource
    private BillExpenseCommonService billExpenseCommonService;

    @Autowired
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;
    @Autowired
    private PcxBillTravelFellowsDao pcxBillTravelFellowsDao;
    @Autowired
    private PcxBillContractRelDao pcxBillContractRelDao;
    @Resource
    private PcxBillOuterEmpDao pcxBillOuterEmpDao;
    @Autowired
    private PcxBillRelationDao pcxBillRelationDao;
    @Resource
    private PcxBillTripCityDayDao pcxBillTripCityDayDao;
    @Resource
    private PcxBillExpDetailCommonDao pcxBillExpDetailCommonDao;
    @Resource
    private DefaultBillExpenseDetailService defaultBillExpenseDetailService;
    @Resource
    private PcxBillExpCommonDao pcxBillExpCommonDao;
    @Resource
    private PcxBillTripSegmentDao pcxBillTripSegmentDao;
    @Resource
    private PcxBillAmtApportionDao pcxBillAmtApportionDao;
    @Resource
    private PcxBillAmtApportionDepartmentDao pcxBillAmtApportionDepartmentDao;
    @Resource
    private PcxBillExtraSubsidyDao pcxBillExtraSubsidyDao;
    @Resource
    private PcxBillExtraAttachDao pcxBillExtraAttachDao;
    @Autowired
    private IWitAuditRuleService witAuditRuleService;

    private final static String BILL_FLAG = "0";
    private final static String ATTACH_FLAG = "1";

    @Transactional(rollbackFor = Exception.class)
    public String createExpenseBill(SaveEcsExpenseDTO dto) {

        //保存报销单
        PcxBill pcxBill = billMainService.saveOrUpdate(dto.getBill());
        //把匹配的票改为报销状态
        buildEcsRel(pcxBill, dto.getAllEcsList(), true, dto.getAllAttachRelList(), null, null, null);

        String billId = pcxBill.getId();

        commonOpt(dto);

        return billId;
    }

    private void buildEcsRel(PcxBill bill,
                             List<PcxExpDetailEcsRel> allEcsRel,
                             boolean createBill,
                             List<PcxBillExpAttachRel> allAttachRelList,
                             PcxBillContractRel contractRel,
                             List<PcxBillExtraAttach> allExtraAttachList,
                             List<PcxBillExtraAttach> delExtraAttachList) {
        List<PcxBillAttachRelationVO> attachList = billExpenseCommonService.getAttachList(bill);
        PcxBillRelationVO apply = billExpenseCommonService.getApplyRelation(bill);
        if (apply != null && apply.getIsVirtual() == 1) {
            PcxBillAttachRelationVO applyAttach = new PcxBillAttachRelationVO();
            applyAttach.setAttachId(apply.getRelBillId());
            attachList.add(applyAttach);
        }
        if (CollectionUtils.isNotEmpty(attachList)){
            List<String> attachIds = pcxExpDetailEcsRelDao.selectExistsAttachIdList(attachList.stream().map(PcxBillAttachRelationVO::getAttachId).distinct().collect(Collectors.toList()));
            attachList = attachList.stream().filter(item-> attachIds.contains(item.getAttachId())).collect(Collectors.toList());
        }
        //如果附件清单不为空，则查一下电子凭证中附件是否都是自己的
        WitRuleResult witQuery = new WitRuleResult();
        witQuery.setBillId(bill.getId());
        witQuery.setFiscal(bill.getFiscal());
        witQuery.setAgyCode(bill.getAgyCode());
        List<String> witAttachIds = witAuditRuleService.queryRuleAttach(witQuery);
        List<PcxBillExtraAttach> extraAttachList = getExtraAttachList(allExtraAttachList, delExtraAttachList);
        //如果是创建报销单时，票和附件没有就不需要调用，其他情况有可能是删除了票，或附件，要调用一下删除关联关系
        if (CollectionUtils.isEmpty(allEcsRel)
                && Objects.isNull(contractRel)
                && CollectionUtils.isEmpty(attachList)
                && CollectionUtils.isNotEmpty(extraAttachList)
                && CollectionUtils.isEmpty(witAttachIds)
                && createBill){
            return;
        }

        bindEcsRel(bill, allEcsRel, allAttachRelList, contractRel, attachList, extraAttachList, witAttachIds);
    }

    private List<PcxBillExtraAttach> getExtraAttachList(List<PcxBillExtraAttach> allExtraAttachList, List<PcxBillExtraAttach> delExtraAttachList) {
        if (CollectionUtils.isEmpty(allExtraAttachList)){
            return allExtraAttachList;
        }
        if(CollectionUtils.isNotEmpty(delExtraAttachList)){
            Set<String> setKey = new HashSet<>();
            for (PcxBillExtraAttach pcxBillExtraAttach : delExtraAttachList) {
                String key = String.format("%s-%d", pcxBillExtraAttach.getEmpCode(), pcxBillExtraAttach.getExtraType());
                setKey.add(key);
            }
            allExtraAttachList = allExtraAttachList.stream().filter(item->{
                String key = String.format("%s-%d", item.getEmpCode(), item.getExtraType());
                return !setKey.contains(key);
            }).collect(Collectors.toList());
        }
        return allExtraAttachList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void bindEcsRel(PcxBill bill,
                           List<PcxBillAttachRelationVO> attachList) {
        List<PcxExpDetailEcsRel> ecsRelList = pcxExpDetailEcsRelDao.selectByBillId(bill.getId());
        List<PcxBillExpAttachRel> attachRelList = pcxBillExpAttachRelDao.selectByBillId(bill.getId());
        PcxBillContractRel contractRel = pcxBillContractRelDao.selectByBillId(bill.getId());
        List<PcxBillExtraAttach> extraAttachList = pcxBillExtraAttachDao.selectByBillId(bill.getId());
        if (CollectionUtils.isNotEmpty(attachList)){
            List<String> attachIds = pcxExpDetailEcsRelDao.selectExistsAttachIdList(attachList.stream().map(PcxBillAttachRelationVO::getAttachId).distinct().collect(Collectors.toList()));
            attachList = attachList.stream().filter(item-> attachIds.contains(item.getAttachId())).collect(Collectors.toList());
        }
        bindEcsRel(bill, ecsRelList, attachRelList, contractRel, attachList, extraAttachList, Lists.newArrayList());
    }



    private void bindEcsRel(PcxBill bill,
                            List<PcxExpDetailEcsRel> allEcsRel,
                            List<PcxBillExpAttachRel> allAttachRelList,
                            PcxBillContractRel contractRel,
                            List<PcxBillAttachRelationVO> attachList,
                            List<PcxBillExtraAttach> extraAttachList,
                            List<String> witAttachIds) {
        BuildExpRelQO qo = new BuildExpRelQO();
        qo.setAgencyCode(bill.getAgyCode());
        qo.setTargetBillId(bill.getId());
        qo.setTargetBillNo(bill.getBillNo());
        qo.setTargetFiscalYear(Integer.valueOf(bill.getFiscal()));
        qo.setMofDivCode(bill.getMofDivCode());
        qo.setUserCode(bill.getClaimantCode());
        qo.setUserName(bill.getClaimantName());
        allEcsRel = allEcsRel.stream().filter(item->StringUtil.isNotEmpty(item.getEcsBillId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allEcsRel)
                || Objects.nonNull(contractRel)
                || CollectionUtils.isNotEmpty(allAttachRelList)
                || CollectionUtils.isNotEmpty(attachList)
                || CollectionUtils.isNotEmpty(extraAttachList)
                || CollectionUtils.isNotEmpty(witAttachIds)){
            List<BuildExpRelQO.InvoiceQO> billList = new ArrayList<>();
            //真正生成费用或者明细的票
            Map<String, List<PcxExpDetailEcsRel>> collect = allEcsRel.stream()
                    .collect(Collectors.groupingBy(PcxExpDetailEcsRel::getEcsBillId));
            qo.setBillList(billList);
            for (Map.Entry<String, List<PcxExpDetailEcsRel>> ecsEntity : collect.entrySet()) {
                BuildExpRelQO.InvoiceQO invoiceQO = buildInvoiceAttachQO(ecsEntity.getKey(), bill, BILL_FLAG);
                billList.add(invoiceQO);
            }
            Set<String> setKey = new HashSet<>();
            if (CollectionUtils.isNotEmpty(allAttachRelList)){
                for (PcxBillExpAttachRel attachRel : allAttachRelList) {
                    if (setKey.contains(attachRel.getAttachId())){
                        continue;
                    }
                    setKey.add(attachRel.getAttachId());
                    BuildExpRelQO.InvoiceQO attachQO = buildInvoiceAttachQO(attachRel.getAttachId(), bill, ATTACH_FLAG);
                    billList.add(attachQO);
                }
            }
            if (Objects.nonNull(contractRel)){
                BuildExpRelQO.InvoiceQO attachQO = buildInvoiceAttachQO(contractRel.getContractId(), bill, ATTACH_FLAG);
                billList.add(attachQO);
            }
            if (CollectionUtils.isNotEmpty(extraAttachList)){
                for (PcxBillExtraAttach pcxBillExtraAttach : extraAttachList) {
                    if (setKey.contains(pcxBillExtraAttach.getFileId())){
                        continue;
                    }
                    setKey.add(pcxBillExtraAttach.getFileId());
                    BuildExpRelQO.InvoiceQO attachQO = buildInvoiceAttachQO(pcxBillExtraAttach.getFileId(), bill, ATTACH_FLAG);
                    billList.add(attachQO);
                }
            }
            if (CollectionUtils.isNotEmpty(attachList)){
                for (PcxBillAttachRelationVO pcxBillAttachRelationVO : attachList) {
                    if (setKey.contains(pcxBillAttachRelationVO.getAttachId())){
                        continue;
                    }
                    setKey.add(pcxBillAttachRelationVO.getAttachId());
                    BuildExpRelQO.InvoiceQO attachQO = buildInvoiceAttachQO(pcxBillAttachRelationVO.getAttachId(), bill, ATTACH_FLAG);
                    billList.add(attachQO);
                }
            }

            if (CollectionUtils.isNotEmpty(witAttachIds)){
                for (String attachId: witAttachIds) {
                    if (setKey.contains(attachId)){
                        continue;
                    }
                    setKey.add(attachId);
                    BuildExpRelQO.InvoiceQO attachQO = buildInvoiceAttachQO(attachId, bill, ATTACH_FLAG);
                    billList.add(attachQO);
                }
            }
            EcsServiceMsg ecsServiceMsg = ecsBillExternalService.buildExpRel(qo);
            if (!ecsServiceMsg.isSuccess()){
                throw new RuntimeException("更新票状态失败");
            }
        }else{
            EcsServiceMsg ecsServiceMsg = ecsBillExternalService.deleteExpRel(qo);
            if (!ecsServiceMsg.isSuccess()){
                throw new RuntimeException("更新票状态失败");
            }
        }
    }

    private BuildExpRelQO.InvoiceQO buildInvoiceAttachQO(String attachId, PcxBill bill, String billAttachType){
        BuildExpRelQO.InvoiceQO attachQO = new BuildExpRelQO.InvoiceQO();
        attachQO.setBillId(attachId);
        attachQO.setBillAttachType(billAttachType);
        attachQO.setAgencyCode(bill.getAgyCode());
        attachQO.setFiscalYear(Integer.valueOf(bill.getFiscal()));
        attachQO.setMofDivCode(bill.getMofDivCode());
        return attachQO;
    }


    @Transactional(rollbackFor = Exception.class)
    public String addEcsBill(SaveEcsExpenseDTO dto) {       //同行人信息


        //先把匹配的票改为报销状态，更新失败是否报错回滚
        //保存报销单
        PcxBill pcxBill = billMainService.saveOrUpdate(dto.getBill());
        String billId = pcxBill.getId();

        //新添加的票和原有关联的票一起调ecs
        buildEcsRel(pcxBill, dto.getAllEcsList(), false, dto.getAllAttachRelList(), null, dto.getAllExtraAttachList(), dto.getDelExtraAttachList());

        commonOpt(dto);

        return billId;
    }

    private void delTripIfNeed(SaveEcsExpenseDTO dto) {
        if (dto.isDoTrip()){
            pcxBillTravelTripDao.delete(Wrappers.lambdaQuery(PcxBillTravelTrip.class)
                    .eq(PcxBillTravelTrip::getBillId, dto.getBill().getId()));
            pcxBillExpDetailTravelDao.updateTripIdEmpty(dto.getBill().getId());
            //删除补助的支出标准结果
            pcxBillExpStandResultDao.delete(Wrappers.lambdaQuery(PcxBillExpStandResult.class)
                    .eq(PcxBillExpStandResult::getBillId, dto.getBill().getId())
                    .eq(PcxBillExpStandResult::getDetailId, "")
                    .eq(PcxBillExpStandResult::getEcsBillId, ""));
            pcxBillTripCityDayDao.delete(Wrappers.lambdaQuery(PcxBillTripCityDay.class)
                    .eq(PcxBillTripCityDay::getBillId, dto.getBill().getId()));

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDetailAndPayment(SaveEcsExpenseDTO dto) {
        //调ecs同步费用明细和支付信息
        Response response = ecsBillExternalService.expInfoSave(dto.getUpdateEcsBillDTO());
        if (!response.getCode().equals(Response.SUCCESS_CODE)){
            log.error("同步电子凭证信息失败 {}", response.getMsg());
            throw new RuntimeException("同步电子凭证信息失败");
        }
        PcxBill bill = dto.getBill();
        PcxExpDetailEcsRel rel = dto.getEcsRel();

        billMainService.saveOrUpdate(bill);

        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .eq(PcxExpDetailEcsRel::getEcsBillId, rel.getEcsBillId()));

        //删除旧的支付信息
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, bill.getId())
                .eq(PcxEcsSettl::getEcsBillId, rel.getEcsBillId()));

        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, rel.getEcsBillId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));

        commonOpt(dto);
    }

    private void batchInsertNewEcsRel(List<PcxExpDetailEcsRel> newRelList, String billId) {
        if (CollectionUtils.isNotEmpty(newRelList)){
            List<String> unMatchEcsBillIds = newRelList.stream().filter(item -> !isTransExpense(item)).map(PcxExpDetailEcsRel::getEcsBillId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unMatchEcsBillIds)){
                //现在只删除了重复未匹配的票
                //todo 未匹配的票，添加附件后，重新出发添加票流程，需要把历史这个票未匹配的数据删除掉
                pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                        .eq(PcxExpDetailEcsRel::getBillId, billId)
                        .eq(PcxExpDetailEcsRel::getIsConfirm, InvoiceDtoWrapper.InvoiceFlag.UN_CONFIRM.getCode())
                        .in(PcxExpDetailEcsRel::getEcsBillId, unMatchEcsBillIds));
            }
            for (PcxExpDetailEcsRel newRel : newRelList) {
                newRel.setBillId(billId);
                pcxExpDetailEcsRelDao.insert(newRel);
            }
        }
    }

    private void batchInsertEcsSettlement(List<PcxEcsSettl> settlList, String billId) {
        if (CollectionUtils.isNotEmpty(settlList)){
            for (PcxEcsSettl ecsSettl : settlList) {
                ecsSettl.setBillId(billId);
                pcxEcsSettlDao.insert(ecsSettl);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNoEcsDetail(SaveEcsExpenseDTO dto) {
        PcxBill bill = dto.getBill();

        billMainService.saveOrUpdate(bill);

        buildEcsRel(bill, dto.getAllEcsList(), false, dto.getAllAttachRelList(), null, dto.getAllExtraAttachList(), dto.getDelExtraAttachList());
        //删除历史的费用明细关联附件信息
        if (!dto.isAdd() && StringUtil.isNotEmpty(dto.getRelId())){
            if (SaveEcsExpenseDTO.STAND_RESULT_DETAIL.equals(dto.getStandResultFlag())){
                pcxBillExpStandResultDao.delete(Wrappers.lambdaQuery(PcxBillExpStandResult.class)
                        .eq(PcxBillExpStandResult::getBillId, bill.getId())
                        .eq(PcxBillExpStandResult::getDetailId, dto.getRelId()));
            }else{
                pcxBillExpStandResultDao.delete(Wrappers.lambdaQuery(PcxBillExpStandResult.class)
                        .eq(PcxBillExpStandResult::getBillId, bill.getId())
                        .eq(PcxBillExpStandResult::getEcsBillId, dto.getRelId()));
            }
            pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                    .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                    .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.EXP_DETAIL.getCode())
                    .eq(PcxBillExpAttachRel::getRelId, dto.getRelId()));
        }

        commonOpt(dto);

    }

    private void saveOrUpdateExpBase(Collection<PcxBillExpBase> values, PcxBill view) {
        if (CollectionUtils.isNotEmpty(values)){
            for (PcxBillExpBase pcxBillExpBase : values) {
                BillExpenseService<PcxBillExpBase> bean = ExpenseBeanUtil.getBean(pcxBillExpBase.getExpenseCode(), view.getBizType());
                bean.saveOrUpdate(pcxBillExpBase, view);
            }
        }
    }

    private void saveOrUpdateExpCommon(Collection<PcxBillExpCommon> values, PcxBill view) {

        for (PcxBillExpCommon pcxBillExpBase : values) {
            PcxBillExpCommon pcxBillExpCommon = pcxBillExpCommonDao.selectById(pcxBillExpBase.getId());
            if (Objects.isNull(pcxBillExpCommon)){
                pcxBillExpBase.setBillId(view.getId());
                pcxBillExpCommonDao.insert(pcxBillExpBase);
            }else {
                pcxBillExpCommonDao.updateById(pcxBillExpBase);
            }
        }
    }

    private void saveOrUpdateDetailList(List<PcxBillExpDetailBase> needSaveOrUpdateDetailList,
                                        Map<String, String> parentMap,
                                        PcxBill view,
                                        Map<String, PcxBillExpBase> baseMap
    ) {
        Map<String, List<PcxBillExpDetailBase>> detailMap = needSaveOrUpdateDetailList.stream()
                .collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode())));
        for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : detailMap.entrySet()) {
            BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(entry.getKey());
            PcxBillExpBase pcxBillExpBase = baseMap.get(entry.getKey());
            entry.getValue().forEach(item->item.setBillId(view.getId()));
            detailBean.saveOrUpdate(pcxBillExpBase, entry.getValue(), view);
        }
    }

    private void delOldDetailList(List<PcxBillExpDetailBase> delOldDetailList, Map<String, String> parentMap) {
        if (CollectionUtils.isNotEmpty(delOldDetailList)){
            Map<String, List<PcxBillExpDetailBase>> oldDetailMap = delOldDetailList.stream()
                    .collect(Collectors.groupingBy(item -> parentMap.get(item.getExpDetailCode())));
            for (Map.Entry<String, List<PcxBillExpDetailBase>> entry : oldDetailMap.entrySet()) {
                BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> detailBean = ExpenseBeanUtil.getDetailBean(entry.getKey());
                detailBean.deleteByIds(entry.getValue().stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList()));
            }
        }
    }

    private void batchInsertFellow(List<PcxBillTravelFellows> fellowsList, String billId){
        if (CollectionUtils.isNotEmpty(fellowsList)){
            pcxBillTravelFellowsDao.delete(Wrappers.lambdaQuery(PcxBillTravelFellows.class)
                    .eq(PcxBillTravelFellows::getBillId, billId));
            for (PcxBillTravelFellows pcxBillTravelFellows : fellowsList) {
                pcxBillTravelFellows.setBillId(billId);
                pcxBillTravelFellowsDao.insert(pcxBillTravelFellows);
            }
        }
    }
    private void batchInsertTrip(List<PcxBillTravelTrip> tripList, String billId){
        if (CollectionUtils.isNotEmpty(tripList)){
            for (PcxBillTravelTrip travelTrip : tripList) {
                travelTrip.setBillId(billId);
                pcxBillTravelTripDao.insert(travelTrip);
            }
        }
    }
    private void updateTravelDetailTripId(Map<String, Set<String>> tripDetailIdMap){
        if (Objects.nonNull(tripDetailIdMap) && !tripDetailIdMap.isEmpty()){
            for (Map.Entry<String, Set<String>> entry : tripDetailIdMap.entrySet()) {
                pcxBillExpDetailTravelDao.updateTripId(new ArrayList<>(entry.getValue()), entry.getKey());
            }
        }
    }

    public void batchInsertAttachRel(List<PcxBillExpAttachRel> attachRelList, String billId){
        if (CollectionUtils.isNotEmpty(attachRelList)){
            for (PcxBillExpAttachRel attachRel : attachRelList) {
                attachRel.setBillId(billId);
                pcxBillExpAttachRelDao.insert(attachRel);
            }
        }
    }

    private void batchInsertStandResult(List<PcxBillExpStandResult> standResultList, String billId){
        if (CollectionUtils.isNotEmpty(standResultList)){
            for (PcxBillExpStandResult standResult : standResultList) {
                standResult.setBillId(billId);
                pcxBillExpStandResultDao.insert(standResult);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String delDetailAndEcsBill(SaveEcsExpenseDTO dto) {                //同行人信息
        log.info("解除票状态");
        if (dto.isDelEcsRel()){
            buildEcsRel(dto.getBill(), dto.getAllEcsList(), false, dto.getAllAttachRelList(), null, dto.getAllExtraAttachList(), dto.getDelExtraAttachList());
        }

        billMainService.saveOrUpdate(dto.getBill());

        if (CollectionUtils.isNotEmpty(dto.getDelEcsRelList())){
            pcxExpDetailEcsRelDao.deleteBatchIds(dto.getDelEcsRelList().stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
            pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                    .eq(PcxEcsSettl::getEcsBillId, dto.getDelEcsRelList().get(0).getEcsBillId())
                    .eq(PcxEcsSettl::getBillId, dto.getDelEcsRelList().get(0).getBillId()));
        }
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, dto.getBill().getId())
                .eq(PcxBillExpAttachRel::getRelId, dto.getDelAttachRelId()));

        commonOpt(dto);

        return dto.getBill().getId();
    }

    private void commonOpt(SaveEcsExpenseDTO dto){
        //保存费用
        saveOrUpdateExpBase(dto.getBaseExpList(), dto.getBill());

        Map<String, PcxBillExpBase> baseMap = dto.getBaseExpList().stream()
                .collect(Collectors.toMap(PcxBillExpBase::getExpenseCode, Function.identity(), (key1, key2) -> key1));

        //如果有匹配行程动作，则把老的行程都删掉
        delTripIfNeed(dto);

        //保存费用明细
        saveOrUpdateDetailList(dto.getInsertOrUpdateDetailList(), dto.getParentMap(), dto.getBill(), baseMap);

        delOldDetailList(dto.getDelOldDelDetail(), dto.getParentMap());

        if (Objects.nonNull(dto.getTripDto())){
            //保存行程
            batchInsertTrip(dto.getTripDto().getTripList(), dto.getBill().getId());
            //给明细更新行程id
            updateTravelDetailTripId(dto.getTripDto().getTripDetailIdMap());
        }

        //保存票关联关系，包括关联费用的，关联明细的，未匹配的
        batchInsertNewEcsRel(dto.getAddEcsRel(), dto.getBill().getId());

        delOldStandResult(dto.getDelStandResultList());

        batchInsertFellow(dto.getFellowsList(), dto.getBill().getId());

        //批量插入标准匹配结果
        batchInsertStandResult(dto.getStandList(), dto.getBill().getId());

        //添加新的费用明细与附件关联信息
        batchInsertAttachRel(dto.getAddAttachRelList(), dto.getBill().getId());

        batchInsertEcsSettlement(dto.getSettlList(), dto.getBill().getId());

        batchInsertTripCityDays(dto.getTripCityDays(), dto.getBill());

        delOldTripCityDays(dto.getDelTripCityDays());

        batchInsertBillOuterEmp(dto.getAddOuterEmp(), dto.getUpdateOuterEmp(), dto.getBill());

        batchInsertOrUpdateTripSegment(dto.getTripSegments(), dto.getBill());

        batchInsertOrUpdateAmtApportion(dto.getAmtApportions(), dto.getAmtApportionDepartments(), dto.getBill());

        delExtraSubsidy(dto.getDelExtraList(), dto.getDelExtraAttachList());

        batchInsertExtraSubsidy(dto.getAddExtraList(), dto.getAddExtraAttachList(), dto.getUpdateExtraSubsidyQO());
    }

    private void delExtraSubsidy(List<PcxBillExtraSubsidy> delExtraList, List<PcxBillExtraAttach> delExtraAttachList) {
        if (CollectionUtils.isNotEmpty(delExtraList)){
            pcxBillExtraSubsidyDao.deleteBatchIds(delExtraList.stream().map(PcxBillExtraSubsidy::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(delExtraAttachList)){
            pcxBillExtraAttachDao.deleteBatchIds(delExtraAttachList.stream().map(PcxBillExtraAttach::getId).collect(Collectors.toList()));
        }
    }

    private void batchInsertExtraSubsidy(List<PcxBillExtraSubsidy> addExtraList, List<PcxBillExtraAttach> addExtraAttachList, UpdateExtraSubsidyQO updateExtraSubsidyQO) {
        if (Objects.nonNull(updateExtraSubsidyQO)){
            pcxBillExtraSubsidyDao.delete(Wrappers.lambdaQuery(PcxBillExtraSubsidy.class)
                    .eq(PcxBillExtraSubsidy::getBillId, updateExtraSubsidyQO.getBillId())
                    .eq(PcxBillExtraSubsidy::getEmpCode, updateExtraSubsidyQO.getEmpCode()));
            pcxBillExtraAttachDao.delete(Wrappers.lambdaQuery(PcxBillExtraAttach.class)
                    .eq(PcxBillExtraAttach::getBillId, updateExtraSubsidyQO.getBillId())
                    .eq(PcxBillExtraAttach::getEmpCode, updateExtraSubsidyQO.getEmpCode()));
        }
        if(CollectionUtils.isNotEmpty(addExtraList)){
            for (PcxBillExtraSubsidy extraSubsidy : addExtraList) {
                extraSubsidy.setBillId(updateExtraSubsidyQO.getBillId());
                pcxBillExtraSubsidyDao.insert(extraSubsidy);
            }
        }
        if(CollectionUtils.isNotEmpty(addExtraAttachList)){
            for (PcxBillExtraAttach extraAttach : addExtraAttachList) {
                extraAttach.setBillId(updateExtraSubsidyQO.getBillId());
                pcxBillExtraAttachDao.insert(extraAttach);
            }
        }
    }

    private void delOldTripCityDays(List<PcxBillTripCityDay> delTripCityDays) {
        if (CollectionUtils.isNotEmpty(delTripCityDays)){
            pcxBillTripCityDayDao.deleteBatchIds(delTripCityDays.stream().map(PcxBillTripCityDay::getId).collect(Collectors.toList()));
        }
    }


    private void batchInsertOrUpdateAmtApportion(List<PcxBillAmtApportion> amtApportions,
                                                 List<PcxBillAmtApportionDepartment> amtApportionDepartments,
                                                 PcxBill bill) {
        pcxBillAmtApportionDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
                .eq(PcxBillAmtApportion::getBillId, bill.getId()));
        pcxBillAmtApportionDepartmentDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
                .eq(PcxBillAmtApportionDepartment::getBillId, bill.getId()));
        if (CollectionUtils.isNotEmpty(amtApportions)){
            for (PcxBillAmtApportion amtApportion : amtApportions) {
                amtApportion.setBillId(bill.getId());
                pcxBillAmtApportionDao.insert(amtApportion);
            }
        }
        if (CollectionUtils.isNotEmpty(amtApportionDepartments)){
            for (PcxBillAmtApportionDepartment amtApportionDepartment : amtApportionDepartments) {
                amtApportionDepartment.setBillId(bill.getId());
                pcxBillAmtApportionDepartmentDao.insert(amtApportionDepartment);
            }
        }
    }

    private void batchInsertOrUpdateTripSegment(List<PcxBillTripSegment> tripSegments, PcxBill bill) {
        pcxBillTripSegmentDao.delete(Wrappers.lambdaQuery(PcxBillTripSegment.class)
                .eq(PcxBillTripSegment::getBillId, bill.getId()));
        if (CollectionUtils.isNotEmpty(tripSegments)){
            for (PcxBillTripSegment tripSegment : tripSegments) {
                tripSegment.setBillId(bill.getId());
                pcxBillTripSegmentDao.insert(tripSegment);
            }
        }
    }

    private void batchInsertTripCityDays(List<PcxBillTripCityDay> tripCityDays, PcxBill bill) {
        if (CollectionUtils.isNotEmpty(tripCityDays)){
            for (PcxBillTripCityDay tripCityDay : tripCityDays) {
                tripCityDay.setBillId(bill.getId());
                tripCityDay.setFiscal(bill.getFiscal());
                tripCityDay.setAgyCode(bill.getAgyCode());
                tripCityDay.setMofDivCode(bill.getMofDivCode());
                pcxBillTripCityDayDao.insert(tripCityDay);
            }
        }
    }

    private void delOldStandResult(List<PcxBillExpStandResult> delStandResultList) {
        if (CollectionUtils.isNotEmpty(delStandResultList)){
            pcxBillExpStandResultDao.deleteBatchIds(delStandResultList.stream()
                    .map(PcxBillExpStandResult::getId)
                    .collect(Collectors.toList()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String createCommonExpenseBill(PcxBill bill,
                                          List<PcxBillExpCommon> baseExpList,
                                          List<PcxExpDetailEcsRel> allEcsRel,
                                          List<PcxBillExpAttachRel> attachRelList,
                                          List<PcxEcsSettl> settlList,
                                          PcxBillContractRel contractRel,
                                          PcxBillRelation pcxBillRelation,
                                          List<PcxBillExpDetailCommon> detailCommonList,
                                          List<PcxBillAmtApportion> amtApportions,
                                          List<PcxBillAmtApportionDepartment> departmentApportions) {
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);
        saveOrUpdateExpCommon(baseExpList, pcxBill);
        saveOrUpdateCommonDetail(baseExpList, detailCommonList, pcxBill);
        buildEcsRel(pcxBill, allEcsRel, true, attachRelList, contractRel, null, null);
        batchInsertNewEcsRel(allEcsRel, pcxBill.getId());
        batchInsertAttachRel(attachRelList, pcxBill.getId());
        batchInsertEcsSettlement(settlList, pcxBill.getId());
        if (Objects.nonNull(contractRel)){
            contractRel.setBillId(pcxBill.getId());
            pcxBillContractRelDao.insert(contractRel);
        }
        if (Objects.nonNull(pcxBillRelation)){
            pcxBillRelation.setBillId(pcxBill.getId());
            pcxBillRelationDao.insert(pcxBillRelation);
        }
        batchInsertOrUpdateAmtApportion(amtApportions, departmentApportions, pcxBill);
        return pcxBill.getId();
    }

    private void saveOrUpdateCommonDetail(List<PcxBillExpCommon> pcxBillExpCommons, List<PcxBillExpDetailCommon> detailCommonList, PcxBill bill) {
        Map<String, List<PcxBillExpDetailCommon>> map = detailCommonList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getExpenseId() != null ? item.getExpenseId() : "",
                        HashMap::new,
                        Collectors.toList()
                ));
        for (PcxBillExpCommon pcxBillExpCommon : pcxBillExpCommons) {
            List<PcxBillExpDetailCommon> detailCommons = map.get(pcxBillExpCommon.getId());
            if (CollectionUtils.isNotEmpty(detailCommons)){
                defaultBillExpenseDetailService.saveOrUpdate(pcxBillExpCommon, detailCommons, bill);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String addEcsCommonBill(PcxBill bill,
                                   List<PcxBillExpCommon> baseExpList,
                                   List<PcxExpDetailEcsRel> addEcsRel,
                                   List<PcxBillExpAttachRel> addAttachRelList,
                                   List<PcxExpDetailEcsRel> allEcsRelList,
                                   List<PcxBillExpAttachRel> allAttachRelList,
                                   List<PcxEcsSettl> ecsSettlList,
                                   PcxBillContractRel contractRel,
                                   List<PcxBillExpDetailCommon> detailCommons,
                                   List<PcxBillAmtApportion> amtApportions,
                                   List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);
        saveOrUpdateExpCommon(baseExpList, pcxBill);
        saveOrUpdateCommonDetail(baseExpList, detailCommons, pcxBill);
        buildEcsRel(pcxBill, allEcsRelList, false, allAttachRelList, contractRel, null, null);
        batchInsertNewEcsRel(addEcsRel, pcxBill.getId());
        batchInsertAttachRel(addAttachRelList, pcxBill.getId());
        batchInsertEcsSettlement(ecsSettlList, pcxBill.getId());
        batchInsertOrUpdateAmtApportion(amtApportions, amtApportionDepartments, bill);
        return pcxBill.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public String delEcsCommonBill(PcxBill bill, List<PcxBillExpCommon> baseExpList,
                                   List<PcxExpDetailEcsRel> delEcsRelList,
                                   List<PcxExpDetailEcsRel> otherRelList,
                                   List<PcxBillExpAttachRel> attachRelList,
                                   String delAttachRelId,
                                   boolean isDelEcsRel,
                                   PcxBillContractRel contractRel,
                                   List<String> delDetailIds,
                                   List<PcxBillAmtApportion> amtApportions,
                                   List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
        PcxBill pcxBill = billMainService.saveOrUpdate(bill);
        saveOrUpdateExpCommon(baseExpList, pcxBill);
        buildEcsRel(pcxBill, otherRelList, false, attachRelList, contractRel, null, null);
        pcxExpDetailEcsRelDao.deleteBatchIds(delEcsRelList.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, delAttachRelId));
        if (isDelEcsRel){
            pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                    .eq(PcxEcsSettl::getBillId, pcxBill.getId())
                    .eq(PcxEcsSettl::getEcsBillId, delAttachRelId));
        }
        if (CollectionUtils.isNotEmpty(delEcsRelList)){
            pcxBillExpDetailCommonDao.deleteBatchIds(delDetailIds);
        }
        batchInsertOrUpdateAmtApportion(amtApportions, amtApportionDepartments, bill);
        return pcxBill.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEcsCommon(SaveEcsCommonDTO dto) {

        billMainService.saveOrUpdate(dto.getBill());

        saveOrUpdateExpCommon(dto.getBaseExpList(), dto.getBill());

        delEcsRelCommon(dto.getDelEcsRel());
        delDetailCommon(dto.getDelDetailList());
        saveOrUpdateCommonDetail(dto.getBaseExpList(), dto.getInsertOrUpdateDetailList(), dto.getBill());
        insertEcsRelCommon(dto.getAddEcsRel(), dto.getBill().getId());

        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, dto.getBill().getId())
                .eq(PcxBillExpAttachRel::getRelId, dto.getAttachRelId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));

        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, dto.getBill().getId())
                .eq(PcxEcsSettl::getEcsBillId, dto.getAttachRelId()));
        batchInsertAttachRel(dto.getAttachRelList(), dto.getBill().getId());
        batchInsertEcsSettlement(dto.getEcsSettls(), dto.getBill().getId());
        batchInsertOrUpdateAmtApportion(dto.getAmtApportions(), dto.getAmtApportionDepartments(), dto.getBill());
    }

    private void insertEcsRelCommon(List<PcxExpDetailEcsRel> addEcsRelList, String billId) {
        if (CollectionUtils.isNotEmpty(addEcsRelList)){
            for (PcxExpDetailEcsRel rel : addEcsRelList) {
                rel.setBillId(billId);
                pcxExpDetailEcsRelDao.insert(rel);
            }
        }
    }

    private void delDetailCommon(List<PcxBillExpDetailCommon> delDetailList) {
        if (CollectionUtils.isNotEmpty(delDetailList)){
            pcxBillExpDetailCommonDao.deleteBatchIds(delDetailList.stream().map(PcxBillExpDetailCommon::getId).collect(Collectors.toList()));
        }
    }

    private void delEcsRelCommon(List<PcxExpDetailEcsRel> optEcsRelList) {
        if (CollectionUtils.isNotEmpty(optEcsRelList)){
            pcxExpDetailEcsRelDao.deleteBatchIds(optEcsRelList.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNoEcsCommon(SaveEcsCommonDTO dto) {
        billMainService.saveOrUpdate(dto.getBill());
        buildEcsRel(dto.getBill(), dto.getAllEcsRel(), false, dto.getAllAttachRelList(), null, null, null);
        saveOrUpdateExpCommon(dto.getBaseExpList(), dto.getBill());
        delEcsRel(dto.getDelEcsRel());
        batchInsertNewEcsRel(dto.getAddEcsRel(), dto.getBill().getId());
        delDetailCommon(dto.getDelDetailList());

        saveOrUpdateCommonDetail(dto.getBaseExpList(), dto.getInsertOrUpdateDetailList(), dto.getBill());
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, dto.getBill().getId())
                .eq(PcxBillExpAttachRel::getRelId, dto.getAttachRelId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.EXP_DETAIL.getCode()));
        batchInsertAttachRel(dto.getAttachRelList(), dto.getBill().getId());
        batchInsertOrUpdateAmtApportion(dto.getAmtApportions(), dto.getAmtApportionDepartments(), dto.getBill());
    }

    private void delEcsRel(List<PcxExpDetailEcsRel> delEcsRel) {
        if (CollectionUtils.isNotEmpty(delEcsRel)){
            pcxExpDetailEcsRelDao.deleteBatchIds(delEcsRel.stream().map(PcxExpDetailEcsRel::getId).collect(Collectors.toList()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEcsExpType(PcxBill bill,
                                 List<PcxBillExpCommon> expBaseList,
                                 List<PcxExpDetailEcsRel> ecsRelList,
                                 List<PcxBillExpDetailCommon> detailCommonList,
                                 List<PcxBillAmtApportion> amtApportions,
                                 List<PcxBillAmtApportionDepartment> amtApportionDepartments) {
        billMainService.saveOrUpdate(bill);
        saveOrUpdateExpCommon(expBaseList, bill);
        saveOrUpdateCommonDetail(expBaseList, detailCommonList, bill);
        if (CollectionUtils.isNotEmpty(ecsRelList)){
            for (PcxExpDetailEcsRel rel : ecsRelList) {
                pcxExpDetailEcsRelDao.updateById(rel);
            }
        }
        updateTravelAmtApportion(amtApportions, amtApportionDepartments, Lists.newArrayList(), bill, false);
    }


    @Transactional(rollbackFor = Exception.class)
    public void bindContract(PcxBill pcxBill, PcxBillContractRel contractRel, List<PcxExpDetailEcsRel> allEcsRelList,
                             List<PcxBillExpAttachRel> allAttachRel, PcxBillRelation pcxBillRelation) {
        buildEcsRel(pcxBill, allEcsRelList, false, allAttachRel, contractRel, null, null);
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, pcxBill.getId()));
        //创建报销单与合同的关联关系
        pcxBillContractRelDao.insert(contractRel);
        pcxBillRelationDao.insert(pcxBillRelation);
    }

    @Transactional(rollbackFor = Exception.class)
    public void unbindContract(PcxBill bill, List<PcxBillContractRel> relList, List<PcxExpDetailEcsRel> allEcsRelList, List<PcxBillExpAttachRel> allAttachRel) {
        buildEcsRel(bill, allEcsRelList, false, allAttachRel, null, null, null);
        pcxBillContractRelDao.deleteBatchIds(relList.stream().map(PcxBillContractRel::getId).collect(Collectors.toList()));
        pcxBillRelationDao.delete(Wrappers.lambdaQuery(PcxBillRelation.class)
                .eq(PcxBillRelation::getBillId, bill.getId())
                .eq(PcxBillRelation::getRelBillId, relList.get(0).getBillId()));
    }

    private void batchInsertBillOuterEmp(List<PcxBillOuterEmp> initOutEmp, List<PcxBillOuterEmp> updateList, PcxBill bill) {
        if (CollectionUtils.isNotEmpty(initOutEmp)){
            for (PcxBillOuterEmp outerEmp : initOutEmp) {
                outerEmp.setBillId(bill.getId());
                pcxBillOuterEmpDao.insert(outerEmp);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            for (PcxBillOuterEmp outerEmp : updateList) {
                pcxBillOuterEmpDao.updateById(outerEmp);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEcsAttach(PcxBill bill,
                                String ecsBillId,
                                List<PcxBillExpAttachRel> addRelList,
                                List<PcxBillExpAttachRel> allAttachRelList,
                                List<PcxExpDetailEcsRel> allEcsRel) {
        //把匹配的票改为报销状态
        buildEcsRel(bill, allEcsRel, true, allAttachRelList, null, null, null);
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, ecsBillId)
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.ECS.getCode()));
        batchInsertAttachRel(addRelList, bill.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEcsRel(String billId, List<PcxExpDetailEcsRel> ecsRel) {
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, billId)
                .in(PcxExpDetailEcsRel::getEcsBillId, ecsRel.get(0).getEcsBillId()));
        batchInsertNewEcsRel(ecsRel, billId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void unMatchEcsReplenishTrip(SaveEcsExpenseDTO dto) {
        PcxBill bill = dto.getBill();

        billMainService.saveOrUpdate(bill);

        //删除老的票关联关系
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, bill.getId())
                .in(PcxExpDetailEcsRel::getEcsBillId, dto.getDelEcsBillIds()));

        commonOpt(dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateTripTime(SaveEcsExpenseDTO dto) {
        PcxBill bill = dto.getBill();

        billMainService.saveOrUpdate(bill);
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, bill.getId())
                .eq(PcxBillExpAttachRel::getRelId, dto.getDelAttachRelId())
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.EXP_DETAIL.getCode()));
        commonOpt(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAddition(UpdateAdditionQO updateEcsQO) {
        pcxBillExpDetailTravelDao.updateAddition(updateEcsQO);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateTravelAmtApportion(List<PcxBillAmtApportion> apportionList,
                                         List<PcxBillAmtApportionDepartment> amtApportionDepartmentList,
                                         List<PcxBillTripSegment> segments,
                                         PcxBill pcxBill,
                                         boolean updateBill) {
        if (updateBill){
            billMainService.saveOrUpdate(pcxBill);
        }
        pcxBillAmtApportionDepartmentDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
                .eq(PcxBillAmtApportionDepartment::getBillId, pcxBill.getId()));
        List<String> collect = apportionList.stream().map(PcxBillAmtApportion::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(apportionList)){
            pcxBillAmtApportionDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
                    .eq(PcxBillAmtApportion::getBillId, pcxBill.getId())
                    .notIn(PcxBillAmtApportion::getId, collect));
            for (PcxBillAmtApportion amtApportion : apportionList) {
                PcxBillAmtApportion existsApportion = pcxBillAmtApportionDao.selectById(amtApportion.getId());
                if (Objects.nonNull(existsApportion)){
                    pcxBillAmtApportionDao.updateById(amtApportion);
                }else{
                    pcxBillAmtApportionDao.insert(amtApportion);
                }
            }
        }else{
            pcxBillAmtApportionDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
                    .eq(PcxBillAmtApportion::getBillId, pcxBill.getId()));
        }

        if (CollectionUtils.isNotEmpty(amtApportionDepartmentList)){
            for (PcxBillAmtApportionDepartment amtApportionDepartment : amtApportionDepartmentList) {
                pcxBillAmtApportionDepartmentDao.insert(amtApportionDepartment);
            }
        }

        if (CollectionUtils.isNotEmpty(segments)){
            for (PcxBillTripSegment segment : segments) {
                pcxBillTripSegmentDao.updateById(segment);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTravelApportionDetail(List<PcxBillAmtApportionDepartment> list) {
        if (CollectionUtils.isNotEmpty(list)){
            for (PcxBillAmtApportionDepartment amtApportionDepartment : list) {
                pcxBillAmtApportionDepartmentDao.updateById(amtApportionDepartment);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateExtraSubsidy(SaveEcsExpenseDTO dto) {
        PcxBill bill = dto.getBill();

        buildEcsRel(dto.getBill(), dto.getAllEcsList(), false, dto.getAllAttachRelList(), null, dto.getAllExtraAttachList(), null);
        billMainService.saveOrUpdate(bill);

        commonOpt(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void detailMountTrip(SaveEcsExpenseDTO dto) {
        commonOpt(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void erasureCommonOldData(String billId) {
        pcxBillExpCommonDao.delete(Wrappers.lambdaQuery(PcxBillExpCommon.class)
                .eq(PcxBillExpCommon::getBillId, billId));
        pcxBillExpDetailCommonDao.delete(Wrappers.lambdaQuery(PcxBillExpDetailCommon.class)
                .eq(PcxBillExpDetailCommon::getBillId, billId));
        pcxExpDetailEcsRelDao.delete(Wrappers.lambdaQuery(PcxExpDetailEcsRel.class)
                .eq(PcxExpDetailEcsRel::getBillId, billId));
        pcxEcsSettlDao.delete(Wrappers.lambdaQuery(PcxEcsSettl.class)
                .eq(PcxEcsSettl::getBillId, billId));
        pcxBillExpAttachRelDao.delete(Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId));
        pcxBillAmtApportionDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportion.class)
                .eq(PcxBillAmtApportion::getBillId, billId));
        pcxBillAmtApportionDepartmentDao.delete(Wrappers.lambdaQuery(PcxBillAmtApportionDepartment.class)
                .eq(PcxBillAmtApportionDepartment::getBillId, billId));
    }

    @Transactional(rollbackFor = Exception.class)
    public void initBill(PcxBill bill, List<PcxBillExpBase> pcxBillExpBases) {
        billMainService.saveOrUpdate(bill);
        if (CollectionUtils.isNotEmpty(pcxBillExpBases)){
            saveOrUpdateExpBase(pcxBillExpBases, bill);
        }
    }
}
