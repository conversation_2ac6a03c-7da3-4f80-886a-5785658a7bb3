package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.StrUtil;
import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.bill.PcxBillExpDetailTrainingService;
import com.pty.pcx.api.bill.PcxBillExpTrainingService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.FormSettingEnums;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.common.util.PcxDateUtil;
import com.pty.pcx.dao.bill.PcxBillExpTrainingDao;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.qo.bas.PcxBasFormSettingQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.vo.bill.PcxBillListVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.UNIVERSAL_ITEM_CODE;


/**
 * 培训费用处理接口接口
 * <AUTHOR>
 * @since 2025/05/15
 */
@Service("billExpenseService4Training")
@Slf4j
@Indexed
public class BillExpenseService4Training extends BillExpenseCommonService implements BillExpenseService<PcxBillExpTraining> {

    @Autowired
    private PcxBillExpTrainingDao pcxBillExpTrainingDao;

    @Autowired
    private PcxBasFormSettingService pcxBasFormSettingService;

    @Autowired
    private PcxBillExpDetailTrainingService pcxBillExpDetailTrainingService;

    @Autowired
    private PcxBillExpTrainingService pcxBillExpTrainingService;

    /**
     * 校验逻辑（校验专属属性和明细项）
     * @param expBase 校验的块信息
     * @return
     */
    @Override
    public CheckMsg<Void> validate(PcxBillExpTraining expBase, String billFuncCode) {
        if (Objects.isNull(expBase)) {
            return CheckMsg.fail("培训费用信息为空");
        }
        FormSettingEnums.BillFuncCodeEnum billFuncCodeEnum = FormSettingEnums.BillFuncCodeEnum.getByCode(billFuncCode);
        if (Objects.isNull(billFuncCodeEnum)) {
            return CheckMsg.fail("暂不支持单据类型为：[" + billFuncCode + "]的业务操作");
        }
        //查询培训费启用的必填的专属字段
        PcxBasFormSettingQueryQO qo = new PcxBasFormSettingQueryQO(
                FormSettingEnums.FormClassifyEnum.EXPENSE.getCode(),
                billFuncCodeEnum.getBit(),
                expBase.getExpenseCode(),
                FormSettingEnums.FormTypeEnum.EXPENSE_EXCLUSIVE.getCode(),
                expBase.getAgyCode(),
                expBase.getFiscal(),
                expBase.getMofDivCode());
        Response<List<PcxBasFormSettingQO>> response = pcxBasFormSettingService.selectAllFormSetting(qo);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //启用且必填
            List<PcxBasFormSettingQO> formSettingQOS = response.getData().stream().filter(item -> item.getIsEnabled() == PubConstant.LOGIC_TRUE
                    & item.getIsNull() == PubConstant.LOGIC_TRUE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(formSettingQOS)) {
                Map<String, Object> stringObjectMap = BeanUtil.beanToMap(expBase);
                List<String> missingFields = new ArrayList<>();

                for (PcxBasFormSettingQO formSettingQO : formSettingQOS) {
                    //todo 费用承担部门不做校验
                    if (formSettingQO.getFieldValue().equals("departmentCode")){
                        continue;
                    }
                    Object o = stringObjectMap.get(formSettingQO.getFieldValue());
                    if (Objects.isNull(o) || StringUtil.isEmpty(o.toString())) {
                        String fieldName = formSettingQO.getFieldName();
                        missingFields.add(fieldName);
                        log.warn("专属属性:[{}]不能为空", fieldName);
                    }
                }

                if (!missingFields.isEmpty()) {
                    String errorMessage = "专属属性:" + String.join("、", missingFields) + "不能为空";
                    return CheckMsg.fail(errorMessage);
                }
            }
        }
        return CheckMsg.success();
    }

    /**
     * 暂存
     * @param t 费用信息
     * @param pcxBill 单据
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcxBillExpTraining saveOrUpdate(PcxBillExpTraining pcxBillExpTraining, PcxBill pcxBill) {
        PcxBillExpTraining existingRecord = pcxBillExpTrainingDao.selectByUnionKey(pcxBill.getId(), pcxBillExpTraining.getExpenseCode(), pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        pcxBillExpTraining.setDuration(PcxDateUtil.calculateDays(pcxBillExpTraining.getStartDate(),  pcxBillExpTraining.getFinishDate()));
        if (Objects.isNull(existingRecord)) {
            pcxBillExpTraining.setId(Objects.isNull(pcxBillExpTraining.getId()) ? IDGenerator.id() : pcxBillExpTraining.getId());
            pcxBillExpTraining.setBillId(pcxBill.getId());
            pcxBillExpTraining.setAgyCode(pcxBill.getAgyCode());
            pcxBillExpTraining.setFiscal(pcxBill.getFiscal());
            pcxBillExpTraining.setMofDivCode(pcxBill.getMofDivCode());
            pcxBillExpTraining.setTenantId(StringUtil.isNotBlank(pcxBill.getTenantId()) ? pcxBill.getTenantId() : PtyContext.getTenantId());
            //TODO 背景：之前存在一个问题，不传ID但是有具体费用，并且前端有时候更新不给你ID，后端这样处理，
            PcxBillExpTraining billExpTraining = pcxBillExpTrainingDao.selectById(pcxBillExpTraining.getId());
            if (Objects.isNull(billExpTraining)){
                setCreatorInfo(pcxBillExpTraining);
                pcxBillExpTrainingDao.insert(pcxBillExpTraining);
            }else {
                setCreatorInfo(pcxBillExpTraining);
                pcxBillExpTrainingDao.updateById(pcxBillExpTraining);
            }
        } else {
            pcxBillExpTraining.setId(existingRecord.getId());
            setModifierInfo(pcxBillExpTraining);
            pcxBillExpTrainingDao.updateById(pcxBillExpTraining);
        }
        // 补充公共信息
        saveCommonData(pcxBillExpTraining, pcxBill);
        return pcxBillExpTraining;
    }

    /**
     * 费用查看
     * @param expenseCode
     * @param pcxBill
     * @return
     */
    @Override
    public PcxBillExpTraining view(String expenseCode, PcxBill pcxBill) {
        PcxBillExpTraining billExpTraining = pcxBillExpTrainingDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        //特殊业务数据处理补充实现
        return billExpTraining;
    }

    @Override
    public void delete(String expenseCode, PcxBill pcxBill) {
        PcxBillExpTraining billExpTraining = pcxBillExpTrainingDao.selectByUnionKey(pcxBill.getId(), expenseCode, pcxBill.getAgyCode(), pcxBill.getFiscal(), pcxBill.getMofDivCode());
        if (Objects.nonNull(billExpTraining)){
            //删除费用信息
            pcxBillExpTrainingDao.deleteById(billExpTraining.getId());
        }
    }

    @Override
    public void dealContent(List<PcxBillListVO> bills) {
        if(CollectionUtil.isEmpty(bills)) return;

        List<String> billIdList = bills.stream().map(PcxBillListVO::getBillId).collect(Collectors.toList());
        Map<String[], List<PcxBillListVO>> afm$bills = bills.stream().collect(Collectors.groupingBy(bill -> new String[]{bill.getAgyCode(), bill.getFiscal(), bill.getMofDivCode()}));
        afm$bills.forEach((afm, afmBills) -> {
            String _agyCode = afm[0];
            String _fiscal = afm[1];
            String _mofDivCode = afm[2];
            // 查询费用
            List<PcxBillExpTraining> trainings = pcxBillExpTrainingService.selectList(billIdList, _agyCode, _fiscal, _mofDivCode);

            // 将差旅信息及其详情按单据ID分组，便于后续处理
            Map<String, List<PcxBillExpTraining>> trainingMap = trainings.stream().collect(Collectors.groupingBy(PcxBillExpTraining::getBillId));

            // 遍历每个单据，准备其对应的差旅信息及其详情
            bills.forEach(bill -> {
                List<PcxBillExpTraining> trainingList = trainingMap.get(bill.getBillId());

                // 通用报销不返回content
                if (!bill.getItemCode().equals(UNIVERSAL_ITEM_CODE))
                    // 准备数据
                    prepareContent(bill,trainingList);
            });
        });
    }

    /**
     * 准备培训费首页回显数据
     * @param bill
     * @param trainingList
     */
    private void prepareContent(PcxBillListVO bill, List<PcxBillExpTraining> trainingList) {
        if (CollectionUtil.isEmpty(trainingList)) return ;

        Optional<PcxBillExpTraining> first = trainingList.stream().filter(item -> item.getExpenseCode().equals(PcxBillProcessConstant.ExpenseProcessBeanEnum.TRAINING.getCode())).findFirst();
        if (first.isPresent()) {
            PcxBillExpTraining train = first.get();
            PcxBillListVO.BillContentVO content = new PcxBillListVO.BillContentVO();
            content.setStartDate(train.getStartDate());
            content.setEndDate(train.getFinishDate());
            content.setDays(
                    StrUtil.isBlank(train.getStartDate()) || StrUtil.isBlank(train.getFinishDate()) ? 0 :
                            (int) cn.hutool.core.date.DateUtil.between(cn.hutool.core.date.DateUtil.parseDate(train.getStartDate()), cn.hutool.core.date.DateUtil.parseDate(train.getFinishDate()), DateUnit.DAY) + 1
            );

            List<Map<String, Object>> eventInfos = new ArrayList<>();

            Map<String, Object> styleMap01 = new HashMap<>();
            styleMap01.put(PcxConstant.StyleKeyword.FONT_WEIGHT, PcxConstant.StyleKeyword.BOLD);
            Map<String, Object> styleMap02 = new HashMap<>();
            styleMap02.put(PcxConstant.StyleKeyword.COLOR, PcxConstant.StyleKeyword.GREY);

            Map<String, Object> map01 = new HashMap<>();
            map01.put("style",  null);
            map01.put("label",  "在");
            eventInfos.add(map01);

            if (StringUtil.isNotEmpty(train.getRegionName())){
                Map<String, Object> map02 = new HashMap<>();
                map02.put("style",  styleMap01);
                map02.put("label",  train.getRegionName());
                eventInfos.add(map02);
            }

            Map<String, Object> map03 = new HashMap<>();
            map03.put("style",  styleMap01);
            map03.put("label",  "培训");
            eventInfos.add(map03);

            if (train.getPeopleNum() != null){
                Map<String, Object> map04 = new HashMap<>();
                map04.put("style",  styleMap01);
                map04.put("label",  train.getPeopleNum() + "人");
                eventInfos.add(map04);
            }

            if (train.getWorkerNum() != null){
                Map<String, Object> map05 = new HashMap<>();
                map05.put("style",  styleMap02);
                map05.put("label",  "（工作人员" + train.getWorkerNum() + "人）");
                eventInfos.add(map05);
            }

            content.setEventInfos(eventInfos);
            bill.setContent(content);
        }
    }
    /**
     * 设置创建者信息
     * @param billExpTraining 会议费用对象
     */
    private void setCreatorInfo(PcxBillExpTraining billExpTraining) {
        billExpTraining.setCreator(PtyContext.getUsername());
        billExpTraining.setCreatorName(PtyContext.getUsernameCn());
        billExpTraining.setCreatedTime(DateUtil.nowTime());
    }

    /**
     * 设置修改者信息
     * @param billExpTraining 会议费用对象
     */
    private void setModifierInfo(PcxBillExpTraining billExpTraining) {
        billExpTraining.setModifier(PtyContext.getUsername());
        billExpTraining.setModifierName(PtyContext.getUsernameCn());
        billExpTraining.setModifiedTime(DateUtil.nowTime());
    }
}
