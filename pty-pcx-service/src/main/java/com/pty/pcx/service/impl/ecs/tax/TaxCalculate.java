package com.pty.pcx.service.impl.ecs.tax;

import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.entity.bill.PcxExpDetailEcsRel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 计算进项税
 */
@Indexed
@Service
public class TaxCalculate {
    @Autowired
    private List<EcsTaxCalculate> calculateList;

    @PostConstruct
    public void init(){
        for (int i = 1; i < calculateList.size(); i++) {
            calculateList.get(i-1).setNext(calculateList.get(i));
        }
    }

    public void calculate(PcxExpDetailEcsRel ecsRel, PcxBillExpDetailBase detail){
        calculateList.get(0).calculate(ecsRel, detail);
    }
}
