package com.pty.pcx.service.impl.bill.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.pty.pcx.api.bill.PcxBillExpCommonService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.enu.abroad.AbroadRateEnum;
import com.pty.pcx.common.exception.ForbidTipsException;
import com.pty.pcx.common.exception.WarningTipsException;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.qo.bill.PcxBillBalanceQO;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.bill.PcxBillRelationQO;
import com.pty.pcx.qo.bill.PcxBillSettlementQO;
import com.pty.pcx.qo.ecs.EcsExpMatchQO;
import com.pty.pcx.qo.ecs.EcsRelQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailSaveQO;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.BillValidateService;
import com.pty.pcx.util.ExpenseBeanUtil;
import com.pty.pcx.vo.treasurypay.detail.PayDetailVO;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.pty.pcx.common.constant.PcxConstant.UNIVERSAL_ITEM_CODE;
import static com.pty.pcx.common.enu.PositionEnum.MAKE_BILL;

@Service
@Slf4j
@Indexed
public class BillValidateServiceImpl implements BillValidateService {
    @Autowired
    private IWitAuditRuleService witAuditRuleService;
    @Autowired
    private BillMainService billMainService;
    @Autowired
    private PcxBillExpCommonService pcxBillExpCommonService;


    @Override
    public void preData(PcxBillQO qo) {
        //处理主单信息
        BeanUtil.copyProperties(qo, qo.getPcxBill(), CopyOptions.create().setIgnoreNullValue(true).setOverride(false));
        if (StringUtil.isEmpty(qo.getPcxBill().getId())){
            qo.getPcxBill().setId(qo.getBillId());
        }

        //处理费用承担部门
        Optional.ofNullable(qo.getExpenseList())
                .filter(CollectionUtil::isNotEmpty)
                .map(list -> list.stream()
                        .filter(exp -> StringUtil.isNotEmpty(exp.getDepartmentCode()))
                        .collect(Collectors.toMap(
                                PcxBillExpBase::getDepartmentCode,
                                PcxBillExpBase::getDepartmentName,
                                (existing, replacement) -> existing))
                )
                .ifPresent(map -> {
                    qo.getPcxBill().setExpDepartmentCodes(String.join(",", map.keySet()));
                    qo.getPcxBill().setExpDepartmentNames(String.join(",", map.values()));
                });
        //处理赋值bizType
        this.handleBidTypeAssignment(qo);
        //纸电对比状态在其他操作会更新，此处不更新
        qo.getPcxBill().setComparedStatus(null);
        // 如果是借款单或申请单，设置为"无需比对"
        if (BillFuncCodeEnum.LOAN.getCode().equals(qo.getPcxBill().getBillFuncCode()) ||
                BillFuncCodeEnum.APPLY.getCode().equals(qo.getPcxBill().getBillFuncCode())) {
            // 无需比对
            qo.getPcxBill().setComparedStatus(ComparedResultStatus.NO_NEED_CHECK.getCode());
        }
        //制单岗位 （处理录入金额，并将核定金额设置为录入金额）
        if (PositionEnum.MAKE_BILL.getCode().equals(qo.getPositionCode())){
            preMakeBill(qo);
        }
        //财务岗位 （处理核定金额）
        if (PositionEnum.isFinance(qo.getPositionCode())){
            preFinanceAudit(qo);
        }
    }

    /**
     * 处理 bidType 的赋值逻辑
     *
     * @param qo 传入的 PcxBillQO 对象
     */
    private void handleBidTypeAssignment(PcxBillQO qo) {
        PcxBill bill = qo.getPcxBill();
        List<PcxBillExpBase> expenseList = qo.getExpenseList();
        //从expenseList list中获取费用类型代码 expenseCode list<String>
        if (CollectionUtils.isEmpty(expenseList)) {
            return;
        }
        if (bill == null) {
            return; // 如果 bill 为 null，直接返回
        }
        List<String> expenseCodes = expenseList.stream()
                .map(PcxBillExpBase::getExpenseCode)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.toList());
        // 如果 bidType 为 null，则进行处理
        if (bill.getBizType() == null || bill.getBizType() == 0) {
            // 处理逻辑：如果是申请单（apply），事项编码不能为空
            if (BillFuncCodeEnum.APPLY.getCode().equals(qo.getBillFuncCode()) && !expenseCodes.contains(PcxConstant.TRAVEL_EXPENSE_30211)) {
                if (StringUtil.isEmpty(qo.getItemCode())) {
                    log.error("处理单据事bizType信息时,事项编码不能为空");
                }
                if(expenseCodes.contains(PcxConstant.TRAINING_EXPENSE_30216)){
                    qo.getPcxBill().setBizType(ItemBizTypeEnum.TRAINING.getCode());
                    qo.getPcxBill().setBizTypeName(ItemBizTypeEnum.TRAINING.getName());
                }else if (expenseCodes.contains(PcxConstant.MEETING_EXPENSE_30215)){
                    qo.getPcxBill().setBizType(ItemBizTypeEnum.MEETING.getCode());
                    qo.getPcxBill().setBizTypeName(ItemBizTypeEnum.MEETING.getName());
                }else if (expenseCodes.contains(PcxConstant.TREAT_EXPENSE_30217)){
                    qo.getPcxBill().setBizType(ItemBizTypeEnum.INLANDFEE.getCode());
                    qo.getPcxBill().setBizTypeName(ItemBizTypeEnum.INLANDFEE.getName());
                }else if(expenseCodes.contains(PcxConstant.ABROAD_EXPENSE_30212)){
                    qo.getPcxBill().setBizType(ItemBizTypeEnum.ABROAD.getCode());
                    qo.getPcxBill().setBizTypeName(ItemBizTypeEnum.ABROAD.getName());
                }
            }
        }
    }


    private void abroadPreData(PcxBillQO qo) {
        List<PcxBillExpDetailBase> details = Optional.ofNullable(qo.getExpenseDetailList())
                .filter(list -> !list.isEmpty())
                .filter(list -> isAbroadExpense(list.get(0)))
                .orElse(Collections.emptyList());

        if (details.isEmpty()) return;
        qo.getPcxBill().setBizType(ItemBizTypeEnum.ABROAD.getCode());
        BigDecimal[] totals = details.parallelStream()
                .filter(PcxBillExpDetailAbroad.class::isInstance)
                .map(PcxBillExpDetailAbroad.class::cast)
                .map(this::convertCurrency)
                .reduce(
                        new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO},
                        (acc, curr) -> new BigDecimal[]{
                                acc[0].add(curr[0]),
                                acc[1].add(curr[1])
                        },
                        (a, b) -> new BigDecimal[]{
                                a[0].add(b[0]),
                                a[1].add(b[1])
                        });

        qo.getPcxBill().setCheckAmt(totals[0]);
        qo.getPcxBill().setInputAmt(totals[1]);
    }

    // 提取汇率转换逻辑
    private BigDecimal[] convertCurrency(PcxBillExpDetailAbroad detail) {
        if (StringUtil.isEmpty(detail.getCurrency())) {
            log.warn("外币明细[{}]币种为空", detail.getId());
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }

        BigDecimal rate = AbroadRateEnum.getRateByCurrency(detail.getCurrency());
        if (rate == null) {
            log.error("未支持的币种: {}", detail.getCurrency());
            throw new IllegalArgumentException("不支持的货币类型: " + detail.getCurrency());
        }

        return new BigDecimal[]{
                safeDivide(detail.getCheckAmt(), rate),
                safeDivide(detail.getInputAmt(), rate)
        };
    }

    private BigDecimal safeDivide(BigDecimal amount, BigDecimal rate) {
        return Optional.ofNullable(amount)
                .map(a -> a.divide(rate, 2, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
    }

    // 判断是否为出国费的费用明细
    private boolean isAbroadExpense(PcxBillExpDetailBase detail) {
        String expDetailCode = detail.getExpDetailCode();
        return StringUtil.isNotEmpty(expDetailCode)
                && expDetailCode.length() >= 5
                && PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.ABROAD.getCode()
                .equals(expDetailCode.substring(0, 5));
    }

    @Override
    public void validate(PcxBillQO qo) {
        try {
            if (StringUtil.isNotEmpty(qo.getBillId())) {
                PcxBill pcxBill = billMainService.view(qo.getBillId());
                if (pcxBill == null) {
                    throw new ForbidTipsException("当前单据不存在");
                }
            }
            // 处理费用信息
            List<PcxBillExpBase> expenseList = qo.getExpenseList();



            for (PcxBillExpBase expBase : expenseList) {
                if (isClassifyCodePresent(qo, PositionBlockEnum.SPECIFICITY.code)) {
                    validateExpense(expBase, qo);
                }
            }
            // 根据岗位校验
            if (MAKE_BILL.getCode().equals(qo.getPositionCode())) {
                validateMakeBill(qo);
            }
            if (PositionEnum.isFinance(qo.getPositionCode())) {
                validateFinanceAudit(qo);
            }

            // 送审才做稽核校验
            if (Objects.nonNull(qo.getIsApprove()) && qo.getIsApprove() == PubConstant.LOGIC_TRUE) {
                witAuditRuleService.validateRule(qo.getPcxBill(), qo.getPositionCode());
            }
        } catch (WarningTipsException | ForbidTipsException e) {
            log.error("validate error", e);
            throw e;
        } catch (Exception e) {
            log.error("validate error", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private void validateFinanceAudit(PcxBillQO qo) {
        //通用事项未核定费用不能够提交
        if (UNIVERSAL_ITEM_CODE.equals(qo.getItemCode())) {
            List<PcxBillExpCommon> commons = pcxBillExpCommonService.selectList(Collections.singletonList(qo.getBillId()), qo.getAgyCode(), qo.getFiscal(), qo.getMofDivCode());
            if (commons.stream().anyMatch(item -> item.getCheckAmt().compareTo(BigDecimal.ZERO) > 0 && (StringUtil.isEmpty(item.getExpenseCode()) || Objects.equals(item.getExpenseCode(), "*")))) {
                throw new ForbidTipsException("通用事项未核定费用不能够提交");
            }
        }
        //校验经费来源 && 支付明细
        if (CollectionUtil.isEmpty(qo.getClassifyCodes()) || qo.getClassifyCodes().contains(PositionBlockEnum.FUND_SOURCE.getCode())) {
            validateFundSource(qo);
        }
        //申请不校验支付明细
        if (ObjectUtil.notEqual(BillFuncCodeEnum.APPLY.getCode(),qo.getBillFuncCode())){
            validatePayDetail(qo);
        }
    }

    private void validatePayDetail(PcxBillQO qo) {
        if (CollectionUtils.isNotEmpty(qo.getBudget())) {
            List<PcxBillBalanceQO> foundSource = qo.getBudget().stream().filter(item -> BalanceTypeEnum.BUD.getCode().equals(item.getBalanceType())).collect(Collectors.toList());
           //支付明细金额与经费来源金额比较时，应该加上税额的金额
            PayDetailSaveQO payDetail = qo.getPayDetail();
            List<PayDetailVO.PayDetailRow> allRows = Stream.of(
                            payDetail.getSettleBusicard(),
                            payDetail.getSettleCash(),
                            payDetail.getSettleTransfer(),
                            payDetail.getSettleBusiTransfer(),
                            payDetail.getSettleCheque())
                    .filter(Objects::nonNull)
                    .flatMap(list -> list != null ? list.stream() : Stream.empty())
                    .filter(Objects::nonNull).collect(Collectors.toList());

            Map<String, BigDecimal> uk$detailAmt = allRows.stream()
                    .collect(Collectors.toMap(
                            PayDetailVO.PayDetailRow::getBalanceUk,
                            row -> row.getInputAmt().add(row.getTaxAmt()),
                            BigDecimal::add
                    ));
            if (uk$detailAmt.size() != foundSource.size()) {
                throw new ForbidTipsException(String.format("支付明细所用指标%d条和经费来源%d条不匹配", uk$detailAmt.size(), qo.getBudget().size()));
            }
            boolean matchOfFundAndPay = foundSource.stream().allMatch(budget -> budget.getUsedAmt().equals(uk$detailAmt.get(budget.getBalanceUK())));
            if (!matchOfFundAndPay) {
                throw new ForbidTipsException("支付明细录入金额与经费来源本次使用金额不匹配");
            }
        }else {
            BigDecimal inputTotal = Stream.of(qo.getPayDetail().getSettleBusicard(),
                            qo.getPayDetail().getSettleCash(),
                            qo.getPayDetail().getSettleTransfer(),
                            qo.getPayDetail().getSettleBusiTransfer(),
                            qo.getPayDetail().getSettleCheque())
                    .filter(Objects::nonNull).flatMap(Collection::stream)
                    .peek(item->item.setInputAmt(item.getCheckAmt()))
                    .filter(Objects::nonNull).map(PayDetailVO.PayDetailRow::getInputAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            Assert.state(inputTotal.compareTo(qo.getPcxBill().getCheckAmt()) == 0, "结算金额与付款金额不一致");
        }
    }

    private void validateMakeBill(PcxBillQO qo) {
        //冲销借款校验
        if (CollectionUtil.isEmpty(qo.getClassifyCodes()) || qo.getClassifyCodes().contains(PositionBlockEnum.LOAN.getCode())) {
            validateLoan(qo);
        }
        //如果有经费来源校验经费来源
        if (CollectionUtil.isEmpty(qo.getClassifyCodes()) || qo.getClassifyCodes().contains(PositionBlockEnum.FUND_SOURCE.getCode())) {
//            validateFundSource(qo);
        }
        //结算金额校验
        if (CollectionUtil.isEmpty(qo.getClassifyCodes()) || qo.getClassifyCodes().contains(PositionBlockEnum.SETTLEMENT.getCode())) {
            validateSettlement(qo);
        }
    }

    private void validateSettlement(PcxBillQO qo) {
        //单据的结算金额不能大于单据金额
        Map<String, List<PcxBillSettlementQO>> settlement = qo.getSettlement();
        if (CollectionUtil.isNotEmpty(settlement)) {
            BigDecimal total = settlement.values().stream().flatMap(Collection::stream).map(PcxBillSettlementQO::getInputAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (total.compareTo(qo.getPcxBill().getInputAmt().subtract(qo.getPcxBill().getLoanAmt())) > 0) {
                throw new ForbidTipsException("结算方式金额不能超过单据申请金额");
            }
        }
    }

    private void validateLoan(PcxBillQO qo) {
        //冲销借款的金额不能大于单据金额
        List<PcxBillRelationQO> loan = qo.getLoan();
        if (CollectionUtil.isNotEmpty(loan)) {
            BigDecimal total = loan.stream().map(PcxBillRelationQO::getUsedAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (total.compareTo(qo.getPcxBill().getInputAmt()) > 0) {
                throw new ForbidTipsException("冲销借款金额不能大于单据金额");
            }
        }
    }

    private boolean isClassifyCodePresent(PcxBillQO qo, String code) {
        return CollectionUtil.isNotEmpty(qo.getClassifyCodes()) && qo.getClassifyCodes().contains(code);
    }

    private void validateExpense(PcxBillExpBase expBase, PcxBillQO qo) {
        BillExpenseService<PcxBillExpBase> billExpenseService = ExpenseBeanUtil.getBean(expBase.getExpenseCode(), qo.getPcxBill().getBizType());
        if (billExpenseService != null) {
            CheckMsg<Void> billExpenseValidate = billExpenseService.validate(expBase, qo.getPcxBill().getBillFuncCode());
            if (!billExpenseValidate.isSuccess()) {
                throw new ForbidTipsException(billExpenseValidate.getMsgInfo());
            }
        }
    }



    private void validateFundSource(PcxBillQO qo) {
        List<PcxBillBalanceQO> budgets = qo.getBudget();
        if (CollectionUtil.isEmpty(budgets)) return;
        //校验必填项
        boolean b = budgets.stream().anyMatch(item -> StringUtil.isEmpty(item.getExpenseCode())
                || StringUtil.isEmpty(item.getDepartmentCode())
                || StringUtil.isEmpty(item.getBalanceNo())
                || Objects.isNull(item.getUsedAmt())
                || item.getUsedAmt().compareTo(BigDecimal.ZERO) == 0

        );
        // 申请的经费来源可以为0 （借款和报销不能为0）
        if (b && ObjectUtil.notEqual(BillFuncCodeEnum.APPLY.getCode(),qo.getBillFuncCode())) {
            throw new RuntimeException("经费来源数据不能为空");
        }

        BigDecimal budgetAmt = budgets.stream()
                .filter(item -> Objects.equals(item.getBalanceType(), BalanceTypeEnum.BUD.getCode()))
                .map(PcxBillBalanceQO::getUsedAmt)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        // 申请的经费来源不为0时候需校验（借款、报销都不为0）
        if (budgetAmt.compareTo(qo.getPcxBill().getCheckAmt()) != 0) {
            throw new RuntimeException("经费来源有未分配的金额，请确定费用类型，并分配金额");
        }
    }

    private void validatePaymentDetails(PcxBillQO qo) {
        PayDetailVO payDetail = qo.getPayDetail();
        if (payDetail == null) {
            return;
        }
        List<PayDetailVO.PayDetailRow> allSettle = Stream.of(
                cn.hutool.core.collection.CollectionUtil.defaultIfEmpty(payDetail.getSettleBusicard(), new ArrayList<>()),
                cn.hutool.core.collection.CollectionUtil.defaultIfEmpty(payDetail.getSettleCash(), new ArrayList<>()),
                cn.hutool.core.collection.CollectionUtil.defaultIfEmpty(payDetail.getSettleCheque(), new ArrayList<>()),
                cn.hutool.core.collection.CollectionUtil.defaultIfEmpty(payDetail.getSettleTransfer(), new ArrayList<>())
        ).flatMap(Collection::stream).collect(Collectors.toList());
        allSettle = allSettle.stream().filter(a -> a.getInputAmt() != null && a.getInputAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(allSettle)) {
            return;
        }
        BigDecimal total = allSettle.stream().map(PayDetailVO.PayDetailRow::getInputAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (total.compareTo(qo.getPcxBill().getCheckAmt().subtract(qo.getPcxBill().getLoanAmt())) != 0) {
            throw new RuntimeException("支付明细金额与单据核定金额不一致");
        }
    }


    /**
     * 财务岗数据预处理
     *
     * @param qo
     */
    private void preFinanceAudit(PcxBillQO qo) {
        List<PcxBillExpBase> newExpenses = qo.getExpenseList();
        List<PcxBillExpDetailBase> newExpenseDetails = qo.getExpenseDetailList();
        EcsExpMatchQO ecsExpMatch = qo.getEcsExpMatch();
        List<PcxBillBalanceQO> fundSource = qo.getBudget();

        //财务岗位数据预处理
        PcxBill oldPcxbill = billMainService.view(qo.getBillId());
        if (oldPcxbill == null) {
            throw new ForbidTipsException("当前单据不存在");
        }
        //财务岗的冲销借款从关联指标上拿,此处不设置冲销借款
        qo.setLoan(null);
        // 金额汇总 detail->expense->bill 金额汇总到 bill上的inputAmt 和 checkAmt
        if (!Objects.equals(ItemBizTypeEnum.COMMON.getCode(),oldPcxbill.getBizType())){
            //提交的单子从明细上汇总金额
            newExpenses.forEach(expense -> {
                expense.setCheckAmt(BigDecimal.ZERO);
                //获取当前明细的费用
                List<PcxBillExpDetailBase> expDetailBases = newExpenseDetails.stream().filter(expenseDetail -> expenseDetail.getExpDetailCode().startsWith(expense.getExpenseCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(expDetailBases)) {
                    BigDecimal checkAmt = expDetailBases.stream().filter(item -> item.getCheckAmt() != null && item.getCheckAmt().compareTo(BigDecimal.ZERO) > 0).map(PcxBillExpDetailBase::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    expense.setCheckAmt(checkAmt);
                }
            });
            List<PcxBillExpBase> expBasesCheck = newExpenses.stream().filter(item -> item.getCheckAmt() != null && item.getCheckAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(expBasesCheck)) {
                qo.getPcxBill().setCheckAmt(expBasesCheck.stream().map(PcxBillExpBase::getCheckAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            }

        }
        //通用事项没有费用从票上取钱
        if (Objects.equals(ItemBizTypeEnum.COMMON.getCode(),oldPcxbill.getBizType())
                && Objects.nonNull(ecsExpMatch)
                && CollectionUtils.isNotEmpty(ecsExpMatch.getEcsRelList())){
            BigDecimal checkAmt = BigDecimal.ZERO;
            for (EcsRelQO ecsRelQO : ecsExpMatch.getEcsRelList()) {
                checkAmt = checkAmt.add(ecsRelQO.getCheckAmt());
            }
            qo.getPcxBill().setCheckAmt(checkAmt);
        }

        if (CollectionUtil.isNotEmpty(fundSource)) {
            qo.getPcxBill().setProjectCodes(fundSource.stream().map(PcxBillBalanceQO::getProjectCode).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.joining(",")));
            qo.getPcxBill().setProjectNames(fundSource.stream().map(PcxBillBalanceQO::getProjectName).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.joining(",")));
            //申请的核定金额需要从指标上录入
            if (qo.getPcxBill().getBillFunc().equals(BillFuncCodeEnum.APPLY.getCode())) {
                //单据的核定金额没确定，但是已经有了预算指标的金额，所以这里需要把预算指标的金额汇总到单据上
                List<PcxBillBalanceQO> fundSources = fundSource.stream().filter(item -> item.getUsedAmt() != null && item.getUsedAmt().compareTo(BigDecimal.ZERO) > 0 && item.getBalanceType().equals(BalanceTypeEnum.BUD.getCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(fundSources)) {
                    qo.getPcxBill().setCheckAmt(fundSources.stream().map(PcxBillBalanceQO::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
            //财务端会修改冲销金额 汇总冲销金额
            List<PcxBillBalanceQO> loanBalance = fundSource.stream()
                    .filter(item -> item.getUsedAmt() != null
                            && item.getUsedAmt().compareTo(BigDecimal.ZERO) > 0
                            && item.getBalanceType().equals(BalanceTypeEnum.BUD.getCode())
                            && BillFuncCodeEnum.LOAN.getCode().equals(item.getBalanceSource())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(loanBalance)) {
                qo.getPcxBill().setLoanAmt(loanBalance.stream().map(PcxBillBalanceQO::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
    }

    /**
     * 填报岗数据预处理
     *
     * @param qo
     */
    private void preMakeBill(PcxBillQO qo) {
        //制单岗位数据预处理
        if (StringUtil.isEmpty(qo.getBillId())) {
            if (CollectionUtil.isNotEmpty(qo.getLoan())) {
                List<PcxBillRelationQO> loans = qo.getLoan().stream().filter(item -> item.getUsedAmt() != null && item.getUsedAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(loans)) {
                    qo.getPcxBill().setLoanAmt(loans.stream().map(PcxBillRelationQO::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                } else {
                    qo.getPcxBill().setLoanAmt(BigDecimal.ZERO);
                }
            }
            qo.getPcxBill().setInputAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getInputAmt(), BigDecimal.ZERO));
            //填报岗位时核定金额为录入金额
            qo.getPcxBill().setCheckAmt(qo.getPcxBill().getInputAmt());
            qo.getPcxBill().setLoanAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getLoanAmt(), BigDecimal.ZERO));
            qo.getPcxBill().setTaxAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getTaxAmt(), BigDecimal.ZERO));
        } else {
            //更新
            PcxBill oldPcxbill = billMainService.view(qo.getBillId());
            if (oldPcxbill == null) {
                throw new ForbidTipsException("当前单据不存在");
            }
            if (!oldPcxbill.getBillStatus().equals(BillStatusEnum.SAVE.getCode())) {
                throw new ForbidTipsException("当前单据状态不允许修改");
            }
            if (CollectionUtil.isNotEmpty(qo.getLoan())) {
                List<PcxBillRelationQO> loans = qo.getLoan().stream().filter(item -> item.getUsedAmt() != null && item.getUsedAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(loans)) {
                    qo.getPcxBill().setLoanAmt(loans.stream().map(PcxBillRelationQO::getUsedAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                } else {
                    qo.getPcxBill().setLoanAmt(BigDecimal.ZERO);
                }
            }

            if (!qo.getBillFuncCode().equals(BillFuncCodeEnum.EXPENSE.getCode())) {
                qo.getPcxBill().setInputAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getInputAmt(), oldPcxbill.getInputAmt()));
                qo.getPcxBill().setCheckAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getInputAmt(), oldPcxbill.getInputAmt()));
                qo.getPcxBill().setLoanAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getLoanAmt(), oldPcxbill.getLoanAmt()));
                qo.getPcxBill().setTaxAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getTaxAmt(), BigDecimal.ZERO));

            } else {
                //报销不可以操作金额 直接可以选择老的
                qo.getPcxBill().setInputAmt(oldPcxbill.getInputAmt());
                qo.getPcxBill().setCheckAmt(oldPcxbill.getCheckAmt());
                qo.getPcxBill().setLoanAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getLoanAmt(), oldPcxbill.getLoanAmt()));
                qo.getPcxBill().setTaxAmt(ObjectUtil.defaultIfNull(qo.getPcxBill().getTaxAmt(), BigDecimal.ZERO));

            }
        }
    }
}
