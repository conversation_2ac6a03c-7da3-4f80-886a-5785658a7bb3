package com.pty.pcx.service.impl.bas;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pty.pcx.api.bas.PcxSettlementRuleExpService;
import com.pty.pcx.api.bas.PcxSettlementRuleService;
import com.pty.pcx.dao.bas.PcxSettlementRuleExpDao;
import com.pty.pcx.entity.bas.PcxSettlementRuleExp;
import com.pty.pcx.qo.bas.PcxSettlementRuleExpQO;
import com.pty.pcx.vo.bas.PcxSettlementRuleExpVO;
import com.pty.pcx.entity.bas.PcxSettlementRule;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 特殊费用结算规则设置(PcxBasSettlementExpSetting)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-09 17:42:51
 */
@Slf4j
@Indexed
@Service("PcxSettlementRuleExpService")
public class PcxSettlementRuleExpServiceImpl implements PcxSettlementRuleExpService {

    @Autowired
    private PcxSettlementRuleExpDao pcxSettlementRuleExpDao;
    @Autowired
    private PcxSettlementRuleService pcxSettlementRuleService;

    @Override
    public List<PcxSettlementRuleExp> selectAll(PcxSettlementRuleExpQO qo) {
        return pcxSettlementRuleExpDao.selectList(Wrappers.lambdaQuery(PcxSettlementRuleExp.class)
                .eq(PcxSettlementRuleExp::getAgyCode, qo.getAgyCode())
                .eq(PcxSettlementRuleExp::getFiscal, qo.getFiscal())
                .eq(PcxSettlementRuleExp::getMofDivCode, qo.getMofDivCode()));
    }

    @Override
    public Response<PageInfo<?>> selectWithPage(PcxSettlementRuleExpQO qo) {
        PageInfo<PcxSettlementRuleExp> pages = PageHelper.startPage(qo.getPageIndex(),qo.getPageSize()).doSelectPageInfo(()->{
            pcxSettlementRuleExpDao.selectList(Wrappers.lambdaQuery(PcxSettlementRuleExp.class)
                    .eq(PcxSettlementRuleExp::getAgyCode,qo.getAgyCode())
                    .eq(PcxSettlementRuleExp::getFiscal,qo.getFiscal())
                    .eq(PcxSettlementRuleExp::getMofDivCode,qo.getMofDivCode()));
        });
        Map<String, String> stringMap = getSettlementMap(qo);
        pages.setList(pages.getList().stream().map(item->{
            PcxSettlementRuleExpVO pcxBillSettlementVO = new PcxSettlementRuleExpVO();
            BeanUtils.copyProperties(item,pcxBillSettlementVO);
            //是否有其他数据展示
            pcxBillSettlementVO.setAllowSettlementName(createName(stringMap,pcxBillSettlementVO.getAllowSettlement()));
            pcxBillSettlementVO.setDefaultSettlementName(createName(stringMap,pcxBillSettlementVO.getDefaultSettlement()));
            return pcxBillSettlementVO;
        }).collect(Collectors.toList()));
        return Response.success(pages);
    }



    @Override
    public void save(PcxSettlementRuleExpQO pcxBasSettlementExpSettingQO) {
        PcxSettlementRuleExp pcxSettlementRuleExp = new PcxSettlementRuleExp();
        BeanUtils.copyProperties(pcxBasSettlementExpSettingQO, pcxSettlementRuleExp);
        pcxSettlementRuleExp.setId(IDGenerator.id());
        pcxSettlementRuleExpDao.insert(pcxSettlementRuleExp);
    }

    @Override
    public void update(PcxSettlementRuleExpQO pcxBasSettlementExpSettingQO) {
        PcxSettlementRuleExp result = pcxSettlementRuleExpDao.selectById(pcxBasSettlementExpSettingQO.getId());
        if (Objects.isNull(result)) return;
        PcxSettlementRuleExp pcxSettlementRuleExp = new PcxSettlementRuleExp();
        BeanUtils.copyProperties(pcxBasSettlementExpSettingQO, pcxSettlementRuleExp);
        pcxSettlementRuleExp.setId(result.getId());
        pcxSettlementRuleExpDao.updateById(pcxSettlementRuleExp);
    }

    @Override
    public Response<PcxSettlementRuleExpVO> view(PcxSettlementRuleExpQO qo) {
        PcxSettlementRuleExp pcxSettlementRuleExp = pcxSettlementRuleExpDao.selectOne(Wrappers
                .lambdaQuery(PcxSettlementRuleExp.class)
                .eq(PcxSettlementRuleExp::getId,qo.getId())
                .eq(PcxSettlementRuleExp::getAgyCode,qo.getAgyCode())
                .eq(PcxSettlementRuleExp::getFiscal,qo.getFiscal())
                .eq(PcxSettlementRuleExp::getMofDivCode,qo.getMofDivCode()));
        if (Objects.isNull(pcxSettlementRuleExp)) return Response.fail();
        PcxSettlementRuleExpVO pcxBasSettlementExpSettingVO = new PcxSettlementRuleExpVO();
        BeanUtils.copyProperties(pcxSettlementRuleExp,pcxBasSettlementExpSettingVO);
        Map<String, String> stringMap = getSettlementMap(pcxSettlementRuleExp);
        pcxBasSettlementExpSettingVO.setAllowSettlementName(createName(stringMap,pcxBasSettlementExpSettingVO.getAllowSettlement()));
        pcxBasSettlementExpSettingVO.setDefaultSettlementName(createName(stringMap,pcxBasSettlementExpSettingVO.getDefaultSettlement()));
        return Response.success(pcxBasSettlementExpSettingVO);
    }


    @Override
    public Response<PcxSettlementRuleExpVO> selectByExpenseCode(PcxSettlementRuleExpQO qo) {
        PcxSettlementRuleExp pcxSettlementRuleExp = pcxSettlementRuleExpDao.selectOne(Wrappers
                .lambdaQuery(PcxSettlementRuleExp.class)
                .eq(PcxSettlementRuleExp::getExpenseCode,qo.getExpenseCode())
                .eq(PcxSettlementRuleExp::getAgyCode,qo.getAgyCode())
                .eq(PcxSettlementRuleExp::getFiscal,qo.getFiscal())
                .eq(PcxSettlementRuleExp::getMofDivCode,qo.getMofDivCode()));
        if (Objects.isNull(pcxSettlementRuleExp)) return Response.fail();
        PcxSettlementRuleExpVO pcxBasSettlementExpSettingVO = new PcxSettlementRuleExpVO();
        BeanUtils.copyProperties(pcxSettlementRuleExp,pcxBasSettlementExpSettingVO);
        Map<String, String> stringMap = getSettlementMap(pcxSettlementRuleExp);
        pcxBasSettlementExpSettingVO.setAllowSettlementName(createName(stringMap,pcxBasSettlementExpSettingVO.getAllowSettlement()));
        pcxBasSettlementExpSettingVO.setDefaultSettlementName(createName(stringMap,pcxBasSettlementExpSettingVO.getDefaultSettlement()));
        return Response.success(pcxBasSettlementExpSettingVO);
    }

    private Map<String, String>  getSettlementMap(PcxSettlementRuleExp pcxSettlementRuleExp) {
        PcxSettlementRule setting = new PcxSettlementRule();
        BeanUtils.copyProperties(pcxSettlementRuleExp,setting);
        List<PcxSettlementRule> selectList = pcxSettlementRuleService.selectAll(setting);
        return selectList.stream().collect(Collectors.toMap(PcxSettlementRule::getSettlementType, PcxSettlementRule::getSettlementName));
    }

    @Override
    public void delete(PcxSettlementRuleExpQO qo) {
        PcxSettlementRuleExp pcxSettlementRuleExp = pcxSettlementRuleExpDao.selectOne(Wrappers
                .lambdaQuery(PcxSettlementRuleExp.class)
                .eq(PcxSettlementRuleExp::getId,qo.getId())
                .eq(PcxSettlementRuleExp::getAgyCode,qo.getAgyCode())
                .eq(PcxSettlementRuleExp::getFiscal,qo.getFiscal())
                .eq(PcxSettlementRuleExp::getMofDivCode,qo.getMofDivCode()));
        if (Objects.isNull(pcxSettlementRuleExp)) return;
        pcxSettlementRuleExpDao.deleteById(qo.getId());
    }


    private String createName(Map<String, String> stringMap, String allowSettlement) {
        String[] codes = StringUtil.nullToEmpty(allowSettlement).split(",");
        //根据codes获取map中的name,拼接成一个 、分隔的字符串
        List<String> names = Arrays.asList(codes).stream().map(code->stringMap.get(code)).collect(Collectors.toList());
        return StrUtil.join("、",names);
    }
}
