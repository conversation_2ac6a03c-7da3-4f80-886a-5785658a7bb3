package com.pty.pcx.service.impl.wit.func;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Map;

@Slf4j
public class FnCompareDate extends BFunction {
    public static final String FUNC_NAME = "compareDate";

    @Override
    public String getName() {
        return FUNC_NAME;
    }

    public String method() {
        return "compareDate(date1, date2)";
    }

    @Override
    public String methodName() {
        return "比较两个日期的大小";
    }

    @Override
    public String desc() {
        return "比较两个日期, 支持单个和多个，表达式日期1大于或等于日期2返回true 如：compareDate(日期1(list), 日期2), ";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        Object x = arg1.getValue(env);
        Object y = arg2.getValue(env);

        if (x instanceof String) {
            if (StringUtil.isEmpty(x) || StringUtil.isEmpty(y)) {
                return AviatorBoolean.valueOf(true);
            }
            x = convertToStandardFormat(x.toString());
            y = convertToStandardFormat(y.toString());
            DateTime xDate = DateUtil.parse(x.toString());
            DateTime yDate = DateUtil.parse(y.toString());
            if (xDate.compareTo(yDate) > 0) {
                return AviatorBoolean.valueOf(true);
            }
        }

        if (x instanceof Collection) {
            Collection xCollection = (Collection) x;
            for (Object o : xCollection) {
                if (StringUtil.isEmpty(o) || StringUtil.isEmpty(y)) {
                    return AviatorBoolean.valueOf(true);
                }

                o = convertToStandardFormat(o.toString());
                y = convertToStandardFormat(y.toString());

                DateTime xDate = DateUtil.parse(o.toString());
                DateTime yDate = DateUtil.parse(y.toString());
                if (xDate.compareTo(yDate) > 0) {
                    return AviatorBoolean.valueOf(true);
                }
            }
            return AviatorBoolean.valueOf(false);
        }

        return AviatorBoolean.valueOf(false);
    }



    private String convertToStandardFormat(String time) {
        if (time.matches("\\d{2}_\\d{2}_\\d{2}")) {
            // HH_mm_ss to HH:mm:ss
            return time.replace('_', ':');
        } else if ((time.matches("\\d{2}_\\d{2}"))) {
            // HH_mm to HH:mm
            return time.replace('_', ':');
        } else if (time.matches("\\d{4}_\\d{2}_\\d{2}")) {
            // yyyy_MM_dd to yyyy-MM-dd
            return time.substring(0, 4) + "-" + time.substring(5, 7) + "-" + time.substring(8);
        } else if (time.matches("\\d{4}_\\d{2}_\\d{2}_\\d{2}")) {
            // yyyy_MM_dd_HH to yyyy-MM-dd_HH
            return time.substring(0, 10).replace('_', '-') + "_" + time.substring(11);
        } else if (time.matches("\\d{4}_\\d{2}_\\d{2}_\\d{2}_\\d{2}")) {
            // yyyy_MM_dd_HH_mm to yyyy-MM-dd_HH:mm
            return time.substring(0, 10).replace('_', '-') + "_" + time.substring(11).replace('_', ':');
        } else {
            log.warn("Unsupported time format: {}", time);
            return time;
        }
    }
}



