package com.pty.pcx.service.impl.print;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bstek.ureport.definition.Orientation;
import com.bstek.ureport.definition.Paper;
import com.bstek.ureport.definition.PaperType;
import com.bstek.ureport.definition.ReportDefinition;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.RectangleReadOnly;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.pty.ecs.common.enu.EcsEnum;
import com.pty.fileservice.api.IPaAttachService;
import com.pty.fileservice.api.IPubFileService;
import com.pty.fileservice.common.util.FileUploadUtil;
import com.pty.fileservice.entity.PaAttach;
import com.pty.pcx.api.bill.*;
import com.pty.pcx.api.print.IPcxPrintService;
import com.pty.pcx.api.printtemplate.IPcxPrintTemplateService;
import com.pty.pcx.api.setting.IBusinessRuleOptionService;
import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.api.workflow2.IProcessService;
import com.pty.pcx.common.constant.BusinessRuleEnum;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.BillFuncCodeEnum;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.common.enu.PcxExpAttachRelType;
import com.pty.pcx.common.enu.PositionEnum;
import com.pty.pcx.common.enu.wit.SettlementTypeEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dao.bill.PcxBillExpAttachRelDao;
import com.pty.pcx.dao.bill.PcxExpDetailEcsRelDao;
import com.pty.pcx.dto.pa.PaUserDTO;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.meeting.PcxBillExpMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpTraining;
import com.pty.pcx.entity.print.PcxPrintResult;
import com.pty.pcx.entity.printtemplate.PcxPrintTemplate;
import com.pty.pcx.entity.setting.PaOption;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.print.PcxPrintBillQO;
import com.pty.pcx.qo.print.PrintContentQO;
import com.pty.pcx.qo.printtemplate.PcxPrintTemplateQO;
import com.pty.pcx.qo.setting.PaOptionQO;
import com.pty.pcx.qo.workflow2.ProcessHistoryQO;
import com.pty.pcx.service.impl.ureport.PcxUReportBillDataSource;
import com.pty.pcx.util.FileUtil;
import com.pty.pcx.vo.ApprovalProcessDefinitionVO;
import com.pty.pcx.vo.PaOptionVO;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pcx.vo.bill.PcxBillSettlementVO;
import com.pty.pcx.vo.bill.PcxBillVO;
import com.pty.pcx.vo.ecs.EcsExpMatchVO;
import com.pty.pcx.vo.ecs.EcsRelVO;
import com.pty.pcx.vo.workflow2.ProcessCompositeVO;
import com.pty.pcx.vo.workflow2.ProcessHistoryVO;
import com.pty.pcx.vo.workflow2.ProcessHistoryWithDelegateVO;
import com.pty.pub.common.bean.Response;
import com.pty.pub.common.constant.PubConstant;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.DateUtil;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import com.pty.ureport2.api.IReportFileService;
import com.pty.workflow2.extend.pcx.PcxNodeEnum;
import com.pty.workflow2.extend.pcx.PcxProcessDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.ofdrw.converter.export.ImageExporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.pty.pcx.common.constant.BusinessRuleEnum.BusinessOptionEnum.APPROVAL_ENABLE;

@Service
@Indexed
@Slf4j
public class PcxPrintServiceImpl implements IPcxPrintService {

    @Autowired
    private PcxBillExpAttachRelDao pcxBillExpAttachRelDao;

    @Autowired
    private PcxExpDetailEcsRelDao pcxExpDetailEcsRelDao;

    @Autowired
    private IPaAttachService paAttachService;

    @Autowired
    private IBusinessRuleOptionService businessRuleOptionService;

    @Autowired(required = false)
    private IReportFileService reportFileService;

    @Autowired
    private PcxBillService pcxBillService;

    @Autowired
    private IPubFileService pubFileService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private IPcxPrintTemplateService pcxPrintTemplateService;

    @Autowired
    private IProcessService processService;

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;

    @Autowired
    private PcxBillExpMeetingService pcxBillExpMeetingService;

    @Autowired
    private PcxBillExpTrainingService pcxBillExpTrainingService;

    @Autowired
    private PcxBillExpInlandfeeService pcxBillExpInlandfeeService;

    @Autowired
    private IWitAuditRuleService witAuditRuleService;

    private static PcxExpDetailEcsRel defaultEcsRel = new PcxExpDetailEcsRel();

    static {
        defaultEcsRel.setEcsBillType("");
        defaultEcsRel.setEcsBillKind("");
        defaultEcsRel.setCadfAmt(BigDecimal.ZERO);
        defaultEcsRel.setIsRealName(-1);
    }
    @Override
    public CheckMsg<String> getPrintPdf(PcxPrintBillQO qo) {
        if(ObjectUtils.isEmpty(qo)){
            return CheckMsg.fail("请勾选需要打印的单据");
        }
        CheckMsg<PcxBillVO> view = pcxBillService.view(qo.getId());
        if(!view.isSuccess()){
            return CheckMsg.fail(view.getMsgInfo());
        }
        // 处理生成pdf
        String attachId = processPrintPdf(view.getData(), qo);
        CheckMsg<String> result = CheckMsg.success();
        result.setData(attachId);
        return result;
    }

    private String  processPrintPdf(PcxBillVO pcxBillVO, PcxPrintBillQO qo) {
        return processPrintPdf(pcxBillVO,qo,Boolean.FALSE);
    }

    private String  processPrintPdf(PcxBillVO pcxBillVO, PcxPrintBillQO qo,Boolean isBatch) {
        boolean isWxApp = StringUtil.isEmpty(qo.getPrintContent()) && StringUtil.isEmpty(qo.getTemplateCode());
        PaAttach paAttach = null;
        String fileName = IDGenerator.id();
        Map<String, String> optionMap = getOptionMap(qo);
        String printBill = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        // 是否为粘贴单模式
        boolean isPasteBill = PcxConstant.PrintBillType.PASTE_BILL.equals(printBill);
        String templName = isWxApp? optionMap.get(BusinessRuleEnum.BusinessOptionEnum.PRINT_TMPL.getOptCode()):getTemplate(qo,pcxBillVO,isBatch);
        if(isBatch){
           isPasteBill = Boolean.FALSE;
        }
        if(isUniversalBill(pcxBillVO,isPasteBill)){
            // 如果是通用报销单，使用通用报销单模版
            templName = "universal.ureport.xml";
        }
        if (reportFileService.fileIsExist(templName) == PubConstant.LOGIC_FALSE) {
            log.warn(">>>生成pdf接口没有找到该ureport文件:{}", templName);
            throw new RuntimeException("生成pdf接口没有找到该ureport文件");
        }
        paAttach = makeBillPdf(pcxBillVO, templName, fileName, printBill,isWxApp);
        if (isWxApp && isPasteBill) {
            return paAttach.getAttachId();
        }
        // 打印发票/附件设置
        AtomicReference<String> isPrintAttachment = new AtomicReference<>(PubConstant.STR_LOGIC_TRUE);// 是否打印附件（默认打印）
        AtomicReference<String> isPrintInvoice = new AtomicReference<>(PcxConstant.PrintExpenseOption.PRINT_ALL);// 是否打印发票（默认打印）
        List<PrintContentQO> printContentQOList = JSONObject.parseArray(qo.getPrintContent(), PrintContentQO.class);
        if(CollectionUtil.isNotEmpty(printContentQOList)){
            printContentQOList.forEach(printContentQO -> {
                if (PcxConstant.PrintBillType.ATTACHMENT.equals(printContentQO.getPrintBillType())) {// 附件
                    if (PubConstant.STR_LOGIC_FALSE.equals(printContentQO.getIsPrint())) {// 不打印
                        isPrintAttachment.set(PubConstant.STR_LOGIC_FALSE);
                    }
                } else if (PcxConstant.PrintBillType.EXPENSE_BILL.equals(printContentQO.getPrintBillType())) {// 发票
                    if (PubConstant.STR_LOGIC_FALSE.equals(printContentQO.getIsPrint())) {// 不打印
                        isPrintInvoice.set(PcxConstant.PrintExpenseOption.NOT_PRINT);
                    }
                }
            });

        }
        if (!isWxApp && PubConstant.STR_LOGIC_FALSE.equals(isPrintAttachment.get()) && PcxConstant.PrintExpenseOption.NOT_PRINT.equals(isPrintInvoice.get())) {
            return paAttach.getAttachId();
        }
        // 根据纸张大小和方向，调整整体pdf模版信息
        ReportDefinition reportDefinition = reportFileService.getTemplateInfo(templName, new HashMap<>(), null);
        // 纸张
        Paper paper = reportDefinition.getPaper();
        // 纸张类型
        PaperType paperType = paper.getPaperType();
        // 纸张方向
        Orientation orientation = paper.getOrientation();
        // 组装附件
        String invoiceOption =  isWxApp ? optionMap.get(BusinessRuleEnum.BusinessOptionEnum.INVOICE_OPTION.getOptCode()) : isPrintInvoice.get();
        String attachmentOption = isWxApp ?  optionMap.get(BusinessRuleEnum.BusinessOptionEnum.ATTACHMENT_OPTION.getOptCode()) : isPrintAttachment.get();
        List<PaAttach> orgFileAttach = getAttachList(pcxBillVO,invoiceOption,attachmentOption,isWxApp);
        if (CollectionUtil.isEmpty(orgFileAttach)) {
            return paAttach.getAttachId();
        }
        byte[] billPdf = pubFileService.fileDownload(paAttach);
        // 存在附件的情况下，合并成一个pdf文件.itext
        List<byte[]> imageBytes = new ArrayList<>();
        imageBytes.add(billPdf);
        for (PaAttach fileAttach : orgFileAttach) {
            byte[] bytes = pubFileService.fileDownload(fileAttach);
            if (bytes.length > 4) {
                imageBytes.add(bytes);
            }
        }
        fileName = IDGenerator.id();
        byte[] bytes = convertImagesToPdf(imageBytes,paperType,orientation,fileName);
        try {
            paAttach = this.upLoadPdf(fileName, bytes);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("PDF upload failed", e);
        }
        return paAttach.getAttachId();
    }

    /***
     * 判断是否为报销单打印并且是通用报销
     * @param pcxBillVO
     * @param isPasteBill
     * @return
     */
    private boolean isUniversalBill(PcxBillVO pcxBillVO, boolean isPasteBill) {
        return !ObjectUtils.isEmpty(pcxBillVO.getBasicInfo())
                && !isPasteBill
                && PcxConstant.UNIVERSAL_ITEM_CODE.equals(pcxBillVO.getBasicInfo().getItemCode());
    }

    /****
     * 获取到所有的业务规则
     * @param qo
     * @return
     */
    private Map<String, String> getOptionMap(PcxPrintBillQO qo) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(qo.getMofDivCode());
        paOptionQO.setFiscal(Integer.parseInt(qo.getFiscal()));
        paOptionQO.setAgyCode(qo.getAgyCode());
        List<PaOptionVO> optionVOList = businessRuleOptionService.getAgyOpt(paOptionQO);
        return optionVOList.stream()
                .collect(Collectors.toMap(PaOptionVO::getOptCode, PaOptionVO::getOptValue));
    }

    private PaAttach makeBillPdf(PcxBillVO pcxBillVO, String templName, String fileName, String printBill,boolean isWxApp) {
        PaAttach paAttach = new PaAttach();
        PcxPrintResult pcxPrintResult = new PcxPrintResult();
        List<PcxBillExpDetailBase> expenseDetails = pcxBillVO.getExpenseDetail();
        PcxBill basicInfo = pcxBillVO.getBasicInfo();
        // 构建 specificityList 数据
        List<Map<String, Object>> specificityList = buildSpecificityList(pcxBillVO, printBill, expenseDetails,isWxApp);
        List<PcxBillSettlementVO> settlements = pcxBillVO.getSettlements();
        PcxBillSettlementVO pcxBillSettlementVO = new PcxBillSettlementVO();
        if (CollectionUtil.isNotEmpty(settlements)) {
            // 20250626 - mashaojie 和产品确认去掉跟人转账的过滤，找第一条作为结算方式展示
            pcxBillSettlementVO = settlements.stream()
                    .findFirst().orElse(new PcxBillSettlementVO());
        }
        pcxPrintResult.setSpecificity(specificityList);
        Map<String, String> expSupplyMap = processExpense(basicInfo, expenseDetails);
        pcxPrintResult.setRows(Arrays.asList(convertToMap(basicInfo,pcxBillSettlementVO,specificityList,expSupplyMap)));
        pcxPrintResult.setProcessNode(assembleProcessNode(pcxBillVO.getApprovalProcess()));
        String idKey = StringUtil.getUUID();
        cacheManager.getCache(PcxUReportBillDataSource.PCX_CHECK_KEY).put(idKey, pcxPrintResult);
        Map<String, Object> param = new HashMap<>();
        param.put("id", idKey);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            reportFileService.exportPdf(templName ,  param, outputStream);
            paAttach = this.upLoadPdf(fileName, outputStream.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return paAttach;
    }

    /***
     * 处理会议、招待、培训等费用明细特有字段
     * @param basicInfo
     * @param expenseDetails
     * @return
     */
    private Map<String, String> processExpense(PcxBill basicInfo, List<PcxBillExpDetailBase> expenseDetails) {
        Map<String, Long> expenseCounts = expenseDetails.stream()
                .collect(Collectors.groupingBy(
                        item -> {
                            if (item.getExpDetailCode().startsWith(PcxConstant.MEETING_EXPENSE_30215)) {
                                return PcxConstant.MEETING_EXPENSE_30215;
                            } else if (item.getExpDetailCode().startsWith(PcxConstant.TRAINING_EXPENSE_30216)) {
                                return PcxConstant.TRAINING_EXPENSE_30216;
                            } else if (item.getExpDetailCode().startsWith(PcxConstant.TREAT_EXPENSE_30217)) {
                                return PcxConstant.TREAT_EXPENSE_30217;
                            }
                            return "other";
                        },
                        Collectors.counting()
                ));
        Map<String, String> processExpense = new HashMap<>();
        if (expenseCounts.getOrDefault(PcxConstant.MEETING_EXPENSE_30215, 0L) > 0) {
            processExpense = processExpense(PcxConstant.MEETING_EXPENSE_30215, basicInfo);
        }
        if (expenseCounts.getOrDefault(PcxConstant.TRAINING_EXPENSE_30216, 0L) > 0) {
            processExpense = processExpense(PcxConstant.TRAINING_EXPENSE_30216, basicInfo);
        }
        if (expenseCounts.getOrDefault(PcxConstant.TREAT_EXPENSE_30217, 0L) > 0) {
            processExpense = processExpense(PcxConstant.TREAT_EXPENSE_30217, basicInfo);
        }
        return processExpense;
    }

    /****
     * 处理参数
     * @param expenseType
     * @param basicInfo
     * @return
     */
    private Map<String, String> processExpense(String expenseType, PcxBill basicInfo) {
        Map<String, String> result = new HashMap<>();
        String KEY_DURATION = "duration";
        String KEY_ACTIVITY_NAME = "activityName";
        String KEY_PEOPLE_NUM = "peopleNum";
        switch (expenseType) {
            case PcxConstant.MEETING_EXPENSE_30215:
                List<PcxBillExpMeeting> pcxBillExpMeetings = pcxBillExpMeetingService.selectList(
                        Collections.singletonList(basicInfo.getId()), basicInfo.getAgyCode(), basicInfo.getFiscal(), basicInfo.getMofDivCode());
                if (CollectionUtil.isNotEmpty(pcxBillExpMeetings)) {
                    PcxBillExpMeeting pcxBillExpMeeting = pcxBillExpMeetings.get(0);
                    result.put(KEY_DURATION, StringUtil.getStringValue(pcxBillExpMeeting.getDuration()));
                    result.put(KEY_ACTIVITY_NAME, pcxBillExpMeeting.getMeetingName());
                    result.put(KEY_PEOPLE_NUM, StringUtil.getStringValue(pcxBillExpMeeting.getPeopleNum()));
                }
                break;
            case PcxConstant.TRAINING_EXPENSE_30216:
                List<PcxBillExpTraining> pcxBillExpTrainings = pcxBillExpTrainingService.selectList(
                        Collections.singletonList(basicInfo.getId()), basicInfo.getAgyCode(), basicInfo.getFiscal(), basicInfo.getMofDivCode());
                if (CollectionUtil.isNotEmpty(pcxBillExpTrainings)) {
                    PcxBillExpTraining pcxBillExpTraining = pcxBillExpTrainings.get(0);
                    result.put(KEY_DURATION, StringUtil.getStringValue(pcxBillExpTraining.getDuration()));
                    result.put(KEY_ACTIVITY_NAME, pcxBillExpTraining.getTrainingName());
                    result.put(KEY_PEOPLE_NUM, StringUtil.getStringValue(pcxBillExpTraining.getPeopleNum()));
                }
                break;
            case PcxConstant.TREAT_EXPENSE_30217:
                List<PcxBillExpInlandfee> pcxBillExpInlandfees = pcxBillExpInlandfeeService.selectList(
                        Collections.singletonList(basicInfo.getId()), basicInfo.getAgyCode(), basicInfo.getFiscal(), basicInfo.getMofDivCode());
                if (CollectionUtil.isNotEmpty(pcxBillExpInlandfees)) {
                    PcxBillExpInlandfee pcxBillExpInlandfee = pcxBillExpInlandfees.get(0);
                    result.put(KEY_DURATION, StringUtil.getStringValue(pcxBillExpInlandfee.getDuration()));
                    result.put(KEY_ACTIVITY_NAME, pcxBillExpInlandfee.getUnitName());
                    result.put(KEY_PEOPLE_NUM, StringUtil.getStringValue(pcxBillExpInlandfee.getVisitPeopleNum()));
                }
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 构建 specificityList 数据，根据 printBill 类型和 expenseDetail 处理不同的逻辑
     *
     * @param pcxBillVO 账单视图对象，包含账单相关信息
     * @param printBill 打印账单类型（如 PASTE_BILL）
     * @param expenseDetail 费用明细列表
     * @return 构建完成的 specificityList，包含账单或费用明细的映射数据
     */
    private List<Map<String, Object>> buildSpecificityList(PcxBillVO pcxBillVO, String printBill, List<PcxBillExpDetailBase> expenseDetail,boolean isWxApp) {
        // 初始化 specificityList 用于存储处理后的数据
        List<Map<String, Object>> specificityList = new ArrayList<>();
        // 处理 PASTE_BILL 类型且费用明细为空的情况
        if (PcxConstant.PrintBillType.PASTE_BILL.equals(printBill) && CollectionUtil.isEmpty(expenseDetail)) {
            // 获取 ECS 匹配信息
            EcsExpMatchVO ecsExpMatch = pcxBillVO.getEcsExpMatch();
            if (!ObjectUtils.isEmpty(ecsExpMatch) && CollectionUtil.isNotEmpty(ecsExpMatch.getEcsRelList())) {
                // 筛选纸质账单（PAPER 类型）
                List<EcsRelVO> paperEcsRels = ecsExpMatch.getEcsRelList().stream()
                        .filter(item -> EcsEnum.BillKind.PAPER.getCode().equals(item.getEcsBillKind()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paperEcsRels)) {
                    // 遍历纸质账单，构建每个账单的属性映射
                    for (EcsRelVO paperEcsRel : paperEcsRels) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("ecsAmt", paperEcsRel.getEcsAmt()); // ECS 金额
                        map.put("inputAmt", paperEcsRel.getInputAmt()); // 输入金额
                        map.put("checkAmt", paperEcsRel.getCheckAmt()); // 核查金额
                        map.put("taxRate", paperEcsRel.getTaxRate()); // 税率
                        map.put("taxAmt", paperEcsRel.getTaxAmt()); // 税额
                        map.put("ecsBillId", paperEcsRel.getEcsBillId()); // ECS 账单 ID
                        map.put("ecsCheckStatus", paperEcsRel.getEcsCheckStatus()); // ECS 核查状态
                        map.put("checkReason", paperEcsRel.getCheckReason()); // 核查原因
                        map.put("ecsBillKind", paperEcsRel.getEcsBillKind()); // ECS 账单种类
                        map.put("ecsBillType", paperEcsRel.getEcsBillType()); // ECS 账单类型
                        specificityList.add(map);
                    }
                }
            }
        } else {
            // 处理费用明细不为空的情况，获取 ECS 关联数据
            // 提取费用明细的 ID 列表（过滤空值）
            List<String> detailIds = expenseDetail.stream()
                    .map(PcxBillExpDetailBase::getId)
                    .filter(StringUtil::isNotBlank)
                    .collect(Collectors.toList());
            // 根据明细 ID 查询 ECS 关联数据
            List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelService.selectByDetailIds(detailIds);
            // 定义过滤条件：确保 cadfAmt 和 detailId 不为空
            Predicate<PcxExpDetailEcsRel> condition = item -> !ObjectUtils.isEmpty(item.getCadfAmt()) && !ObjectUtils.isEmpty(item.getDetailId());
            // 如果是 PASTE_BILL 类型，仅保留纸质账单
            Boolean isWxPaste =  isWxApp && PcxConstant.PrintBillType.PASTE_BILL.equals(printBill);
            if (isWxPaste) {
                condition = item -> !StringUtil.isNotBlank(item.getEcsBillId())
                        || EcsEnum.BillKind.PAPER.getCode().equals(item.getEcsBillKind());
            }
            // 构建 ECS 关联数据的映射（以 detailId 为键）
            Map<String, PcxExpDetailEcsRel> pcxExpDetailEcsRelMap = pcxExpDetailEcsRels.stream()
                    .filter(condition)
                    .collect(Collectors.toMap(
                            PcxExpDetailEcsRel::getDetailId,
                            item -> item,
                            (existing, replacement) -> existing // 保留现有记录以避免重复
                    ));
            // 转换费用明细和 ECS 关联数据为 specificityList
            specificityList.addAll(convertToListMap(pcxBillVO.getExpenseDetail(), pcxExpDetailEcsRelMap,isWxPaste));
        }

        return specificityList;
    }

    // 获取打印模版
    private String getTemplate(PcxPrintBillQO qo,PcxBillVO pcxBillVO,Boolean isBatch) {
        String printTmplCode = qo.getTemplateCode();
        if(StringUtil.isEmpty(printTmplCode) && !isBatch){
            PaOptionQO paOptionQO = new PaOptionQO();
            paOptionQO.setMofDivCode(qo.getMofDivCode());
            paOptionQO.setFiscal(Integer.parseInt(qo.getFiscal()));
            paOptionQO.setTenantId(StringUtil.isNotBlank(qo.getTenantId())?PcxConstant.SYS_ID:qo.getTenantId());
            paOptionQO.setAgyCode(qo.getAgyCode());
            String optionValue = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_TMPL.getOptCode());
            if (StringUtil.isNotBlank(optionValue)) {
                printTmplCode = optionValue;
            }
        }else{
            List<PcxBillExpBase> specificity = pcxBillVO.getSpecificity();
            Optional<PcxBillExpBase> expBaseOptional = specificity.stream().filter(item -> StringUtil.isNotBlank(item.getExpenseCode()) &&
                    !PcxConstant.UNIVERSAL_EXPENSE_CODE.equals(item.getExpenseCode())).peek(PcxBillExpBase::getExpenseCode).findFirst();
            if(expBaseOptional.isPresent() && !ObjectUtils.isEmpty(pcxBillVO.getBasicInfo())){
                PcxBillExpBase pcxBillExpBase = expBaseOptional.get();
                PcxBill basicInfo = pcxBillVO.getBasicInfo();
                PcxPrintTemplateQO pcxPrintTemplateQO = new PcxPrintTemplateQO();
                pcxPrintTemplateQO.setFiscal(Integer.valueOf(basicInfo.getFiscal()));
                pcxPrintTemplateQO.setAgyCode(basicInfo.getAgyCode());
                pcxPrintTemplateQO.setMofDivCode(basicInfo.getMofDivCode());
                pcxPrintTemplateQO.setFuncCode(pcxBillExpBase.getExpenseCode());
                pcxPrintTemplateQO.setFuncType("2");
                pcxPrintTemplateQO.setBilltypeCode(basicInfo.getBillFuncCode());
                List<PcxPrintTemplate> pcxPrintTemplates = pcxPrintTemplateService.selectByQO(pcxPrintTemplateQO);
                if(CollectionUtil.isNotEmpty(pcxPrintTemplates)){
                    return pcxPrintTemplates.get(0).getTmpValue();
                }
            }
        }
        if(StringUtil.isEmpty(printTmplCode)){
            printTmplCode = "universal.ureport.xml";
        }
        return printTmplCode;
    }

    /**
     * 将图片字节数组列表转换为 PDF 文件
     * @param imageBytes 图片字节数组列表（支持 PNG、PDF、OFD 格式）
     * @param paperType 纸张类型（如 A4、Letter）
     * @param orientation 页面方向（横向或纵向）
     * @param fileName 文件名，用于生成临时目录路径
     * @return 生成的 PDF 字节数组，异常时返回 null
     */
    private byte[] convertImagesToPdf(List<byte[]> imageBytes, PaperType paperType, Orientation orientation, String fileName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 获取原始纸张宽高
            float width = paperType.getPaperSize().getWidth();
            float height = paperType.getPaperSize().getHeight();
            RectangleReadOnly pageSize;
            if (orientation.equals(Orientation.landscape)) {
                // 横向页面，确保宽 > 高
                if (width < height) {
                    pageSize = new RectangleReadOnly(height, width);
                } else {
                    pageSize = new RectangleReadOnly(width, height);
                }
            } else {
                // 纵向页面，确保高 > 宽
                if (height < width) {
                    pageSize = new RectangleReadOnly(width, height);
                } else {
                    pageSize = new RectangleReadOnly(height, width);
                }
            }

            // 初始化 PDF 文档
            Document document = new Document(pageSize);
            document.setMargins(0, 0, 0, 0);
            PdfWriter.getInstance(document, outputStream);
            document.open();

            // 遍历图片字节数组
            for (byte[] imageData : imageBytes) {
                processImage(document, imageData, pageSize, fileName);
            }

            document.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("PDF 转换失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理单个图片数据并添加到 PDF 文档
     * @param document PDF 文档
     * @param imageData 图片字节数据
     * @param pageSize 页面尺寸
     * @param fileName 文件名，用于 OFD 文件处理
     * @throws Exception 如果处理失败
     */
    private void processImage(Document document, byte[] imageData, RectangleReadOnly pageSize, String fileName) throws Exception {
        if (isPdf(imageData)) {
            // 处理 PDF 文件
            List<byte[]> pageImages = extractPageAsImage(imageData);
            if (pageImages != null) {
                for (byte[] pageImage : pageImages) {
                    addScaledImage(document, rotateImageToLandscape(Image.getInstance(pageImage),pageSize), pageSize);
                }
            }
        } else if (isOfdFile(imageData)) {
            // 处理 OFD 文件
            String tempDirPath = pubFileService.genTempDirPathStr(PcxConstant.SYS_ID) + "/" + fileName;
            Path tempPath = Paths.get(tempDirPath);
            try (ImageExporter exporter = new ImageExporter(new ByteArrayInputStream(imageData), tempPath)) {
                exporter.export();
                byte[] bytes = FileUtil.readPngFilesToBinary(tempDirPath);
                addScaledImage(document, rotateImageToLandscape(Image.getInstance(bytes),pageSize), pageSize);
            } finally {
                FileUtil.cleanTempDirectory(tempDirPath);
            }
        } else {
            // 处理普通图片
            addScaledImage(document, rotateImageToLandscape(Image.getInstance(imageData),pageSize), pageSize);
        }
    }

    private Image rotateImageToLandscape(Image pageImage,RectangleReadOnly pageSize) throws Exception {
        boolean needsRotation = (pageSize.getWidth() > pageSize.getHeight() && pageImage.getWidth() < pageImage.getHeight()) ||
                (pageSize.getWidth() <= pageSize.getHeight() && pageImage.getWidth() < pageImage.getHeight());
        if (!needsRotation) {
            return Image.getInstance(pageImage);
        }
        byte[] imageBytes = pageImage.getOriginalData();
        if (imageBytes == null) {
            throw new Exception("Cannot retrieve image data for rotation");
        }
        BufferedImage bufferedImage = ImageIO.read(new java.io.ByteArrayInputStream(imageBytes));
        if (bufferedImage == null) {
            throw new Exception("Failed to convert image to BufferedImage");
        }
        BufferedImage rotatedImage = new BufferedImage(bufferedImage.getHeight(), bufferedImage.getWidth(), bufferedImage.getType());
        Graphics2D g2d = rotatedImage.createGraphics();
        g2d.translate(rotatedImage.getWidth(), 0);
        g2d.rotate(Math.toRadians(90));
        g2d.drawImage(bufferedImage, 0, 0, null);
        g2d.dispose();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(rotatedImage, "png", baos);
        return Image.getInstance(baos.toByteArray());
    }

    /**
     * 缩放图片并添加到 PDF 文档
     * @param document PDF 文档
     * @param image    图片对象
     * @param pageSize 页面尺寸
     * @throws Exception 如果添加失败
     */
    private void addScaledImage(Document document, Image image, RectangleReadOnly pageSize) throws Exception {
        float pageWidth = pageSize.getWidth();
        float pageHeight = pageSize.getHeight();
        float imageWidth = image.getWidth();
        float imageHeight = image.getHeight();
        // 计算缩放因子
        float scaleFactor = Math.min(pageWidth / imageWidth, pageHeight / imageHeight);
        image.scaleAbsolute(imageWidth * scaleFactor, imageHeight * scaleFactor);
        image.setAlignment(Image.ALIGN_TOP);
        // 添加图片并新建页面
        document.add(image);
        document.newPage();
    }

    private static List<byte[]> extractPageAsImage(byte[] pdfBytes) {
        try (PDDocument document = PDDocument.load(pdfBytes)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int numberOfPages = document.getNumberOfPages();
            List<byte[]> images = new ArrayList<>();
            for (int i = 0; i < numberOfPages; i++) {
                BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(i, 300);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "png", outputStream);
                images.add(outputStream.toByteArray());
            }
            return images;
        } catch (Exception e) {
            log.error("Failed to extract pages as images", e);
            return null;
        }
    }

    /**
     * 判断字节数组是否为 OFD 文件（Java 1.8 版本）
     * @param data 文件字节数组
     * @return 如果是 OFD 文件返回 true，否则返回 false
     */
    private static boolean isOfdFile(byte[] data) {
        // 检查输入是否为空或过短
        if (data == null || data.length < 4) {
            return false;
        }
        // 检查 ZIP 文件头（OFD 文件通常以 ZIP 格式存储，魔数为 PK\x03\x04）
        if (data[0] != 0x50 || data[1] != 0x4B || data[2] != 0x03 || data[3] != 0x04) {
            return false;
        }
        // 进一步验证 ZIP 包中是否包含 OFD.xml 文件
        try (ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(data))) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                if ("OFD.xml".equalsIgnoreCase(entry.getName())) {
                    return true; // 找到 OFD.xml，确认是 OFD 文件
                }
                entry = zis.getNextEntry();
            }
        } catch (IOException e) {
            // 记录日志（根据实际项目日志框架调整）
            log.error("读取 ZIP 结构时出错: {}", e.getMessage());
            return false; // ZIP 解析失败，假设不是 OFD 文件
        }
        return false; // 未找到 OFD.xml，假设不是 OFD 文件
    }

    /***
     * 二进制文件判断是否为pdf 文件
     * @param data
     * @return
     */
    private static boolean isPdf(byte[] data) {
        return data != null && data.length > 4 && new String(data, 0, 4).equals("%PDF");
    }

    @Override
    public Boolean isShowPrintButton(PcxBillQO pcxBillQO) {
        PaOptionQO paOptionQO = new PaOptionQO();
        paOptionQO.setMofDivCode(pcxBillQO.getMofDivCode());
        paOptionQO.setFiscal(Integer.parseInt(pcxBillQO.getFiscal()));
        paOptionQO.setTenantId(pcxBillQO.getTenantId());
        paOptionQO.setAgyCode(pcxBillQO.getAgyCode());
        // 简易版总是展示打印按钮
        boolean approvalEnabled = approvalEnabled(paOptionQO.getFiscal(), pcxBillQO.getAgyCode(), pcxBillQO.getMofDivCode());
        if (!approvalEnabled) {
            return Boolean.TRUE;
        }
        String printNode = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_NODE.getOptCode());
        if(!isInApproval(pcxBillQO,printNode)){
            return Boolean.FALSE;
        }
        String optionValue = businessRuleOptionService.getOptionValueByOptionCode(paOptionQO, BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        if (StringUtil.isEmpty(optionValue)) {
            optionValue = PcxConstant.PrintBillType.EXPENSE_BILL;
        }
        if (PcxConstant.PrintBillType.EXPENSE_BILL.equals(optionValue)) {
            // 打印报销单的时候
            return Boolean.TRUE;
        }
        String billId = pcxBillQO.getBillId();
        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectByBillId(billId);
        List<PcxBillExpAttachRel> pcxBillExpAttachRels = pcxBillExpAttachRelDao.selectList(
                Wrappers.lambdaQuery(PcxBillExpAttachRel.class)
                .eq(PcxBillExpAttachRel::getBillId, billId)
                .eq(PcxBillExpAttachRel::getRelType, PcxExpAttachRelType.EXP_DETAIL.getCode()).eq(PcxBillExpAttachRel::getEcsBillType,EcsEnum.BillType.QUOTA.getCode()));
        // 存在定额发票，需要展示按钮
        if(CollectionUtil.isNotEmpty(pcxBillExpAttachRels)){
            return Boolean.TRUE;
        }
        if (isPasteBillWithPaper(optionValue,pcxExpDetailEcsRels)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private boolean approvalEnabled(Integer fiscal, String agyCode, String mofDivCode) {
        PaOptionQO optionQO = new PaOptionQO();
        optionQO.setMofDivCode(mofDivCode);
        optionQO.setFiscal(fiscal);
        optionQO.setAgyCode(agyCode);
        optionQO.setOptCode(APPROVAL_ENABLE.getOptCode());
        optionQO.setGroupName(APPROVAL_ENABLE.getGroupName());

        // 查询是否开启审批流程
        Response<?> response = businessRuleOptionService.selectByQO(optionQO);
        Assert.isTrue(response.isSuccess(), "查询是否启用审批流程异常");
        Assert.isTrue(Objects.nonNull(response.getData()), "未配置审批流程是否启用参数");
        PaOption approvalEnabled = (PaOption) response.getData();
        return approvalEnabled.getOptValue().equals(APPROVAL_ENABLE.getOptValue());
    }

    /***
     * 当是粘贴单，如果单据均为电子票，打印按钮不显示，不通知
     * @param optionValue
     * @param pcxExpDetailEcsRels
     * @return
     */
    private boolean isPasteBillWithPaper(String optionValue, List<PcxExpDetailEcsRel> pcxExpDetailEcsRels) {
        if(StringUtil.isEmpty(optionValue) || CollectionUtil.isEmpty(pcxExpDetailEcsRels)){
            return Boolean.FALSE;
        }
        return PcxConstant.PrintBillType.PASTE_BILL.equals(optionValue) &&
                pcxExpDetailEcsRels.stream()
                        .anyMatch(item -> EcsEnum.BillKind.PAPER.getCode().equals(item.getEcsBillKind()));
    }

    private Boolean isInApproval(PcxBillQO pcxBillQO,String printNode) {
        ProcessHistoryQO processHistoryQO = new ProcessHistoryQO();
        processHistoryQO.setAgyCode(pcxBillQO.getAgyCode());
        processHistoryQO.setMofDivCode(pcxBillQO.getMofDivCode());
        processHistoryQO.setBillFuncCode(pcxBillQO.getBillFuncCode());
        processHistoryQO.setBillId(pcxBillQO.getBillId());
        processHistoryQO.setTenantId(pcxBillQO.getTenantId());
        CheckMsg<ProcessCompositeVO> processCompositeVOCheckMsg = processService.listHistoryWithDelegate(processHistoryQO);
        // 未开启工作流的不显示打印按钮
        if(!processCompositeVOCheckMsg.isSuccess()){
            return Boolean.FALSE;
        }
        ProcessCompositeVO processCompositeVO = processCompositeVOCheckMsg.getData();
        if(ObjectUtils.isEmpty(processCompositeVO)){
            return Boolean.FALSE;
        }
        List<ProcessHistoryWithDelegateVO> approvalProcess = processCompositeVO.getApprovalProcess();
        if(CollectionUtil.isEmpty(approvalProcess)){
            return Boolean.FALSE;
        }
        // 获取到工作流
        List<ApprovalProcessDefinitionVO.DefinitionRow> wfNodes = getWfNodes(pcxBillQO);
        if(CollectionUtil.isEmpty(wfNodes)){
            log.error("未配置工作流节点");
            return Boolean.FALSE;
        }
        List<String> wfStopCodes = wfNodes.stream()
                .map(ApprovalProcessDefinitionVO.DefinitionRow::getStepCode)
                .collect(Collectors.toList());
        // approvalProcess是一个有序数组，获取到isFinished=0的元素的前一个元素
        String billApprovalPosition = getPreviousPositionCode(approvalProcess);
        int optCodeIndex = wfStopCodes.indexOf(printNode);
        int billNodeIndex = wfStopCodes.indexOf(billApprovalPosition);
        if(billNodeIndex < optCodeIndex){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private String getPreviousPositionCode(List<ProcessHistoryWithDelegateVO> approvalProcess) {
        if (approvalProcess == null || approvalProcess.isEmpty()) {
            return StringUtil.EMPTY;
        }
        ProcessHistoryWithDelegateVO first = approvalProcess.get(0);
        if (first.getIsFinished() != 0) {
            return convertNode(first.getPositionCode());
        }
        for (int i = 0; i < approvalProcess.size() - 1; i++) {
            ProcessHistoryWithDelegateVO current = approvalProcess.get(i);
            if (current.getIsFinished() == 0 && !ObjectUtils.isEmpty(approvalProcess.get(i + 1))) {
                return convertNode(approvalProcess.get(i + 1).getPositionCode());
            }
        }
        return StringUtil.EMPTY;
    }

    private String convertNode(String printNode) {
        if (StringUtil.isEmpty(printNode)) {
            return printNode;
        }
        // 构建映射表
        Map<String, String> nodeMap = new HashMap<>();
        nodeMap.put(PcxNodeEnum.dept_superior_audit.getId(), PcxNodeEnum.dept_superior_audit.getId());
        nodeMap.put(PcxNodeEnum.relevance_audit.getId(), PcxNodeEnum.relevance_audit.getId());
        nodeMap.put(PcxNodeEnum.divisional_agy_superior_audit.getId(), PcxNodeEnum.divisional_agy_superior_audit.getId());
        nodeMap.put(PcxNodeEnum.supervisor_agy_superior_audit.getId(), PcxNodeEnum.supervisor_agy_superior_audit.getId());
        nodeMap.put(PcxNodeEnum.high_agy_superior_audit.getId(), PcxNodeEnum.high_agy_superior_audit.getId());
        nodeMap.put(PcxNodeEnum.finance_audit.getId(), PcxNodeEnum.finance_audit.getId());
        nodeMap.put(PcxNodeEnum.financial_director.getId(), PcxNodeEnum.financial_director.getId());
        // 遍历映射表进行匹配
        for (Map.Entry<String, String> entry : nodeMap.entrySet()) {
            if (printNode.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        // 如果没有匹配项，返回原始值
        return printNode;
    }

    @Override
    public List<Map> getPrintSettingTmpl(PcxBillQO pcxBillQO) {
        List<Map> tmpls = new ArrayList<>();
        Map<String, String> expTmpMap = new HashMap<>();
        expTmpMap.put("tmplType", "expenseBill");
        expTmpMap.put("code", "expenseBill.ureport.xml");
        expTmpMap.put("name", "通用报销单模版");
        tmpls.add(expTmpMap);
        Map<String, String> pasteBill = new HashMap<>();
        pasteBill.put("tmplType", "pasteBill");
        pasteBill.put("code", "pasteBill.ureport.xml");
        pasteBill.put("name", "发票粘贴单模版");
        tmpls.add(pasteBill);
        return tmpls;
    }

    @Override
    public List<Map<String, Object>> getPrintTmpl(PcxBillQO pcxBillQO) {
        // 输入校验，空对象返回空列表
        if (ObjectUtils.isEmpty(pcxBillQO)) {
            return Collections.emptyList();
        }
        // 查询单据详情
        CheckMsg<PcxBillVO> view = pcxBillService.view(pcxBillQO.getBillId());
        if (!view.isSuccess()) {
            return Collections.emptyList();
        }
        PcxBillVO pcxBillVO = view.getData();
        PcxBill basicInfo = pcxBillVO.getBasicInfo();
        List<Map<String, Object>> result = new ArrayList<>();
        // 处理其他类型单据的打印模板 20250617 兼容打车费优先判断特殊模板
        handleOtherTemplates(pcxBillQO, pcxBillVO, basicInfo, result);
        if(CollectionUtil.isNotEmpty(result)){
            return result;
        }
        // 处理通用费用报销
        if (Objects.equals(basicInfo.getBizType(), ItemBizTypeEnum.COMMON.getCode())) {
            return handleCommonExpense(pcxBillQO, result);
        }
        return result;
    }

    /**
     * 处理通用费用报销的模板逻辑
     *
     * @param pcxBillQO 查询条件对象
     * @param result    结果列表
     * @return 模板信息列表
     */
    private List<Map<String, Object>> handleCommonExpense(PcxBillQO pcxBillQO, List<Map<String, Object>> result) {
        // 财务岗位使用固定模板
        if (PositionEnum.isFinance(pcxBillQO.getPositionCode())) {
            result.add(createTemplateMap(StringUtil.getUUID(), "通用报销打印模版", "universal.ureport.xml"));
            return result;
        }
        // 非财务岗位根据业务规则选择模板
        PcxPrintBillQO printBillQO = new PcxPrintBillQO();
        printBillQO.setMofDivCode(pcxBillQO.getMofDivCode());
        printBillQO.setFiscal(pcxBillQO.getFiscal());
        printBillQO.setAgyCode(pcxBillQO.getAgyCode());
        Map<String, String> optionMap = getOptionMap(printBillQO);
        String printBill = optionMap.get(BusinessRuleEnum.BusinessOptionEnum.PRINT_BILL.getOptCode());
        boolean isPasteBill = PcxConstant.PrintBillType.PASTE_BILL.equals(printBill);
        result.add(createTemplateMap(
                StringUtil.getUUID(),
                isPasteBill ? "发票粘贴单模版" : "通用报销单模版",
                isPasteBill ? "pasteBill.ureport.xml" : optionMap.get(BusinessRuleEnum.BusinessOptionEnum.PRINT_TMPL.getOptCode())
        ));
        return result;
    }

    /**
     * 处理其他类型单据的模板查询
     * @param pcxBillQO 查询条件对象
     * @param pcxBillVO 单据详情
     * @param basicInfo 基础信息
     * @param result    结果列表
     * @return 模板信息列表
     */
    private List<Map<String, Object>> handleOtherTemplates(PcxBillQO pcxBillQO, PcxBillVO pcxBillVO, PcxBill basicInfo, List<Map<String, Object>> result) {
        PcxPrintTemplateQO templateQO = new PcxPrintTemplateQO();
        templateQO.setMofDivCode(pcxBillQO.getMofDivCode());
        templateQO.setFiscal(Integer.parseInt(pcxBillQO.getFiscal()));
        templateQO.setAgyCode(pcxBillQO.getAgyCode());

        // 根据单据功能代码设置查询条件
        if (BillFuncCodeEnum.LOAN.getCode().equals(basicInfo.getBillFuncCode())) {
            templateQO.setFuncCode(basicInfo.getItemCode());
        } else {
            List<String> expenseCodes = pcxBillVO.getSpecificity().stream()
                    .map(PcxBillExpBase::getExpenseCode)
                    .collect(Collectors.toList());
            templateQO.setFuncCodes(expenseCodes);
        }
        // 查询模板并转换为结果
        List<PcxPrintTemplate> templates = pcxPrintTemplateService.selectByQO(templateQO);
        templates.forEach(template -> result.add(createTemplateMap(
                template.getId(),
                template.getTmpName(),
                template.getTmpValue()
        )));

        return result;
    }

    /**
     * 创建模板信息的Map对象
     *
     * @param id   模板ID
     * @param name 模板名称
     * @param code 模板代码
     * @return 包含模板信息的Map
     */
    private Map<String, Object> createTemplateMap(String id, String name, String code) {
        Map<String, Object> templateMap = new HashMap<>();
        templateMap.put("id", id);
        templateMap.put("name", name);
        templateMap.put("code", code);
        return templateMap;
    }

    @Override
    public CheckMsg<String> getBatchPdf(PcxPrintBillQO qo) {
        List<String> billIds = qo.getBillIds();
        if(CollectionUtil.isEmpty(billIds)){
            return CheckMsg.fail("请选择需要打印的单据");
        }
        List<String> attachIds = new ArrayList<>();
        for (String billId : billIds) {
            CheckMsg<PcxBillVO> view = pcxBillService.view(billId);
            if(!view.isSuccess()){
                continue;
            }
            attachIds.add(processPrintPdf(view.getData(), qo,Boolean.TRUE));
        }
        if (CollectionUtil.isEmpty(attachIds)) {
            return CheckMsg.fail("没有可合并的PDF文件");
        }
        // 合并PDF文件
        try {
            // 使用 ByteArrayOutputStream 收集合并后的 PDF 数据
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Document document = new Document();
            PdfCopy copy = new PdfCopy(document, baos);
            document.open();
            // 遍历 attachIds，获取每个 PDF 的二进制流并合并
            for (String attachId : attachIds) {
                PaAttach paAttach = new PaAttach();
                paAttach.setAttachId(attachId);
                byte[] bytes = pubFileService.fileDownload(paAttach);
                if (bytes == null || bytes.length == 0) {
                    continue; // 跳过无效的 PDF 数据
                }
                // 使用 ByteArrayInputStream 将 byte[] 转为 PdfReader 可用的输入流
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                PdfReader reader = new PdfReader(bais);
                // 逐页复制到合并文档
                for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                    copy.addPage(copy.getImportedPage(reader, i));
                }
                reader.close();
                bais.close();
            }
            // 关闭文档
            document.close();
            copy.close();
            // 返回合并后的 PDF 二进制流
            byte[] mergedPdfBytes = baos.toByteArray();
            if (mergedPdfBytes.length == 0) {
                return CheckMsg.fail("合并后的PDF为空");
            }
            // 上传文件
            String mergedFileName = IDGenerator.id();
            PaAttach paAttach = this.upLoadPdf(mergedFileName, mergedPdfBytes);
            return CheckMsg.success(paAttach.getAttachId());
        } catch (Exception e) {
            log.error("PDF合并失败"+e.getMessage(), e);
            return CheckMsg.fail("PDF合并失败: " + e.getMessage());
        }
    }

    // 判断是否是图片类型的文件后缀
    private boolean isImageFile(String fileFormat) {
        return fileFormat != null && (fileFormat.toLowerCase().endsWith(".jpg") ||
                fileFormat.toLowerCase().endsWith(".jpeg") ||
                fileFormat.toLowerCase().endsWith(".png") ||
                fileFormat.toLowerCase().endsWith(".gif") ||
                fileFormat.toLowerCase().endsWith(".bmp") ||
                fileFormat.toLowerCase().endsWith(".tiff"));
    }

    private List<PaAttach> getAttachList(PcxBillVO pcxBillVO, String invoiceOption, String attachmentOption,boolean isWxApp) {
        invoiceOption = StringUtil.isEmpty(invoiceOption)?BusinessRuleEnum.BusinessOptionEnum.INVOICE_OPTION.getOptValue():invoiceOption;
        attachmentOption = StringUtil.isEmpty(attachmentOption)?BusinessRuleEnum.BusinessOptionEnum.ATTACHMENT_OPTION.getOptValue():attachmentOption;
        Set<String> attachId = new HashSet<>();
        if(CollectionUtil.isEmpty(pcxBillVO.getAttachList()) && CollectionUtil.isEmpty(pcxBillVO.getExpenseDetail())){
            return new ArrayList<>();
        }
        if(PubConstant.STR_LOGIC_TRUE.equals(attachmentOption)){
            List<String> witRuleAttachIds = getWitRuleAttachIds(pcxBillVO);
            List<String> attachIds = pcxBillVO.getAttachList().stream().map(PcxBillAttachRelationVO::getAttachId).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(witRuleAttachIds)){
                attachIds.addAll(witRuleAttachIds);
            }
            List<String> detailRelAttachIds = getDetailAttachIds(pcxBillVO.getBillId());
            if(CollectionUtil.isNotEmpty(detailRelAttachIds)){
                attachIds.addAll(detailRelAttachIds);
            }
            attachId.addAll(attachIds);
        }
        List<String> detailIds = pcxBillVO.getExpenseDetail().stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        if(!PcxConstant.PrintExpenseOption.NOT_PRINT.equals(invoiceOption) && CollectionUtil.isNotEmpty(detailIds)){
            List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectList(
                    Wrappers.lambdaQuery(PcxExpDetailEcsRel.class).in(PcxExpDetailEcsRel::getDetailId, detailIds));
            if (CollectionUtil.isNotEmpty(pcxExpDetailEcsRels)) {
                // mashaojie - 20250703 pc打印报销单只打印电子票
                Predicate<PcxExpDetailEcsRel> predicate = (PcxConstant.PrintExpenseOption.PRINT_ELECT.equals(invoiceOption))||(!isWxApp)
                        ? item -> !EcsEnum.BillKind.PAPER.getCode().equals(item.getEcsBillKind())
                        : item -> true;
                pcxExpDetailEcsRels.stream()
                        .filter(predicate)
                        .map(PcxExpDetailEcsRel::getFileId)
                        .forEach(attachId::add);
            }
        }
        if(CollectionUtil.isEmpty(attachId)){
            return new ArrayList<>();
        }
        // 查询单据的所有附件，根据file_format字段过滤出图片、opd、pdf
        List<PaAttach> paAttaches = paAttachService.selectByIds(new ArrayList<>(attachId));
        return paAttaches.stream()
                .filter(paAttach ->
                        isImageFile(paAttach.getFileFormat()) ||
                                paAttach.getFileFormat().equalsIgnoreCase(".ofd") ||
                                paAttach.getFileFormat().equalsIgnoreCase(".pdf"))
                .collect(Collectors.toList());
    }

    private List<String> getDetailAttachIds(String billId) {
        return pcxExpDetailEcsRelDao.getDetailAttachIds(billId);
    }

    private List<String> getWitRuleAttachIds(PcxBillVO pcxBillVO) {
        if (pcxBillVO == null || pcxBillVO.getBasicInfo() == null) {
            return Collections.emptyList();
        }
        WitRuleResult witRuleResult = new WitRuleResult();
        witRuleResult.setFiscal(pcxBillVO.getBasicInfo().getFiscal());
        witRuleResult.setAgyCode(pcxBillVO.getBasicInfo().getAgyCode());
        witRuleResult.setMofDivCode(pcxBillVO.getBasicInfo().getMofDivCode());
        witRuleResult.setBillId(pcxBillVO.getBasicInfo().getId());
        return witAuditRuleService.queryRuleAttach(witRuleResult);
    }

    private List<PaAttach> getAttachList(PcxBillVO pcxBillVO) {
        List<String> attachId = new ArrayList<>();
        // 查询附件清单关系
        if(CollectionUtil.isEmpty(pcxBillVO.getAttachList())){
            return new ArrayList<>();
        }
        List<String> attachIds = pcxBillVO.getAttachList().stream().map(PcxBillAttachRelationVO::getAttachId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(attachIds)) {
            attachId.addAll(attachIds);
        }
        List<PcxBillExpAttachRel> pcxBillExpAttachRels = pcxBillExpAttachRelDao.selectList(Wrappers.lambdaQuery(PcxBillExpAttachRel.class).eq(PcxBillExpAttachRel::getBillId, pcxBillVO.getBillId()));
        if (CollectionUtil.isNotEmpty(pcxBillExpAttachRels)) {
            pcxBillExpAttachRels.stream().map(PcxBillExpAttachRel::getAttachId).forEach(attachId::add);
        }
        List<String> detailIds = pcxBillVO.getExpenseDetail().stream().map(PcxBillExpDetailBase::getId).collect(Collectors.toList());
        List<PcxExpDetailEcsRel> pcxExpDetailEcsRels = pcxExpDetailEcsRelDao.selectList(
                Wrappers.lambdaQuery(PcxExpDetailEcsRel.class).in(PcxExpDetailEcsRel::getDetailId, detailIds));
        if (CollectionUtil.isNotEmpty(pcxExpDetailEcsRels)) {
            pcxExpDetailEcsRels.stream().map(PcxExpDetailEcsRel::getFileId).forEach(attachId::add);
        }
        // 查询单据的所有附件，根据file_format字段过滤出图片、opd、pdf
        List<PaAttach> paAttaches = paAttachService.selectByIds(attachId);
        return paAttaches.stream()
                .filter(paAttach ->
                        isImageFile(paAttach.getFileFormat()) ||
                                paAttach.getFileFormat().equalsIgnoreCase(".ofd") ||
                                paAttach.getFileFormat().equalsIgnoreCase(".pdf"))
                .collect(Collectors.toList());
    }

    private Map<String, Object> convertToMap(PcxBill pcxBill,PcxBillSettlementVO pcxBillSettlementVO,List<Map<String, Object>> specificityList,Map<String, String> expSupplyMap) {
        Map<String, Object> result = new HashMap<>();
        // 使用put方法将PcxBill字段的值放入Map
        result.put("id", pcxBill.getId());
        result.put("billNo", pcxBill.getBillNo());
        result.put("claimantCode", pcxBill.getClaimantCode());
        result.put("claimantUserCode", pcxBill.getClaimantUserCode());
        result.put("claimantName", pcxBill.getClaimantName());
        result.put("departmentCode", pcxBill.getDepartmentCode());
        result.put("departmentName", pcxBill.getDepartmentName());
        result.put("transDate", pcxBill.getTransDate());
        result.put("reason", pcxBill.getReason());
        result.put("itemCode", pcxBill.getItemCode());
        result.put("itemName", pcxBill.getItemName());
        result.put("auditTime", pcxBill.getAuditTime());
        result.put("auditCode", pcxBill.getAuditCode());
        result.put("auditName", pcxBill.getAuditName());
        result.put("payTime", pcxBill.getPayTime());
        result.put("payStatus", pcxBill.getPayStatus());
        result.put("billStatus", pcxBill.getBillStatus());
        result.put("approveStatus", pcxBill.getApproveStatus());
        result.put("billFuncCode", pcxBill.getBillFuncCode());
        result.put("billFuncName", pcxBill.getBillFuncName());
        result.put("inputAmt", pcxBill.getInputAmt());
        result.put("checkAmt", pcxBill.getCheckAmt());
        result.put("settlementAmt", pcxBill.getSettlementAmt());
        result.put("loanAmt", pcxBill.getLoanAmt());
        result.put("repayDate", pcxBill.getRepayDate());
        result.put("bizType", pcxBill.getBizType());
        result.put("bizTypeName", pcxBill.getBizTypeName());
        result.put("modifier", pcxBill.getModifier());
        result.put("modifierName", pcxBill.getModifierName());
        result.put("modifiedTime", pcxBill.getModifiedTime());
        result.put("creator", pcxBill.getCreator());
        result.put("creatorName", pcxBill.getCreatorName());
        result.put("createdTime", pcxBill.getCreatedTime());
        result.put("agyCode", pcxBill.getAgyCode());
        result.put("agyName", pcxBill.getAgyName());
        result.put("fiscal", pcxBill.getFiscal());
        result.put("mofDivCode", pcxBill.getMofDivCode());
        result.put("tenantId", pcxBill.getTenantId());
        result.put("expenseCodes", pcxBill.getExpenseCodes());
        result.put("expenseNames", pcxBill.getExpenseNames());
        result.put("expDepartmentCodes", pcxBill.getExpDepartmentCodes());
        result.put("expDepartmentNames", pcxBill.getExpDepartmentNames());
        result.put("projectCodes", pcxBill.getProjectCodes());
        result.put("projectNames", pcxBill.getProjectNames());
        result.put("fundtypeCodes", pcxBill.getFundtypeCodes());
        result.put("fundtypeNames", pcxBill.getFundtypeNames());
        result.put("completedStatus", pcxBill.getCompletedStatus());
        result.put("completedTime", pcxBill.getCompletedTime());
        result.put("completed", pcxBill.getCompleted());
        result.put("completedName", pcxBill.getCompletedName());
        result.put("isVou", pcxBill.getIsVou());
        result.put("vouId", pcxBill.getVouId());
        result.put("vouNo", pcxBill.getVouNo());
        result.put("vouDate", pcxBill.getVouDate());
        // 额外的临时字段
        result.put("keyword", pcxBill.getKeyword());
        result.put("keywordFields", pcxBill.getKeywordFields());
        result.put("ids", pcxBill.getIds());
        result.put("billFuncCodes", pcxBill.getBillFuncCodes());
        result.put("history", pcxBill.getHistory());
        result.put("content", pcxBill.getContent());
        result.put("attachCount", pcxBill.getAttachCount());
        result.put("ecsCount", specificityList.size());
        result.put("todoStatus", pcxBill.getTodoStatus());
        if(!ObjectUtils.isEmpty(pcxBillSettlementVO)){
            result.put("payeeBankName", pcxBillSettlementVO.getPayeeBankName());
            result.put("payeeAccName", pcxBillSettlementVO.getPayeeAccName());
            result.put("payeeAccNo", pcxBillSettlementVO.getPayeeAccNo());
        }
        if(CollectionUtil.isNotEmpty(expSupplyMap)){
            result.putAll(expSupplyMap);
        }
        return result;
    }


    public List<Map<String, Object>> convertToListMap(List<PcxBillExpDetailBase> expenseDetails,Map<String, PcxExpDetailEcsRel> expDetailCadfMap, Boolean isWxPaste) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 遍历每个 ExpenseDetail 对象
        for (PcxBillExpDetailBase expenseDetail : expenseDetails) {
            if(isWxPaste && !expDetailCadfMap.containsKey(expenseDetail.getId())){
                continue;
            }
            PcxExpDetailEcsRel pcxExpDetailEcsRel = expDetailCadfMap.getOrDefault(expenseDetail.getId(), defaultEcsRel);
            if (expenseDetail.getInputAmt() != null
                    && expenseDetail.getInputAmt().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // 创建一个新的 Map
            Map<String, Object> map = new HashMap<>();
            // 使用 put 方法将对象的属性值添加到 Map 中
            map.put("id", expenseDetail.getId());
            map.put("billId", expenseDetail.getBillId());
            map.put("expenseId", expenseDetail.getExpenseId());
            map.put("expDetailCode", StringUtil.isNotBlank(expenseDetail.getExpDetailCode())?expenseDetail.getExpDetailCode():pcxExpDetailEcsRel.getExpenseTypeCode());
            map.put("ecsAmt", expenseDetail.getEcsAmt());
            map.put("inputAmt", expenseDetail.getInputAmt());
            map.put("checkAmt", expenseDetail.getCheckAmt());
            map.put("standAmt", expenseDetail.getStandAmt());
            map.put("source", expenseDetail.getSource());
            map.put("taxRate", expenseDetail.getTaxRate());
            map.put("taxAmt", expenseDetail.getTaxAmt());
            map.put("noEcsReason", expenseDetail.getNoEcsReason());
            map.put("agyCode", expenseDetail.getAgyCode());
            map.put("fiscal", expenseDetail.getFiscal());
            map.put("mofDivCode", expenseDetail.getMofDivCode());
            map.put("tenantId", expenseDetail.getTenantId());
            map.put("empCode", expenseDetail.getEmpCode());
            map.put("ecsRelItemName", expenseDetail.getEcsRelItemName());
            map.put("ecsBillId", expenseDetail.getEcsBillId());
            map.put("ecsCheckStatus", expenseDetail.getEcsCheckStatus());
            map.put("checkReason", expenseDetail.getCheckReason());
            map.put("ecsBillKind", StringUtil.isEmpty(expenseDetail.getEcsBillKind())?pcxExpDetailEcsRel.getEcsBillKind():expenseDetail.getEcsBillKind());
            map.put("ecsBillType", StringUtil.isEmpty(expenseDetail.getEcsBillType())?pcxExpDetailEcsRel.getEcsBillType():expenseDetail.getEcsBillType());
            map.put("noEcsReasonName", expenseDetail.getNoEcsReasonName());
            map.put("standReason", expenseDetail.getStandReason());
            map.put("cadfAmt", ObjectUtils.isEmpty(pcxExpDetailEcsRel.getCadfAmt()) ? BigDecimal.ZERO : pcxExpDetailEcsRel.getCadfAmt());
            String realName = PubConstant.LOGIC_TRUE == (pcxExpDetailEcsRel.getIsRealName())
                    ? "是"
                    : "否";
            map.put("isRealName", realName);
            map.put("ecsNum", expenseDetail.getEcsNum());
            // 将每个 map 加入到结果列表
            resultList.add(map);
        }
        return resultList;
    }

    private List<Map<String, Object>>  assembleProcessNode(List<ProcessHistoryWithDelegateVO> processHistoryWithDelegateVOList) {
        if (CollectionUtil.isEmpty(processHistoryWithDelegateVOList)) {
            return new ArrayList<>();
        }
        Map<String, ProcessHistoryWithDelegateVO> processNodeMap = getProcessHistoryWithDelegateVOMap(processHistoryWithDelegateVOList);
        if (ObjectUtils.isEmpty(processNodeMap)) {
            return new ArrayList<>();
        }
        Map<String, String> positionMap = new HashMap<>();
        processNodeMap.forEach((positionCode, processHistory) -> {
            List<ProcessHistoryVO> approvalProcess = Optional.ofNullable(processHistory)
                    .map(ProcessHistoryWithDelegateVO::getApprovalProcess)
                    .orElse(Collections.emptyList());
            if (approvalProcess.isEmpty()) return;
            List<PaUserDTO> userCodes = Optional.ofNullable(approvalProcess.get(0).getUserCodes())
                    .orElse(Collections.emptyList());
            String userNames = userCodes.stream()
                    .map(PaUserDTO::getUserName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
            mergePositionNames(positionCode, userNames, positionMap);
        });
        // 组装PcxProcessNode对象
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.dept_superior_audit.getId()), positionMap.get(PcxNodeEnum.dept_superior_audit.getId()));
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.superior_audit.getId()), positionMap.get(PcxNodeEnum.superior_audit.getId()));
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.divisional_agy_superior_audit.getId()), positionMap.get(PcxNodeEnum.divisional_agy_superior_audit.getId()));
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.financial_director.getId()), positionMap.get(PcxNodeEnum.financial_director.getId()));
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.supervisor_agy_superior_audit.getId()), positionMap.get(PcxNodeEnum.supervisor_agy_superior_audit.getId()));
        resultMap.put(StringUtil.lineToHump(PcxNodeEnum.finance_audit.getId()), positionMap.get(PcxNodeEnum.finance_audit.getId()));
        List<Map<String, Object>> result = new ArrayList<>();
        result.add(resultMap);
        return result;
    }

    private void mergePositionNames(String positionCode, String userNames, Map<String, String> positionMap) {
        Map<String, String> nodeEnumMap = new HashMap<>();
        nodeEnumMap.put(PcxNodeEnum.finance_audit.getId(), PcxNodeEnum.finance_audit.getId());
        nodeEnumMap.put(PcxNodeEnum.dept_superior_audit.getId(), PcxNodeEnum.dept_superior_audit.getId());
        nodeEnumMap.put(PcxNodeEnum.divisional_agy_superior_audit.getId(), PcxNodeEnum.divisional_agy_superior_audit.getId());
        nodeEnumMap.put(PcxNodeEnum.supervisor_agy_superior_audit.getId(), PcxNodeEnum.supervisor_agy_superior_audit.getId());
        nodeEnumMap.put(PcxNodeEnum.high_agy_superior_audit.getId(), PcxNodeEnum.high_agy_superior_audit.getId());
        nodeEnumMap.put(PcxNodeEnum.financial_director.getId(), PcxNodeEnum.financial_director.getId());
        for (Map.Entry<String, String> entry : nodeEnumMap.entrySet()) {
            if (positionCode.startsWith(entry.getKey())) {
                String existingNames = positionMap.getOrDefault(entry.getValue(), "");
                String mergedNames = existingNames.isEmpty() ? userNames : existingNames + "," + userNames;
                positionMap.put(entry.getValue(), mergedNames);
                return;
            }
        }
        positionMap.put(positionCode, userNames);
    }

    private Map<String, ProcessHistoryWithDelegateVO> getProcessHistoryWithDelegateVOMap(List<ProcessHistoryWithDelegateVO> processList) {
        if (CollectionUtil.isEmpty(processList)) {
            return Collections.emptyMap();
        }
        // 处理processList
        processList = processHistoryWithDelegate(processList);
        LinkedList<ProcessHistoryWithDelegateVO> linkedList = new LinkedList<>();
        Boolean isBack = Boolean.FALSE;
        for (int i = 0; i < processList.size(); i++) {
            while (isBack){
                // 如果是回退或撤销操作，则从链表中弹出最后一个元素
                if(linkedList.isEmpty()){
                    isBack = Boolean.FALSE;
                    break;
                }
                ProcessHistoryWithDelegateVO processHistoryWithDelegateVO = processList.get(i);
                ProcessHistoryWithDelegateVO lastProcess = linkedList.get(linkedList.size() - 1);
                if (processHistoryWithDelegateVO.getPositionCode().equals(lastProcess.getPositionCode())) {
                    // 如果当前节点与链表最后一个节点的positionCode同
                    if(PubConstant.LOGIC_TRUE == lastProcess.getIsFinished()){
                        linkedList.add(lastProcess);
                    }else{
                        linkedList.pop();
                    }
                    isBack = Boolean.FALSE;
                } else {
                    // 如果不相同，则从链表中弹出最后一个元素
                    linkedList.pop();
                }
            }
            if("rollback_task".equals(processList.get(i).getOperationName()) || "revoke_task".equals(processList.get(i).getOperationName())){
                isBack = Boolean.TRUE;
            }else{
                linkedList.add(processList.get(i));
            }
        }
    return linkedList.stream()
                .collect(Collectors.toMap(ProcessHistoryWithDelegateVO::getPositionCode, Function.identity(), (existing, replacement) -> existing));
    }

    private List<ProcessHistoryWithDelegateVO> processHistoryWithDelegate(List<ProcessHistoryWithDelegateVO> processList){
        if (CollectionUtil.isEmpty(processList)) {
            return Collections.emptyList();
        }
        int index = 0;
        for (ProcessHistoryWithDelegateVO process : processList) {
            if (PubConstant.LOGIC_FALSE == process.getIsFinished()) {
                return processList.subList(0, index);
            }
            index++;
        }
        return processList;
    }

    PaAttach upLoadPdf(String fileName, byte[] content) throws IOException {
        String fullFileName = new StringBuilder(fileName).append(".pdf").toString();
        Path path =
                Paths.get(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.DATE_PATTERN10)));
        PaAttach paAttach =
                PaAttach.create(fileName, fullFileName, FileUploadUtil.getFileSize(content.length), content);
        paAttach.setSaveFileName(fullFileName);
//        paAttach.setBusinessDir(path.toString());
//        paAttach.setRelPath(path + File.separator + fullFileName);
        //调用公共的附件上传接口
        pubFileService.uploadAttach(paAttach,paAttach,Boolean.FALSE);
        return paAttach;
    }

    private List<ApprovalProcessDefinitionVO.DefinitionRow> getWfNodes(PcxBillQO qo) {
        CheckMsg<Object> msg = null;
        try {
            msg = processService.queryProcessDefinition(qo.getAgyCode(), qo.getMofDivCode(), qo.getBillFuncCode(), qo.getTenantId());
        } catch (Exception e) {
            log.error("查询工作流失败"+e.getMessage(), e);
            return Collections.emptyList();
        }
        if (ObjectUtils.isEmpty(msg) || !msg.isSuccess()) {
            return Collections.emptyList();
        }
        PcxProcessDefinition def = (PcxProcessDefinition) msg.getData();
        return def.getNodes().stream()
                .filter(node -> !node.getId().equals(PcxNodeEnum.start.getId()) && !node.getId().equals(PcxNodeEnum.end.getId()))
                .map(node -> {
                    ApprovalProcessDefinitionVO.DefinitionRow row = new ApprovalProcessDefinitionVO.DefinitionRow();
                    row.setOrder(node.getOrder());
                    row.setEnabled(!node.isSkip());
                    row.setStepCode(node.getId());
                    row.setStepName(node.getLabel());
                    row.setDesc(node.getDescription());
                    return row;
                }).sorted(Comparator.comparingInt(ApprovalProcessDefinitionVO.DefinitionRow::getOrder))
                .collect(Collectors.toList());
    }
}
