package com.pty.pcx.service.impl.ecs.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.pty.mad.entity.PaValset;
import com.pty.pcx.api.bas.IPcxBasCityPeakService;
import com.pty.pcx.api.bas.PcxBasCityClassifyService;
import com.pty.pcx.api.costcontrollevel.PcxCostControlLevelService;
import com.pty.pcx.common.constant.PcxCityClassifyConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pcx.entity.costcontrollevel.PcxCostControlLevel;
import com.pty.pcx.pa.IPcxValSetService;
import com.pty.pcx.qo.bas.PeakDateQO;
import com.pty.pcx.qo.bas.QueryCityPeakClassifyQO;
import com.pty.pcx.qo.costcontrollevel.PcxEmployeeWithCostLevelQO;
import com.pty.pcx.util.StandMatchUtil;
import com.pty.pcx.vo.bas.CityPeakVO;
import com.pty.pcx.vo.bill.ExpStandVO;
import com.pty.pcx.vo.costcontrollevel.PcxEmployeeWithCostLevelVO;
import com.pty.pub.common.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Indexed;
import org.springframework.stereotype.Service;

import java.time.MonthDay;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Indexed
@Service
@Slf4j
public abstract class StandMatchAbstractHandler {
    @Autowired
    private PcxCostControlLevelService pcxCostControlLevelService;
    @Autowired
    private IPcxValSetService pcxValSetService;
    @Autowired
    private PcxBasCityClassifyService pcxBasCityClassifyService;
    @Autowired
    private IPcxBasCityPeakService pcxBasCityPeakService;

    /**
     * 获取人员信息的费控级别
     * @param travel
     * @return
     */
    protected List<String> getEmployeeBudLevel(PcxBillExpDetailTravel travel) {
        if (StringUtil.isNotEmpty(travel.getBudLevel())){
            return Arrays.asList(travel.getBudLevel().split(","));
        }
        if (travel.getEmpType().equals(PcxConstant.EMP_TYPE_INNER)){
            PcxEmployeeWithCostLevelQO pcxBaseDTO = new PcxEmployeeWithCostLevelQO();
            pcxBaseDTO.setAgyCode(travel.getAgyCode());
            pcxBaseDTO.setFiscal(Integer.valueOf(travel.getFiscal()));
            pcxBaseDTO.setMofDivCode(travel.getMofDivCode());
            pcxBaseDTO.setMadCodes(Collections.singletonList(travel.getEmpCode()));
            List<PcxEmployeeWithCostLevelVO> empsWithCostLevelByCodes = pcxCostControlLevelService.getEmpsWithCostLevelByCodes(pcxBaseDTO);

            if (CollectionUtils.isEmpty(empsWithCostLevelByCodes)){
                return Lists.newArrayList();
            }
            PcxEmployeeWithCostLevelVO pcxEmployeeWithCostLevelVO = empsWithCostLevelByCodes.get(0);
            if (CollectionUtils.isEmpty(pcxEmployeeWithCostLevelVO.getPcxCostControlLevels())){
                return Lists.newArrayList();
            }
            return pcxEmployeeWithCostLevelVO.getPcxCostControlLevels().stream().map(PcxCostControlLevel::getCostControlCode).collect(Collectors.toList());
        }else {
            //外单位
            return Collections.singletonList(travel.getBudLevel());
        }
    }


    protected PcxBasCityClassify getCityClassify(String cityCode,String agyCode,String fiscal, String mofDivCode) {
        PcxBasCityClassify qo = new PcxBasCityClassify();
        qo.setAgyCode(agyCode);
        qo.setFiscal(fiscal);
        qo.setMofDivCode(mofDivCode);
        qo.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_CITY);
        qo.setDataCode(cityCode);
        List<PcxBasCityClassify> pcxBasCityClassifies = pcxBasCityClassifyService.selectList(qo);
        if (CollectionUtils.isNotEmpty(pcxBasCityClassifies)){
            return pcxBasCityClassifies.get(0);
        }else{
            return null;
        }
    }

    protected PcxBasCityClassify getCityClassifyExpense(String cityCode,String agyCode,String fiscal, String mofDivCode,String expenseCode) {
        PcxBasCityClassify qo = new PcxBasCityClassify();
        qo.setAgyCode(agyCode);
        qo.setFiscal(fiscal);
        qo.setMofDivCode(mofDivCode);
        qo.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_CITY);
        qo.setDataCode(cityCode);
        qo.setExpenseTypeCode(expenseCode);
        List<PcxBasCityClassify> pcxBasCityClassifies = pcxBasCityClassifyService.selectList(qo);
        if (CollectionUtils.isNotEmpty(pcxBasCityClassifies)){
            return pcxBasCityClassifies.get(0);
        }else{
            return null;
        }
    }

    /**
     * 获取城市的淡旺季
     * @param endCityCode
     * @param startTime
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    protected CityPeakVO getCityType(String endCityCode, String startTime, String finishTime,String agyCode, String fiscal, String mofDivCode) {
        //获取当前城市是否属于旺季
        QueryCityPeakClassifyQO pcxBasCityPeakQO = new QueryCityPeakClassifyQO();
        pcxBasCityPeakQO.setAgyCode(agyCode);
        pcxBasCityPeakQO.setFiscal(fiscal);
        pcxBasCityPeakQO.setMofDivCode(mofDivCode);
        pcxBasCityPeakQO.setClassifyType(PcxCityClassifyConstant.CLASSIFY_TYPE_PEAK);
        pcxBasCityPeakQO.setDataCodes(Lists.newArrayList(endCityCode));
        PeakDateQO peakDateQO = new PeakDateQO(startTime.substring(0,10),finishTime.substring(0,10));
        pcxBasCityPeakQO.setPeakDateQO(peakDateQO);
        List<CityPeakVO> cityPeakDate = pcxBasCityClassifyService.getCityPeakDate(pcxBasCityPeakQO);
        if (CollectionUtils.isNotEmpty(cityPeakDate)){
            CityPeakVO cityPeakVO = cityPeakDate.get(0);
            return cityPeakVO;
        }
        return null;
    }

    private static final DateTimeFormatter MONTH_DAY_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");

    private static boolean isCurrentDateInRange(String current,String startDateStr, String endDateStr) {
        try {
            MonthDay startDate = MonthDay.parse(startDateStr, MONTH_DAY_FORMATTER);
            MonthDay endDate = MonthDay.parse(endDateStr, MONTH_DAY_FORMATTER);
            MonthDay currentDate = MonthDay.parse(current, MONTH_DAY_FORMATTER);

            if (startDate.isBefore(endDate)) {
                return !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);
            } else {
                // 处理跨年的情况，例如 11-01 到 02-28
                return !currentDate.isAfter(endDate) || !currentDate.isBefore(startDate);
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected format: MM-dd", e);
        }
    }
    /**
     * 规则匹配
     * @param expStandVO
     * @param factors
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    protected PcxBillExpStandResult match(ExpStandVO expStandVO, String realValue,List<String> factors,String fiscal, String mofDivCode) {
        List<ColumnAndRowCode> columnAndRowCode = getColumnAndRowCode(factors);
        for (ColumnAndRowCode c : columnAndRowCode) {
            String columnValueCode = c.getColumnCode();
            String rowValueCode = c.getRowCode();
            List<StandMatchUtil.ValSet> paValsets = getValueSet(fiscal, mofDivCode, expStandVO.getValueSource(), columnValueCode, rowValueCode);
            PcxBillExpStandResult standResult = StandMatchUtil.commonMatch(expStandVO, paValsets, columnValueCode, rowValueCode, realValue);
            if (standResult != null) {
                return standResult;
            }
        }
        return null;
    }




    private List<StandMatchUtil.ValSet> getValueSet(String fiscal, String mofDivCode, String valueSource, String columnValueCode, String rowValueCode) {
        if (StringUtil.isEmpty(valueSource)) return null;
        PaValset paValset = new PaValset();
        //查询比较值集并且填充结果
        if (valueSource.equals("column")){
            paValset.setValsetCode(columnValueCode);
        }else {
            paValset.setValsetCode(rowValueCode);
        }
        paValset.setFiscal(Integer.valueOf(fiscal));
        paValset.setMofDivCode(mofDivCode);
        List<PaValset> paValsets = pcxValSetService.selectByValsetCode(paValset);
        //开始匹配
        List<StandMatchUtil.ValSet>  valSets = paValsets.stream().map(item -> {
            StandMatchUtil.ValSet valSet = new StandMatchUtil.ValSet();
            valSet.setCode(item.getValCode());
            valSet.setName(item.getVal());
            valSet.setSort(item.getOrdSeq());
            return valSet;
        }).collect(Collectors.toList());
        return valSets;
    }




    private List<ColumnAndRowCode> getColumnAndRowCode(List<String> factories){
        List<ColumnAndRowCode> columnAndRowCodes = new ArrayList<>();
        for (int i = 0; i < factories.size(); i++) {
            for (int j = i + 1; j < factories.size(); j++) {
                ColumnAndRowCode columnAndRowCode1 = new ColumnAndRowCode();
                columnAndRowCode1.setColumnCode(factories.get(i));
                columnAndRowCode1.setRowCode(factories.get(j));
                columnAndRowCodes.add(columnAndRowCode1);

                ColumnAndRowCode columnAndRowCode2 = new ColumnAndRowCode();
                columnAndRowCode2.setColumnCode(factories.get(j));
                columnAndRowCode2.setRowCode(factories.get(i));
                columnAndRowCodes.add(columnAndRowCode2);
            }
        }

        return columnAndRowCodes;
    }

    @Data
    static class ColumnAndRowCode{
        String columnCode;
        String rowCode;
    }

    public Collection getIntersection(List<String> list, List<String> condition) {
        return CollectionUtil.intersection(list, condition);
    }
}
