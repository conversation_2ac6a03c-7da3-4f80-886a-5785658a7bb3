package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.entity.bill.PcxBillExpCommon;
import org.apache.ibatis.annotations.Param;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;
import java.util.List;

/**
 * 单据费用表_通用(PcxBillExpCommon)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-25 14:23:02
 */
@Indexed
@MyBatisDao
public interface PcxBillExpCommonDao extends BaseMapper<PcxBillExpCommon>{

    /**
     * 删除
     * @param billId
     * @param expenseCode
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    default int deleteByUnionKey(String billId,String expenseCode,String agyCode,String fiscal,String mofDivCode){
        return this.delete(Wrappers.lambdaQuery(PcxBillExpCommon.class).eq(PcxBillExpCommon::getBillId,billId)
                .eq(PcxBillExpCommon::getExpenseCode,expenseCode)
                .eq(PcxBillExpCommon::getAgyCode,agyCode)
                .eq(PcxBillExpCommon::getFiscal,fiscal)
                .eq(PcxBillExpCommon::getMofDivCode,mofDivCode));
    }

    /**
     * 唯一主键查询
     * @param billId
     * @param expenseCode
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    default PcxBillExpCommon selectByUnionKey(String billId,String expenseCode,String agyCode,String fiscal,String mofDivCode){
        return this.selectOne(Wrappers.lambdaQuery(PcxBillExpCommon.class).eq(PcxBillExpCommon::getBillId,billId)
                .eq(PcxBillExpCommon::getExpenseCode,expenseCode)
                .eq(PcxBillExpCommon::getAgyCode,agyCode)
                .eq(PcxBillExpCommon::getFiscal,fiscal)
                .eq(PcxBillExpCommon::getMofDivCode,mofDivCode));
    }

}

