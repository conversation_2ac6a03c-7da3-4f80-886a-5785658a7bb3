package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.entity.bill.PcxBillExpDetailCommon;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 单据费用明细_通用_明细(PcxBillExpDetailCommon)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-05 13:36:44
 */
@Indexed
@MyBatisDao
public interface PcxBillExpDetailCommonDao extends BaseMapper<PcxBillExpDetailCommon>{

    default List<PcxBillExpDetailCommon> selectByBillId(String billId){
        return selectList(Wrappers.lambdaQuery(PcxBillExpDetailCommon.class).eq(PcxBillExpDetailCommon::getBillId, billId));
    }

}

