package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.entity.bill.PcxBillExpStandResult;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 费用支出标准结果(PcxBillExpStandResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-05 15:34:59
 */
@Indexed
@MyBatisDao
public interface PcxBillExpStandResultDao extends BaseMapper<PcxBillExpStandResult>{


    default List<PcxBillExpStandResult> selectListByBillId(String billId){
        return selectList(Wrappers.lambdaQuery(PcxBillExpStandResult.class).eq(PcxBillExpStandResult::getBillId, billId));
    }
}

