package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

/**
 * 培训费用明细表(PcxBillExpDetailTraining)表数据库访问层
 * 提供培训费相关数据的CRUD操作
 *
 * <AUTHOR>
 * @since 2025-03-04 17:42:51
 */
@Indexed
@MyBatisDao
public interface PcxBillExpDetailTrainingDao extends BaseMapper<PcxBillExpDetailTraining> {
    // 继承BaseMapper接口，默认提供基本的CRUD操作
    // 如需自定义查询方法，可在此添加（手动实现xml文件）
}