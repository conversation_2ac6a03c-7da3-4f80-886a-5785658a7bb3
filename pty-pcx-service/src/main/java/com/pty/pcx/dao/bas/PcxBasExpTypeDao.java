package com.pty.pcx.dao.bas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.bas.PcxBasExpTypeQO;
import com.pty.pcx.qo.ecs.common.QueryExpenseTypeAdditionQO;
import com.pty.pcx.vo.PcxBasExpTypeVO;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

@Indexed
@MyBatisDao
public interface PcxBasExpTypeDao extends BaseMapper<PcxBasExpType> {
    void insertSelective(PcxBasExpType pcxBasExpType);

    void deleteById(String id);

    PcxBasExpType getBaseExpByQO(PcxBasExpTypeQO qo);

    List<PcxBasExpType> selectSimpleList(PcxBasExpTypeQO qo);

    List<PcxBasExpTypeVO> getTreeData(PcxBasExpTypeQO qo);

    void deleteByQO(PcxBasExpTypeQO pcxBasExpTypeQO);

    void disEnableOrEnableById(PcxBasExpType pcxBasExpType);

    List<PcxBasExpType> selectNoBindItemExpTypeList(PcxBasExpTypeQO expTypeQO);

    List<PcxBasExpType> selectNoAdditionFormsettingExpenseDetail();

    String selectSingleExpenseDetailCode(QueryExpenseTypeAdditionQO qo);

    List<PcxBasExpType> selectCommonExpenseNoRemarkField();

    void updateByQo(PcxBasExpTypeQO qo);

    List<PcxBasExpType> selectItemExpenseTypeList(PcxBasExpTypeQO pcxBasExpTypeQO);
}