package com.pty.pcx.dao.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.dto.bill.PcxBillBalanceDTO;
import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pcx.vo.balance.ProjectBalVO;
import com.pty.pub.common.anno.MyBatisDao;
import com.pty.pub.common.util.StringUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 单据预算关联表(PcxBillBalance)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-25 16:20:59
 */
@Indexed
@MyBatisDao
public interface PcxBillBalanceDao extends BaseMapper<PcxBillBalance>{


    default List<PcxBillBalance> selectByBillId(String billId){
        return selectList(Wrappers.lambdaQuery(PcxBillBalance.class).eq(PcxBillBalance::getBillId,billId));
    }

    default List<PcxBillBalance> selectByBillIds(List<String> billIds){
        return selectList(Wrappers.lambdaQuery(PcxBillBalance.class).in(PcxBillBalance::getBillId,billIds));
    }

    default int deleteByBillId(String billId,String balanceType){
        return delete(Wrappers.lambdaQuery(PcxBillBalance.class)
                .eq(PcxBillBalance::getBillId,billId)
                .eq(StringUtil.isNotEmpty(balanceType),PcxBillBalance::getBalanceType,balanceType));
    }

    /**
     * 查询历史的单据额度
     * @param billId
     * @param balanceIds
     * @return
     */
    List<PcxBillBalance> getSumPayAmt(@Param("billId") String billId, @Param("billFunc1") String billFunc1, @Param("billFunc2") String billFunc2, @Param("balanceIds") List<String> balanceIds);

    /**
     * 在报销单终审的时候，查询所有的金额，billID 因为是报销单更新状态在查询金额之后，所以这里先查一下
     *
     */
    List<PcxBillBalance> getSumExpenseLoanBackAmt(@Param("billId") String billId, @Param("balanceIds") List<String> balanceIds);


    /**
     * 不包含当前
     * @param billId
     * @param balanceIds
     * @return
     */
    List<PcxBillBalance> getSumExpenseLoanBackAmtExeCurrBill(@Param("billId")String billId, @Param("balanceIds")List<String> balanceIds);

    /**
     * 查询项目额度的项目信息
     * @param deptCodes
     * @return
     */
    List<ProjectBalVO> getAuthIbalBalance(@Param("deptCodes") List<String> deptCodes);

    /**
     * 查询当前额度的单据信息
     * @param billFuncCode
     * @param balanceIds
     * @return
     */
    List<PcxBillBalanceDTO> selectByBalanceId(@Param("billFuncCode")String billFuncCode, @Param("balanceIds")List<String> balanceIds);

    /**
     * 根据ibalId查询预算ID
     * @param ibalId
     * @return
     */
    String getDefaultBudByIbalId(@Param("ibalId")String ibalId);
}

