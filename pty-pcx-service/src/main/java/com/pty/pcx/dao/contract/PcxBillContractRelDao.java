package com.pty.pcx.dao.contract;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pty.pcx.entity.contract.PcxBillContractRel;
import com.pty.pub.common.anno.MyBatisDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 报销单关联合同付款计划(PcxBillContractRel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-16 16:01:20
 */
@Indexed
@MyBatisDao
public interface PcxBillContractRelDao extends BaseMapper<PcxBillContractRel>{


    default PcxBillContractRel selectByBillId(String billId){
        List<PcxBillContractRel> relList = selectList(Wrappers.lambdaQuery(PcxBillContractRel.class)
                .eq(PcxBillContractRel::getBillId, billId));
        if (CollectionUtils.isNotEmpty(relList)){
            return relList.get(0);
        }
        return null;
    }
}
