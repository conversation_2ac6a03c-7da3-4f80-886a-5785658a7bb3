package com.pty.pcx.dao.bas;

import com.pty.pcx.entity.bas.PcxVehicleInfo;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;
import java.util.List;

/**
 * (PcxVehicleInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-08 11:06:57
 */
@Indexed
@MyBatisDao
public interface PcxVehicleInfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxVehicleInfo selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxVehicleInfo 实例对象
     * @return 对象列表
     */
    List<PcxVehicleInfo> selectList(PcxVehicleInfo pcxVehicleInfo);
	
    /**
     * 新增数据
     *
     * @param pcxVehicleInfo 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxVehicleInfo pcxVehicleInfo);

    /**
     * 修改数据
     *
     * @param pcxVehicleInfo 实例对象
     * @return 影响行数
     */
    int update(PcxVehicleInfo pcxVehicleInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

}
