package com.pty.pcx.dao.stand;

import com.pty.pcx.entity.stand.PcxStandAbroad;
import com.pty.pcx.qo.bill.PcxBillAbroadTripQO;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * 支出标准-出国标准(PcxStandAbroad)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-04 19:19:54
 */
@Indexed
@MyBatisDao
public interface PcxStandAbroadDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxStandAbroad selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxStandAbroad 实例对象
     * @return 对象列表
     */
    List<PcxStandAbroad> selectList(PcxStandAbroad pcxStandAbroad);
	
    /**
     * 新增数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxStandAbroad pcxStandAbroad);

    /**
     * 修改数据
     *
     * @param pcxStandAbroad 实例对象
     * @return 影响行数
     */
    int update(PcxStandAbroad pcxStandAbroad);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 按照年度、单位、区划、国家、城市集合进行删除
     * @param deleteParam
     * @return
     */
    int deleteForRebuild(PcxStandAbroad deleteParam);

        /**
     * 根据城市编码集合查询标准数据
     * @param cityCodes 城市编码集合
     * @return 标准数据列表
     */
    List<PcxStandAbroad> selectByCityCodes(PcxBillAbroadTripQO tripQO);
}
