package com.pty.pcx.dao.ecs;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pty.pcx.entity.ecscompared.PcxEcsComparedResult;
import com.pty.pcx.qo.ecs.compared.PcxEcsComparedResultQO;
import com.pty.pub.common.anno.MyBatisDao;
import org.springframework.stereotype.Indexed;

import java.util.List;

@Indexed
@MyBatisDao
public interface PcxEcsComparedResultDao  extends BaseMapper<PcxEcsComparedResult> {
    List<PcxEcsComparedResult> queryList(PcxEcsComparedResultQO qo);

    void batchInsert(List<PcxEcsComparedResult> qoList);
}
