package com.pty.pcx.dao.treasurypay.change;

import com.pty.pcx.entity.treasurypay.change.PcxChangeBill;
import com.pty.pcx.qo.treasurypay.change.PcxChangeBillQO;
import com.pty.pcx.vo.treasurypay.change.PcxChangeBillVO;
import com.pty.pub.common.anno.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Indexed;

import java.util.List;

/**
 * (PcxChangeBill)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-24 18:39:52
 */
@Indexed
@MyBatisDao
public interface PcxChangeBillDao {

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxChangeBillQo 实例对象
     * @return 对象列表
     */
    List<PcxChangeBill> select(PcxChangeBillQO pcxChangeBillQo);

    /**
     *
     * @param billIds 单据id
     * @param changeBillStatus 申请单单据状态 （默认不查询 已确认状态单据）
     * @return
     */
    List<PcxChangeBillVO> selectByBillIds(@Param("list") List<String> billIds,
                                          @Param("changeBillStatus") String changeBillStatus);

    /**
     * 基于单位、区划、创建用户来查询变更说明信息
     * @param pcxChangeBillQo
     * @return
     */
    List<String> getCorrectionDesc(@Param("userCode") String userCode,
                                   @Param("agyCode") String agyCode,
                                   @Param("mofDivCode") String mofDivCode);;
    /**
     * 基于ID查询
     * @param id
     * @return
     */
    PcxChangeBill selectById(String id);

    /**
     * 新增数据
     *
     * @param pcxChangeBill 实例对象
     * @return 影响行数
     */
    int insert(PcxChangeBill pcxChangeBill);

    /**
     * 修改数据
     *
     * @param pcxChangeBill 实例对象
     * @return 影响行数
     */
    int updateById(PcxChangeBill pcxChangeBill);


}


