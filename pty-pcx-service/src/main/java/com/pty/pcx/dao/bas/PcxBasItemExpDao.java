package com.pty.pcx.dao.bas;

import com.pty.pcx.entity.bas.PcxBasItemExp;
import com.pty.pcx.qo.bas.PcxBasItemExpQO;
import com.pty.pub.common.anno.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Indexed;

import java.util.List;

@Indexed
@MyBatisDao
public interface PcxBasItemExpDao{

    List<PcxBasItemExp> selectByQO(PcxBasItemExpQO pcxBasItemExpQO);

    int insert(PcxBasItemExp pcxBasItemExp);

    int updateById(PcxBasItemExp pcxBasItemExp);

    void deleteByItemCode(PcxBasItemExpQO pcxBasItemExpQO);

    List<String> selectValidIsLeafTypeIds();

    void updateExpenseTypeIsLeaf(@Param("ids") List<String> ids);

}
