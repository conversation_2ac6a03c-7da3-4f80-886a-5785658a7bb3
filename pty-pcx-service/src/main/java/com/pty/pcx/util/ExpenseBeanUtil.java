package com.pty.pcx.util;

import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.ItemBizTypeEnum;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.entity.bill.*;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import com.pty.pcx.service.impl.bill.handler.BillExpenseDetailService;
import com.pty.pcx.service.impl.bill.handler.BillExpenseService;
import com.pty.pcx.service.impl.ecs.handler.StandMatchHandler;
import com.pty.pub.common.util.SpringUtil;
import com.pty.pub.common.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

public class ExpenseBeanUtil {
    public static BillExpenseService<PcxBillExpBase> getBean(String code, Integer bizType) {
        //通用报销流程费用都记录在common表里面
        if (Objects.equals(bizType, ItemBizTypeEnum.COMMON.getCode())) {
            return SpringUtil.getBean(PcxBillProcessConstant.ExpenseProcessBeanEnum.DEFAULT.getProcessBeanName(), BillExpenseService.class);
        }
        if (Objects.equals(bizType, ItemBizTypeEnum.TRAVEL.getCode())) {
            return SpringUtil.getBean(PcxBillProcessConstant.ExpenseProcessBeanEnum.TRAVEL.getProcessBeanName(), BillExpenseService.class);
        }

        for (PcxBillProcessConstant.ExpenseProcessBeanEnum expenseProcessBeanEnum : PcxBillProcessConstant.ExpenseProcessBeanEnum.values()) {
            if (expenseProcessBeanEnum.getCode().equals(code)) {
                return SpringUtil.getBean(expenseProcessBeanEnum.getProcessBeanName(), BillExpenseService.class);
            }
        }
        return SpringUtil.getBean(PcxBillProcessConstant.ExpenseProcessBeanEnum.DEFAULT.getProcessBeanName(), BillExpenseService.class);
    }

    public static <T> T getBean(Class<T> clazz) {
        return SpringUtil.getBean(clazz);
    }


    public static BillExpenseDetailService<PcxBillExpDetailBase, PcxBillExpBase> getDetailBean(String code) {
        for (PcxBillProcessConstant.ExpenseDetailProcessBeanEnum expenseType : PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.values()) {
            if (expenseType.getCode().equals(code)) {
                return SpringUtil.getBean(expenseType.getProcessBeanName(), BillExpenseDetailService.class);
            } else if (code.contains(expenseType.getCode())) {
                return SpringUtil.getBean(expenseType.getProcessBeanName(), BillExpenseDetailService.class);
            }
        }
        return SpringUtil.getBean(PcxBillProcessConstant.ExpenseDetailProcessBeanEnum.DEFAULT.getProcessBeanName(), BillExpenseDetailService.class);
    }

    /**
     * 获取对应处理类
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getDetailBean(Class<T> clazz) {
        return SpringUtil.getBean(clazz);
    }

    /**
     * 获取费用bean
     *
     * @param expenseCode
     * @return
     */
    public static PcxBillExpBase getEntityBean(String expenseCode) {
        if (PcxConstant.TRAVEL_EXPENSE_30211.equals(expenseCode))
            return new PcxBillExpTravel();
        return new PcxBillExpCommon();
    }

    /**
     * 获取费用bean
     *
     * @param expenseCode
     * @return
     */
    public static PcxBillExpDetailBase getEntityDetailBean(String expenseCode) {
        if (PcxConstant.TRAVEL_EXPENSE_30211.equals(expenseCode)) {
            return new PcxBillExpDetailTravel();
        } else if (PcxConstant.MEETING_EXPENSE_30215.equals(expenseCode) || expenseCode.contains(PcxConstant.MEETING_EXPENSE_30215)) {
            return new PcxBillExpDetailMeeting();
        } else if (PcxConstant.TRAINING_EXPENSE_30216.equals(expenseCode) || expenseCode.contains(PcxConstant.TRAINING_EXPENSE_30216)) {
            return new PcxBillExpDetailTraining();
        }
        return new PcxBillExpDetailBase();
    }


    /**
     * 获取标准匹配处理器
     *
     * @param expenseDetailCode
     * @return
     */
    public static StandMatchHandler getStandMatchHandler(String expenseDetailCode) {
        for (PcxBillProcessConstant.StandMatchDetailProcessBeanEnum expenseType : PcxBillProcessConstant.StandMatchDetailProcessBeanEnum.values()) {
            if (expenseType.getCode().equals(expenseDetailCode)) {
                return SpringUtil.getBean(expenseType.getProcessBeanName(), StandMatchHandler.class);
            }
        }
        return null;
    }

    /****
     * 组装数据，给费用明细名称添加父级名称
     * @param pcxBasExpTypeList
     * @return
     */
    public static List<PcxBasExpType> getAssembleExpList(List<PcxBasExpType> pcxBasExpTypeList) {
        // 如果输入列表为null或空，直接返回
        if (pcxBasExpTypeList == null || pcxBasExpTypeList.isEmpty()) {
            return pcxBasExpTypeList;
        }
        List<PcxBasExpType> pcxBasExpTypes = new ArrayList<>(pcxBasExpTypeList);
        // 创建一个映射，用于快速查找expenseCode对应的PcxBasExpType
        Map<String, PcxBasExpType> codeToExpTypeMap = pcxBasExpTypeList.stream()
                .filter(expType -> expType.getExpenseCode() != null)
                .collect(Collectors.toMap(
                        PcxBasExpType::getExpenseCode,
                        expType -> expType,
                        (e1, e2) -> e1 // 处理重复项，保留第一个
                ));

        // 遍历每个费用类型，组装其完整的费用名称
        for (PcxBasExpType expType : pcxBasExpTypes) {
            expType.setExpenseName(buildFullExpenseName(expType, codeToExpTypeMap, new HashSet<>(),expType.getDetailLevel()));
        }
        return pcxBasExpTypes;
    }

    private static String buildFullExpenseName(PcxBasExpType expType, Map<String, PcxBasExpType> codeMap, Set<String> visited,Integer detailLevel) {
        // 如果对象或费用名称为空，返回空字符串
        if (expType == null || expType.getExpenseName() == null) {
            return "";
        }
        if(detailLevel == null) {
            return expType.getExpenseName();
        }

        String currentName = expType.getExpenseName();
        String parentCode = expType.getParentCode();
        // 如果原始费用级别为一级，直接返回当前费用名称
        if (Objects.equals(detailLevel,1)) {
            return currentName;
        }
        //如果递归进来的费用级别为1,返回空
        if(Objects.equals(expType.getDetailLevel(),1)){
            return "";
        }
        // 检查循环引用
        if (visited.contains(expType.getExpenseCode())) {
            return currentName; // 避免因循环引用导致的无限递归
        }
        // 标记当前节点为已访问
        visited.add(expType.getExpenseCode());
        // 查找父级对象
        PcxBasExpType parent = codeMap.get(parentCode);
        if (parent == null) {
            return currentName; // 未找到父级，返回当前名称
        }
        // 递归获取父级的完整名称
        String parentFullName = buildFullExpenseName(parent, codeMap, visited, detailLevel);
        // 递归完成后移除当前节点的访问标记
        visited.remove(expType.getExpenseCode());
        // 如果父级名称不为空，组合父级和当前名称
        if(StringUtil.isNotEmpty(parentFullName)){
            return parentFullName + "-" + currentName;
        }
        return currentName;
    }
}
