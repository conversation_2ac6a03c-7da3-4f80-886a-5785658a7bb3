package com.pty.pcx.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pty.mad.entity.MadProject;
import com.pty.pcx.api.bas.IPcxBasItemService;
import com.pty.pcx.api.bill.PcxBillAmtApportionDepartmentService;
import com.pty.pcx.api.bill.PcxBillService;
import com.pty.pcx.api.bill.PcxExpDetailEcsRelService;
import com.pty.pcx.api.project.IPcxProjectExpenseService;
import com.pty.pcx.api.setting.IPcxPaFieldSettingService;
import com.pty.pcx.api.treasurypay.detail.IPcxBillPayDetailService;
import com.pty.pcx.common.constant.PcxBillProcessConstant;
import com.pty.pcx.common.constant.PcxConstant;
import com.pty.pcx.common.enu.*;
import com.pty.pcx.common.util.BudgetCtrlUtil;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.balance.BudBalanceDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;
import com.pty.pcx.entity.bill.PcxBillExpBase;
import com.pty.pcx.mad.IMadAgyAccountExternalService;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.bill.PcxBillListQO;
import com.pty.pcx.qo.bill.PcxBillQO;
import com.pty.pcx.qo.bill.PcxBillViewQO;
import com.pty.pcx.qo.mad.MadAgyAccountQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailQO;
import com.pty.pcx.qo.treasurypay.detail.PayDetailShadowQO;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.service.impl.bud.PcxBudBalanceService;
import com.pty.pcx.util.trans.PcxBillTransformer;
import com.pty.pcx.vo.BaseDataVo;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.balance.ProjectBalVO;
import com.pty.pcx.vo.bill.*;
import com.pty.pcx.vo.workflow2.DoneTaskVO;
import com.pty.pcx.vo.workflow2.TodoTaskVO;
import com.pty.pub.common.util.CollectionUtil;
import com.pty.pub.common.util.PtyContext;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pty.pcx.common.constant.PcxConstant.*;
import static java.util.stream.Collectors.groupingBy;

@Component
@Slf4j
public class PcxBillViewWrapperUtil {

    @Autowired
    private BillExpenseCommonService billExpenseCommonService;
    @Autowired
    private BillMainService billMainService;

    @Autowired
    private PcxExpDetailEcsRelService pcxExpDetailEcsRelService;
    @Autowired
    private PcxBillService pcxBillService;
    @Autowired
    private IPcxBillPayDetailService pcxBillPayDetailService;
    @Autowired
    private IPcxProjectExpenseService pcxProjectExpenseService;
    @Autowired
    private IPcxPaFieldSettingService pcxPaFieldSettingService;
    @Autowired
    private IMadAgyAccountExternalService madAgyAccountExternalService;
    @Autowired
    private PcxBillAmtApportionDepartmentService pcxBillAmtApportionDepartmentService;
    @Autowired
    private PcxBudBalanceService pcxBudBalanceService;
    @Autowired
    private IPcxBasItemService pcxBasItemService;

    /**
     * 封装数据
     * @param qo
     * @param data
     */
    public void wrap(PcxBillViewQO qo, PcxBillVO data) {
        try {
            PcxBill pcxBill = data.getBasicInfo();

            //事项如果不控制预算权限则不处理经费来源
            PcxBasItemQO itemQO = new PcxBasItemQO();
            itemQO.setItemCode(pcxBill.getItemCode());
            itemQO.setMofDivCode(pcxBill.getMofDivCode());
            itemQO.setFiscal(pcxBill.getFiscal());
            itemQO.setAgyCode(pcxBill.getAgyCode());
            itemQO.setBilltypeCode(pcxBill.getBillFunc());
            boolean isCtrlBudget =pcxBasItemService.isIsCtrlBudget(itemQO);
            //如果前端有需要展示经费来源则处理经费来源
            boolean isReLoadBalance = false;
            if (isCtrlBudget) {
                try {
                    //处理经费来源
                    boolean hasFundSource = CollectionUtil.isEmpty(qo.getViewType()) || qo.getViewType().contains(PositionBlockEnum.FUND_SOURCE.getCode());
                    List<PcxBillBalanceVO> pcxBillBalances = Optional.ofNullable(data.getBudget()).orElseGet(Lists::newArrayList);

                    if (hasFundSource) {
                        isReLoadBalance = handleFundSource(qo, pcxBillBalances, pcxBill);
                    }

                    //设置前端展示的经费来源
                    if (CollectionUtil.isNotEmpty(pcxBillBalances)) {
                        List<PcxBillBalanceMergeVO> pcxBillBalanceMergeVOS = FundSourceFlushUtil.balanceToMerge(pcxBillBalances,qo.getPositionCode());
                        List<PcxBillBalanceMergeVO> pcxBillBalanceMergeVOS1 = loadDefaultBalance(pcxBillBalanceMergeVOS, qo.getPositionCode(), pcxBill);
                        data.setFundSource(pcxBillBalanceMergeVOS1);
                    }
                }catch (Exception e){
                    log.error("查看封装经费来源异常",e);
                }

            }


            //设置前端展示的结算方式
            if (CollectionUtil.isNotEmpty(data.getSettlements())) {
                try {
                    data.setSettlement(data.getSettlements().stream().collect(groupingBy(PcxBillSettlementVO::getSettlementType)));
                }catch (Exception e){
                    log.error("查看封装结算方式异常",e);
                }
            }

            //处理支付明细
            if ((Objects.isNull(data.getPayDetail()) || isReLoadBalance )&& PositionEnum.isFinance(qo.getPositionCode())) {
                try {
                    PayDetailShadowQO shadowQO = PayDetailShadowQO.builder()
                            .billId(pcxBill.getId())
                            .fundSource(data.getFundSource())
                            .allocateType(PayDetailQO.AllocateTypeEnum.ORDER)
                            .build();
                    data.setPayDetail(pcxBillPayDetailService.calculatePayDetails(PcxBillTransformer.INSTANCE.toPayDetailQO(shadowQO), Boolean.FALSE).getData());
                }catch (Exception e){
                    log.error("查看封装支付明细异常",e);
                }
            }
            try {
                //查询单位账户信息
                handleAccount(data, pcxBill);
            }catch (Exception e) {
                log.error("查看封装银行账户异常",e);
            }
            try {
                //封装主信息的其他属性（PC端定制查看）
                handleBasic(data, pcxBill);
            }catch (Exception e) {
                log.error("查看主信息的其他属性（PC端定制查看）异常",e);
            }
        }catch (Exception e){
            log.error("查看单据封装数据异常",e);
        }
    }


    /**
     * 从费用分摊初始化费用来源数据
     * @param pcxBillBalances
     * @param pcxBill
     * @return
     */
    public boolean reLoadDefault(List<PcxBillBalanceVO> pcxBillBalances, PcxBill pcxBill) {
        if (pcxBillBalances == null) {
            pcxBillBalances = Lists.newArrayList();
        }
        processMatchBalance(pcxBillBalances, pcxBill);
        return true;
    }


    /**
     * 根据费用获取经费来源
     * @param expenseCode
     * @param expenseName
     * @param pcxBill
     * @return
     */
    public List<PcxBillBalanceVO> loadDefaultByExpense(String expenseCode, String expenseName, PcxBill pcxBill) {
        List<PcxBillBalanceVO> result = Lists.newArrayList();
        List<PcxBillAmtApportionDepartment> billAmtApportionDepartments = pcxBillAmtApportionDepartmentService.selectByBillId(pcxBill.getId());
        //todo 公务支出的会议培训招待不支持分摊
        boolean isNonApportion = (!Objects.equals(ItemBizTypeEnum.COMMON.getCode(),pcxBill.getBizType())
                && (expenseCode.equals(MEETING_EXPENSE_30215) || expenseCode.equals(TRAINING_EXPENSE_30216) || expenseCode.equals(TREAT_EXPENSE_30217)))
                || CollectionUtils.isEmpty(billAmtApportionDepartments);
        if (isNonApportion){
            PcxBillBalanceVO pcxBillBalanceVO = getPcxBillBalanceVO(expenseCode, expenseName, pcxBill);
            result.add(pcxBillBalanceVO);
            return result;
        }
        List<PcxBillAmtApportionDepartment> collect = billAmtApportionDepartments.stream()
                .filter(item -> Objects.equals(expenseCode, item.getExpenseTypeCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)){
            //部门费用分组
            Map<String, List<PcxBillAmtApportionDepartment>> commonMaps = collect.stream()
                    .collect(groupingBy(item -> item.getExpenseTypeCode() + "-" + item.getDepartmentCode()));
            for (Map.Entry<String, List<PcxBillAmtApportionDepartment>> stringListEntry : commonMaps.entrySet()) {
                List<PcxBillAmtApportionDepartment> balanceVOS = stringListEntry.getValue();
                PcxBillAmtApportionDepartment department = balanceVOS.get(0);
                BigDecimal totalShareRate = balanceVOS.stream().map(PcxBillAmtApportionDepartment::getDepartmentRate).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal totalShareAmt = balanceVOS.stream().map(PcxBillAmtApportionDepartment::getDepartmentAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                PcxBillBalanceVO balance = new PcxBillBalanceVO();
                balance.setBillId(department.getBillId());
                balance.setBalanceSource(pcxBill.getBillFuncCode());
                balance.setExpenseCode(department.getExpenseTypeCode());
                balance.setExpenseName(department.getExpenseTypeName());
                balance.setBudDepartmentCode(department.getBudDepartmentCode());
                balance.setBudDepartmentName(department.getBudDepartmentName());
                balance.setDepartmentCode(department.getDepartmentCode());
                balance.setDepartmentName(department.getDepartmentName());
                balance.setShareAmt(totalShareAmt);
                balance.setUsedAmt(totalShareAmt);
                balance.setShareRate(totalShareRate);
                result.add(balance);
            }
        } else {
            result.add(getPcxBillBalanceVO(expenseCode, expenseName, pcxBill));
        }
        return result;
    }

    private PcxBillBalanceVO getPcxBillBalanceVO(String expenseCode, String expenseName, PcxBill pcxBill) {
        PcxBillBalanceVO pcxBillBalanceVO = new PcxBillBalanceVO();
        pcxBillBalanceVO.setBillId(pcxBill.getId());
        pcxBillBalanceVO.setBalanceSource(pcxBill.getBillFuncCode());
        pcxBillBalanceVO.setBillFuncCode(pcxBill.getBillFuncCode());
        pcxBillBalanceVO.setExpenseName(expenseName);
        pcxBillBalanceVO.setExpenseCode(expenseCode);
        pcxBillBalanceVO.setDepartmentCode(pcxBill.getSingleDepartmentCode());
        pcxBillBalanceVO.setDepartmentName(pcxBill.getSingleDepartmentName());

        //加载默认的部门
        PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
        pcxBalancesQO.setFiscal(pcxBill.getFiscal());
        pcxBalancesQO.setAgyCode(pcxBill.getAgyCode());
        pcxBalancesQO.setMofDivCode(pcxBill.getMofDivCode());
        pcxBalancesQO.setDepartmentCode(pcxBill.getSingleDepartmentCode());
        BaseDataVo deFaultDepartment = pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
        if (Objects.nonNull(deFaultDepartment)){
            pcxBillBalanceVO.setBudDepartmentCode(deFaultDepartment.getCode());
            pcxBillBalanceVO.setBudDepartmentName(deFaultDepartment.getName());
        }

        pcxBillBalanceVO.setShareRate(new BigDecimal(100));
        pcxBillBalanceVO.setShareAmt(Objects.nonNull(pcxBill.getInputAmt()) ? pcxBill.getInputAmt() : pcxBill.getCheckAmt());
        pcxBillBalanceVO.setUsedAmt(Objects.nonNull(pcxBill.getInputAmt()) ? pcxBill.getInputAmt() : pcxBill.getCheckAmt());
        return pcxBillBalanceVO;
    }

    /**
     * 加载默认的经费来源，指标信息，账号信息
     * @param pcxBillBalanceMergeVOs
     * @param positionCode
     * @param pcxBill
     * @return
     */
    public List<PcxBillBalanceMergeVO> loadDefaultBalance(List<PcxBillBalanceMergeVO>  pcxBillBalanceMergeVOs, String positionCode,PcxBill pcxBill) {
        try {
            for (PcxBillBalanceMergeVO pcxBillBalanceMergeVO : pcxBillBalanceMergeVOs) {
                // 加载默认付款账户
                if (StringUtil.isEmpty(pcxBillBalanceMergeVO.getPayAccountNo())) {
                    loadDefaultPayAccount(pcxBillBalanceMergeVO, pcxBill);
                }

                // 加载默认的费用承担部门
                if (StringUtil.isEmpty(pcxBillBalanceMergeVO.getDepartmentCode())) {
                    loadDefaultDepartment(pcxBillBalanceMergeVO, pcxBill);
                }

                //加载默认预算部门
                if (StringUtil.isEmpty(pcxBillBalanceMergeVO.getBudDepartmentCode())) {
                    loadDefaultBudDepartment(pcxBillBalanceMergeVO, pcxBill);
                }

                // 加载默认经费来源
                if (StringUtil.isEmpty(pcxBillBalanceMergeVO.getProjectBalanceNo())) {
                    loadDefaultProjectBalance(pcxBillBalanceMergeVO, pcxBill);
                }

                // 加载默认指标
                loadDefaultBudBalance(pcxBillBalanceMergeVO);
            }
        }catch(Exception e){
            // 捕获异常并记录日志
            log.error("加载默认值时发生异常", e);
            throw new RuntimeException("加载默认值失败", e);
        }

        if (!PositionEnum.isFinance(positionCode)){
            return getMakeBillBalanceVO(pcxBillBalanceMergeVOs);
        }
        return pcxBillBalanceMergeVOs;
    }

    private List<PcxBillBalanceMergeVO> getMakeBillBalanceVO(List<PcxBillBalanceMergeVO> pcxBillBalanceMergeVOs) {
        //按部门进行分组统计
        List<PcxBillBalanceMergeVO> newLists = Lists.newArrayList();
        List<PcxBillBalanceMergeVO> megreList = pcxBillBalanceMergeVOs.stream().
                filter(item -> StringUtil.isNotEmpty(item.getProjectBalanceNo())).collect(Collectors.toList());
        List<PcxBillBalanceMergeVO> noMegreList = pcxBillBalanceMergeVOs.stream().
                filter(item -> StringUtil.isEmpty(item.getProjectBalanceNo())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noMegreList)){
            newLists.addAll(noMegreList);
        }
        if (CollectionUtil.isNotEmpty(megreList)){
            //按照费用部门预算项目进行分组
            Map<String, List<PcxBillBalanceMergeVO>> map = megreList.stream().collect(Collectors.groupingBy(item->item.getExpenseCode()+item.getDepartmentCode()+item.getProjectBalanceNo()));
            for (Map.Entry<String, List<PcxBillBalanceMergeVO>> entry : map.entrySet()) {
                List<PcxBillBalanceMergeVO> balanceVOS = entry.getValue();
                BigDecimal totalShareRate = balanceVOS.stream().map(PcxBillBalanceMergeVO::getShareRate).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal totalShareAmt = balanceVOS.stream().map(PcxBillBalanceMergeVO::getShareAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal projectUsedAmt = balanceVOS.stream().map(PcxBillBalanceMergeVO::getProjectUsedAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                PcxBillBalanceMergeVO balanceVO = balanceVOS.get(0);
                PcxBillBalanceMergeVO mergeVO = new PcxBillBalanceMergeVO();
                BeanUtils.copyProperties(balanceVO, mergeVO);
                mergeVO.setShareAmt(totalShareAmt);
                mergeVO.setShareRate(totalShareRate);
                mergeVO.setProjectUsedAmt(projectUsedAmt);
                mergeVO.setProjectBalanceId(balanceVO.getProjectBalanceId());
                mergeVO.setProjectBalanceNo(balanceVO.getProjectBalanceNo());
                mergeVO.setBalanceNo(null);
                mergeVO.setBalanceId(null);
                mergeVO.setBalanceType(null);
                mergeVO.setUsedAmt(null);
                newLists.add(mergeVO);
            }
        }

        return newLists;
    }

    private void handleAccount(PcxBillVO data, PcxBill pcxBill) {
        MadAgyAccountQO query = MadAgyAccountQO.builder()
                .agyCode(pcxBill.getAgyCode())
                .mofDivCode(pcxBill.getMofDivCode())
                .fiscal(pcxBill.getFiscal())
                .build();
        List<JSONObject> accountCodes = madAgyAccountExternalService.getAgyAccountCodeAndDesc(query);
        data.setAgyAccounts(accountCodes);
    }

    private void handleBasic(PcxBillVO data, PcxBill pcxBill) {
        if (CollectionUtils.isNotEmpty(data.getSpecificity())) {
            List<PcxBillExpBase> specificity = data.getSpecificity();
            Optional<PcxBillExpBase> first = specificity.stream().filter(item -> item.getExpenseCode().equals(PcxBillProcessConstant.ExpenseProcessBeanEnum.TRAVEL.getCode())).findFirst();
            if (first.isPresent()) {
                PcxBillExpBase travel = first.get();
                pcxBill.setContent(travel.createContent(pcxBill.getClaimantName(), pcxBill.getReason()));
            }
        }
        if (StringUtil.isEmpty(pcxBill.getContent())) {
            pcxBill.setContent(String.format("%s，%s，", pcxBill.getClaimantName(), pcxBill.getReason()));
        }
        pcxBill.setEcsCount(pcxExpDetailEcsRelService.countEcs(pcxBill.getId()));
        if (CollectionUtils.isNotEmpty(data.getAttachList())) {
            pcxBill.setAttachCount(data.getAttachList().size());
        }

        PcxBillQO pcxBillQO = new PcxBillQO();
        pcxBillQO.setBillId(pcxBill.getId());
        pcxBillQO.setAgyCode(pcxBill.getAgyCode());
        pcxBillQO.setFiscal(pcxBill.getFiscal());
        pcxBillQO.setMofDivCode(pcxBill.getMofDivCode());
        pcxBill.setTodoStatus(getTodoStatus(pcxBillQO));
    }


    /**
     * 查询单据的审批状态信息
     *
     * @param pcxBillQO
     * @return
     */
    private String getTodoStatus(PcxBillQO pcxBillQO) {
        PcxBillListQO qo = new PcxBillListQO();
        BeanUtil.copyProperties(pcxBillQO, qo);
        //设置填报人
        qo.setClaimantUserCode(PtyContext.getUsername());
        List<TodoTaskVO> todoTasks = pcxBillService.fetchTodoTasks(qo);
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(todoTasks)) {
            List<String> bizIds = todoTasks.stream().map(TodoTaskVO::getBusiId).collect(Collectors.toList());
            if (cn.hutool.core.collection.CollectionUtil.contains(bizIds, pcxBillQO.getBillId())) {
                //待审批
                return BillApproveStatusEnum.SAVE.getCode();
            }
        }

        List<DoneTaskVO> doneTasks = pcxBillService.fetchDoneTasks(qo);
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(doneTasks)) {
            List<String> bizIds = doneTasks.stream().map(DoneTaskVO::getBusinessKey).collect(Collectors.toList());
            if (cn.hutool.core.collection.CollectionUtil.contains(bizIds, pcxBillQO.getBillId())) {
                //待审批
                return BillApproveStatusEnum.APPROVED.getCode();
            }
        }

        List<TodoTaskVO> approvingTasks = pcxBillService.fetchApprovingTasks(qo);
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(approvingTasks)) {
            List<String> bizIds = approvingTasks.stream().map(TodoTaskVO::getBusiId).collect(Collectors.toList());
            if (cn.hutool.core.collection.CollectionUtil.contains(bizIds, pcxBillQO.getBillId())) {
                //待审批
                return BillApproveStatusEnum.APPROVING.getCode();
            }
        }
        return null;
    }

    private boolean handleFundSource(PcxBillViewQO qo, List<PcxBillBalanceVO> pcxBillBalances, PcxBill pcxBill) {
        String relPositionCode = qo.getPositionCode();
        String billFuncCode = pcxBill.getBillFuncCode();

        //财务岗需要带出关联的申请和借款的指标信息
        if (PositionEnum.isFinance(qo.getPositionCode())) {
            handleFinanceAudit(pcxBillBalances, pcxBill, relPositionCode, billFuncCode);
        }

        boolean isLoadDefault = reLoadDefault(pcxBillBalances, pcxBill);
        return isLoadDefault;
    }

    private void handleFinanceAudit(List<PcxBillBalanceVO> pcxBillBalances, PcxBill pcxBill, String relPositionCode, String billFuncCode) {
        if (StringUtil.isNotBlank(relPositionCode)
                && PositionEnum.isFinance(relPositionCode)
                && pcxBillBalances.stream().noneMatch(balance -> BalanceTypeEnum.BUD.getCode().equals(balance.getBalanceType()))
                && BillFuncCodeEnum.EXPENSE.getCode().equals(billFuncCode)) {
            handleRelation(pcxBill, pcxBillBalances, BillFuncCodeEnum.APPLY.getCode(), billExpenseCommonService::getApplyRelation);
            handleRelations(pcxBill, pcxBillBalances, BillFuncCodeEnum.LOAN.getCode(), billExpenseCommonService::getLoanRelation);
        }
    }

    private void processMatchBalance(List<PcxBillBalanceVO> pcxBillBalances,  PcxBill pcxBill) {
        List<PcxBillAmtApportionDepartment> commons = pcxBillAmtApportionDepartmentService.selectByBillId(pcxBill.getId());

        String expenseCode = pcxBill.getExpenseCodes();
        //todo 公务支出的会议培训招待不支持分摊
        boolean isNonApportion = (!Objects.equals(ItemBizTypeEnum.COMMON.getCode(),pcxBill.getBizType())
                && (expenseCode.equals(MEETING_EXPENSE_30215) || expenseCode.equals(TRAINING_EXPENSE_30216) || expenseCode.equals(TREAT_EXPENSE_30217)))
                || CollectionUtils.isEmpty(commons);
        //如果commons为空 默认生成一个分摊
        if (isNonApportion) {
            commons = Lists.newArrayList();
            PcxBillAmtApportionDepartment common = new PcxBillAmtApportionDepartment();
            common.setBillId(pcxBill.getId());
            common.setDepartmentCode(pcxBill.getSingleDepartmentCode());
            common.setDepartmentName(pcxBill.getSingleDepartmentName());
            common.setDepartmentRate(new BigDecimal(100));
            common.setDepartmentAmt(pcxBill.getInputAmt());
            common.setInputAmt(pcxBill.getInputAmt());
            PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
            pcxBalancesQO.setFiscal(pcxBill.getFiscal());
            pcxBalancesQO.setAgyCode(pcxBill.getAgyCode());
            pcxBalancesQO.setMofDivCode(pcxBill.getMofDivCode());
            pcxBalancesQO.setDepartmentCode(pcxBill.getDepartmentCode());
            BaseDataVo deFaultDepartment = pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
            if (Objects.nonNull(deFaultDepartment)){
                common.setBudDepartmentCode(deFaultDepartment.getCode());
                common.setBudDepartmentName(deFaultDepartment.getName());
            }
            //如果只有一个费用则设置，如果不是则设置为通用的
            if (StringUtil.isNotEmpty(pcxBill.getExpenseCodes()) && pcxBill.getExpenseCodes().split(",").length == 1 && ObjectUtil.notEqual(pcxBill.getExpenseCodes(), PcxConstant.UNIVERSAL_EXPENSE_CODE)) {
                common.setExpenseTypeCode(pcxBill.getExpenseCodes());
                common.setExpenseTypeName(pcxBill.getExpenseNames());
            } else {
                common.setExpenseTypeCode(PcxConstant.UNIVERSAL_EXPENSE_CODE);
                common.setExpenseTypeName(PcxConstant.UNIVERSAL_EXPENSE_NAME);
            }
            commons.add(common);
        }
        //按照费用和部门进行分组
        Map<String, List<PcxBillAmtApportionDepartment>> commonMaps = commons.stream()
                .collect(groupingBy(item -> item.getExpenseTypeCode() + "-" + item.getDepartmentCode()));
        //汇总总金额
        BigDecimal totalAmt = commons.stream().map(PcxBillAmtApportionDepartment::getDepartmentAmt)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (totalAmt.compareTo(BigDecimal.ZERO) > 0){
            //重新生成分摊数据并且计算比例
            List<PcxBillAmtApportionDepartment> newCommon = Lists.newArrayList();
            for (Map.Entry<String, List<PcxBillAmtApportionDepartment>> stringListEntry : commonMaps.entrySet()) {
                List<PcxBillAmtApportionDepartment> value = stringListEntry.getValue();
                PcxBillAmtApportionDepartment pcxBillAmtApportionDepartment = value.get(0);
                BigDecimal itemTotal = value.stream().map(PcxBillAmtApportionDepartment::getDepartmentAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                pcxBillAmtApportionDepartment.setDepartmentAmt(itemTotal);
                //如果是最后一次循环 比例通过100去减以前的
                if (!newCommon.isEmpty() && newCommon.size() == commonMaps.size() - 1) {
                    BigDecimal bigDecimal = newCommon.stream()
                            .map(PcxBillAmtApportionDepartment::getDepartmentRate)
                            .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    pcxBillAmtApportionDepartment.setDepartmentRate(BigDecimal.valueOf(100).subtract(bigDecimal));
                } else {
                    pcxBillAmtApportionDepartment.setDepartmentRate(itemTotal.divide(totalAmt, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
                }
                newCommon.add(pcxBillAmtApportionDepartment);
            }
            //重新赋值
            commons = newCommon;
        }

        // 移除费用类型不存在的数据
        Set<String> commonExpenseCodes = commons.stream()
                .map(item->item.getExpenseTypeCode()+"-"+item.getDepartmentCode())
                .collect(Collectors.toSet());
        pcxBillBalances.removeIf(item -> !commonExpenseCodes.contains(item.getExpenseCode()+"-"+item.getDepartmentCode()));

        //将费用分摊转换成经费来源
        for (PcxBillAmtApportionDepartment department : commons) {
            List<PcxBillBalanceVO> billBalanceVOS = pcxBillBalances.stream()
                    .filter(balanceVO -> Objects.equals(balanceVO.getExpenseCode(), department.getExpenseTypeCode())
                                         && Objects.equals(balanceVO.getDepartmentCode(), department.getDepartmentCode())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(billBalanceVOS) ){
                billBalanceVOS.forEach(balanceVO -> {
                    balanceVO.setShareRate(department.getDepartmentRate());
                    balanceVO.setShareAmt(department.getDepartmentAmt());
                    balanceVO.setDepartmentCode(department.getDepartmentCode());
                    balanceVO.setDepartmentName(department.getDepartmentName());
                    balanceVO.setExpenseCode(department.getExpenseTypeCode());
                    balanceVO.setExpenseName(department.getExpenseTypeName());
                });
            }else {
                PcxBillBalanceVO balance = new PcxBillBalanceVO();
                balance.setBillId(department.getBillId());
                balance.setBalanceSource(pcxBill.getBillFuncCode());
                balance.setExpenseCode(department.getExpenseTypeCode());
                balance.setExpenseName(department.getExpenseTypeName());
                balance.setDepartmentCode(department.getDepartmentCode());
                balance.setDepartmentName(department.getDepartmentName());
                balance.setBudDepartmentCode(department.getBudDepartmentCode());
                balance.setBudDepartmentName(department.getBudDepartmentName());
                balance.setBalanceType(BalanceTypeEnum.IBAL.getCode());
                balance.setUsedAmt(department.getDepartmentAmt());
                balance.setShareRate(department.getDepartmentRate());
                balance.setShareAmt(department.getDepartmentAmt());

                pcxBillBalances.add(balance);
            }
        }
    }

    private void handleRelation(PcxBill pcxBill, List<PcxBillBalanceVO> pcxBillBalances, String balanceSource, Function<PcxBill, PcxBillRelationVO> relationGetter) {
        PcxBillRelationVO relation = relationGetter.apply(pcxBill);
        if (relation != null && !containsBalanceSource(pcxBillBalances, balanceSource)) {
            addRelatedBalances(pcxBill, pcxBillBalances, relation, true);
        }
    }

    private void handleRelations(PcxBill pcxBill, List<PcxBillBalanceVO> pcxBillBalances, String balanceSource, Function<PcxBill, List<PcxBillRelationVO>> relationsGetter) {
        List<PcxBillRelationVO> relations = relationsGetter.apply(pcxBill);
        if (CollectionUtil.isNotEmpty(relations) && !containsBalanceSource(pcxBillBalances, balanceSource)) {
            relations.forEach(relation -> addRelatedBalances(pcxBill, pcxBillBalances, relation, false));
        }
    }

    private boolean containsBalanceSource(List<PcxBillBalanceVO> pcxBillBalances, String balanceSource) {
        return pcxBillBalances.stream().anyMatch(item -> balanceSource.equals(item.getBalanceSource()));
    }

    private void addRelatedBalances(PcxBill mainBill, List<PcxBillBalanceVO> pcxBillBalances, PcxBillRelationVO billRelationVO, boolean isMerge) {
        try {
            PcxBill relPcxBill = billMainService.view(billRelationVO.getRelBillId());
            if (relPcxBill == null) {
                return;
            }
            List<PcxBillBalanceVO> relatedBalances = billExpenseCommonService.getFundSource(relPcxBill);
            if (CollectionUtil.isNotEmpty(relatedBalances)) {
                int index = 0;
                for (PcxBillBalanceVO item : relatedBalances) {
                    boolean flag = false;
                    if (isMerge) {
                        flag = mergeBalances(pcxBillBalances, billRelationVO, item);
                    }
                    if (!flag) {
                        addNewBalance(mainBill, pcxBillBalances, billRelationVO, item, index);
                        index++;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取关联指标信息失败", e);
        }
    }

    private boolean mergeBalances(List<PcxBillBalanceVO> pcxBillBalances, PcxBillRelationVO billRelationVO, PcxBillBalanceVO item) {
        return pcxBillBalances.stream()
                .filter(old -> old.getBalanceId().equals(item.getBalanceId()))
                .findFirst()
                .map(old -> {
                    old.setRelBillId(billRelationVO.getRelBillId());
                    old.setRelBillNo(billRelationVO.getRelBillNo());
                    old.setBalanceSource(billRelationVO.getRelBillFuncCode());
                    if (billRelationVO.getRelBillFuncCode().equals(BillFuncCodeEnum.LOAN.getCode())) {
                        old.setLoanUsedAmt(billRelationVO.getCheckAmt().subtract(billRelationVO.getLoanAmt()));
                    }
                    if (billRelationVO.getRelBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode())) {
                        old.setApplyUsedAmt(billRelationVO.getCheckAmt().subtract(billRelationVO.getLoanAmt()));
                    }
                    return true;
                }).orElse(false);
    }

    private void addNewBalance(PcxBill mainBill, List<PcxBillBalanceVO> pcxBillBalances, PcxBillRelationVO billRelationVO, PcxBillBalanceVO item, int index) {
        item.setId(null);
        item.setBillId(mainBill.getId());
        item.setRelBillId(billRelationVO.getRelBillId());
        item.setRelBillNo(billRelationVO.getRelBillNo());
        item.setBalanceSource(billRelationVO.getRelBillFuncCode());
        if (billRelationVO.getRelBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode())) {
            item.setUsedAmt(billRelationVO.getUsedAmt());
        } else {
            if (index == 0 && item.getBalanceType().equals(BalanceTypeEnum.IBAL.getCode())) {
                item.setUsedAmt(billRelationVO.getUsedAmt());
            } else {
                item.setUsedAmt(BigDecimal.ZERO);
            }
        }
        if (billRelationVO.getRelBillFuncCode().equals(BillFuncCodeEnum.LOAN.getCode())) {
            item.setLoanUsedAmt(billRelationVO.getCheckAmt().subtract(billRelationVO.getLoanAmt()));
        }
        if (billRelationVO.getRelBillFuncCode().equals(BillFuncCodeEnum.APPLY.getCode())) {
            item.setApplyUsedAmt(billRelationVO.getCheckAmt().subtract(billRelationVO.getLoanAmt()));
        }
        pcxBillBalances.add(item);
    }

    private void loadDefaultPayAccount(PcxBillBalanceMergeVO pcxBillBalanceMergeVO, PcxBill pcxBill) {
        MadAgyAccountQO query = MadAgyAccountQO.builder()
                .agyCode(pcxBill.getAgyCode())
                .mofDivCode(pcxBill.getMofDivCode())
                .fiscal(pcxBill.getFiscal())
                .build();

        List<JSONObject> accountCodes = madAgyAccountExternalService.getAgyAccountCodeAndDesc(query);

        if (CollectionUtils.isNotEmpty(accountCodes)) {
            List<JSONObject> basicAccounts = accountCodes.stream()
                    .filter(acc -> "06".equals(acc.getString("type")))
                    .collect(Collectors.toList());

            if (basicAccounts.size() == 1) {
                JSONObject account = basicAccounts.get(0);
                pcxBillBalanceMergeVO.setPayAccountName(StrUtil.firstNonBlank(account.getString("name")));
                pcxBillBalanceMergeVO.setPayAccountNo(StrUtil.firstNonBlank(account.getString("code")));
            }
        }
    }

    private void loadDefaultDepartment(PcxBillBalanceMergeVO pcxBillBalanceMergeVO, PcxBill pcxBill) {
        pcxBillBalanceMergeVO.setDepartmentCode(pcxBill.getSingleDepartmentCode());
        pcxBillBalanceMergeVO.setDepartmentName(pcxBill.getSingleDepartmentName());
    }

    private void loadDefaultBudDepartment(PcxBillBalanceMergeVO pcxBillBalanceMergeVO, PcxBill pcxBill) {
        PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
        pcxBalancesQO.setFiscal(String.valueOf(pcxBill.getFiscal()));
        pcxBalancesQO.setMofDivCode(pcxBill.getMofDivCode());
        if(StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getDepartmentCode())){
            pcxBalancesQO.setDepartmentCode(pcxBillBalanceMergeVO.getDepartmentCode());
        }else {
            pcxBalancesQO.setDepartmentCode(pcxBill.getSingleDepartmentCode());
        }
        BaseDataVo deFaultDepartment = pcxBudBalanceService.getDeFaultDepartment(pcxBalancesQO);
        if (deFaultDepartment != null) {
            pcxBillBalanceMergeVO.setBudDepartmentCode(deFaultDepartment.getCode());
            pcxBillBalanceMergeVO.setBudDepartmentName(deFaultDepartment.getName());
        }
    }

    private void loadDefaultProjectBalance(PcxBillBalanceMergeVO pcxBillBalanceMergeVO, PcxBill pcxBill) {
        if (StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getExpenseCode())
                && StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getBudDepartmentCode())) {

            List<MadProject> madProjects = pcxProjectExpenseService.selectByExpenseAndDepartment(
                    pcxBill.getAgyCode(),pcxBill.getFiscal(),pcxBill.getMofDivCode(),
                    pcxBillBalanceMergeVO.getExpenseCode());

            if (CollectionUtil.isNotEmpty(madProjects)) {
                PcxBalancesQO pcxBalancesQO = new PcxBalancesQO();
                pcxBalancesQO.setAgyCode(pcxBill.getAgyCode());
                pcxBalancesQO.setFiscal(pcxBill.getFiscal());
                pcxBalancesQO.setExpenseCode(pcxBillBalanceMergeVO.getExpenseCode());
                pcxBalancesQO.setMofDivCode(pcxBill.getMofDivCode());
                pcxBalancesQO.setProjectCodes(madProjects.stream().map(MadProject::getMadCode).collect(Collectors.toList()));
                pcxBalancesQO.setDepartmentCode(pcxBillBalanceMergeVO.getBudDepartmentCode());

                CheckMsg<List<ProjectBalVO>> projectData = pcxPaFieldSettingService.getProjectData(pcxBalancesQO);
                if (projectData.isSuccess() && CollectionUtil.isNotEmpty(projectData.getData())) {
                    List<ProjectBalVO> balances = projectData.getData();
                    ProjectBalVO balance = balances.get(0);
                    pcxBillBalanceMergeVO.setProjectBalanceId(balance.getBalanceId());
                    pcxBillBalanceMergeVO.setProjectBalanceNo(balance.getBalanceNo());
                    pcxBillBalanceMergeVO.setProjectCode(balance.getProjectCode());
                    pcxBillBalanceMergeVO.setProjectName(balance.getProjectName());
                }
            }
        }
    }

    /**
     * 加载指标信息
     */
    private void loadDefaultBudBalance(PcxBillBalanceMergeVO pcxBillBalanceMergeVO) {
        BudBalanceDTO balance = null;
        //无指标信息时使用经费来源信息查询
        if (StringUtil.isEmpty(pcxBillBalanceMergeVO.getBalanceNo()) && StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getProjectBalanceId())) {
            balance = pcxBudBalanceService.getDefaultBudByIbalId(pcxBillBalanceMergeVO.getProjectBalanceId());
        }else if (StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getBalanceNo()) && StringUtil.isNotEmpty(pcxBillBalanceMergeVO.getBalanceId())) {
            //直接使用指标信息查询
            balance = pcxBudBalanceService.getBalanceByIds(pcxBillBalanceMergeVO.getBalanceId());
        }
        if (Objects.nonNull(balance)) {
            pcxBillBalanceMergeVO.setBalanceId(balance.getBalanceId());
            pcxBillBalanceMergeVO.setBalanceNo(balance.getBalanceNo());
            pcxBillBalanceMergeVO.setBalanceType(BalanceTypeEnum.BUD.getCode());
            pcxBillBalanceMergeVO.setBalanceAmt(balance.getBalanceAmt());
            pcxBillBalanceMergeVO.setTotalAmt(balance.getTotalAmt());

            //预算项
            pcxBillBalanceMergeVO.setBuditem(balance.getBuditemCode() + balance.getBuditemName());
            pcxBillBalanceMergeVO.setBuditemCode(balance.getBuditemCode());
            pcxBillBalanceMergeVO.setBuditemName(balance.getBuditemName());
            //部门经济分类
            pcxBillBalanceMergeVO.setExpeco(balance.getExpecoCode() + balance.getExpecoName());
            pcxBillBalanceMergeVO.setExpecoCode(balance.getExpecoCode());
            pcxBillBalanceMergeVO.setExpecoName(balance.getExpecoName());
            //预算来源
            pcxBillBalanceMergeVO.setBudsource(balance.getBudsourceCode() + balance.getBudsourceName());
            pcxBillBalanceMergeVO.setBudsourceCode(balance.getBudsourceCode());
            pcxBillBalanceMergeVO.setBudsourceName(balance.getBudsourceName());
            //资金类型
            pcxBillBalanceMergeVO.setFundtype(balance.getFundtypeCode() + balance.getFundtypeName());
            pcxBillBalanceMergeVO.setFundtypeCode(balance.getFundtypeCode());
            pcxBillBalanceMergeVO.setFundtypeName(balance.getFundtypeName());
        }
    }
}