package com.pty.pcx.util;

import com.alibaba.fastjson.JSONObject;
import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pub.common.util.IDGenerator;
import com.pty.pub.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * sql动态生成
 * <AUTHOR>
 * @since 2024/11/27
 */
@Slf4j
public class SqlSymbolUtil {
    public static final String BIG_DECIMAL = "BigDecimal";
    public static final String BLOB = "Blob";
    public static final String DATE = "Date";
    public static final String DATE_TIME = "DateTime";
    public static final String TIME = "Time";
    public static final String DOUBLE = "double";
    public static final String INT = "int";
    public static final String STRING = "string";
    public static final String TEXT = "Text";
    public static final String TEXT_AREA = "textarea";
    public static final String BIG_INT = "bigint";

    enum DbType{
        Mysql,
        Oracle,
        DM,
    }
    /**
     * 生成ID
     * @return
     */
    private static String getIdWorkerId() {
        return IDGenerator.id();
    }


    /**
     *
     * 获取动态插入数据语句
     * @param tbname
     * @param fields
     * @param jsonObject
     * @return
     */
    public static Map<String, Object> getInsertSql(String tbname, List<PcxBasFormSetting> fields, JSONObject jsonObject) {
        StringBuilder fieldSql = new StringBuilder();
        StringBuilder valueSql = new StringBuilder();
        String idValue = getIdWorkerId();

        Map<String, Object> hashMap = new HashMap<>();
        Iterator<PcxBasFormSetting> iterator = fields.iterator();
        while (iterator.hasNext()) {
            PcxBasFormSetting onlCgformField = iterator.next();
            String dbFieldName = onlCgformField.getFieldValue();
            // 拼接SQL: xxx = #{yyy}
            String str = getSql(onlCgformField, jsonObject, hashMap);
            if (fieldSql.length() == 0) {
                fieldSql.append(dbFieldName);
                valueSql.append(str);
            } else {
                fieldSql.append("," + dbFieldName);
                valueSql.append("," + str);
            }
        }
        String ddl = "insert into " + getSubstring(tbname) + "(" + "id," + fieldSql + ") values(" + idValue + "," + valueSql + ")";

        hashMap.put("execute_sql_string", ddl);
        hashMap.put("id", idValue);
        log.info("--动态表单保存sql--> {}" , ddl);
        return hashMap;

    }


    /**
     * 获取动态更新sql语句
     * @param tableName
     * @param fields
     * @param jsonObject
     * @return
     */
    public static Map<String, Object> getUpdateSql(String tableName, List<PcxBasFormSetting> fields, JSONObject jsonObject) {
        StringBuilder stringBuilder = new StringBuilder();
        Map<String, Object> hashMap = new HashMap<>();
        Iterator<PcxBasFormSetting> iterator = fields.iterator();

        while (iterator.hasNext()) {
            PcxBasFormSetting onlCgformField = iterator.next();
            String dbFieldName = onlCgformField.getFieldValue();
            String str = getSql(onlCgformField, jsonObject, hashMap);
            stringBuilder.append(dbFieldName + "=" + str + ",");
        }

        String updateField = stringBuilder.toString();
        if (updateField.endsWith(",")) {
            updateField = updateField.substring(0, updateField.length() - 1);
        }

        String sql = "update " + getSubstring(tableName) + " set " + updateField + " where 1=1  " + " AND " + "id" + "='" + jsonObject.getString("id")+"'";
        log.info("--动态sql--> {} ", sql);
        hashMap.put("execute_sql_string", sql);
        return hashMap;

    }


    public static String getSubstring(String s) {
        return Pattern.matches("^[a-zA-z].*\\$\\d+$", s) ? s.substring(0, s.lastIndexOf("$")) : s;
    }

    public static String getSql(PcxBasFormSetting onlCgformField, JSONObject jsonObject, Map<String, Object> map) {
        // 获取数据表字段类型,转小写
        String dbType = onlCgformField.getDataTypeCode().toLowerCase();
        // 获取数据表字段名
        String dbFieldName = onlCgformField.getFieldValue();
        if (jsonObject.get(dbFieldName) == null) {
            return "null";
        } else if (INT.toLowerCase().equals(dbType)) {//int
            map.put(dbFieldName, jsonObject.getIntValue(dbFieldName));
            return "#{" + dbFieldName + ",jdbcType=INTEGER}";
        } else if (BIG_INT.toLowerCase().equals(dbType)) {
            map.put(dbFieldName, jsonObject.getLongValue(dbFieldName));
            return "#{" + dbFieldName + ",jdbcType=BIGINT}";
        } else if (DOUBLE.toLowerCase().equals(dbType)) {
            map.put(dbFieldName, jsonObject.getDoubleValue(dbFieldName));
            return "#{" + dbFieldName + ",jdbcType=DOUBLE}";
        } else if (BIG_DECIMAL.toLowerCase().equals(dbType)) {
            map.put(dbFieldName, new BigDecimal(jsonObject.getString(dbFieldName)));
            return "#{" + dbFieldName + ",jdbcType=DECIMAL}";
        } else if (BLOB.toLowerCase().equals(dbType)) {
            map.put(dbFieldName, jsonObject.getString(dbFieldName) != null ? jsonObject.getString(dbFieldName).getBytes() : null);
            return "#{" + dbFieldName + ",jdbcType=BLOB}";
        } else {
            String str = jsonObject.getString(dbFieldName);
            if (StringUtil.isEmpty(str)){
                map.put(dbFieldName, "");
            }else {
                map.put(dbFieldName, str.trim());
            }
            return "#{" + dbFieldName + ",jdbcType=VARCHAR}";
        }
    }
}
