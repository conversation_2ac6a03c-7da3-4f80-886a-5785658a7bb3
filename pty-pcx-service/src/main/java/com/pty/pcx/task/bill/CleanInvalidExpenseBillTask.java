package com.pty.pcx.task.bill;

import com.pty.pcx.api.ecs.EcsProcessService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class CleanInvalidExpenseBillTask implements Job {

    @Resource
    private EcsProcessService ecsProcessService;
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String executeInterval = mergedJobDataMap.getString("param");
        log.info("CleanInvalidExpenseBillTask::run, param:{}", executeInterval);
        long interval = 0;
        try {
            interval = Long.parseLong(executeInterval)*1000;
        } catch (NumberFormatException e) {
            interval = 300000;
        }
        try {
            ecsProcessService.cleanInvalidExpenseBill(interval);
        } catch (Exception e) {
            log.error("CleanInvalidExpenseBillTask,error", e);
        }

    }
}
