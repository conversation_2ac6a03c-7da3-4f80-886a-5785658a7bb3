package com.pty.pcx.task.exptype;

import com.pty.pcx.api.bas.PcxBasFormSettingService;
import com.pty.pcx.api.ecs.EcsProcessService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class InitExpenseTypeAdditionTask implements Job {

    @Resource
    private PcxBasFormSettingService pcxBasFormSettingService;
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("InitExpenseTypeAdditionTask::run");
        try {
            pcxBasFormSettingService.initExpenseTypeAddition();
        } catch (Exception e) {
            log.error("InitExpenseTypeAdditionTask,error", e);
        }

    }
}
