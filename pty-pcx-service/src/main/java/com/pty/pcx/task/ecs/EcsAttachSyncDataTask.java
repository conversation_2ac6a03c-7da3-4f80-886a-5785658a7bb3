package com.pty.pcx.task.ecs;

import com.pty.pcx.api.wit.IWitAuditRuleService;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.service.impl.bill.handler.BillMainService;
import com.pty.pcx.service.impl.bill.handler.impl.BillExpenseCommonService;
import com.pty.pcx.service.impl.ecs.EcsExpTransService;
import com.pty.pcx.vo.bill.PcxBillAttachRelationVO;
import com.pty.pcx.vo.bill.PcxBillRelationVO;
import com.pty.pub.entity.PaPartition;
import com.pty.pub.schedule.quartz.AbstractPartitionQuartzJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class EcsAttachSyncDataTask extends AbstractPartitionQuartzJob<String> {
    public static final String BIZ_TYPE = "pty_ecs_attach_data";

    @Autowired
    private BillMainService billMainService;
    @Autowired
    private BillExpenseCommonService billExpenseCommonService;
    @Autowired
    private IWitAuditRuleService witAuditRuleService;
    @Autowired
    private EcsExpTransService ecsExpTransService;

    @Override
    public String getBizType() {
        return BIZ_TYPE;
    }

    @Override
    public void execute(PaPartition<String> task) {
        String billId = task.getBody();
        PcxBill pcxBill = billMainService.view(billId);
        List<PcxBillAttachRelationVO>  attachList = billExpenseCommonService.getAttachList(pcxBill);
        if (attachList == null || attachList.isEmpty()) {
            attachList = new ArrayList<>();
        }
        try {
            PcxBillRelationVO apply = billExpenseCommonService.getApplyRelation(pcxBill);
            if (apply != null && apply.getIsVirtual() == 1) {
                PcxBillAttachRelationVO applyAttach = new PcxBillAttachRelationVO();
                applyAttach.setAttachId(apply.getRelBillId());
                attachList.add(applyAttach);
            }
        }catch (Exception e){
            log.error("获取申请单附件失败，bilId {}",pcxBill.getId(), e);
        }

        //加载集合规则附件
        WitRuleResult result = new WitRuleResult();
        result.setBillId(pcxBill.getId());
        result.setFiscal(pcxBill.getFiscal());
        result.setAgyCode(pcxBill.getAgyCode());
        List<String> attaachIds = witAuditRuleService.queryRuleAttach(result);
        if (CollectionUtils.isNotEmpty(attaachIds)){
            for (String attaachId : attaachIds) {
                PcxBillAttachRelationVO applyAttach = new PcxBillAttachRelationVO();
                applyAttach.setAttachId(attaachId);
                attachList.add(applyAttach);
            }
        }

        try {
            if (!attachList.isEmpty()) {
                ecsExpTransService.bindEcsRel(pcxBill, attachList);
            }
        }catch (Exception e){
            log.error("同步附件清单失败，bilId {}",pcxBill.getId(), e);
        }
    }

    @Override
    protected List<PaPartition<String>> selectTasks(Integer lastExecuteMinInterval, Integer maxErrorCount, Integer limit, Integer recentDay) {
        return super.selectTasks(1, maxErrorCount, limit, recentDay);
    }
}
