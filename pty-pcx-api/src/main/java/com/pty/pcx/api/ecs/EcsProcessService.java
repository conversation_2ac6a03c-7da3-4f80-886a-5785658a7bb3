package com.pty.pcx.api.ecs;

import com.pty.pcx.CodeNameVO;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.bill.extrasubsidy.UpdateExtraSubsidyQO;
import com.pty.pcx.qo.ecs.*;
import com.pty.pcx.qo.ecs.common.*;

import javax.validation.Valid;
import java.util.List;

/**
 * ecs票加工处理服务
 */
public interface EcsProcessService {

    /**
     * 按票ids发起报销
     * @param expenseQO
     * @return
     */
    CheckMsg<?> startExpense(StartExpenseQO expenseQO);

    /**
     * 添加发票
     * @param invoiceQO
     * @return
     */
    CheckMsg<?> addEcsBill(AddInvoicesQO invoiceQO);

    /**
     * 报销单理票结果
     * @param billId
     * @return
     */
    CheckMsg<?> ecsView(String billId);

    /**
     * 删除费用和票
     * @param qo
     * @return
     */
    CheckMsg<?> delEcsBillExpense(DelExpenseQO qo);


    CheckMsg updateEcs(UpdateEcsQO updateQO);

    /**
     * 理票页面查看票的信息
     * @param updateEcsDetailAndPaymentQO
     * @return
     */
    CheckMsg<?> ecsExpView(UpdateEcsDetailAndPaymentQO updateEcsDetailAndPaymentQO);


    CheckMsg ecsItemExpView(UpdateEcsQO updateQO);

    /**
     * 手动添加无票费用明细
     * @param updateNoEcsDetailQO
     * @return
     */
    CheckMsg<?> updateNoEcsDetail(UpdateNoEcsDetailQO updateNoEcsDetailQO);

    CheckMsg<List<CodeNameVO>> getNoEcsReason(QueryNoEcsQO qo);

    /**
     * 按票ids发起报销
     * @param expenseQO
     * @return
     */
    CheckMsg<?> startCommonExpense(StartExpenseQO expenseQO);

    /**
     * 添加票
     * @param invoiceQO
     * @return
     */
    CheckMsg addEcsCommonBill(AddInvoicesQO invoiceQO);

    /**
     * 删除票
     * @param qo
     * @return
     */
    CheckMsg<?> delEcsCommonExpense(DelEcsCommonQO qo);

    /**
     * 更新票信息
     * @param commonQO
     * @return
     */
    CheckMsg<?> updateEcsCommon(UpdateEcsCommonQO commonQO);

    /**
     * 添加或修改无票费用
     * @param commonQO
     * @return
     */
    CheckMsg updateNoEcsCommon(UpdateNoEcsCommonQO commonQO);

    /**
     * 更新票费用类型
     * @param ecsRelSumQO
     * @return
     */
    CheckMsg updateEcsExpType(EcsRelSumQO ecsRelSumQO);

    /**
     * 更新票费用类型
     * @param ecsRelSumQO
     * @return
     */
    CheckMsg updateEcsExpTypeNew(EcsRelSumQO ecsRelSumQO);

    CheckMsg ecsRelList(QueryEcsRelItemQO itemQO);

    CheckMsg ecsRelItem(QueryEcsRelItemQO itemQO);

    CheckMsg commonBillExpenseTypeList(CommonExpenseTypeQO qo);

    CheckMsg ecsExpCommonView(UpdateEcsCommonQO updateEcsCommonQO);

    CheckMsg<?> calculateTaxAudit(EcsCalculateTaxQO qo);

    CheckMsg queryExpenseTypeAddition(QueryExpenseTypeAdditionQO updateQO) ;

    CheckMsg<?> bindContract(BindContractQO bindContractQO);

    CheckMsg<?> unbindContract(BindContractQO bindContractQO);

    CheckMsg updateEcsAttach(UpdateEcsAttachQO updateEcsAttachQO);

    List<InvoiceDtoWrapper> analysisEcs(ExpInvoiceQO invoiceQO, String itemCode);

    CheckMsg<?> unMatchEcsReplenishTrip(UnMatchEcsReplenishQO unMatchEcsReplenishQO);

    CheckMsg<?> updateTripTime(UpdateTripTimeQO updateTripTimeQO);

    CheckMsg<?> updateAddition(UpdateAdditionQO updateEcsQO);

    CheckMsg<?> queryDetailAddition(QueryDetailAdditionQO queryDetailAdditionQO);

    int cleanInvalidExpenseBill(long interval);

    CheckMsg<?> updateExtraSubsidy(UpdateExtraSubsidyQO qo);

    CheckMsg<?> queryEcsModifyField(QueryEcsModifyFieldQO qo);

    CheckMsg<?> queryNoEcsDetail(QueryNoEcsDetailQO qo);

    CheckMsg<?> detailMountTrip(DetailMountTripQO qo);

    CheckMsg<?> changeItem(ChangeItemQO qo);
}
