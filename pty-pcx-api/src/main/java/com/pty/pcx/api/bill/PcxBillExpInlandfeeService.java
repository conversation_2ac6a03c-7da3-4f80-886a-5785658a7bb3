package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillExpInlandfee;
import com.pty.pcx.qo.ecs.AddInvoicesQO;
import com.pty.pcx.qo.ecs.InvoiceDtoWrapper;
import com.pty.pcx.qo.ecs.StartExpenseQO;
import com.pty.pcx.qo.ecs.common.UpdateEcsCommonQO;
import com.pty.pcx.qo.ecs.common.UpdateNoEcsCommonQO;

import java.util.List;
import java.util.Map;

public interface PcxBillExpInlandfeeService {

    /**
     * 根据条件查询招待费用主表数据
     * @param billIds
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<PcxBillExpInlandfee> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode);

    String saveBillAndExpense(StartExpenseQO expenseQO, List<InvoiceDtoWrapper> wrappers);

    void addEcsBills(AddInvoicesQO expenseQO, List<InvoiceDtoWrapper> wrappers, PcxBill view);

    void updateEcsBill(UpdateEcsCommonQO ecsCommonQO, PcxBill view);

    void updateNoEcs(UpdateNoEcsCommonQO qo, PcxBill view);
}