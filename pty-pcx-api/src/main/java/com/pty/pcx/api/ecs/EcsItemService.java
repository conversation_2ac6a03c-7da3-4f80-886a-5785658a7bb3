package com.pty.pcx.api.ecs;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.qo.ecs.ExpInvoiceQO;
import com.pty.pcx.qo.ecs.QueryAllowChangeItemListQO;

/**
 * 发票事项服务
 */
public interface EcsItemService {

    /**
     * 按发票列表和用户返回事项列表
     * @param invoiceQO
     * @return
     */
    CheckMsg<?> getItemList(ExpInvoiceQO invoiceQO);

    CheckMsg<?> allowChangeItemList(QueryAllowChangeItemListQO qo);
}
