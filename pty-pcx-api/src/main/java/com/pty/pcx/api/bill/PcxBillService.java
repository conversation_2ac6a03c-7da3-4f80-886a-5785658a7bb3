package com.pty.pcx.api.bill;


import com.pty.pcx.common.enu.PositionBlockEnum;
import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.bill.PcxBillSettlement;
import com.pty.pcx.entity.wit.WitRuleResult;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pcx.qo.bill.*;
import com.pty.pcx.vo.bill.*;
import com.pty.pcx.vo.workflow2.DoneTaskVO;
import com.pty.pcx.vo.workflow2.TodoTaskVO;
import com.pty.pub.common.bean.PageResult;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 单据主表(PcxBill)表服务接口
 * <AUTHOR>
 * @since 2024-11-25 14:23:02
 */
public interface PcxBillService {
    /**
     * 获取报销人员信息判断是否可以发起报销（入口判断）
     * @param qo
     * @return
     */
    CheckMsg<MadEmployeeDTO> getExpenseUserInfo(EmpInfoQO qo);

    /**
     * 存储或者更新主单费用、费用明细信息并校验送审 根据参数判断是否需要送审
     * @param qo 保存数据
     * @return
     */
    CheckMsg<Map<String,Object>> saveDataAndApproved(PcxBillQO qo);

    /**
     * 仅保存更新单据信息
     * @param qo
     * @return
     */
    PcxBill saveOrUpdate(PcxBillQO qo);

    /**
     * 查询基础单据
     * @param billId
     * @return
     */
    PcxBill viewBasic(String billId);

    /**
     * 查询单据信息(如果能确定自己获取的单据信息，最好增加查询需要的块)
     * @param billId
     * @param viewType null 查询所有
     * @return
     */
    CheckMsg<PcxBillVO> view(String billId,PositionBlockEnum... viewType);

    /**
     * 根据viewType查询
     * @param billId
     * @param viewTypes null 查询所有
     * @return
     */
    CheckMsg<PcxBillVO> view(String billId, List<String> viewTypes);

    /**
     * 跳到下一待办
     * @return
     */
    CheckMsg<String> next(PcxBillViewQO qo);

    /**
     * 查询代办任务数量
     * @return
     */
    CheckMsg<Integer> todoTaskNum(PcxBillListQO qo);

    /**
     * 查询当前人的报销，申请，借款数量
     * @param qo
     * @return
     */
    CheckMsg<Map<String, Long>> billFuncTaskNum(PcxBillListQO qo);

    /**
     * 查询单据列表
     * @param qo
     * @return
     */
    CheckMsg<PcxBillPageResult<PcxBillListVO>> selectWithPage(PcxBillListQO qo);

    /**
     * 导出支出台账列表
     * @param qo
     * @return
     */
    ResponseEntity<byte[]> exportData(PcxBillExportQO qo) throws IOException;

    /**
     * 查询支出台账列表
     * @param qo
     * @return
     */
    CheckMsg<PageResult<PcxBillExpLedgerVO>> selectExpLedgerPage(PcxBillListQO qo);

    /**
     * 获取单据金额统计数据
     * @param qo
     * @return
     */
    CheckMsg<Map<String, BigDecimal>> getAmtSum(PcxBillListQO qo);

    /**
     * 获取最终申请单据
     * @param qo
     * @return
     */
    CheckMsg<Map<String, Object>> getFinalBill(PcxBillQO qo);

    /**
     * 获取
     * @param qo
     * @return
     */
    CheckMsg<?> getEnableBillType(PcxMadBaseQO qo);

    /**
     * 删除单据
     * @param qo
     * @return
     */
    CheckMsg<Void> deleteData(PcxBillQO qo);

    /**
     * 默认结算查询
     * @param expenseQO
     * @return
     */
    CheckMsg<Map<String, List<PcxBillSettlement>>> selectDefaultSettlement(DefaultSettlementQO expenseQO);

    /**
     * 默认结算方式
     * @param expenseQO
     * @return
     */
    CheckMsg<Map<String, List<PcxBillSettlement>>> defaultLabourSettlement(DefaultSettlementQO expenseQO);

    /**
     * 查询单据费用标准
     */
    CheckMsg<List<ExpStandSnapshotsFrontVO>> getBillExpenseStand(PcxBillExpenseStandQO qo);
    /**
     * 送审接口
     * @param qo
     * @return
     */
    CheckMsg<Map<String, Object>> approved(PcxBillApprovedVO qo);

    /**
     * 保存关联单据
     * @param qo
     * @return
     */
    CheckMsg<?> saveRel(PcxBillRelationQO qo);

    /**
     * 单据稽核
     * @param billId 单据Id
     * @param anchorPoints 提交锚点
     * @return
     */
    CheckMsg<List<WitRuleResult>> auditRule(String billId, List<String> anchorPoints);

    /**
     * 下载单据所有附件
     * @param qo
     * @return
     */
    ResponseEntity<byte[]> downAllBillAttachZip(List<String> qo) throws IOException;

    CheckMsg<?> changeBalance(PcxBillQO qo, Boolean aBoolean);

    /**
     * 获取单据的基本信息
     * @param billId
     * @return
     */
    PcxBillBasicVO basicInfo(String billId);

    List<PcxBill> selectByBillIds(List<String> billIds);

    PageResult<PcxBillRepaymentVO> getPcxBillRepaymentList(PcxBillRepaymentQO qo);

    List<PcxBillRepaymentVO> getPcxBillRepaymentList(String billId);

    CheckMsg<Map<String, Object>> addRepayment(PcxBillAddRepaymentQO qo);

    /**
     * 查询申请单费用标准
     * @param qo
     * @return
     */
    CheckMsg<List<ExpStandSnapshotsFrontVO>> getReqBillExpenseStand(PcxReqBillExpenseStandQO qo);

    /*****
     * pc 端获取申请单据
     * @param qo
     * @return
     */
    CheckMsg<PageResult<PcxBill>> getApplyBill(PcxBillQueryQO qo);

    /**
     * 费用办结
     * @param qo
     * @return
     */
    CheckMsg<Map<String, Object>> finishBill(PcxBillExpenseCompletedQO qo);

    /**
     * 业务操作单据批量结项
     * @param qo
     * @return
     */
    CheckMsg<?> setSettle(PcxBillQO qo) throws Exception;


    /**
     * 查询本人年度报销金额和笔数
     * @param qo
     * @return
     */
    CheckMsg<PcxMyFiscalExpenseVO> getMyFiscalExpenseInfo(PcxMyFiscalExpenseQO qo);

    List<String> selectInvalidBillIds();

    /**
     * 根据查询对象获取待办任务列表
     * 此方法主要用于处理待办任务的查询逻辑，根据查询对象的条件决定是否查询待办任务，
     * 并将查询结果返回
     *
     * @param qo 查询对象，包含查询待办任务所需的条件，如标签、部门代码、机构代码和用户代码
     * @return 返回待办任务列表，如果查询失败或没有待办任务，则返回空列表
     */
    List<TodoTaskVO> fetchTodoTasks(PcxBillListQO qo);
    /**
     * 根据查询对象获取用户审批中的任务列表
     * 此方法专注于处理审批中的任务，特别是当查询标签为null或特定值时
     * 它通过调用流程服务来获取待办任务，旨在为用户提供一个筛选机制，
     * 以便用户可以轻松地获取他们提交审批的任务
     *
     * @param qo 查询对象，包含查询审批中任务所需的参数，如MOF部门代码、机构代码、用户代码和标签
     * @return 返回一个待审批任务的列表如果查询条件不满足或服务调用失败，将返回一个空列表
     */
    List<TodoTaskVO> fetchApprovingTasks(PcxBillListQO qo);
    /**
     * 根据查询对象获取用户审批中的任务列表
     * 此方法专注于处理审批中的任务，特别是当查询标签为null或特定值时
     * 它通过调用流程服务来获取待办任务，旨在为用户提供一个筛选机制，
     * 以便用户可以轻松地获取他们提交审批的任务
     *
     * @param qo 查询对象，包含查询审批中任务所需的参数，如MOF部门代码、机构代码、用户代码和标签
     * @return 返回一个待审批任务的列表如果查询条件不满足或服务调用失败，将返回一个空列表
     */
    List<DoneTaskVO> fetchDoneTasks(PcxBillListQO qo);

    void updateComparedStatus(PcxBillQO qo);

    /**
     * 查询当前单据推荐的代报人
     * @param billId
     * @return
     */
    List<MadEmployeeDTO> selectClaimant(String billId);


    /**
     * 重新打开单据
     * @param qo
     * @return
     */
    boolean reOpenBill(PcxBillQO qo);

    PcxBillVO calculateBillAmt(PcxBillCalculateQO qo);
    /**
     * 初始化单据
     * @param qo
     * @return
     */
    CheckMsg initBill(InitBillQO qo);
}

