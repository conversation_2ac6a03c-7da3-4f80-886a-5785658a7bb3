package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBillAmtApportionDepartment;

import java.util.List;

/**
 * 报销单费用分摊部门(PcxBillAmtApportionDepartment)表服务接口
 * <AUTHOR>
 * @since 2025-04-01 07:38:57
 */
public interface PcxBillAmtApportionDepartmentService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBillAmtApportionDepartment selectById(String id);

    void batchUpdate(List<PcxBillAmtApportionDepartment> amtApportionDepartments);

    /**
     * 根据主表id查询部门分摊的逻辑
     * @param apportionId
     * @return
     */
    List<PcxBillAmtApportionDepartment> selectByApportionId(String apportionId);

    /**
     * 分摊信息
     * @param apportionIds
     * @return
     */
    List<PcxBillAmtApportionDepartment> selectBatchByApportionId(List<String> apportionIds);


    /**
     * 根据报销单id查询部门分摊的逻辑
     * @param billId
     * @return
     */
    List<PcxBillAmtApportionDepartment> selectByBillId(String billId);

    /**
     *
     * @param billId
     * @param expenseCode
     * @param deptCode
     * @return
     */
    List<PcxBillAmtApportionDepartment> selectByBillIdAndDept(String billId,String expenseCode,String deptCode);
}


