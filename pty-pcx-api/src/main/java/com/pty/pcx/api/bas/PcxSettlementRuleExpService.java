package com.pty.pcx.api.bas;

import com.github.pagehelper.PageInfo;
import com.pty.pcx.entity.bas.PcxSettlementRuleExp;
import com.pty.pcx.qo.bas.PcxSettlementRuleExpQO;
import com.pty.pcx.vo.bas.PcxSettlementRuleExpVO;
import com.pty.pub.common.bean.Response;
import java.util.List;

/**
 * 特殊费用结算规则设置(PcxBasSettlementExpSetting)表服务接口
 * <AUTHOR>
 * @since 2024-12-09 17:42:51
 */
public interface PcxSettlementRuleExpService {

    /**
     * 查询全部特殊费用结算规则设置
     * @param qo
     * @return
     */
    List<PcxSettlementRuleExp> selectAll(PcxSettlementRuleExpQO qo);

    Response<PageInfo<?>> selectWithPage(PcxSettlementRuleExpQO pcxBasSettlementExpSettingQO);

    /**
     * 保存
     * @param pcxBasSettlementExpSettingQO
     */
    void save(PcxSettlementRuleExpQO pcxBasSettlementExpSettingQO);

    /**
     * 更新
     * @param pcxBasSettlementExpSettingQO
     * @return
     */
    void update(PcxSettlementRuleExpQO pcxBasSettlementExpSettingQO);

    /**
     * 回显
     * @param qo
     * @return
     */
    Response<PcxSettlementRuleExpVO> view(PcxSettlementRuleExpQO qo);

    /**
     * 根据费用类型查询
     * @param qo
     * @return
     */
    Response<PcxSettlementRuleExpVO> selectByExpenseCode(PcxSettlementRuleExpQO qo);

    /**
     * 回显
     * @param qo
     * @return
     */
    void delete(PcxSettlementRuleExpQO qo);
}

