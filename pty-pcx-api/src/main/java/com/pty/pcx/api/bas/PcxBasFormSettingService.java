package com.pty.pcx.api.bas;

import com.pty.pcx.entity.bas.PcxBasFormSetting;
import com.pty.pcx.entity.bill.PcxBillExpDetailBase;
import com.pty.pcx.qo.bas.PcxBasFormSettingListUpdateQO;
import com.pty.pcx.qo.bas.PcxBasFormSettingQueryQO;
import com.pty.pcx.qo.bill.apportion.ApportionBaseQO;
import com.pty.pcx.qo.ecs.common.QueryExpenseTypeAdditionQO;
import com.pty.pcx.vo.positionblock.BlockPropertyVO;
import com.pty.pub.common.bean.Response;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表单字段设置(PcxBasFormSetting)表服务接口
 * <AUTHOR>
 * @since 2024-11-05 19:37:30
 */
public interface PcxBasFormSettingService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBasFormSetting selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxBasFormSetting 实例对象
     * @return 对象列表
     */
    List<PcxBasFormSetting> selectList(PcxBasFormSetting pcxBasFormSetting);
	
    /**
     * 新增数据
     *
     * @param pcxBasFormSetting 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxBasFormSetting pcxBasFormSetting);

    /**
     * 修改数据
     *
     * @param pcxBasFormSetting 实例对象
     * @return 影响行数
     */
    int update(PcxBasFormSetting pcxBasFormSetting);

    Response selectAllFormSetting(PcxBasFormSettingQueryQO pcxBasFormSettingQueryQO);

    Response<?> updateFormSettingList(PcxBasFormSettingListUpdateQO pcxBasFormSettingListUpdateQO);

    Response<?> selectStandElement(PcxBasFormSettingQueryQO pcxBasFormSettingQueryQO);

    Response<?> selectExpenseFormElement(PcxBasFormSettingQueryQO pcxBasFormSettingQueryQO);

    Object selectCondition(String formType, PcxBasFormSettingQueryQO qo);

    List<PcxBasFormSetting> selectByQO(PcxBasFormSettingQueryQO qo);

    List<PcxBasFormSetting> selectValidNotNullSettings(String fiscal, String avgCode, String mofDivCode);

    boolean isConfirm(List<? extends PcxBillExpDetailBase> detailList,
                      Map<String, List<PcxBasFormSetting>> formSettingMap,
                      Set<String> hintList,
                      Set<String> lackFieldValue);

    void initExpenseTypeAddition();

    void initCommonExpenseRemarkField();

    List<BlockPropertyVO> getDefaultExpenseTypeAdditionField();

    List<BlockPropertyVO> getTravelExpenseTypeAdditionField(ApportionBaseQO qo);

    List<BlockPropertyVO> collectExpenseTypeAdditionField(QueryExpenseTypeAdditionQO qo);
}


