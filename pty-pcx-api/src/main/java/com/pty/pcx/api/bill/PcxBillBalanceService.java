package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBillBalance;
import com.pty.pcx.qo.bill.PcxBillPaySettlementBalanceQO;

import java.util.List;

/**
 * 单据预算关联表(PcxBillBalance)表服务接口
 * <AUTHOR>
 * @since 2024-11-25 16:20:59
 */
public interface PcxBillBalanceService {
    /**
     * 通过ID查询单条数据
     * @param id 主键
     * @return 实例对象
     */
    List<PcxBillBalance> selectByBillId(String id);
    /**
     * 通过ID批量查询单条数据
     * @param ids 主键
     * @return 实例对象
     */
    List<PcxBillBalance> selectByBillIds(List<String> ids);
}

