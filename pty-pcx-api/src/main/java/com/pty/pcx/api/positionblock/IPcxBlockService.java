package com.pty.pcx.api.positionblock;

import com.pty.pcx.qo.positionblock.PcxBlockCondQO;
import com.pty.pcx.vo.positionblock.PcxBlockVO;

import java.util.List;

/***
 *
 * @ClassName: IPcxBlockService
 * @Description: 岗位样式块接口; 组装岗位样式块
 * <AUTHOR>
 */
public interface IPcxBlockService {
    /***
     * 获取岗位样式块信息 ；
     * 原则上不允许做查询操作，只能做组装操作
     * @return
     */
    List<PcxBlockVO> getBlockInfo(PcxBlockCondQO pcxBlockCondQO);
}
