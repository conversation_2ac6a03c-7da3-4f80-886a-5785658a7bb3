package com.pty.pcx.api.bill;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bill.PcxBillRelation;
import com.pty.pcx.qo.bill.PcxBillRelationQO;

import java.util.List;

/**
 * 单据关联表(PcxBillRelation)表服务接口
 * <AUTHOR>
 * @since 2024-11-25 16:21:00
 */
public interface PcxBillRelationService {
  List<PcxBillRelation> selectByBillId(String billId);

  List<PcxBillRelation> selectByBillIds(List<String> billIds, String relBillFuncCode);

  List<PcxBillRelation> selectByRelBillIds(List<String> billIds);

  CheckMsg<?> save(PcxBillRelationQO qo);

  PcxBillRelation selectApplyRelByQO(PcxBillRelationQO qo);
}

