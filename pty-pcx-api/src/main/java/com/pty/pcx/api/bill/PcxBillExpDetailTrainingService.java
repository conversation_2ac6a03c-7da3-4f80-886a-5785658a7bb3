package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.training.PcxBillExpDetailTraining;
import java.util.List;

public interface PcxBillExpDetailTrainingService {

    /**
     * 获取培训费用明细表数据
     * @param billIds
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<PcxBillExpDetailTraining> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode);

    List<PcxBillExpDetailTraining> selectBatchIds(List<String> collect);

    void deleteBatchIds(List<String> collect);
}