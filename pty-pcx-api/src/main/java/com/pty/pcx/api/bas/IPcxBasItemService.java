package com.pty.pcx.api.bas;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.qo.bas.PcxBasItemQO;
import com.pty.pcx.qo.treasurypay.detail.BillFuncCodeItemRelQO;
import com.pty.pcx.vo.PcxBasItemVO;
import com.pty.pcx.vo.bas.BasItemVO;
import com.pty.pub.common.bean.Response;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * (PcxBasItem)表服务接口
 * <AUTHOR>
 * @since 2024-10-24 21:24:59
 */
public interface IPcxBasItemService {

    /**
     * 新增数据
     *
     * @param pcxBasItem 实例对象
     * @return 影响行数
     */
    Response<?> save(PcxBasItemQO pcxBasItem);

    /**
     * 修改数据
     *
     * @param pcxBasItem 实例对象
     * @return 影响行数
     */
    Response<?> updateById(PcxBasItemQO pcxBasItem);

    /**
     * 分页查询
     * @param pcxBasItemQO 查询对象
     * @return 对象列表
     */
    Response<?> selectWithPage(PcxBasItemQO pcxBasItemQO);

    /***
     * 根据id 查询单个数据详情
     * @param pcxBasItemQO
     * @return
     */
    PcxBasItemVO selectById(PcxBasItemQO pcxBasItemQO);

    /***
     * 根据itemCode 查询单个数据详情
     * @param pcxBasItemQO
     * @return
     */
    PcxBasItemVO selectByItemCode(PcxBasItemQO pcxBasItemQO);

    /***
     * 事项类型启用
     * @param pcxBasItemQO
     * @param isEnable 1- 启用 ； 0-停用
     * @return
     */
    Response<?> disEnableOrEnableByQO(PcxBasItemQO pcxBasItemQO, Integer isEnable);

    Response<?> deleteById(PcxBasItemQO pcxBasItemQO);

    CheckMsg getAll(PcxBasItemQO pcxBasItemQO);

    Response<?> getTopLevelItem(PcxBasItemQO pcxBasItemQO);

    List<BasItemVO> getOwnItem(PcxBasItemQO pcxBasItemQO);

    List<PcxBasItem> selectByParentCodes(List<String> itemCodeList, @NotBlank(message = "单位编码不能为空") String agyCode, @NotBlank(message = "区划不能为空") String mofDivCode, @NotBlank(message = "年度不能为空") String fiscal);

    CheckMsg<?> getRootItem(PcxBasItemQO pcxBasItemQO);

    Response<?> billFuncCodeItemRel(BillFuncCodeItemRelQO qo);

    /**
     * 查询是否启用预算
     * @param qo
     * @return
     */
    boolean isIsCtrlBudget(PcxBasItemQO qo);

}
