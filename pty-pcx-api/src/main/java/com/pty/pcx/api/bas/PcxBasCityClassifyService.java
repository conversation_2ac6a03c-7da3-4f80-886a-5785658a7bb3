package com.pty.pcx.api.bas;


import com.pty.mad.vo.BaseDataVo;
import com.pty.pcx.entity.bas.PcxBasCityClassify;
import com.pty.pcx.qo.bas.BatchUpdateCityClassifyQO;
import com.pty.pcx.qo.bas.BizQueryAllCityClassifyQO;
import com.pty.pcx.qo.bas.QueryCityPeakClassifyQO;
import com.pty.pcx.vo.bas.CityPeakVO;
import com.pty.pub.common.bean.Response;

import java.util.List;

/**
 * 城市分类表(PcxBasCityClassify)表服务接口
 * <AUTHOR>
 * @since 2024-11-08 10:06:08
 */
public interface PcxBasCityClassifyService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PcxBasCityClassify selectById(String id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pcxBasCityClassify 实例对象
     * @return 对象列表
     */
    List<PcxBasCityClassify> selectList(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 新增数据
     *
     * @param pcxBasCityClassify 实例对象
     * @return 影响行数
     */
    int insertSelective(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 修改数据
     *
     * @param pcxBasCityClassify 实例对象
     * @return 影响行数
     */
    int update(PcxBasCityClassify pcxBasCityClassify);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    Response<?> updateCityClassify(BatchUpdateCityClassifyQO batchUpdateCityClassifyQO);

    Response<?> bizSelectAllClassify(BizQueryAllCityClassifyQO queryAllCityClassifyQO);

    List<BaseDataVo> selectCityClassify(BizQueryAllCityClassifyQO qo);

    /**
     * 判断城市是否是淡旺季
     * @param queryCityPeakClassifyQO
     * @return
     */
    List<CityPeakVO> isCityPeak(QueryCityPeakClassifyQO queryCityPeakClassifyQO);

    /**
     * 获取城市淡旺季
     * @param queryCityPeakClassifyQO
     * @return
     */
    List<CityPeakVO> getCityPeakDate(QueryCityPeakClassifyQO queryCityPeakClassifyQO);

    /**
     * 根据类型编码获取 淡旺季
     * @param classifyCode
     * @return
     */
    CityPeakVO getCityPeakDateByClassifyCode(String classifyCode);

}


