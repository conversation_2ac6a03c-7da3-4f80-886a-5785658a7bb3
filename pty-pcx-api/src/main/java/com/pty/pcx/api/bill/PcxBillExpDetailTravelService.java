package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBillExpDetailTravel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 单据费用明细_差旅费_明细(PcxBillExpDetailTravel)表服务接口
 * <AUTHOR>
 * @since 2024-11-25 14:23:04
 */
public interface PcxBillExpDetailTravelService {

    List<PcxBillExpDetailTravel> selectList(List<String> billIdList, String agyCode, String fiscal, String mofDivCode);

    /**
     * 汇总某个费用明细的金额
     * @param empCode
     * @return
     */
    BigDecimal sumYearSub(String empCode);
}

