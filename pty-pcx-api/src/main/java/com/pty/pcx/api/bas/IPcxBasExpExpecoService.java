package com.pty.pcx.api.bas;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bas.PcxBasExpExpeco;
import com.pty.pcx.entity.bas.PcxBasExpType;
import com.pty.pcx.qo.bas.PcxBasExpExpecoQO;

import java.util.List;

public interface IPcxBasExpExpecoService {
    CheckMsg<?> save(PcxBasExpExpecoQO qo);

    CheckMsg<?> batchSave(List<PcxBasExpExpecoQO> pcxBasExpExpecoQOList);

    CheckMsg<?> deleteByExpense(PcxBasExpExpecoQO qo);

    List<PcxBasExpExpeco>  selectByExpenseCode(PcxBasExpExpecoQO qo);

    List<PcxBasExpType> selectByExpeco(PcxBasExpExpecoQO qo);
}
