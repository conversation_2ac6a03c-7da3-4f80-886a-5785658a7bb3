package com.pty.pcx.api.transfer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pty.pcx.entity.bill.PcxBill;
import com.pty.pcx.entity.transfer.PcxBillSync;
import com.pty.pub.common.bean.Response;

import java.util.List;

public interface PcxBillSyncService extends IService<PcxBillSync> {

    PcxBillSync getPcxBillSyncByBillId(String billId);

    List<PcxBill> getUnSyncedPcxBill(Integer bizType, Integer syncStatus);

    /**
     * 抽取待同步草稿单据
     */
    void extractDraftPcxBillSync();

    /**
     * 抽取待同步单据
     */
    void extractPcxBillSync();

    void updateSyncStatus(PcxBillSync pcxBillSync);

    void resetPcxBillSync(String id);

    /**
     * 设置单据支付信息
     */
    void setBillPaymentInfo();

    /**
     * 设置单据支付状态
     */
    void setBillPayStatus();

    /**
     * 撤销单据
     */
    Response<?> revokeBill(String billId);

    /**
     * 重发已撤销单据
     */
    void resendRevokedBills();

    /**
     * 关账单据重发
     */
    void resendClosedBills();

    /**
     * 删除无用的报销单信息
     */
    void deleteUselessBills();

}
