package com.pty.pcx.api.bill;

import com.pty.pcx.entity.bill.PcxBillExpDetailCommon;
import com.pty.pcx.entity.bill.meeting.PcxBillExpDetailMeeting;
import java.util.List;

public interface PcxBillExpDetailMeetingService {

    /**
     * 根据条件查询会议费用明细表数据
     * @param billIds
     * @param agyCode
     * @param fiscal
     * @param mofDivCode
     * @return
     */
    List<PcxBillExpDetailMeeting> selectList(List<String> billIds, String agyCode, String fiscal, String mofDivCode);

    List<PcxBillExpDetailMeeting> selectBatchIds(List<String> collect);

    void deleteBatchIds(List<String> collect);
}