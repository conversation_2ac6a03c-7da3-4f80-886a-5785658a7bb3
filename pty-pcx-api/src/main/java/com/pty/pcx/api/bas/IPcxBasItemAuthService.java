package com.pty.pcx.api.bas;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.bas.PcxBasItem;
import com.pty.pcx.entity.bas.PcxBasItemAuth;
import com.pty.pcx.qo.bas.PcxBasItemAuthQO;

import java.util.List;

public interface IPcxBasItemAuthService {
    CheckMsg<?> saveBatch(List<PcxBasItemAuth> pcxBasItemAuthList);
    CheckMsg<?> deleteByItemCode(PcxBasItemAuthQO pcxBasItemAuthQO);
    List<PcxBasItemAuth> selectByQO(PcxBasItemAuthQO qo);
    List<PcxBasItem> getOwnItem(PcxBasItemAuthQO pcxBasItemAuthQO);
}
