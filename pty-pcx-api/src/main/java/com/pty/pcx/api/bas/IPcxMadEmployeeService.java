package com.pty.pcx.api.bas;

import com.pty.mad.qo.MadSearchAllAccInfoQO;
import com.pty.pcx.dto.mad.MadEmployeeDTO;
import com.pty.pcx.qo.bas.PcxMadBaseQO;
import com.pty.pub.common.bean.Response;

import java.util.List;

public interface IPcxMadEmployeeService {

    List<MadEmployeeDTO> select(PcxMadBaseQO pcxDepartmentQO);

    /**
     * 搜索个人、公务卡和对公账户
     * @param searchQO
     * @return
     */
    Response<?> searchAllAccountInfo(MadSearchAllAccInfoQO searchQO);


    List<MadEmployeeDTO> selectByMadCodes(List<String> madCodes, String agyCode, Integer fiscal, String mofDivCode);

    List<MadEmployeeDTO> selectByUserCodes(List<String> userCodes, String agyCode, Integer fiscal, String mofDivCode);


    List<MadEmployeeDTO> selectByUkExFiscals(List<String> ukExFiscals, Integer fiscal);
}
