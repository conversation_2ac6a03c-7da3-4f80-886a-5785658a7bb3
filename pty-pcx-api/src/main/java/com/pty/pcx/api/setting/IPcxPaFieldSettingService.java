package com.pty.pcx.api.setting;

import com.pty.pcx.common.util.CheckMsg;
import com.pty.pcx.entity.setting.PaFieldSetting;
import com.pty.pcx.qo.balance.PcxBalancesQO;
import com.pty.pcx.qo.setting.InvoiceGuideQO;
import com.pty.pcx.qo.setting.PaFieldSettingQO;
import com.pty.pcx.vo.balance.ProjectBalVO;
import com.pty.pcx.vo.setting.InvoiceGuideVO;
import com.pty.pcx.vo.setting.PaFieldSettingVO;

import java.util.List;

public interface IPcxPaFieldSettingService {

    /**
     * 查询预算指标
     * @param paFieldSettingQO
     * @return
     */
    List<PaFieldSettingVO> getMetricSetting(PaFieldSettingQO paFieldSettingQO);

    /**
     * 更新预算指标
     * @param paFieldSettingList
     */
    CheckMsg<?> deleteAndInsert(List<PaFieldSetting> paFieldSettingList);

    /**
     * 修改列表页字段配置
     * @param paFieldSettingList
     */
    CheckMsg<?> updatePaFieldSettingByUser(List<PaFieldSetting> paFieldSettingList);

    /***
     *  获取项目的指标、额度信息
     * @param qo
     * @return
     */
    CheckMsg<List<ProjectBalVO>> getProjectData(PcxBalancesQO qo);

    CheckMsg<?> getBalanceData(PcxBalancesQO qo);

    List<PaFieldSettingVO> getSettings(PaFieldSettingQO paFieldSettingQO);

    List<PaFieldSettingVO> getUserSettings(PaFieldSettingQO paFieldSettingQO);

    /**
     * 查询开票助手-开票指引
     * @param invoiceGuideQO
     * @return
     */
    CheckMsg<List<InvoiceGuideVO>> selectInvoiceGuide(InvoiceGuideQO invoiceGuideQO);
}
